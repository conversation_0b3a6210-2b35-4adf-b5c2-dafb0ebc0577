#!/usr/bin/env python3
"""
Test script to verify venue context is being passed correctly.
"""


def test_venue_context_logic():
    """Test the venue context logic for different views."""

    # Simulate different view scenarios
    test_cases = [
        # (view_name, has_venue_context, venue_exists, expected_sidebar_text)
        ("dashboard/provider/", True, True, "My Venue"),
        (
            "dashboard/provider/todays-bookings/",
            False,
            True,
            "My Venue",
        ),  # Should show My Venue if venue exists
        ("accounts/provider/team/", True, True, "My Venue"),  # Now passes venue context
        (
            "accounts/provider/profile/",
            True,
            True,
            "My Venue",
        ),  # Now passes venue context
        (
            "venues/provider/venues/101/",
            True,
            True,
            "My Venue",
        ),  # Already passes venue context
        ("dashboard/provider/", False, False, "Create Venue"),
        ("accounts/provider/team/", True, False, "Create Venue"),  # No venue exists
    ]

    print("Testing venue context logic for different views:")
    print("=" * 60)

    for view_name, has_venue_context, venue_exists, expected in test_cases:
        # Simulate the updated template logic:
        # {% if not has_venue and not venue %} -> Create Venue
        # {% elif venue %} -> My Venue
        # {% else %} -> Create Venue
        if not has_venue_context and not venue_exists:
            result = "Create Venue"
        elif venue_exists:
            result = "My Venue"
        else:
            result = "Create Venue"

        status = "✓ PASS" if result == expected else "✗ FAIL"
        print(f"View: {view_name}")
        print(f"  Has venue context: {has_venue_context}, Venue exists: {venue_exists}")
        print(f"  Expected: {expected}, Got: {result} - {status}")
        print()


if __name__ == "__main__":
    test_venue_context_logic()
