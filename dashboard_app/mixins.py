# """Reusable mixins for dashboard access control."""

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import redirect

# --- Local App Imports ---
from .constants import ADMIN_ONLY_ERROR, CUSTOMER_ONLY_ERROR, PROVIDER_ONLY_ERROR


# --- RoleRequiredMixin ---
class RoleRequiredMixin(LoginRequiredMixin):
    role_attr = None
    error_message = ""

    def dispatch(self, request, *args, **kwargs):
        if self.role_attr and not getattr(request.user, self.role_attr, False):
            messages.error(request, self.error_message)
            return redirect("venues_app:home")
        return super().dispatch(request, *args, **kwargs)


# --- CustomerRequiredMixin ---
class CustomerRequiredMixin(RoleRequiredMixin):
    role_attr = "is_customer"
    error_message = CUSTOMER_ONLY_ERROR


# --- ProviderRequiredMixin ---
class ProviderRequiredMixin(RoleRequiredMixin):
    role_attr = "is_service_provider"
    error_message = PROVIDER_ONLY_ERROR


# --- StaffRequiredMixin ---
class StaffRequiredMixin(RoleRequiredMixin):
    role_attr = "is_staff"
    error_message = ADMIN_ONLY_ERROR
