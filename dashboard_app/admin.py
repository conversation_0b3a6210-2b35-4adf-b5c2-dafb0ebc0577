# --- Third-Party Imports ---
from django.contrib import admin
from django.utils.html import format_html

# --- Local App Imports ---
from .models import FavoriteVenue


# --- FavoriteVenue Admin ---
@admin.register(FavoriteVenue)
class FavoriteVenueAdmin(admin.ModelAdmin):
    """Admin interface for FavoriteVenue model."""

    list_display = [
        "customer_email",
        "venue_name",
        "venue_city",
        "added_date",
        "venue_status",
    ]
    list_filter = [
        "added_date",
        "venue__approval_status",
        "venue__visibility",
        "venue__city",
        "venue__state",
    ]
    search_fields = [
        "customer__email",
        "venue__venue_name",
        "venue__city",
        "venue__state",
    ]
    readonly_fields = ["added_date", "updated_at"]
    ordering = ["-added_date"]
    date_hierarchy = "added_date"

    def customer_email(self, obj):
        """Return customer email."""
        return obj.customer.email

    customer_email.short_description = "Customer Email"
    customer_email.admin_order_field = "customer__email"

    def venue_name(self, obj):
        """Return venue name."""
        return obj.venue.venue_name

    venue_name.short_description = "Venue Name"
    venue_name.admin_order_field = "venue__venue_name"

    def venue_city(self, obj):
        """Return venue city."""
        return f"{obj.venue.city}, {obj.venue.state}"

    venue_city.short_description = "Location"
    venue_city.admin_order_field = "venue__city"

    def venue_status(self, obj):
        """Return venue status with color coding."""
        if obj.venue.approval_status == "approved" and obj.venue.visibility == "active":
            return format_html('<span style="color: green;">Active</span>')
        elif obj.venue.approval_status == "pending":
            return format_html('<span style="color: orange;">Pending</span>')
        else:
            return format_html('<span style="color: red;">Inactive</span>')

    venue_status.short_description = "Venue Status"

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related("customer", "venue")
