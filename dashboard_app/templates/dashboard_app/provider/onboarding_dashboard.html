{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}Welcome to CozyWish - Get Started{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% endblock %}

{% block extra_css %}
<!-- Additional Google Fonts for Display Typography -->
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{% static 'dashboard_app/css/dashboard.css' %}">
<style>
    /* CozyWish Onboarding Dashboard - Professional Design System */

    /* CSS Custom Properties */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-300: #d4d4d4;
        --cw-neutral-400: #a3a3a3;
        --cw-neutral-500: #737373;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-900: #171717;

        /* Semantic Colors */
        --cw-success: #059669;
        --cw-warning: #d97706;
        --cw-error: #dc2626;
        --cw-info: #0284c7;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Typography */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Hero Section */
    .onboarding-hero {
        background: var(--cw-gradient-card-subtle);
        padding: 4rem 0;
        margin-bottom: 3rem;
        border-radius: 1rem;
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(250, 225, 215, 0.3);
    }

    .onboarding-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="onboarding-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fae1d7" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23onboarding-pattern)"/></svg>') repeat;
        opacity: 0.5;
        z-index: 1;
    }

    .onboarding-hero .container {
        position: relative;
        z-index: 2;
    }

    .onboarding-title {
        font-family: var(--cw-font-display);
        font-size: 3rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        line-height: 1.2;
        text-shadow: 0 2px 4px rgba(47, 22, 15, 0.1);
    }

    .onboarding-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-700);
        margin-bottom: 2.5rem;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Progress Bar */
    .progress-cw {
        height: 24px;
        background-color: var(--cw-neutral-200);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .progress-bar-cw {
        background: var(--cw-gradient-brand-button);
        height: 100%;
        border-radius: 12px;
        transition: width 0.8s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
        box-shadow: 0 2px 4px rgba(47, 22, 15, 0.2);
    }

    .progress-text {
        color: var(--cw-neutral-700);
        font-weight: 600;
        margin-top: 1rem;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Section Cards */
    .card-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: var(--cw-gradient-card-subtle);
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .card-cw-header {
        background: var(--cw-brand-accent);
        border-bottom: 1px solid rgba(250, 225, 215, 0.5);
        padding: 1.5rem 2rem;
    }

    .card-cw-header h4 {
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        font-weight: 700;
        margin-bottom: 0;
        font-size: 1.375rem;
    }

    .card-cw-body {
        padding: 2rem;
    }

    /* Checklist Items */
    .checklist-item {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        border-left: 4px solid var(--cw-neutral-300);
        cursor: pointer;
    }

    .checklist-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-accent);
    }

    .checklist-item.completed {
        border-left-color: var(--cw-success);
        background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
        border-color: rgba(5, 150, 105, 0.2);
    }

    .checklist-item.high-priority {
        border-left-color: var(--cw-error);
        background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
        border-color: rgba(220, 38, 38, 0.2);
    }

    .checklist-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .checklist-item.completed .checklist-icon {
        background: var(--cw-success);
        color: white;
    }

    .checklist-item.high-priority .checklist-icon {
        background: var(--cw-error);
        color: white;
    }

    .checklist-item:not(.completed):not(.high-priority) .checklist-icon {
        background: var(--cw-neutral-200);
        color: var(--cw-neutral-600);
    }

    .checklist-content h5 {
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1.125rem;
    }

    .checklist-description {
        color: var(--cw-neutral-600);
        margin-bottom: 1rem;
        line-height: 1.5;
    }

    /* Feature Cards */
    .feature-card {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: var(--cw-gradient-card-subtle);
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        padding: 1.5rem 1rem;
        text-align: center;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .feature-title {
        font-family: var(--cw-font-heading);
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
    }

    .feature-description {
        color: var(--cw-neutral-600);
        line-height: 1.6;
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .feature-benefits {
        text-align: left;
    }

    .feature-benefits li {
        color: var(--cw-neutral-700);
        font-size: 0.8rem;
        margin-bottom: 0.375rem;
    }

    /* Help Resources */
    .help-resource {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 0.75rem;
        padding: 1.25rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .help-resource:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-accent);
    }

    .help-resource h6 {
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .help-resource .text-muted {
        color: var(--cw-neutral-600) !important;
    }

    /* Quick Action Buttons */
    .quick-action-btn {
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        transition: all 0.3s ease;
        font-family: var(--cw-font-heading);
        font-weight: 600;
        text-align: left;
        border: none;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .quick-action-btn.btn-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
    }

    .quick-action-btn.btn-success {
        background: linear-gradient(135deg, var(--cw-success) 0%, #047857 100%);
        border: none;
    }

    .quick-action-btn.btn-info {
        background: linear-gradient(135deg, var(--cw-info) 0%, #0369a1 100%);
        border: none;
    }

    /* Motivational Footer */
    .motivational-footer {
        background: var(--cw-brand-accent);
        border: 1px solid rgba(250, 225, 215, 0.5);
        border-radius: 1rem;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .motivational-footer::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footer-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M15,5 Q20,10 15,15 Q10,10 15,5" fill="%23f1d4c4" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23footer-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .motivational-footer .container,
    .motivational-footer > * {
        position: relative;
        z-index: 2;
    }

    .motivational-footer h4 {
        font-family: var(--cw-font-display);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .motivational-footer p {
        color: var(--cw-neutral-700);
        font-size: 1.125rem;
        margin-bottom: 2rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Text Utilities */
    .text-brand-cw {
        color: var(--cw-brand-primary) !important;
    }

    .text-neutral-cw {
        color: var(--cw-neutral-600) !important;
    }

    .bg-brand-accent-cw {
        background-color: var(--cw-brand-accent) !important;
    }

    /* Badge Styles */
    .badge.bg-success {
        background-color: var(--cw-success) !important;
    }

    .badge.bg-danger {
        background-color: var(--cw-error) !important;
    }

    .badge.bg-secondary {
        background-color: var(--cw-neutral-500) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .onboarding-title {
            font-size: 2.25rem;
        }

        .onboarding-subtitle {
            font-size: 1.125rem;
        }

        .card-cw-header,
        .card-cw-body {
            padding: 1.5rem;
        }

        .checklist-item {
            padding: 1.25rem;
        }

        .checklist-icon {
            width: 50px;
            height: 50px;
        }

        .feature-card {
            padding: 1.5rem 1rem;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .motivational-footer {
            padding: 2rem 1.5rem;
        }

        .motivational-footer h4 {
            font-size: 1.5rem;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            padding: 0.875rem 1.5rem;
            font-size: 0.95rem;
        }
    }

    @media (max-width: 576px) {
        .onboarding-hero {
            padding: 3rem 0;
            margin-bottom: 2rem;
        }

        .onboarding-title {
            font-size: 1.875rem;
        }

        .checklist-item {
            flex-direction: column;
            text-align: center;
        }

        .checklist-icon {
            margin-bottom: 1rem;
            margin-right: 0;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }

        .quick-action-btn {
            padding: 1rem;
            font-size: 0.9rem;
        }
    }

    /* Accessibility Improvements */
    .btn-cw-primary:focus,
    .btn-cw-secondary:focus,
    .checklist-item:focus {
        outline: 3px solid var(--cw-brand-accent);
        outline-offset: 2px;
    }

    .checklist-item {
        cursor: pointer;
        position: relative;
    }

    .checklist-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 0.75rem;
        transition: box-shadow 0.2s ease;
    }

    .checklist-item:focus::before {
        box-shadow: 0 0 0 3px var(--cw-brand-accent);
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .onboarding-hero {
            background: white;
            border: 2px solid var(--cw-brand-primary);
        }

        .card-cw {
            border: 2px solid var(--cw-brand-primary);
            background: white;
        }

        .feature-card {
            border: 2px solid var(--cw-brand-primary);
            background: white;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .checklist-item,
        .feature-card,
        .help-resource,
        .btn-cw-primary,
        .btn-cw-secondary,
        .progress-bar-cw {
            transition: none;
        }

        .checklist-item:hover,
        .feature-card:hover,
        .help-resource:hover,
        .btn-cw-primary:hover,
        .btn-cw-secondary:hover {
            transform: none;
        }
    }

    /* Touch device optimizations */
    @media (hover: none) and (pointer: coarse) {
        .checklist-item:hover,
        .feature-card:hover,
        .help-resource:hover {
            transform: none;
        }

        .checklist-item,
        .feature-card,
        .help-resource {
            transition: background-color 0.2s ease;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            min-height: 44px;
            min-width: 44px;
        }
    }
</style>
{% endblock %}

{% block content %}
<main class="container-fluid" role="main">
    <!-- Welcome Hero Section -->
    <section class="onboarding-hero text-center" aria-labelledby="welcome-heading">
        <div class="container">
            <h1 id="welcome-heading" class="onboarding-title">
                <i class="fas fa-spa text-brand-cw me-3" aria-hidden="true"></i>
                Welcome to CozyWish
                {% if provider_profile.business_name %}
                    <span class="d-block mt-2">{{ provider_profile.business_name }}!</span>
                {% else %}
                    <span class="d-block mt-2">{{ provider_profile.user.get_full_name|default:provider_profile.user.username }}!</span>
                {% endif %}
            </h1>
            <p class="onboarding-subtitle">
                You're just a few steps away from connecting with customers and growing your wellness business.
                Let's get you set up for success on the premier spa and wellness marketplace!
            </p>

            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="progress-cw mb-3" role="progressbar"
                         aria-label="Onboarding progress: {{ onboarding_progress.completed_steps }} of 5 steps completed"
                         aria-valuenow="{{ onboarding_progress.progress_percentage }}"
                         aria-valuemin="0" aria-valuemax="100">
                        <div class="progress-bar-cw"
                             style="width: {{ onboarding_progress.progress_percentage }}%">
                            {{ onboarding_progress.completed_steps }} of 5 completed
                        </div>
                    </div>
                    <p class="progress-text mb-0">
                        <strong>{{ onboarding_progress.completed_steps }}</strong> out of <strong>5</strong> steps completed
                        {% if onboarding_progress.progress_percentage == 100 %}
                            <span class="d-block mt-1 text-success" role="status" aria-live="polite">🎉 Congratulations! Your setup is complete!</span>
                        {% elif onboarding_progress.progress_percentage >= 75 %}
                            <span class="d-block mt-1" role="status" aria-live="polite">Almost there! You're doing great!</span>
                        {% elif onboarding_progress.progress_percentage >= 50 %}
                            <span class="d-block mt-1" role="status" aria-live="polite">Great progress! Keep going!</span>
                        {% else %}
                            <span class="d-block mt-1" role="status" aria-live="polite">Let's get started on your journey!</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <div class="row">
        <!-- Left Column: Checklist -->
        <div class="col-lg-8">
            <section class="card-cw" aria-labelledby="checklist-heading">
                <header class="card-cw-header">
                    <h2 id="checklist-heading" class="mb-0">
                        <i class="fas fa-clipboard-check me-3" aria-hidden="true"></i>
                        Getting Started Checklist
                    </h2>
                </header>
                <div class="card-cw-body">
                    <p class="text-neutral-cw mb-4">
                        Complete these steps to unlock all features and start accepting bookings from customers.
                    </p>

                    <ol class="list-unstyled" role="list">
                        {% for item in onboarding_checklist %}
                        <li class="checklist-item {% if item.completed %}completed{% elif item.priority == 'high' %}high-priority{% endif %}"
                            role="listitem"
                            tabindex="0"
                            aria-label="Step {{ forloop.counter }}: {{ item.step }}. {% if item.completed %}Completed{% elif item.priority == 'high' %}Required{% else %}Optional{% endif %}">
                            <div class="d-flex align-items-center">
                                <div class="checklist-icon" aria-hidden="true">
                                    <i class="{{ item.icon }} fa-lg"></i>
                                </div>
                                <div class="flex-grow-1 checklist-content">
                                    <h3 class="mb-1">
                                        {{ item.step }}
                                        {% if item.completed %}
                                            <span class="badge bg-success ms-2" role="status" aria-label="Completed">
                                                <i class="fas fa-check" aria-hidden="true"></i> Completed
                                            </span>
                                        {% elif item.priority == 'high' %}
                                            <span class="badge bg-danger ms-2" role="status" aria-label="Required step">Required</span>
                                        {% else %}
                                            <span class="badge bg-secondary ms-2" role="status" aria-label="Optional step">Optional</span>
                                        {% endif %}
                                    </h3>
                                    <p class="checklist-description">{{ item.description }}</p>
                                    {% if not item.completed and item.url %}
                                        <a href="{% url item.url %}" class="btn-cw-primary btn-sm"
                                           aria-label="Start {{ item.step }}">
                                            <i class="fas fa-arrow-right me-2" aria-hidden="true"></i>Get Started
                                        </a>
                                    {% elif item.completed %}
                                        <span class="text-success" role="status">
                                            <i class="fas fa-check-circle me-2" aria-hidden="true"></i>Well done! This step is complete.
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </li>
                        {% endfor %}
                    </ol>
                </div>
            </section>

            <!-- Feature Preview Cards -->
            <section class="card-cw" aria-labelledby="features-heading">
                <header class="card-cw-header">
                    <h2 id="features-heading" class="mb-0">
                        <i class="fas fa-star me-3" aria-hidden="true"></i>
                        What You'll Unlock
                    </h2>
                </header>
                <div class="card-cw-body">
                    <p class="text-neutral-cw mb-4">
                        Here's what you'll be able to do once you complete your venue setup:
                    </p>

                    <div class="row g-4" role="list">
                        {% for feature in feature_preview %}
                        <div class="col-md-6 col-lg-4" role="listitem">
                            <article class="feature-card" aria-labelledby="feature-{{ forloop.counter }}-title">
                                <div class="feature-icon" aria-hidden="true" style="{% if feature.title == 'Accept Bookings' %}background: var(--cw-neutral-500) !important;{% endif %}">
                                    <i class="{{ feature.icon }}"></i>
                                </div>
                                <h3 id="feature-{{ forloop.counter }}-title" class="feature-title">{{ feature.title }}</h3>
                                <p class="feature-description">{{ feature.description }}</p>
                                <ul class="list-unstyled feature-benefits" aria-label="Benefits of {{ feature.title }}">
                                    {% for benefit in feature.benefits %}
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2" aria-hidden="true"></i>{{ benefit }}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </article>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </section>
        </div>

        <!-- Right Column: Help & Resources -->
        <aside class="col-lg-4" role="complementary">
            <!-- Help Resources -->
            <section class="card-cw" aria-labelledby="help-guides-heading">
                <header class="card-cw-header">
                    <h2 id="help-guides-heading" class="mb-0">
                        <i class="fas fa-question-circle me-3" aria-hidden="true"></i>
                        Help & Guides
                    </h2>
                </header>
                <div class="card-cw-body">
                    <nav aria-label="Help and guide resources">
                        {% for guide in help_resources.guides %}
                        <article class="help-resource">
                            <div class="d-flex align-items-center">
                                <div class="me-3" aria-hidden="true">
                                    <i class="{{ guide.icon }} text-brand-cw fa-lg"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h3 class="mb-1">{{ guide.title }}</h3>
                                    <p class="text-muted mb-0 small">{{ guide.description }}</p>
                                </div>
                                <div>
                                    <a href="{{ guide.url }}" class="btn-cw-secondary btn-sm"
                                       target="_blank" rel="noopener noreferrer"
                                       aria-label="Open {{ guide.title }} guide (opens in new tab)">
                                        <i class="fas fa-external-link-alt" aria-hidden="true"></i>
                                    </a>
                                </div>
                            </div>
                        </article>
                        {% endfor %}
                    </nav>
                </div>
            </section>

            <!-- Support Resources -->
            <section class="card-cw" aria-labelledby="support-heading">
                <header class="card-cw-header">
                    <h2 id="support-heading" class="mb-0">
                        <i class="fas fa-headset me-3" aria-hidden="true"></i>
                        Support
                    </h2>
                </header>
                <div class="card-cw-body">
                    <nav aria-label="Support resources">
                        {% for support in help_resources.support %}
                        <article class="help-resource">
                            <div class="d-flex align-items-center">
                                <div class="me-3" aria-hidden="true">
                                    <i class="{{ support.icon }} text-success fa-lg"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h3 class="mb-1">{{ support.title }}</h3>
                                    <p class="text-muted mb-0 small">{{ support.description }}</p>
                                </div>
                                <div>
                                    <a href="{{ support.url }}" class="btn-cw-secondary btn-sm"
                                       target="_blank" rel="noopener noreferrer"
                                       aria-label="Access {{ support.title }} support (opens in new tab)">
                                        <i class="fas fa-external-link-alt" aria-hidden="true"></i>
                                    </a>
                                </div>
                            </div>
                        </article>
                        {% endfor %}
                    </nav>
                </div>
            </section>
        </aside>
    </div>

    <!-- Motivational Footer -->
    <section class="row mt-5" aria-labelledby="cta-heading">
        <div class="col-12">
            <div class="motivational-footer">
                <h2 id="cta-heading" class="mb-3">
                    <i class="fas fa-spa me-3" aria-hidden="true"></i>
                    Ready to Transform Your Business?
                </h2>
                <p class="mb-4">
                    Join thousands of wellness professionals who are already growing their business with CozyWish.
                    Your success story starts here!
                </p>
                <p class="text-neutral-cw">
                    Complete your venue setup to unlock all features and start accepting bookings from customers.
                </p>
            </div>
        </div>
    </section>
</main>
{% endblock %}

{% block extra_js %}
<script>
    // CozyWish Onboarding Dashboard Interactive Elements
    document.addEventListener('DOMContentLoaded', function() {
        // Animate progress bar on load with brand styling
        const progressBar = document.querySelector('.progress-bar-cw');
        if (progressBar) {
            const width = progressBar.style.width;
            progressBar.style.width = '0%';
            setTimeout(() => {
                progressBar.style.width = width;
                progressBar.style.transition = 'width 1.2s cubic-bezier(0.4, 0, 0.2, 1)';
            }, 300);
        }

        // Enhanced click tracking for checklist items
        document.querySelectorAll('.checklist-item').forEach(item => {
            item.addEventListener('click', function(e) {
                // Don't trigger if clicking on a button or link
                if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON' || e.target.closest('a, button')) {
                    return;
                }

                const button = this.querySelector('.btn-cw-primary, .btn-cw-secondary, .btn');
                if (button) {
                    button.click();
                }
            });

            // Add hover effect enhancement
            item.addEventListener('mouseenter', function() {
                if (!this.classList.contains('completed')) {
                    this.style.borderColor = 'var(--cw-brand-accent)';
                }
            });

            item.addEventListener('mouseleave', function() {
                if (!this.classList.contains('completed') && !this.classList.contains('high-priority')) {
                    this.style.borderColor = 'rgba(250, 225, 215, 0.3)';
                }
            });
        });

        // Add smooth scroll behavior for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading states for action buttons
        document.querySelectorAll('.btn-cw-primary, .btn-cw-secondary').forEach(button => {
            button.addEventListener('click', function() {
                if (this.href && !this.href.includes('#')) {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                    this.style.pointerEvents = 'none';

                    // Reset after 3 seconds if page hasn't changed
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.style.pointerEvents = 'auto';
                    }, 3000);
                }
            });
        });


    });
</script>
{% endblock %}
