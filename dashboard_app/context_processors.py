from django.contrib.auth.models import Anonymous<PERSON>ser


def provider_context(request):
    """
    Context processor to provide provider-specific variables globally.
    """
    context = {}

    # Only add provider context if user is authenticated and is a service provider
    if request.user.is_authenticated and hasattr(
        request.user, "serviceproviderprofile"
    ):
        try:
            provider_profile = request.user.serviceproviderprofile
            venue = getattr(provider_profile, "venue", None)
            context["has_venue"] = venue is not None
            context["provider_profile"] = provider_profile
            context["venue"] = venue
        except Exception:
            # If there's any error getting the provider profile, default to no venue
            context["has_venue"] = False
            context["provider_profile"] = None
            context["venue"] = None
    else:
        # For non-providers or anonymous users, default to no venue
        context["has_venue"] = False
        context["provider_profile"] = None
        context["venue"] = None

    return context
