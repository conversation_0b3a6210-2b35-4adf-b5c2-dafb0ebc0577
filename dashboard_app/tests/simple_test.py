"""
Simple test case to verify that the test setup works correctly for dashboard_app.
"""

# --- Third-Party Imports ---
from django.apps import apps
from django.test import TestCase


class SimpleTestCase(TestCase):
    """Simple test case to verify that the test setup works correctly."""

    def test_app_installed(self):
        """Test that the dashboard_app is installed."""
        self.assertTrue(apps.is_installed("dashboard_app"))

    def test_models_exist(self):
        """Test that the models exist."""
        from dashboard_app.models import FavoriteVenue

        self.assertTrue(FavoriteVenue)

    def test_forms_exist(self):
        """Test that the forms exist."""
        from dashboard_app.forms import DateRangeForm

        self.assertTrue(DateRangeForm)

    def test_views_exist(self):
        """Test that the views exist."""
        from dashboard_app import views

        self.assertTrue(hasattr(views, "customer_dashboard_view"))
        self.assertTrue(hasattr(views, "ProviderDashboardView"))
        self.assertTrue(hasattr(views, "admin_dashboard"))

    def test_urls_exist(self):
        """Test that the URLs exist."""
        from dashboard_app import urls

        self.assertTrue(hasattr(urls, "urlpatterns"))
        self.assertTrue(len(urls.urlpatterns) > 0)
