"""Service provider dashboard views."""

# --- Standard Library Imports ---
import csv
from datetime import date, timedelta
from decimal import Decimal

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Avg, Count, Q, Sum
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.views import View
from django.views.decorators.http import require_http_methods
from django.views.generic import DetailView, ListView

# --- Local App Imports ---
from accounts_app.models import (
    CustomerProfile,
    CustomUser,
    ServiceProviderProfile,
    TeamMember,
)
from booking_cart_app.models import Booking, BookingItem
from venues_app.models import Service, Venue

from ..constants import (
    ADMIN_ONLY_ERROR,
    CUSTOMER_ONLY_ERROR,
    DASHBOARD_ACCESS_SUCCESS,
    FAVORITE_ADDED_SUCCESS,
    FAVORITE_ALREADY_EXISTS,
    FAVORITE_REMOVED_SUCCESS,
    PERMISSION_DENIED_ERROR,
    PROVIDER_ONLY_ERROR,
    VENUE_NOT_FOUND_ERROR,
)
from ..decorators import customer_required, provider_required, staff_required
from ..forms import DateRangeForm
from ..helpers import get_date_range, get_int_param, get_valid_param
from ..logging_utils import (
    log_admin_dashboard_access,
    log_admin_data_access,
    log_analytics_access,
    log_booking_status_access,
    log_dashboard_access,
    log_dashboard_activity,
    log_dashboard_filter_usage,
    log_error,
    log_favorite_venue_event,
    log_profile_access,
    log_provider_dashboard_activity,
    log_provider_earnings_access,
    log_provider_service_stats_access,
    log_security_event,
    log_system_health_check,
    performance_monitor,
)
from ..mixins import ProviderRequiredMixin
from ..models import FavoriteVenue

# --- Provider Dashboard Views ---


def get_onboarding_checklist(provider_profile):
    """
    Get onboarding checklist with completion status for service providers.

    Args:
        provider_profile: ServiceProviderProfile instance

    Returns:
        List of dictionaries with step information and completion status
    """
    venue = getattr(provider_profile, "venue", None)

    checklist = [
        {
            "step": "Create venue profile",
            "description": "Set up your venue with basic information like name, description, and location",
            "completed": venue is not None,
            "icon": "fas fa-store",
            "url": "venues_app:venue_create_wizard_default" if not venue else None,
            "priority": "high",
        },
        {
            "step": "Add services and pricing",
            "description": "List the services you offer with descriptions and pricing",
            "completed": (
                venue and venue.services.filter(is_active=True).exists()
                if venue
                else False
            ),
            "icon": "fas fa-concierge-bell",
            "url": "venues_app:service_create" if venue else None,
            "priority": "high",
        },
        {
            "step": "Upload photos",
            "description": "Showcase your venue with high-quality photos to attract customers",
            "completed": venue and venue.images.exists() if venue else False,
            "icon": "fas fa-camera",
            "url": "venues_app:manage_venue_images" if venue else None,
            "priority": "high",
        },
        {
            "step": "Set operating hours",
            "description": "Define when your venue is open and available for bookings",
            "completed": (
                venue and venue.operating_hours_set.exists() if venue else False
            ),
            "icon": "fas fa-clock",
            "url": "venues_app:manage_operating_hours" if venue else None,
            "priority": "high",
        },
        {
            "step": "Submit for approval",
            "description": "Get your venue approved to start accepting bookings from customers",
            "completed": (
                venue and venue.approval_status in ["approved", "pending"]
                if venue
                else False
            ),
            "icon": "fas fa-check-circle",
            "url": None,  # Handled via dashboard submission
            "priority": "high",
        },
        {
            "step": "Add team members",
            "description": "Invite staff to help manage your venue and bookings (optional)",
            "completed": provider_profile.team.filter(is_active=True).exists(),
            "icon": "fas fa-users",
            "url": "accounts_app:team_member_list",
            "priority": "medium",
        },
    ]

    return checklist


def get_onboarding_progress(provider_profile):
    """
    Calculate onboarding progress statistics.

    Args:
        provider_profile: ServiceProviderProfile instance

    Returns:
        Dictionary with progress statistics
    """
    checklist = get_onboarding_checklist(provider_profile)
    total_steps = len(checklist)
    completed_steps = sum(1 for item in checklist if item["completed"])
    progress_percentage = (
        int((completed_steps / total_steps) * 100) if total_steps > 0 else 0
    )

    return {
        "total_steps": total_steps,
        "completed_steps": completed_steps,
        "progress_percentage": progress_percentage,
        "remaining_steps": total_steps - completed_steps,
    }


def get_feature_preview_cards():
    """
    Get feature preview cards showing what providers will unlock with venue creation.

    Returns:
        List of dictionaries with feature information
    """
    features = [
        {
            "title": "Accept Bookings",
            "description": "Let customers discover and book your services online 24/7",
            "icon": "fas fa-calendar-check",
            "color": "primary",
            "benefits": [
                "Automated booking system",
                "Real-time availability",
                "Customer notifications",
            ],
        },
        {
            "title": "Manage Calendar",
            "description": "Track appointments, manage availability, and organize your schedule",
            "icon": "fas fa-calendar-alt",
            "color": "success",
            "benefits": [
                "Visual calendar view",
                "Appointment reminders",
                "Schedule optimization",
            ],
        },
        {
            "title": "Earn Revenue",
            "description": "Process secure payments and track your earnings with detailed reports",
            "icon": "fas fa-chart-line",
            "color": "info",
            "benefits": [
                "Secure payment processing",
                "Revenue analytics",
                "Financial reporting",
            ],
        },
        {
            "title": "Customer Reviews",
            "description": "Build your reputation with customer feedback and ratings",
            "icon": "fas fa-star",
            "color": "warning",
            "benefits": [
                "Customer feedback system",
                "Rating management",
                "Reputation building",
            ],
        },
        {
            "title": "Analytics Dashboard",
            "description": "Get insights on bookings, revenue, and business performance",
            "icon": "fas fa-chart-bar",
            "color": "danger",
            "benefits": ["Performance metrics", "Customer insights", "Growth tracking"],
        },
        {
            "title": "Marketing Tools",
            "description": "Promote your venue to local customers and increase visibility",
            "icon": "fas fa-bullhorn",
            "color": "secondary",
            "benefits": [
                "Local discovery",
                "Promotional campaigns",
                "Customer acquisition",
            ],
        },
    ]

    return features


def get_help_resources():
    """
    Get help and support resources for onboarding providers.

    Returns:
        Dictionary with categorized help resources
    """
    resources = {
        "guides": [
            {
                "title": "Venue Creation Guide",
                "description": "Step-by-step guide to setting up your venue",
                "icon": "fas fa-book",
                "url": "https://docs.cozywish.com/venue-creation-guide",  # External URL
                "type": "guide",
            },
            {
                "title": "Service Setup Tutorial",
                "description": "Learn how to add and price your services",
                "icon": "fas fa-play-circle",
                "url": "https://docs.cozywish.com/service-setup-tutorial",  # External URL
                "type": "video",
            },
            {
                "title": "Photo Guidelines",
                "description": "Tips for taking great venue photos",
                "icon": "fas fa-camera",
                "url": "https://docs.cozywish.com/photo-guidelines",  # External URL
                "type": "guide",
            },
        ],
        "support": [
            {
                "title": "Contact Support",
                "description": "Get help from our support team",
                "icon": "fas fa-headset",
                "url": "https://support.cozywish.com/contact",  # External URL
                "type": "contact",
            },
            {
                "title": "FAQ Section",
                "description": "Find answers to common questions",
                "icon": "fas fa-question-circle",
                "url": "https://support.cozywish.com/faq",  # External URL
                "type": "faq",
            },
            {
                "title": "Community Forum",
                "description": "Connect with other service providers",
                "icon": "fas fa-comments",
                "url": "https://community.cozywish.com",  # External URL
                "type": "community",
            },
        ],
        "quick_actions": [
            {
                "title": "Start Creating Venue",
                "description": "Begin the venue creation process",
                "icon": "fas fa-plus-circle",
                "url": "venues_app:venue_create",  # This will redirect appropriately
                "type": "action",
                "color": "primary",
            },
            {
                "title": "Watch Tutorial",
                "description": "Learn the basics in 5 minutes",
                "icon": "fas fa-play",
                "url": "https://docs.cozywish.com/getting-started-video",  # External URL - changed from '#'
                "type": "action",
                "color": "info",
            },
        ],
    }

    return resources


class ProviderDashboardView(ProviderRequiredMixin, View):
    """Service provider dashboard."""

    @performance_monitor("provider_dashboard_access")
    def get(self, request):
        try:
            provider_profile = get_object_or_404(
                ServiceProviderProfile, user=request.user
            )
            venue = getattr(provider_profile, "venue", None)

            # Always render full dashboard, but include onboarding data when no venue exists
            has_venue = venue is not None
            onboarding_mode = not has_venue

            # Get onboarding data for providers without venues
            onboarding_checklist = None
            onboarding_progress = None
            feature_preview = None
            help_resources = None

            if not has_venue:
                onboarding_checklist = get_onboarding_checklist(provider_profile)
                onboarding_progress = get_onboarding_progress(provider_profile)
                feature_preview = get_feature_preview_cards()
                help_resources = get_help_resources()

            # Skip caching when in onboarding mode to ensure fresh data
            cache_key = f"provider_dashboard_{request.user.id}"
            context = None
            if has_venue:
                context = cache.get(cache_key)
                if context:
                    return render(
                        request, "dashboard_app/provider/dashboard.html", context
                    )

            # Initialize default values for when no venue exists
            todays_bookings = Booking.objects.none()
            recent_bookings = []
            paginator = None
            total_bookings = 0
            pending_bookings = 0
            confirmed_bookings = 0
            completed_bookings = 0
            monthly_earnings = Decimal("0.00")

            # Initialize venue approval data
            approval_progress = None
            can_submit_for_approval = False

            # Initialize default values for venue-related data
            today = timezone.now().date()
            approval_progress = None
            can_submit_for_approval = False
            todays_bookings = Booking.objects.none()
            recent_bookings = None
            paginator = None
            total_bookings = 0
            pending_bookings = 0
            confirmed_bookings = 0
            completed_bookings = 0
            monthly_earnings = Decimal("0.00")
            todays_bookings_count = 0

            # Only fetch venue-related data if venue exists
            if venue:
                # Get venue approval progress
                approval_progress = venue.get_approval_requirements()
                can_submit_for_approval = venue.can_submit_for_approval()

                # Calculate venue completeness score
                venue_completeness_score = venue.calculate_completeness_score()

                # Calculate profile completeness score
                provider_profile = venue.service_provider
                profile_completeness_score = 0

                # Profile completeness calculation
                profile_items = 0
                completed_profile_items = 0

                # Business information
                profile_items += 1
                if provider_profile.legal_name:
                    completed_profile_items += 1

                profile_items += 1
                if (
                    provider_profile.address
                    and provider_profile.city
                    and provider_profile.state
                ):
                    completed_profile_items += 1

                profile_items += 1
                if provider_profile.phone:
                    completed_profile_items += 1

                profile_items += 1
                if provider_profile.user.email:
                    completed_profile_items += 1

                # Social media and website
                profile_items += 1
                if (
                    provider_profile.website
                    or provider_profile.instagram
                    or provider_profile.facebook
                ):
                    completed_profile_items += 1

                # Team members
                profile_items += 1
                if provider_profile.team.filter(is_active=True).exists():
                    completed_profile_items += 1

                profile_completeness_score = (
                    int((completed_profile_items / profile_items) * 100)
                    if profile_items > 0
                    else 0
                )

                todays_bookings = (
                    Booking.objects.filter(venue=venue, items__scheduled_date=today)
                    .select_related("customer")
                    .prefetch_related("items", "items__service")
                    .distinct()
                    .order_by("items__scheduled_time")[:10]
                )
                todays_bookings_count = todays_bookings.count()

                week_ago = today - timedelta(days=7)
                recent_qs = (
                    Booking.objects.filter(venue=venue, booking_date__gte=week_ago)
                    .select_related("customer")
                    .prefetch_related("items", "items__service")
                    .order_by("-booking_date")
                )

                page = get_int_param(request, "page", default=1, minimum=1)
                paginator = Paginator(recent_qs, 5)
                recent_bookings = paginator.get_page(page)

                total_bookings = Booking.objects.filter(venue=venue).count()
                pending_bookings = Booking.objects.filter(
                    venue=venue, status="pending"
                ).count()
                confirmed_bookings = Booking.objects.filter(
                    venue=venue, status="confirmed"
                ).count()
                completed_bookings = Booking.objects.filter(
                    venue=venue, status="completed"
                ).count()

                current_month_start = today.replace(day=1)
                monthly_earnings = BookingItem.objects.filter(
                    booking__venue=venue,
                    booking__status__in=["confirmed", "completed"],
                    scheduled_date__gte=current_month_start,
                ).aggregate(total=Sum("service_price"))["total"] or Decimal("0.00")

            team_members_count = TeamMember.objects.filter(
                service_provider=provider_profile, is_active=True
            ).count()

            # Log dashboard access with appropriate details
            log_details = {
                "has_venue": has_venue,
                "onboarding_mode": onboarding_mode,
                "team_members_count": team_members_count,
            }

            if venue:
                log_details.update(
                    {
                        "venue_id": venue.id,
                        "venue_name": venue.venue_name,
                        "todays_bookings": todays_bookings_count,
                        "total_bookings": total_bookings,
                        "pending_bookings": pending_bookings,
                        "confirmed_bookings": confirmed_bookings,
                        "completed_bookings": completed_bookings,
                        "monthly_earnings": str(monthly_earnings),
                    }
                )
            else:
                # Add onboarding-specific logging details
                if onboarding_progress:
                    log_details.update(
                        {
                            "progress_percentage": onboarding_progress[
                                "progress_percentage"
                            ],
                            "completed_steps": onboarding_progress["completed_steps"],
                            "total_steps": onboarding_progress["total_steps"],
                        }
                    )

            log_dashboard_access(
                user=request.user,
                dashboard_type="provider_onboarding" if onboarding_mode else "provider",
                request=request,
                details=log_details,
            )

            context = {
                "provider_profile": provider_profile,
                "venue": venue,
                "has_venue": has_venue,
                "onboarding_mode": onboarding_mode,
                "todays_bookings": todays_bookings,
                "todays_bookings_count": todays_bookings_count,
                "recent_bookings": recent_bookings,
                "paginator": paginator,
                "total_bookings": total_bookings,
                "pending_bookings": pending_bookings,
                "confirmed_bookings": confirmed_bookings,
                "completed_bookings": completed_bookings,
                "monthly_earnings": monthly_earnings,
                "team_members_count": team_members_count,
                "approval_progress": approval_progress,
                "can_submit_for_approval": can_submit_for_approval,
                "venue_completeness_score": venue_completeness_score if venue else 0,
                "profile_completeness_score": (
                    profile_completeness_score if venue else 0
                ),
                # Onboarding data (only populated when no venue exists)
                "onboarding_checklist": onboarding_checklist,
                "onboarding_progress": onboarding_progress,
                "feature_preview": feature_preview,
                "help_resources": help_resources,
            }

            # Only cache when venue exists to ensure fresh onboarding data
            if has_venue:
                cache.set(cache_key, context, settings.DASHBOARD_CACHE_TIMEOUT)

            return render(request, "dashboard_app/provider/dashboard.html", context)

        except Exception as e:
            log_error(
                error_type="provider_dashboard_access",
                error_message="Error loading provider dashboard",
                user=request.user,
                request=request,
                exception=e,
            )
            # Show detailed error in development mode
            if settings.DEBUG:
                import traceback

                error_details = (
                    f"Dashboard Error: {str(e)}\n\nTraceback:\n{traceback.format_exc()}"
                )
                messages.error(request, error_details)
            else:
                messages.error(
                    request,
                    "There was an error loading your dashboard. Please try again.",
                )
            return redirect("home")


@provider_required
@performance_monitor("provider_todays_bookings_access")
def provider_todays_bookings_view(request):
    """
    Provider today's bookings view showing detailed bookings for today.
    """
    # Role check handled by provider_required decorator

    try:
        # Get service provider profile and venue
        provider_profile = get_object_or_404(ServiceProviderProfile, user=request.user)
        venue = getattr(provider_profile, "venue", None)

        if not venue:
            context = {
                "feature_name": "Today's Bookings",
                "feature_description": "Track and manage all your daily appointments and customer bookings in one place.",
                "feature_benefits": [
                    "View all bookings scheduled for today",
                    "See customer details and service information",
                    "Manage booking status and confirmations",
                    "Track your daily schedule and availability",
                ],
            }
            return render(
                request, "dashboard_app/provider/feature_locked.html", context
            )

        # Get today's date
        today = timezone.now().date()

        # Get filter parameters
        status_filter = get_valid_param(
            request, "status", {"all", "pending", "confirmed", "completed"}, "all"
        )

        # Get today's bookings with booking items
        todays_booking_items = (
            BookingItem.objects.filter(booking__venue=venue, scheduled_date=today)
            .select_related("booking", "service")
            .order_by("scheduled_time", "service__service_title")
        )

        # Apply status filter
        if status_filter != "all":
            todays_booking_items = todays_booking_items.filter(
                booking__status=status_filter
            )

        # Group bookings by time slot for better display
        bookings_by_time = {}
        for item in todays_booking_items:
            time_key = item.scheduled_time.strftime("%H:%M")
            if time_key not in bookings_by_time:
                bookings_by_time[time_key] = []
            bookings_by_time[time_key].append(item)

        # Sort time slots
        sorted_bookings = dict(sorted(bookings_by_time.items()))

        # Get booking counts for filter display
        total_count = BookingItem.objects.filter(
            booking__venue=venue, scheduled_date=today
        ).count()
        pending_count = BookingItem.objects.filter(
            booking__venue=venue, scheduled_date=today, booking__status="pending"
        ).count()
        confirmed_count = BookingItem.objects.filter(
            booking__venue=venue, scheduled_date=today, booking__status="confirmed"
        ).count()
        completed_count = BookingItem.objects.filter(
            booking__venue=venue, scheduled_date=today, booking__status="completed"
        ).count()

        # Log today's bookings access
        log_dashboard_activity(
            activity_type="provider_todays_bookings_access",
            user=request.user,
            request=request,
            details={
                "venue_id": venue.id,
                "booking_count": total_count,
                "status_filter": status_filter,
            },
        )

        context = {
            "provider_profile": provider_profile,
            "venue": venue,
            "bookings_by_time": sorted_bookings,
            "today": today,
            "status_filter": status_filter,
            "total_count": total_count,
            "pending_count": pending_count,
            "confirmed_count": confirmed_count,
            "completed_count": completed_count,
        }

        return render(request, "dashboard_app/provider/todays_bookings.html", context)

    except Exception as e:
        log_error(
            error_type="provider_todays_bookings_access",
            error_message="Error loading provider today's bookings",
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(
            request, "There was an error loading today's bookings. Please try again."
        )
        return redirect("dashboard_app:provider_dashboard")


@provider_required
@performance_monitor("provider_earnings_reports_access")
def provider_earnings_reports_view(request):
    """
    Provider earnings reports view with date range filtering and visualizations.
    """
    # Role check handled by provider_required decorator

    try:
        # Get service provider profile and venue
        provider_profile = get_object_or_404(ServiceProviderProfile, user=request.user)
        venue = getattr(provider_profile, "venue", None)

        if not venue:
            context = {
                "feature_name": "Earnings Reports",
                "feature_description": "Track your revenue, analyze earnings trends, and get insights into your business performance.",
                "feature_benefits": [
                    "View detailed earnings breakdown by date range",
                    "Analyze revenue trends and patterns",
                    "Track earnings by service type",
                    "Generate financial reports for your business",
                ],
            }
            return render(
                request, "dashboard_app/provider/feature_locked.html", context
            )

        # Handle date range form
        form = DateRangeForm(request.GET or None)

        today = timezone.now().date()
        default_start = today.replace(day=1)
        start_date, end_date = get_date_range(
            form, default_start=default_start, default_end=today
        )

        # Get earnings data for the date range
        earnings_data = BookingItem.objects.filter(
            booking__venue=venue,
            booking__status__in=["confirmed", "completed"],
            scheduled_date__range=[start_date, end_date],
        ).aggregate(
            total_earnings=Sum("service_price"),
            total_bookings=Count("id"),
            avg_booking_value=Avg("service_price"),
        )

        total_earnings = earnings_data["total_earnings"] or Decimal("0.00")
        total_bookings = earnings_data["total_bookings"] or 0
        avg_booking_value = earnings_data["avg_booking_value"] or Decimal("0.00")

        # Get earnings by service
        earnings_by_service = (
            BookingItem.objects.filter(
                booking__venue=venue,
                booking__status__in=["confirmed", "completed"],
                scheduled_date__range=[start_date, end_date],
            )
            .values("service__service_title")
            .annotate(total=Sum("service_price"), count=Count("id"))
            .order_by("-total")[:10]
        )

        # Get daily earnings for chart (last 30 days max)
        chart_start = max(start_date, today - timedelta(days=30))
        daily_earnings = (
            BookingItem.objects.filter(
                booking__venue=venue,
                booking__status__in=["confirmed", "completed"],
                scheduled_date__range=[chart_start, end_date],
            )
            .values("scheduled_date")
            .annotate(daily_total=Sum("service_price"))
            .order_by("scheduled_date")
        )

        # Log earnings access
        log_dashboard_activity(
            activity_type="provider_earnings_access",
            user=request.user,
            request=request,
            details={
                "venue_id": venue.id,
                "date_range": f"{start_date} to {end_date}",
                "total_earnings": str(total_earnings),
                "total_bookings": total_bookings,
            },
        )

        context = {
            "provider_profile": provider_profile,
            "venue": venue,
            "form": form,
            "start_date": start_date,
            "end_date": end_date,
            "total_earnings": total_earnings,
            "total_bookings": total_bookings,
            "avg_booking_value": avg_booking_value,
            "earnings_by_service": earnings_by_service,
            "daily_earnings": daily_earnings,
        }

        return render(request, "dashboard_app/provider/earnings_reports.html", context)

    except Exception as e:
        log_error(
            error_type="provider_earnings_access",
            error_message="Error loading provider earnings reports",
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(
            request, "There was an error loading earnings reports. Please try again."
        )
        return redirect("dashboard_app:provider_dashboard")


@provider_required
@performance_monitor("provider_service_performance_access")
def provider_service_performance_view(request):
    """
    Provider service performance view showing metrics for each service.
    """
    # Role check handled by provider_required decorator

    try:
        # Get service provider profile and venue
        provider_profile = get_object_or_404(ServiceProviderProfile, user=request.user)
        venue = getattr(provider_profile, "venue", None)

        if not venue:
            context = {
                "feature_name": "Service Performance",
                "feature_description": "Analyze how your services are performing and identify opportunities for growth.",
                "feature_benefits": [
                    "Track performance metrics for each service",
                    "Identify your most popular and profitable services",
                    "Monitor booking trends and customer preferences",
                    "Make data-driven decisions to optimize your business",
                ],
            }
            return render(
                request, "dashboard_app/provider/feature_locked.html", context
            )

        # Get all services for this venue
        services = Service.objects.filter(venue=venue, is_active=True)

        # Get performance data for each service
        service_performance = []
        for service in services:
            # Get booking statistics
            total_bookings = BookingItem.objects.filter(
                service=service, booking__status__in=["confirmed", "completed"]
            ).count()

            total_revenue = BookingItem.objects.filter(
                service=service, booking__status__in=["confirmed", "completed"]
            ).aggregate(total=Sum("service_price"))["total"] or Decimal("0.00")

            # Get recent bookings (last 30 days)
            thirty_days_ago = timezone.now().date() - timedelta(days=30)
            recent_bookings = BookingItem.objects.filter(
                service=service,
                booking__status__in=["confirmed", "completed"],
                scheduled_date__gte=thirty_days_ago,
            ).count()

            # Calculate average rating (if reviews exist)
            # Note: This assumes a Review model exists - adjust as needed
            avg_rating = None
            try:
                from review_app.models import Review

                reviews = Review.objects.filter(venue=service.venue)
                if reviews.exists():
                    avg_rating = reviews.aggregate(avg=Avg("rating"))["avg"]
            except ImportError:
                pass  # Review model doesn't exist yet

            service_performance.append(
                {
                    "service": service,
                    "total_bookings": total_bookings,
                    "total_revenue": total_revenue,
                    "recent_bookings": recent_bookings,
                    "avg_rating": avg_rating,
                    "revenue_per_booking": (
                        total_revenue / total_bookings
                        if total_bookings > 0
                        else Decimal("0.00")
                    ),
                }
            )

        # Sort by total revenue (best performing first)
        service_performance.sort(key=lambda x: x["total_revenue"], reverse=True)

        # Log service performance access
        log_dashboard_activity(
            activity_type="provider_service_performance_access",
            user=request.user,
            request=request,
            details={"venue_id": venue.id, "services_count": len(service_performance)},
        )

        context = {
            "provider_profile": provider_profile,
            "venue": venue,
            "service_performance": service_performance,
        }

        return render(
            request, "dashboard_app/provider/service_performance.html", context
        )

    except Exception as e:
        log_error(
            error_type="provider_service_performance_access",
            error_message="Error loading provider service performance",
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(
            request,
            "There was an error loading service performance data. Please try again.",
        )
        return redirect("dashboard_app:provider_dashboard")


@provider_required
@performance_monitor("provider_earnings_export")
def provider_earnings_export(request):
    """Export provider earnings data as CSV."""
    provider_profile = get_object_or_404(ServiceProviderProfile, user=request.user)
    venue = getattr(provider_profile, "venue", None)
    if not venue:
        messages.info(request, "Create your venue first to export earnings data.")
        return redirect("venues_app:venue_create")

    form = DateRangeForm(request.GET or None)
    today = timezone.now().date()
    default_start = today.replace(day=1)
    start_date, end_date = get_date_range(
        form, default_start=default_start, default_end=today
    )

    items = (
        BookingItem.objects.filter(
            booking__venue=venue,
            booking__status__in=["confirmed", "completed"],
            scheduled_date__range=[start_date, end_date],
        )
        .select_related("service", "booking")
        .order_by("scheduled_date")
    )

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = (
        f'attachment; filename="earnings_{start_date}_{end_date}.csv"'
    )
    writer = csv.writer(response)
    writer.writerow(["Date", "Service", "Price", "Booking ID"])
    for item in items:
        writer.writerow(
            [
                item.scheduled_date,
                item.service.service_title,
                item.service_price,
                item.booking.id,
            ]
        )
    return response


@provider_required
@performance_monitor("provider_team_management_redirect")
def provider_team_management_view(request):
    """
    Provider team management view - redirects to accounts_app team management.
    """
    # Role check handled by provider_required decorator

    # Log team management access
    log_dashboard_activity(
        activity_type="provider_team_management_redirect",
        user=request.user,
        request=request,
        details={"redirect_to": "accounts_app:team_member_list"},
    )

    # Redirect to accounts_app team management
    messages.info(request, "Redirected to team management page.")
    return redirect("accounts_app:team_member_list")


@provider_required
@performance_monitor("provider_venue_approval_submission")
def provider_venue_approval_submission(request):
    """
    Handle venue approval submission from provider dashboard.
    """
    if request.method != "POST":
        messages.error(request, "Invalid request method.")
        return redirect("dashboard_app:provider_dashboard")

    try:
        # Get service provider profile and venue
        provider_profile = get_object_or_404(ServiceProviderProfile, user=request.user)
        venue = getattr(provider_profile, "venue", None)

        if not venue:
            messages.error(request, "No venue found. Please create your venue first.")
            return redirect("venues_app:venue_create")

        # Check if venue can be submitted for approval
        success, message, missing_requirements = venue.submit_for_approval()

        if success:
            messages.success(request, message)

            # Log the submission
            log_dashboard_activity(
                activity_type="venue_approval_submission",
                user=request.user,
                request=request,
                details={
                    "venue_id": venue.id,
                    "venue_name": venue.venue_name,
                    "approval_status": venue.approval_status,
                },
            )
        else:
            messages.error(request, message)

            # If there are missing requirements, show them
            if missing_requirements:
                requirement_messages = []
                for req in missing_requirements[:3]:  # Show first 3 requirements
                    requirement_messages.append(
                        f"• {req['requirement']}: {req['description']}"
                    )

                if len(missing_requirements) > 3:
                    requirement_messages.append(
                        f"• And {len(missing_requirements) - 3} more requirements..."
                    )

                messages.info(
                    request,
                    f"Missing requirements:\n" + "\n".join(requirement_messages),
                )

        return redirect("dashboard_app:provider_dashboard")

    except Exception as e:
        log_error(
            error_type="venue_approval_submission",
            error_message="Error submitting venue for approval",
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(
            request, "Unable to submit venue for approval. Please try again."
        )
        return redirect("dashboard_app:provider_dashboard")
