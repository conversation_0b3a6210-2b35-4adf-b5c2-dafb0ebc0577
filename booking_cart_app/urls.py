# Django imports
from django.urls import path

# Local imports
from . import views
from .views import ajax

app_name = "booking_cart_app"

urlpatterns = [
    # ===== CUSTOMER URLS =====
    # Cart management
    path("cart/", views.cart_view, name="cart_view"),
    path(
        "cart/update/<int:item_id>/",
        views.update_cart_item_view,
        name="update_cart_item",
    ),
    path(
        "cart/remove/<int:item_id>/",
        views.remove_from_cart_view,
        name="remove_from_cart",
    ),
    # path('cart/clear/', views.clear_cart, name='clear_cart'),  # TODO: Implement clear cart functionality
    # Enhanced Booking Flow
    path(
        "add-to-cart/<int:service_id>/",
        views.enhanced_add_to_cart_view,
        name="enhanced_add_to_cart",
    ),
    # Enhanced AJAX Endpoints for Interactive UI
    path("ajax/slots/<int:service_id>/", ajax.get_time_slots, name="ajax_get_slots"),
    path(
        "ajax/month-availability/<int:service_id>/",
        ajax.get_month_availability,
        name="ajax_monthly_availability",
    ),
    path(
        "ajax/check-availability/<int:service_id>/",
        ajax.check_availability,
        name="ajax_check_availability",
    ),
    path(
        "ajax/service-pricing/<int:service_id>/",
        ajax.get_service_pricing,
        name="ajax_service_pricing",
    ),
    path("ajax/cart-status/", ajax.get_cart_status, name="ajax_cart_status"),
    path(
        "ajax/popular-times/<int:service_id>/",
        ajax.get_popular_times,
        name="ajax_popular_times",
    ),
    # Legacy AJAX endpoints (keep for backward compatibility)
    path(
        "ajax/availability/<int:service_id>/",
        views.get_monthly_availability_ajax,
        name="ajax_monthly_availability_legacy",
    ),
    # API Endpoints
    path(
        "api/services/<int:service_id>/details/",
        views.service_details_api,
        name="service_details_api",
    ),
    path(
        "api/validate-cart-availability/",
        views.validate_cart_availability_api,
        name="validate_cart_availability_api",
    ),
    path("api/lock-cart-slots/", views.lock_cart_slots_api, name="lock_cart_slots_api"),
    path(
        "api/check-provider-conflicts/",
        views.check_provider_conflicts_api,
        name="check_provider_conflicts_api",
    ),
    # Legacy booking flow (keep for backward compatibility)
    path(
        "add-to-cart-legacy/<int:service_id>/",
        views.add_to_cart_view,
        name="add_to_cart",
    ),
    # Checkout and booking management
    path("checkout/", views.checkout_view, name="checkout"),
    # path('checkout/confirm/', views.checkout_confirmation_view, name='checkout_confirmation'),
    path("bookings/", views.booking_list_view, name="booking_list"),
    path(
        "bookings/<slug:booking_slug>/",
        views.booking_detail_view,
        name="booking_detail",
    ),
    path(
        "bookings/<slug:booking_slug>/cancel/",
        views.cancel_booking_view,
        name="cancel_booking",
    ),
    # path('bookings/<slug:booking_slug>/confirmation/', views.booking_confirmation_view, name='booking_confirmation'),
    # ===== PROVIDER URLS =====
    # Availability management
    path(
        "provider/availability/",
        views.provider_availability_list_view,
        name="provider_availability_list",
    ),
    path(
        "provider/availability/<int:service_id>/",
        views.provider_service_availability_view,
        name="provider_service_availability",
    ),
    path(
        "provider/availability/<int:service_id>/calendar/",
        views.provider_availability_calendar_view,
        name="provider_availability_calendar",
    ),
    path(
        "provider/availability/<int:service_id>/add/",
        views.provider_add_availability_view,
        name="provider_add_availability",
    ),
    path(
        "provider/availability/<int:service_id>/bulk/",
        views.provider_bulk_availability_view,
        name="provider_bulk_availability",
    ),
    path(
        "provider/availability/<int:availability_id>/edit/",
        views.provider_edit_availability_view,
        name="provider_edit_availability",
    ),
    path(
        "provider/availability/<int:availability_id>/delete/",
        views.provider_delete_availability_view,
        name="provider_delete_availability",
    ),
    # Provider booking management URLs
    path(
        "provider/dashboard/",
        views.provider_booking_dashboard_view,
        name="provider_booking_dashboard",
    ),
    path(
        "provider/bookings/",
        views.provider_booking_list_view,
        name="provider_booking_list",
    ),
    path(
        "provider/bookings/today/",
        views.provider_todays_bookings_view,
        name="provider_todays_bookings",
    ),
    path(
        "provider/booking/<slug:booking_slug>/",
        views.provider_booking_detail_view,
        name="provider_booking_detail",
    ),
    path(
        "provider/booking/<slug:booking_slug>/accept/",
        views.provider_accept_booking_view,
        name="provider_accept_booking",
    ),
    path(
        "provider/booking/<slug:booking_slug>/decline/",
        views.provider_decline_booking_view,
        name="provider_decline_booking",
    ),
    path(
        "provider/booking/<slug:booking_slug>/complete/",
        views.provider_mark_completed_view,
        name="provider_mark_completed",
    ),
    # Enhanced provider views
    path(
        "provider/schedule/daily/",
        views.provider_daily_schedule_view,
        name="provider_daily_schedule",
    ),
    path(
        "provider/notifications/",
        views.provider_notification_center_view,
        name="provider_notification_center",
    ),
    path(
        "provider/customers/",
        views.provider_customer_profiles_view,
        name="provider_customer_profiles",
    ),
    path(
        "provider/bookings/bulk-action/",
        views.provider_bulk_booking_action_view,
        name="provider_bulk_booking_action",
    ),
    # Enhanced customer views
    path(
        "bookings/enhanced/",
        views.enhanced_booking_list_view,
        name="enhanced_booking_list",
    ),
    path(
        "bookings/<str:booking_slug>/cancel/enhanced/",
        views.enhanced_cancel_booking_view,
        name="enhanced_cancel_booking",
    ),
    path(
        "api/booking/availability/",
        views.check_booking_availability_api,
        name="check_booking_availability_api",
    ),
    # ===== ADMIN URLS =====
    # Admin dashboard and overview
    path(
        "admin/dashboard/",
        views.admin_booking_dashboard_view,
        name="admin_booking_dashboard",
    ),
    path(
        "admin/analytics/",
        views.admin_booking_analytics_view,
        name="admin_booking_analytics",
    ),
    # Admin booking management
    path("admin/bookings/", views.admin_booking_list_view, name="admin_booking_list"),
    path(
        "admin/bookings/<slug:booking_slug>/",
        views.admin_booking_detail_view,
        name="admin_booking_detail",
    ),
    path(
        "admin/bookings/<slug:booking_slug>/status/",
        views.admin_update_booking_status_view,
        name="admin_update_booking_status",
    ),
    # Admin dispute resolution
    path("admin/disputes/", views.admin_dispute_list_view, name="admin_dispute_list"),
    path(
        "admin/disputes/<slug:booking_slug>/resolve/",
        views.admin_resolve_dispute_view,
        name="admin_resolve_dispute",
    ),
]
