"""
AJAX views for the booking cart app providing real-time data for enhanced UI components.
"""

import json
from datetime import datetime, timedelta

from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.views.decorators.http import require_http_methods

from venues_app.models import Service

from ..models import Booking, BookingItem, CartItem
from ..utils import check_service_availability, get_available_time_slots


@require_http_methods(["GET"])
def get_time_slots(request, service_id):
    """
    AJAX endpoint to get available time slots for a specific service and date.
    Enhanced with pricing and availability information.
    """
    try:
        service = get_object_or_404(Service, id=service_id)
        selected_date_str = request.GET.get("date")

        if not selected_date_str:
            return JsonResponse({"error": "Date parameter is required"}, status=400)

        try:
            selected_date = datetime.strptime(selected_date_str, "%Y-%m-%d").date()
        except ValueError:
            return JsonResponse({"error": "Invalid date format"}, status=400)

        # Check if date is in the past
        if selected_date <= timezone.now().date():
            return JsonResponse(
                {"slots": [], "message": "Past dates are not available"}
            )

        # Get available time slots with enhanced information
        time_slots = get_available_time_slots(service, selected_date)

        # Enhance slots with pricing and availability details
        enhanced_slots = []
        for slot in time_slots:
            # Calculate availability
            booked_count = BookingItem.objects.filter(
                service=service,
                scheduled_date=selected_date,
                scheduled_time=slot["time"],
                booking__status__in=["confirmed", "pending"],
            ).count()

            # Get cart reservations for this time slot
            cart_reserved = CartItem.objects.filter(
                service=service,
                selected_date=selected_date,
                selected_time_slot=slot["time"],
                cart__expires_at__gt=timezone.now(),
            ).count()

            total_reserved = booked_count + cart_reserved
            max_capacity = getattr(service, "max_capacity", 10)
            available_spots = max(0, max_capacity - total_reserved)

            enhanced_slot = {
                "time": slot["time"].strftime("%H:%M"),
                "display": slot["time"].strftime("%I:%M %p"),
                "available_spots": available_spots,
                "total_capacity": max_capacity,
                "price": float(service.price_min),
                "duration_minutes": service.duration_minutes,
                "is_prime_time": is_prime_time(slot["time"]),
            }

            enhanced_slots.append(enhanced_slot)

        return JsonResponse(
            {
                "slots": enhanced_slots,
                "service": {
                    "title": service.service_title,
                    "price_min": float(service.price_min),
                    "price_max": (
                        float(service.price_max)
                        if service.price_max
                        else float(service.price_min)
                    ),
                    "duration": service.duration_minutes,
                },
                "date": selected_date_str,
            }
        )

    except Exception as e:
        return JsonResponse(
            {"error": f"Error loading time slots: {str(e)}"}, status=500
        )


@require_http_methods(["GET"])
def get_month_availability(request, service_id):
    """
    AJAX endpoint to get availability overview for a month (for calendar widget).
    Returns availability indicators for each day.
    """
    try:
        service = get_object_or_404(Service, id=service_id)
        start_date_str = request.GET.get("start")
        end_date_str = request.GET.get("end")

        if not start_date_str or not end_date_str:
            return JsonResponse(
                {"error": "Start and end date parameters are required"}, status=400
            )

        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
        except ValueError:
            return JsonResponse({"error": "Invalid date format"}, status=400)

        availability_data = {}
        current_date = start_date

        while current_date < end_date:
            # Skip past dates
            if current_date <= timezone.now().date():
                current_date += timedelta(days=1)
                continue

            # Get time slots for this date
            time_slots = get_available_time_slots(service, current_date)
            total_slots = len(time_slots)

            if total_slots == 0:
                availability_data[current_date.isoformat()] = {
                    "total_slots": 0,
                    "available_slots": 0,
                    "availability_percentage": 0,
                }
            else:
                available_slots = 0
                max_capacity = getattr(service, "max_capacity", 10)

                for slot in time_slots:
                    # Count bookings for this slot
                    booked_count = BookingItem.objects.filter(
                        service=service,
                        scheduled_date=current_date,
                        scheduled_time=slot["time"],
                        booking__status__in=["confirmed", "pending"],
                    ).count()

                    # Count cart reservations
                    cart_reserved = CartItem.objects.filter(
                        service=service,
                        selected_date=current_date,
                        selected_time_slot=slot["time"],
                        cart__expires_at__gt=timezone.now(),
                    ).count()

                    if (booked_count + cart_reserved) < max_capacity:
                        available_slots += 1

                availability_percentage = (available_slots / total_slots) * 100

                availability_data[current_date.isoformat()] = {
                    "total_slots": total_slots,
                    "available_slots": available_slots,
                    "availability_percentage": availability_percentage,
                }

            current_date += timedelta(days=1)

        return JsonResponse(
            {
                "availability": availability_data,
                "service_id": service_id,
                "period": f"{start_date_str} to {end_date_str}",
            }
        )

    except Exception as e:
        return JsonResponse(
            {"error": f"Error loading availability: {str(e)}"}, status=500
        )


@require_http_methods(["POST"])
@login_required
def check_availability(request, service_id):
    """
    AJAX endpoint to check real-time availability for a specific date, time, and quantity.
    """
    try:
        service = get_object_or_404(Service, id=service_id)

        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({"error": "Invalid JSON data"}, status=400)

        date_str = data.get("date")
        time_str = data.get("time")
        quantity = data.get("quantity", 1)

        if not all([date_str, time_str]):
            return JsonResponse({"error": "Date and time are required"}, status=400)

        try:
            selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            selected_time = datetime.strptime(time_str, "%H:%M").time()
            quantity = int(quantity)
        except (ValueError, TypeError):
            return JsonResponse(
                {"error": "Invalid date, time, or quantity format"}, status=400
            )

        # Use the existing availability checking utility
        is_available, message = check_service_availability(
            service, selected_date, selected_time, quantity
        )

        # Get detailed availability information
        max_capacity = getattr(service, "max_capacity", 10)

        booked_count = BookingItem.objects.filter(
            service=service,
            scheduled_date=selected_date,
            scheduled_time=selected_time,
            booking__status__in=["confirmed", "pending"],
        ).count()

        cart_reserved = (
            CartItem.objects.filter(
                service=service,
                selected_date=selected_date,
                selected_time_slot=selected_time,
                cart__expires_at__gt=timezone.now(),
            )
            .exclude(cart__customer=request.user)
            .count()
        )  # Exclude current user's cart

        available_spots = max(0, max_capacity - booked_count - cart_reserved)

        return JsonResponse(
            {
                "available": is_available,
                "message": message,
                "details": {
                    "max_capacity": max_capacity,
                    "booked_count": booked_count,
                    "cart_reserved": cart_reserved,
                    "available_spots": available_spots,
                    "requested_quantity": quantity,
                    "can_fulfill": available_spots >= quantity,
                },
            }
        )

    except Exception as e:
        return JsonResponse(
            {"error": f"Error checking availability: {str(e)}"}, status=500
        )


@require_http_methods(["GET"])
def get_service_pricing(request, service_id):
    """
    AJAX endpoint to get dynamic pricing information for a service.
    """
    try:
        service = get_object_or_404(Service, id=service_id)
        date_str = request.GET.get("date")
        time_str = request.GET.get("time")

        base_price = float(service.price_min)
        final_price = base_price

        # Apply dynamic pricing if date and time are provided
        if date_str and time_str:
            try:
                selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
                selected_time = datetime.strptime(time_str, "%H:%M").time()

                # Weekend pricing (example)
                if selected_date.weekday() >= 5:  # Weekend
                    final_price *= 1.2  # 20% markup

                # Prime time pricing (example)
                if is_prime_time(selected_time):
                    final_price *= 1.15  # 15% markup

                # Holiday pricing could be added here

            except ValueError:
                pass  # Use base price if date/time parsing fails

        return JsonResponse(
            {
                "base_price": float(service.price_min),
                "final_price": round(final_price, 2),
                "price_max": (
                    float(service.price_max)
                    if service.price_max
                    else float(service.price_min)
                ),
                "currency": "USD",
                "pricing_factors": {
                    "weekend_markup": (
                        selected_date.weekday() >= 5
                        if "selected_date" in locals()
                        else False
                    ),
                    "prime_time_markup": (
                        is_prime_time(selected_time)
                        if "selected_time" in locals()
                        else False
                    ),
                },
            }
        )

    except Exception as e:
        return JsonResponse({"error": f"Error getting pricing: {str(e)}"}, status=500)


@require_http_methods(["GET"])
@login_required
def get_cart_status(request):
    """
    AJAX endpoint to get current cart status and timer information.
    """
    try:
        from ..utils import get_or_create_cart

        cart = get_or_create_cart(request.user)

        if not cart:
            return JsonResponse(
                {
                    "has_cart": False,
                    "items_count": 0,
                    "total_price": 0.00,
                    "expires_at": None,
                }
            )

        return JsonResponse(
            {
                "has_cart": True,
                "items_count": cart.items.count(),
                "total_price": float(cart.total_price),
                "expires_at": cart.expires_at.isoformat() if cart.expires_at else None,
                "time_remaining": (
                    str(cart.expires_at - timezone.now()) if cart.expires_at else None
                ),
                "items": [
                    {
                        "id": item.id,
                        "service_title": item.service.service_title,
                        "quantity": item.quantity,
                        "total_price": float(item.total_price),
                        "selected_date": item.selected_date.isoformat(),
                        "selected_time": item.selected_time_slot.strftime("%H:%M"),
                    }
                    for item in cart.items.all()
                ],
            }
        )

    except Exception as e:
        return JsonResponse(
            {"error": f"Error getting cart status: {str(e)}"}, status=500
        )


def is_prime_time(time_obj):
    """
    Helper function to determine if a time slot is considered prime time.
    Prime time: 10 AM - 2 PM and 6 PM - 9 PM
    """
    hour = time_obj.hour
    return (10 <= hour < 14) or (18 <= hour < 21)


@require_http_methods(["GET"])
def get_popular_times(request, service_id):
    """
    AJAX endpoint to get popular booking times for a service (for UI hints).
    """
    try:
        service = get_object_or_404(Service, id=service_id)

        # Get booking patterns from last 30 days
        from datetime import timedelta

        from django.db.models import Count

        thirty_days_ago = timezone.now().date() - timedelta(days=30)

        popular_times = (
            BookingItem.objects.filter(
                service=service,
                scheduled_date__gte=thirty_days_ago,
                booking__status="confirmed",
            )
            .values("scheduled_time")
            .annotate(booking_count=Count("id"))
            .order_by("-booking_count")[:5]
        )

        formatted_times = []
        for time_data in popular_times:
            formatted_times.append(
                {
                    "time": time_data["scheduled_time"].strftime("%H:%M"),
                    "display": time_data["scheduled_time"].strftime("%I:%M %p"),
                    "booking_count": time_data["booking_count"],
                    "popularity_score": min(
                        100, (time_data["booking_count"] / 10) * 100
                    ),  # Scale to percentage
                }
            )

        return JsonResponse(
            {
                "popular_times": formatted_times,
                "period_days": 30,
                "service_id": service_id,
            }
        )

    except Exception as e:
        return JsonResponse(
            {"error": f"Error getting popular times: {str(e)}"}, status=500
        )
