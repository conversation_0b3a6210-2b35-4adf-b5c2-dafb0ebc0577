# Django imports
import json
from datetime import datetime, time, timedelta

from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.exceptions import ValidationError
from django.core.paginator import Paginator
from django.db import models, transaction
from django.db.models import Count, Q, Sum
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.http import require_http_methods

from discount_app.utils import get_best_discount, record_discount_usage

# Local imports
from venues_app.models import Service, Venue

from ..forms import (
    AddToCartForm,
    AdminBookingStatusForm,
    BookingActionForm,
    BookingCancellationForm,
    BookingFilterForm,
    CheckoutForm,
    DateRangeAvailabilityForm,
    DisputeResolutionForm,
    ServiceAvailabilityForm,
    UpdateCartItemForm,
)
from ..models import Booking, BookingItem, Cart, CartItem, ServiceAvailability
from ..utils import (
    check_service_availability,
    create_booking_from_cart,
    get_available_time_slots,
    get_booking_analytics,
    get_cart_total,
    get_dispute_analytics,
    get_or_create_cart,
    get_venue_booking_stats,
)

try:
    from notifications_app.utils import (
        notify_booking_cancelled,
        notify_new_booking,
        run_async,
    )

    NOTIFICATIONS_ENABLED = True
except ImportError:  # pragma: no cover - notifications optional
    NOTIFICATIONS_ENABLED = False

    def notify_new_booking(booking):  # pragma: no cover - fallback
        pass

    def notify_booking_cancelled(booking):  # pragma: no cover - fallback
        pass

    def run_async(func, *args, **kwargs):  # pragma: no cover - fallback
        func(*args, **kwargs)


try:
    from utils.logging_utils import (
        get_app_logger,
        log_error,
        log_security_event,
        log_user_activity,
    )

    LOGGING_ENABLED = True
except ImportError:  # pragma: no cover - logging optional
    LOGGING_ENABLED = False

    def get_app_logger(app_name, logger_type=""):
        import logging

        return logging.getLogger(app_name)

    def log_user_activity(
        app_name, activity_type, user=None, request=None, details=None
    ):
        pass

    def log_error(
        app_name,
        error_type,
        error_message,
        user=None,
        request=None,
        exception=None,
        details=None,
    ):
        pass

    def log_security_event(
        app_name, event_type, user_email=None, request=None, details=None
    ):
        pass
