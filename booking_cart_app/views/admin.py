from .common import *

# ===== ADMIN VIEWS =====


@login_required
def admin_booking_dashboard_view(request):
    """
    Admin dashboard for booking management with overview statistics.
    """
    # Check if user is an admin
    if not request.user.is_staff:
        messages.error(request, "Only administrators can access this page.")
        return redirect("home")

    # Get date range for filtering (default to last 30 days)
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)

    # Get filter parameters
    date_range = request.GET.get("date_range", "30")
    if date_range == "7":
        start_date = end_date - timedelta(days=7)
    elif date_range == "90":
        start_date = end_date - timedelta(days=90)
    elif date_range == "all":
        start_date = None

    # Get comprehensive analytics
    analytics = get_booking_analytics(
        user=None, venue=None, start_date=start_date, end_date=end_date
    )
    dispute_analytics = get_dispute_analytics(start_date=start_date, end_date=end_date)
    venue_stats = get_venue_booking_stats()[:10]  # Top 10 venues

    # Get recent bookings requiring attention
    disputed_bookings = Booking.objects.filter(
        status=Booking.DISPUTED, dispute_resolved_at__isnull=True
    ).order_by("-dispute_filed_at")[:5]

    pending_bookings = Booking.objects.filter(status=Booking.PENDING).order_by(
        "-booking_date"
    )[:10]

    # Get daily booking trends for the chart
    daily_bookings = []
    for i in range(7):
        date = end_date - timedelta(days=i)
        count = Booking.objects.filter(booking_date__date=date).count()
        daily_bookings.append({"date": date.strftime("%Y-%m-%d"), "count": count})
    daily_bookings.reverse()

    context = {
        "analytics": analytics,
        "dispute_analytics": dispute_analytics,
        "venue_stats": venue_stats,
        "disputed_bookings": disputed_bookings,
        "pending_bookings": pending_bookings,
        "daily_bookings": daily_bookings,
        "date_range": date_range,
        "start_date": start_date,
        "end_date": end_date,
    }

    return render(request, "booking_cart_app/admin/dashboard.html", context)


@login_required
def admin_booking_list_view(request):
    """
    List all bookings with filtering and search capabilities for admin.
    """
    # Check if user is an admin
    if not request.user.is_staff:
        messages.error(request, "Only administrators can access this page.")
        return redirect("home")

    # Initialize filter form
    filter_form = BookingFilterForm(request.GET or None)

    # Get all bookings
    bookings = Booking.objects.select_related(
        "customer", "venue", "venue__service_provider"
    ).prefetch_related("items__service")

    # Apply filters
    if filter_form.is_valid():
        # Status filter
        status = filter_form.cleaned_data.get("status")
        if status:
            bookings = bookings.filter(status=status)

        # Venue filter
        venue = filter_form.cleaned_data.get("venue")
        if venue:
            bookings = bookings.filter(venue=venue)

        # Date range filter
        date_from = filter_form.cleaned_data.get("date_from")
        date_to = filter_form.cleaned_data.get("date_to")
        if date_from:
            bookings = bookings.filter(booking_date__date__gte=date_from)
        if date_to:
            bookings = bookings.filter(booking_date__date__lte=date_to)

        # Search filter
        search = filter_form.cleaned_data.get("search")
        if search:
            bookings = bookings.filter(
                Q(booking_id__icontains=search)
                | Q(customer__email__icontains=search)
                | Q(venue__venue_name__icontains=search)
            )

    # Order by booking date (newest first)
    bookings = bookings.order_by("-booking_date")

    # Get quick stats
    total_count = bookings.count()
    disputed_count = bookings.filter(status=Booking.DISPUTED).count()
    pending_count = bookings.filter(status=Booking.PENDING).count()

    # Pagination
    paginator = Paginator(bookings, 20)
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    context = {
        "bookings": page_obj,
        "filter_form": filter_form,
        "total_count": total_count,
        "disputed_count": disputed_count,
        "pending_count": pending_count,
        "status_choices": Booking.STATUS_CHOICES,
    }

    return render(request, "booking_cart_app/admin/booking_list.html", context)


@login_required
def admin_booking_detail_view(request, booking_slug):
    """
    Display detailed information about a specific booking for admin.
    """
    # Check if user is an admin
    if not request.user.is_staff:
        messages.error(request, "Only administrators can access this page.")
        return redirect("home")

    # Get the booking
    booking = get_object_or_404(Booking, slug=booking_slug)

    # Get all booking items
    booking_items = booking.items.all().order_by("scheduled_date", "scheduled_time")

    # Check available actions
    can_update_status = booking.status not in [Booking.COMPLETED]
    can_resolve_dispute = (
        booking.status == Booking.DISPUTED and not booking.dispute_resolved_at
    )

    context = {
        "booking": booking,
        "booking_items": booking_items,
        "can_update_status": can_update_status,
        "can_resolve_dispute": can_resolve_dispute,
        "status_choices": Booking.STATUS_CHOICES,
    }

    return render(request, "booking_cart_app/admin/booking_detail.html", context)


@login_required
def admin_update_booking_status_view(request, booking_slug):
    """
    Update booking status (admin action).
    """
    # Check if user is an admin
    if not request.user.is_staff:
        messages.error(request, "Only administrators can access this page.")
        return redirect("home")

    # Get the booking
    booking = get_object_or_404(Booking, slug=booking_slug)

    if request.method == "POST":
        form = AdminBookingStatusForm(booking=booking, data=request.POST)
        if form.is_valid():
            new_status = form.cleaned_data["status"]
            admin_notes = form.cleaned_data.get("admin_notes", "")
            old_status = booking.status

            try:
                with transaction.atomic():
                    # Update booking status
                    booking.status = new_status
                    booking.save()

                    # Log admin activity
                    if LOGGING_ENABLED:
                        log_user_activity(
                            "booking_cart_app",
                            "admin_status_update",
                            user=request.user,
                            request=request,
                            details={
                                "booking_id": str(booking.booking_id),
                                "old_status": old_status,
                                "new_status": new_status,
                                "admin_notes": admin_notes,
                                "customer_email": booking.customer.email,
                            },
                        )

                    # Send notification if status changed
                    if NOTIFICATIONS_ENABLED and old_status != new_status:
                        from notifications_app.utils import (
                            notify_booking_status_changed,
                        )

                        try:
                            notify_booking_status_changed(booking, old_status)
                        except ImportError:
                            pass  # notifications_app not available

                    messages.success(
                        request,
                        f"Booking status updated from {old_status} to {new_status}.",
                    )
                    return redirect(
                        "booking_cart_app:admin_booking_detail",
                        booking_slug=booking_slug,
                    )

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        "booking_cart_app",
                        "admin_status_update_error",
                        f"Error updating booking status: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={"booking_id": str(booking.booking_id)},
                    )
                messages.error(request, f"Error updating booking status: {str(e)}")
    else:
        form = AdminBookingStatusForm(booking=booking)

    context = {
        "form": form,
        "booking": booking,
    }

    return render(request, "booking_cart_app/admin/update_booking_status.html", context)


@login_required
def admin_resolve_dispute_view(request, booking_slug):
    """
    Resolve a booking dispute (admin action).
    """
    # Check if user is an admin
    if not request.user.is_staff:
        messages.error(request, "Only administrators can access this page.")
        return redirect("home")

    # Get the booking
    booking = get_object_or_404(Booking, slug=booking_slug)

    # Check if booking is disputed and unresolved
    if booking.status != Booking.DISPUTED:
        messages.error(request, "This booking is not disputed.")
        return redirect(
            "booking_cart_app:admin_booking_detail", booking_slug=booking_slug
        )

    if booking.dispute_resolved_at:
        messages.error(request, "This dispute has already been resolved.")
        return redirect(
            "booking_cart_app:admin_booking_detail", booking_slug=booking_slug
        )

    if request.method == "POST":
        form = DisputeResolutionForm(request.POST)
        if form.is_valid():
            resolution_status = form.cleaned_data["resolution_status"]
            resolution_notes = form.cleaned_data["resolution_notes"]
            refund_amount = form.cleaned_data.get("refund_amount")
            notify_parties = form.cleaned_data.get("notify_parties", True)

            try:
                with transaction.atomic():
                    # Resolve the dispute
                    booking.resolve_dispute(
                        resolution_notes=resolution_notes, new_status=resolution_status
                    )

                    # Log admin activity
                    if LOGGING_ENABLED:
                        log_user_activity(
                            "booking_cart_app",
                            "dispute_resolved",
                            user=request.user,
                            request=request,
                            details={
                                "booking_id": str(booking.booking_id),
                                "resolution_status": resolution_status,
                                "resolution_notes": resolution_notes,
                                "refund_amount": (
                                    str(refund_amount) if refund_amount else None
                                ),
                                "customer_email": booking.customer.email,
                                "dispute_filed_by": booking.dispute_filed_by,
                            },
                        )

                    # Send notifications if requested
                    if NOTIFICATIONS_ENABLED and notify_parties:
                        # notify_dispute_resolved(booking, resolution_notes)
                        pass

                    messages.success(request, "Dispute resolved successfully.")
                    return redirect(
                        "booking_cart_app:admin_booking_detail",
                        booking_slug=booking_slug,
                    )

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        "booking_cart_app",
                        "dispute_resolution_error",
                        f"Error resolving dispute: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={"booking_id": str(booking.booking_id)},
                    )
                messages.error(request, f"Error resolving dispute: {str(e)}")
    else:
        form = DisputeResolutionForm()

    context = {
        "form": form,
        "booking": booking,
    }

    return render(request, "booking_cart_app/admin/resolve_dispute.html", context)


@login_required
def admin_dispute_list_view(request):
    """
    List all disputed bookings for admin review.
    """
    # Check if user is an admin
    if not request.user.is_staff:
        messages.error(request, "Only administrators can access this page.")
        return redirect("home")

    # Get filter parameters
    status_filter = request.GET.get("status", "unresolved")

    # Get disputed bookings
    disputes = (
        Booking.objects.filter(status=Booking.DISPUTED)
        .select_related("customer", "venue", "venue__service_provider")
        .prefetch_related("items__service")
    )

    # Apply status filter
    if status_filter == "unresolved":
        disputes = disputes.filter(dispute_resolved_at__isnull=True)
    elif status_filter == "resolved":
        disputes = disputes.filter(dispute_resolved_at__isnull=False)

    # Order by dispute filed date (newest first)
    disputes = disputes.order_by("-dispute_filed_at")

    # Get quick stats
    total_disputes = disputes.count()
    unresolved_disputes = disputes.filter(dispute_resolved_at__isnull=True).count()
    resolved_disputes = disputes.filter(dispute_resolved_at__isnull=False).count()

    # Pagination
    paginator = Paginator(disputes, 15)
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    context = {
        "disputes": page_obj,
        "status_filter": status_filter,
        "total_disputes": total_disputes,
        "unresolved_disputes": unresolved_disputes,
        "resolved_disputes": resolved_disputes,
    }

    return render(request, "booking_cart_app/admin/dispute_list.html", context)


@login_required
def admin_booking_analytics_view(request):
    """
    Display comprehensive booking analytics for admin.
    """
    # Check if user is an admin
    if not request.user.is_staff:
        messages.error(request, "Only administrators can access this page.")
        return redirect("home")

    # Get date range parameters
    date_range = request.GET.get("date_range", "30")
    custom_start = request.GET.get("start_date")
    custom_end = request.GET.get("end_date")

    # Calculate date range
    end_date = timezone.now().date()
    if date_range == "7":
        start_date = end_date - timedelta(days=7)
        period_name = "Last 7 Days"
    elif date_range == "90":
        start_date = end_date - timedelta(days=90)
        period_name = "Last 90 Days"
    elif date_range == "custom" and custom_start and custom_end:
        start_date = datetime.strptime(custom_start, "%Y-%m-%d").date()
        end_date = datetime.strptime(custom_end, "%Y-%m-%d").date()
        period_name = f"{start_date} to {end_date}"
    else:
        start_date = end_date - timedelta(days=30)
        period_name = "Last 30 Days"

    # Get comprehensive analytics
    analytics = get_booking_analytics(
        user=None, venue=None, start_date=start_date, end_date=end_date
    )
    dispute_analytics = get_dispute_analytics(start_date=start_date, end_date=end_date)
    venue_stats = get_venue_booking_stats()

    # Get daily booking trends
    daily_trends = []
    current_date = start_date
    while current_date <= end_date:
        daily_bookings = Booking.objects.filter(booking_date__date=current_date)
        daily_trends.append(
            {
                "date": current_date.strftime("%Y-%m-%d"),
                "total": daily_bookings.count(),
                "confirmed": daily_bookings.filter(status=Booking.CONFIRMED).count(),
                "cancelled": daily_bookings.filter(status=Booking.CANCELLED).count(),
                "disputed": daily_bookings.filter(status=Booking.DISPUTED).count(),
            }
        )
        current_date += timedelta(days=1)

    # Get top performing venues
    top_venues = sorted(venue_stats, key=lambda x: x["total_revenue"], reverse=True)[
        :10
    ]

    # Get booking status distribution
    status_distribution = []
    for status_code, status_name in Booking.STATUS_CHOICES:
        count = Booking.objects.filter(
            status=status_code,
            booking_date__date__gte=start_date,
            booking_date__date__lte=end_date,
        ).count()
        if count > 0:
            status_distribution.append(
                {
                    "status": status_name,
                    "count": count,
                    "percentage": round(
                        (
                            (count / analytics["total_bookings"] * 100)
                            if analytics["total_bookings"] > 0
                            else 0
                        ),
                        1,
                    ),
                }
            )

    context = {
        "analytics": analytics,
        "dispute_analytics": dispute_analytics,
        "venue_stats": venue_stats,
        "top_venues": top_venues,
        "daily_trends": daily_trends,
        "status_distribution": status_distribution,
        "date_range": date_range,
        "period_name": period_name,
        "start_date": start_date,
        "end_date": end_date,
        "custom_start": custom_start,
        "custom_end": custom_end,
    }

    return render(request, "booking_cart_app/admin/analytics.html", context)
