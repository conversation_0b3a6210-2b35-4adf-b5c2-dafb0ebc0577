from django.core.management.base import BaseCommand

from booking_cart_app.utils import clean_expired_cart_items


class Command(BaseCommand):
    """Remove expired carts and their items."""

    help = "Delete carts that have passed their expiration time along with items."

    def handle(self, *args, **options):
        cleaned = clean_expired_cart_items()
        if cleaned:
            self.stdout.write(
                self.style.SUCCESS(f"Cleaned {cleaned} expired cart item(s).")
            )
        else:
            self.stdout.write(self.style.SUCCESS("No expired cart items found."))
