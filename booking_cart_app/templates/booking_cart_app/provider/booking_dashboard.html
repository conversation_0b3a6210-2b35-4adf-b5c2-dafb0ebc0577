{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Provider Dashboard - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        border: none;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.2s ease;
    }
    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }
    .metric-icon {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }
    .booking-card {
        border: 1px solid #e9ecef;
        border-radius: 12px;
        transition: all 0.2s ease;
    }
    .booking-card:hover {
        border-color: #007bff;
        box-shadow: 0 2px 10px rgba(0,123,255,0.15);
    }
    .status-badge {
        font-weight: 500;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.875rem;
    }
    .timeline-item {
        border-left: 3px solid #e9ecef;
        padding-left: 1rem;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
    }
    .timeline-item:last-child {
        border-left-color: transparent;
        margin-bottom: 0;
    }
    .violation-alert {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        border: none;
        border-radius: 12px;
    }
    .quick-action-btn {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        background: white;
        color: #6c757d;
        transition: all 0.2s ease;
        text-decoration: none;
        padding: 0.75rem 1.5rem;
        display: inline-block;
    }
    .quick-action-btn:hover {
        border-color: #007bff;
        color: #007bff;
        text-decoration: none;
        transform: translateY(-1px);
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">Provider Dashboard</h2>
                    <p class="text-muted mb-0">{{ today|date:"F d, Y" }} - Manage your bookings and appointments</p>
                </div>
                <div>
                    <a href="{% url 'booking_cart_app:provider_todays_bookings' %}" class="btn btn-primary">
                        <i class="fas fa-calendar-day me-2"></i>Today's Schedule
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Violations Alert -->
    {% if booking_violations %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert violation-alert">
                <h6 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>Booking Limit Exceeded!
                </h6>
                <p class="mb-0">
                    You have exceeded the maximum of 10 concurrent bookings for some time slots today.
                    This may affect service quality. Please consider rescheduling some appointments.
                </p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Analytics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Total Bookings</div>
                            <div class="h4 mb-0 fw-bold">{{ status_counts.total }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Pending</div>
                            <div class="h4 mb-0 fw-bold">{{ status_counts.pending }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Today's Bookings</div>
                            <div class="h4 mb-0 fw-bold">{{ status_counts.todays_total }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="metric-icon" style="background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Monthly Revenue</div>
                            <div class="h4 mb-0 fw-bold">${{ monthly_revenue|floatformat:0 }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Today's Bookings -->
        <div class="col-lg-8 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-calendar-day me-2"></i>Today's Appointments
                        </h6>
                        <a href="{% url 'booking_cart_app:provider_todays_bookings' %}" class="btn btn-outline-primary btn-sm">
                            View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if todays_bookings %}
                        {% for booking in todays_bookings %}
                        <div class="booking-card p-3 mb-3">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0">{{ booking.customer.get_full_name|default:booking.customer.email }}</h6>
                                        {% if booking.status == 'confirmed' %}
                                            <span class="status-badge bg-success text-white">{{ booking.get_status_display }}</span>
                                        {% elif booking.status == 'pending' %}
                                            <span class="status-badge bg-warning text-dark">{{ booking.get_status_display }}</span>
                                        {% elif booking.status == 'completed' %}
                                            <span class="status-badge bg-info text-white">{{ booking.get_status_display }}</span>
                                        {% else %}
                                            <span class="status-badge bg-secondary text-white">{{ booking.get_status_display }}</span>
                                        {% endif %}
                                    </div>
                                    <div class="text-muted small mb-1">
                                        <i class="fas fa-clock me-1"></i>
                                        {% for item in booking.items.all|slice:":1" %}
                                            {{ item.scheduled_time }}
                                        {% endfor %}
                                    </div>
                                    <div class="text-muted small">
                                        <i class="fas fa-spa me-1"></i>
                                        {% for item in booking.items.all|slice:":2" %}
                                            {{ item.service_title }}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                        {% if booking.items.count > 2 %}
                                            <span class="text-muted">+{{ booking.items.count|add:"-2" }} more</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-4 text-md-end">
                                    <div class="fw-bold mb-2">${{ booking.total_price }}</div>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'booking_cart_app:provider_booking_detail' booking.slug %}"
                                           class="btn btn-outline-primary btn-sm">View</a>
                                        {% if booking.status == 'confirmed' %}
                                        <a href="{% url 'booking_cart_app:provider_mark_completed' booking.slug %}"
                                           class="btn btn-success btn-sm">Complete</a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No appointments today</h6>
                            <p class="text-muted mb-0">You have a free day! Consider promoting your services.</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Upcoming Bookings -->
            <div class="card dashboard-card mt-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-calendar-week me-2"></i>Upcoming This Week
                    </h6>
                </div>
                <div class="card-body">
                    {% if upcoming_bookings %}
                        {% for booking in upcoming_bookings %}
                        <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                            <div>
                                <div class="fw-bold">{{ booking.customer.get_full_name|default:booking.customer.email }}</div>
                                <div class="text-muted small">
                                    {% for item in booking.items.all|slice:":1" %}
                                        {{ item.scheduled_date|date:"M d" }} at {{ item.scheduled_time }}
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">${{ booking.total_price }}</div>
                                {% if booking.status == 'confirmed' %}
                                    <span class="badge bg-success">Confirmed</span>
                                {% elif booking.status == 'pending' %}
                                    <span class="badge bg-warning text-dark">Pending</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-calendar-week fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No upcoming bookings this week</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Recent Activity -->
            <div class="card dashboard-card mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-bell me-2"></i>Recent Activity
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_status_changes %}
                        {% for change in recent_status_changes %}
                        <div class="timeline-item">
                            <div class="fw-bold small">{{ change.booking.customer.get_full_name|default:change.booking.customer.email }}</div>
                            <div class="text-muted small">
                                Booking {{ change.new_status|title }}
                            </div>
                            <div class="text-muted small">{{ change.changed_at|timesince }} ago</div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No recent activity</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card dashboard-card">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="quick-action-btn">
                            <i class="fas fa-list me-2"></i>All Bookings
                        </a>
                        <a href="{% url 'booking_cart_app:provider_booking_list' %}?status=pending" class="quick-action-btn">
                            <i class="fas fa-clock me-2"></i>Pending Requests
                        </a>
                        <a href="{% url 'booking_cart_app:provider_availability_list' %}" class="quick-action-btn">
                            <i class="fas fa-calendar-plus me-2"></i>Manage Availability
                        </a>
                        <a href="{% url 'venues_app:service_list' %}" class="quick-action-btn">
                            <i class="fas fa-spa me-2"></i>Manage Services
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
