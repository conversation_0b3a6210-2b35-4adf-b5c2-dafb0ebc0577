{% extends "base.html" %}
{% load static %}
{% load humanize %}

{% block title %}Daily Schedule - {{ target_date|date:"F j, Y" }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
<style>
    .schedule-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 20px 0;
    }

    .schedule-header {
        background: white;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    }

    .date-navigation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .date-nav-btn {
        background: #4f46e5;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .date-nav-btn:hover {
        background: #4338ca;
        transform: translateY(-1px);
    }

    .current-date {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
    }

    .stat-card.revenue {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .stat-card.availability {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .stat-card.conflicts {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 4px;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .schedule-grid {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    }

    .schedule-hour {
        display: flex;
        border-bottom: 1px solid #e5e7eb;
        min-height: 80px;
    }

    .schedule-hour:last-child {
        border-bottom: none;
    }

    .hour-label {
        width: 80px;
        background: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #6b7280;
        border-right: 1px solid #e5e7eb;
    }

    .hour-content {
        flex: 1;
        padding: 12px;
        position: relative;
    }

    .booking-item {
        background: #dbeafe;
        border: 2px solid #3b82f6;
        border-radius: 8px;
        padding: 8px 12px;
        margin: 4px 0;
        cursor: pointer;
        transition: all 0.2s;
        position: relative;
    }

    .booking-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .booking-item.confirmed {
        background: #d1fae5;
        border-color: #10b981;
    }

    .booking-item.pending {
        background: #fef3c7;
        border-color: #f59e0b;
    }

    .booking-item.completed {
        background: #e0e7ff;
        border-color: #8b5cf6;
    }

    .booking-item.conflict {
        background: #fee2e2;
        border-color: #ef4444;
        animation: pulse 2s infinite;
    }

    .booking-customer {
        font-weight: 600;
        font-size: 0.9rem;
        margin-bottom: 2px;
    }

    .booking-service {
        font-size: 0.8rem;
        color: #6b7280;
        margin-bottom: 2px;
    }

    .booking-time {
        font-size: 0.75rem;
        opacity: 0.8;
    }

    .booking-status {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #3b82f6;
    }

    .booking-status.confirmed {
        background: #10b981;
    }

    .booking-status.pending {
        background: #f59e0b;
    }

    .booking-status.completed {
        background: #8b5cf6;
    }

    .availability-indicator {
        background: #f0f9ff;
        border: 1px dashed #0ea5e9;
        border-radius: 6px;
        padding: 4px 8px;
        margin: 2px 0;
        font-size: 0.75rem;
        color: #0369a1;
    }

    .conflict-alert {
        background: #fef2f2;
        border: 1px solid #fca5a5;
        border-radius: 8px;
        padding: 12px;
        margin: 8px 0;
        color: #b91c1c;
    }

    .bulk-actions {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: white;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        display: none;
        z-index: 1000;
    }

    .bulk-actions.show {
        display: block;
        animation: slideUp 0.3s ease;
    }

    .quick-action-btn {
        background: #4f46e5;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        margin: 0 4px;
        cursor: pointer;
        font-size: 0.85rem;
        transition: all 0.2s;
    }

    .quick-action-btn:hover {
        background: #4338ca;
        transform: translateY(-1px);
    }

    .quick-action-btn.decline {
        background: #ef4444;
    }

    .quick-action-btn.decline:hover {
        background: #dc2626;
    }

    .quick-action-btn.complete {
        background: #10b981;
    }

    .quick-action-btn.complete:hover {
        background: #059669;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    @keyframes slideUp {
        from { transform: translateY(100%); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .loading-spinner {
        background: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
    }

    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .date-navigation {
            flex-direction: column;
            gap: 16px;
        }

        .hour-label {
            width: 60px;
            font-size: 0.8rem;
        }

        .bulk-actions {
            bottom: 10px;
            right: 10px;
            left: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="schedule-container">
    <div class="container-fluid">
        <!-- Schedule Header -->
        <div class="schedule-header">
            <!-- Date Navigation -->
            <div class="date-navigation">
                <button class="date-nav-btn" onclick="navigateDate('{{ prev_date|date:"Y-m-d" }}')">
                    <i class="fas fa-chevron-left"></i> Previous Day
                </button>

                <div class="current-date">
                    {{ target_date|date:"l, F j, Y" }}
                    {% if target_date == today %}
                        <span class="badge bg-primary ms-2">Today</span>
                    {% endif %}
                </div>

                <button class="date-nav-btn" onclick="navigateDate('{{ next_date|date:"Y-m-d" }}')">
                    Next Day <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <!-- Daily Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ day_stats.total_bookings }}</div>
                    <div class="stat-label">Total Bookings</div>
                </div>
                <div class="stat-card revenue">
                    <div class="stat-value">${{ day_stats.total_revenue|floatformat:0 }}</div>
                    <div class="stat-label">Revenue</div>
                </div>
                <div class="stat-card availability">
                    <div class="stat-value">{{ day_stats.available_slots }}</div>
                    <div class="stat-label">Available Slots</div>
                </div>
                <div class="stat-card conflicts">
                    <div class="stat-value">{{ schedule_hours|length }}</div>
                    <div class="stat-label">Active Hours</div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="d-flex gap-2 mb-3">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshSchedule()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <button class="btn btn-outline-success btn-sm" onclick="exportSchedule()">
                    <i class="fas fa-download"></i> Export
                </button>
                <button class="btn btn-outline-info btn-sm" onclick="toggleBulkMode()">
                    <i class="fas fa-tasks"></i> Bulk Actions
                </button>
            </div>
        </div>

        <!-- Schedule Grid -->
        <div class="schedule-grid">
            {% for hour_data in schedule_hours %}
            <div class="schedule-hour" data-hour="{{ hour_data.hour }}">
                <div class="hour-label">
                    {{ hour_data.hour_display }}
                </div>
                <div class="hour-content">
                    <!-- Availability Indicators -->
                    {% for slot in hour_data.availability %}
                    <div class="availability-indicator">
                        <i class="fas fa-clock"></i>
                        {{ slot.service.service_title }}
                        ({{ slot.available_spots }} spots available)
                    </div>
                    {% empty %}
                    <div class="availability-indicator" style="opacity: 0.5;">
                        No availability set for this hour
                    </div>
                    {% endfor %}

                    <!-- Bookings -->
                    {% for booking_data in hour_data.bookings %}
                    <div class="booking-item {{ booking_data.booking.status }}"
                         data-booking-id="{{ booking_data.booking.id }}"
                         onclick="selectBooking(this, {{ booking_data.booking.id }})">
                        <div class="booking-status {{ booking_data.booking.status }}"></div>
                        <div class="booking-customer">
                            {{ booking_data.booking.customer.get_full_name|default:booking_data.booking.customer.email }}
                        </div>
                        <div class="booking-service">
                            {{ booking_data.item.service_title }}
                        </div>
                        <div class="booking-time">
                            {{ booking_data.item.scheduled_time|time:"g:i A" }}
                            ({{ booking_data.duration_minutes }}min) -
                            ${{ booking_data.booking.total_price }}
                        </div>
                    </div>
                    {% endfor %}

                    <!-- Conflict Alerts -->
                    {% for conflict in hour_data.conflicts %}
                    <div class="conflict-alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Overbooking Alert:</strong>
                        {{ conflict.count }} bookings at {{ conflict.time|time:"g:i A" }}
                        (Max: 10)
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% empty %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No schedule data for this date</h5>
                <p class="text-muted">Set up availability slots to start accepting bookings.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Bulk Actions Panel -->
<div class="bulk-actions" id="bulkActionsPanel">
    <h6 class="mb-3">Bulk Actions (<span id="selectedCount">0</span> selected)</h6>
    <div class="d-flex gap-2">
        <button class="quick-action-btn" onclick="bulkAction('accept')">
            <i class="fas fa-check"></i> Accept All
        </button>
        <button class="quick-action-btn decline" onclick="bulkAction('decline')">
            <i class="fas fa-times"></i> Decline All
        </button>
        <button class="quick-action-btn complete" onclick="bulkAction('complete')">
            <i class="fas fa-check-double"></i> Complete All
        </button>
        <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
            Clear
        </button>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
        <div>Processing...</div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedBookings = new Set();
let bulkMode = false;

function navigateDate(date) {
    window.location.href = `?date=${date}`;
}

function refreshSchedule() {
    window.location.reload();
}

function exportSchedule() {
    const date = '{{ target_date|date:"Y-m-d" }}';
    window.open(`/api/schedule/export/?date=${date}`, '_blank');
}

function toggleBulkMode() {
    bulkMode = !bulkMode;
    document.body.classList.toggle('bulk-mode', bulkMode);

    if (!bulkMode) {
        clearSelection();
    }
}

function selectBooking(element, bookingId) {
    if (!bulkMode) {
        // Single booking action - show details
        showBookingDetails(bookingId);
        return;
    }

    // Bulk mode - select/deselect
    element.classList.toggle('selected');

    if (selectedBookings.has(bookingId)) {
        selectedBookings.delete(bookingId);
    } else {
        selectedBookings.add(bookingId);
    }

    updateBulkActions();
}

function updateBulkActions() {
    const panel = document.getElementById('bulkActionsPanel');
    const count = document.getElementById('selectedCount');

    count.textContent = selectedBookings.size;

    if (selectedBookings.size > 0) {
        panel.classList.add('show');
    } else {
        panel.classList.remove('show');
    }
}

function clearSelection() {
    selectedBookings.clear();
    document.querySelectorAll('.booking-item.selected').forEach(item => {
        item.classList.remove('selected');
    });
    updateBulkActions();
}

function bulkAction(action) {
    if (selectedBookings.size === 0) {
        alert('No bookings selected');
        return;
    }

    const confirmMessage = `Are you sure you want to ${action} ${selectedBookings.size} booking(s)?`;
    if (!confirm(confirmMessage)) {
        return;
    }

    showLoading(true);

    const formData = new FormData();
    formData.append('action', action);
    selectedBookings.forEach(id => formData.append('booking_ids', id));

    if (action === 'decline') {
        const reason = prompt('Reason for declining (optional):');
        if (reason) formData.append('decline_reason', reason);
    }

    fetch('{% url "booking_cart_app:provider_bulk_booking_action" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);

        if (data.success && data.success.length > 0) {
            alert(`Successfully processed ${data.success.length} booking(s)`);
            window.location.reload();
        }

        if (data.errors && data.errors.length > 0) {
            alert(`Errors: ${data.errors.join(', ')}`);
        }
    })
    .catch(error => {
        showLoading(false);
        alert('Error processing bulk action: ' + error.message);
    });
}

function showBookingDetails(bookingId) {
    // Redirect to booking detail page
    window.location.href = `/bookings/provider/detail/${bookingId}/`;
}

function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = show ? 'flex' : 'none';
}

// Add CSRF token to page
if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrfmiddlewaretoken';
    csrfInput.value = '{{ csrf_token }}';
    document.body.appendChild(csrfInput);
}

// Auto-refresh every 5 minutes
setInterval(() => {
    if (!bulkMode && selectedBookings.size === 0) {
        refreshSchedule();
    }
}, 300000);
</script>
{% endblock %}
