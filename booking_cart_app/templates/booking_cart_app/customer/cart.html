{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Your Cart - CozyWish{% endblock %}

{% block booking_extra_css %}
<style>
.cart-item {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.cart-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.cart-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 1.5rem;
}

.cart-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.cart-empty i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.quantity-badge {
    background: #667eea;
    color: white;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.price-highlight {
    color: #28a745;
    font-weight: 600;
    font-size: 1.1rem;
}

.cart-summary {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    position: sticky;
    top: 100px;
}

.btn-remove {
    background: none;
    border: 1px solid #dc3545;
    color: #dc3545;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn-remove:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.05);
}

.btn-checkout {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 10px;
    padding: 1rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-checkout:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.cart-timer {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
}

.timer-icon {
    color: #fd7e14;
}

@media (max-width: 768px) {
    .cart-item {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .cart-item .row {
        margin: 0;
    }

    .cart-item .col-md-8,
    .cart-item .col-md-4 {
        padding: 0;
        margin-bottom: 1rem;
    }

    .cart-item .col-md-4 {
        text-align: left !important;
        border-top: 1px solid #e9ecef;
        padding-top: 1rem;
    }

    .cart-summary {
        position: static;
        margin-top: 2rem;
    }

    .btn-checkout {
        width: 100%;
        padding: 1.25rem;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="cart-header">
                    <h3 class="mb-1">
                        <i class="fas fa-shopping-cart me-2"></i>Your Cart
                    </h3>
                    {% if cart.items.count > 0 %}
                    <p class="mb-0 opacity-75">{{ cart.items.count }} item{{ cart.items.count|pluralize }} ready for checkout</p>
                    {% endif %}
                </div>
                <div class="card-body p-0">
                    {% if cart.items.count > 0 %}
                        <div class="p-3">
                            {% for item in cart.items.all %}
                            <div class="cart-item fade-in">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <div class="d-flex align-items-start">
                                            <div class="flex-grow-1">
                                                <h5 class="mb-2 text-dark">{{ item.service.service_title }}</h5>
                                                <p class="text-muted mb-2">{{ item.service.short_description }}</p>

                                                <div class="row g-2 text-sm">
                                                    <div class="col-sm-6">
                                                        <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                                        <strong>Venue:</strong> {{ item.service.venue.venue_name }}
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <i class="fas fa-clock text-info me-1"></i>
                                                        <strong>Duration:</strong> {{ item.service.duration_minutes }} min
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <i class="fas fa-calendar text-success me-1"></i>
                                                        <strong>Date:</strong> {{ item.selected_date|date:"M d, Y" }}
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <i class="fas fa-clock text-warning me-1"></i>
                                                        <strong>Time:</strong> {{ item.selected_time_slot|time:"g:i A" }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-md-end">
                                        <div class="mb-2">
                                            <span class="quantity-badge">
                                                <i class="fas fa-users me-1"></i>{{ item.quantity }} appointment{{ item.quantity|pluralize }}
                                            </span>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">${{ item.price_per_item }} each</small>
                                        </div>
                                        <div class="price-highlight mb-3">
                                            Total: ${{ item.total_price }}
                                        </div>
                                        <div class="d-flex gap-2 justify-content-md-end">
                                            <a href="{% url 'booking_cart_app:update_cart_item' item.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit me-1"></i>Edit
                                            </a>
                                            <form method="post" action="{% url 'booking_cart_app:remove_from_cart' item.id %}" class="d-inline">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-remove"
                                                        onclick="return confirm('Remove this item from your cart?')">
                                                    <i class="fas fa-trash me-1"></i>Remove
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="p-3 border-top">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <h4 class="mb-0 price-highlight">
                                        Total: ${{ cart.total_price }}
                                    </h4>
                                    <small class="text-muted">{{ cart.items.count }} item{{ cart.items.count|pluralize }}</small>
                                </div>
                                <a href="{% url 'booking_cart_app:checkout' %}" class="btn btn-checkout">
                                    <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <div class="cart-empty">
                            <i class="fas fa-shopping-cart"></i>
                            <h4 class="mb-3">Your cart is empty</h4>
                            <p class="mb-4">Discover amazing services and add them to your cart to get started.</p>
                            <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary btn-lg">
                                <i class="fas fa-search me-2"></i>Browse Services
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {% if cart.items.count > 0 %}
            <div class="cart-summary">
                <h5 class="mb-3">
                    <i class="fas fa-receipt me-2"></i>Order Summary
                </h5>

                <div class="d-flex justify-content-between mb-2">
                    <span>Items ({{ cart.items.count }}):</span>
                    <span>${{ cart.total_price }}</span>
                </div>

                <div class="d-flex justify-content-between mb-2">
                    <span>Service Fee:</span>
                    <span class="text-success">FREE</span>
                </div>

                <div class="d-flex justify-content-between mb-2">
                    <span>Taxes:</span>
                    <span>Calculated at checkout</span>
                </div>

                <hr>

                <div class="d-flex justify-content-between mb-3">
                    <strong>Estimated Total:</strong>
                    <strong class="price-highlight">${{ cart.total_price }}</strong>
                </div>

                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Final total will be calculated during checkout
                    </small>
                </div>

                <a href="{% url 'booking_cart_app:checkout' %}" class="btn btn-checkout w-100 mb-3">
                    <i class="fas fa-credit-card me-2"></i>Secure Checkout
                </a>

                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Your payment information is secure
                    </small>
                </div>
            </div>

            <div class="cart-timer">
                <div class="d-flex align-items-center">
                    <i class="fas fa-clock timer-icon me-2"></i>
                    <div class="flex-grow-1">
                        <small class="fw-bold">Cart expires in:</small>
                        <div id="cart-timer" class="fw-bold">{{ cart.expires_at|timeuntil }}</div>
                    </div>
                </div>
            </div>

            <div class="mt-3 p-3 bg-light rounded">
                <h6 class="mb-2">
                    <i class="fas fa-star text-warning me-1"></i>Why Choose CozyWish?
                </h6>
                <ul class="list-unstyled mb-0 small">
                    <li class="mb-1"><i class="fas fa-check text-success me-1"></i> Verified service providers</li>
                    <li class="mb-1"><i class="fas fa-check text-success me-1"></i> Secure online booking</li>
                    <li class="mb-1"><i class="fas fa-check text-success me-1"></i> 24/7 customer support</li>
                    <li><i class="fas fa-check text-success me-1"></i> Satisfaction guarantee</li>
                </ul>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% if cart.items.count > 0 %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh cart timer every minute
    setInterval(function() {
        const timerElement = document.getElementById('cart-timer');
        if (timerElement) {
            fetch('{% url "booking_cart_app:cart_view" %}', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newTimer = doc.getElementById('cart-timer');
                if (newTimer) {
                    timerElement.textContent = newTimer.textContent;
                }
            })
            .catch(err => console.log('Timer update failed:', err));
        }
    }, 60000); // Update every minute
});
</script>
{% endif %}
{% endblock %}
