{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}My Booking Dashboard - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .status-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }
    .status-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    .status-card .card-body {
        padding: 1.5rem;
    }
    .status-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }
    .booking-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        transition: all 0.2s ease;
    }
    .booking-card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    }
    .progress-bar {
        height: 8px;
        border-radius: 4px;
    }
    .status-badge {
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
    }
    .timeline-item {
        border-left: 3px solid #e9ecef;
        padding-left: 1rem;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
    }
    .timeline-item:last-child {
        border-left-color: transparent;
        margin-bottom: 0;
    }
    .timeline-time {
        font-size: 0.875rem;
        color: #6c757d;
    }
    .next-booking-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 16px;
    }
    .view-toggle {
        border-radius: 25px;
        padding: 0.5rem 1.5rem;
        border: 2px solid #e9ecef;
        background: white;
        color: #6c757d;
        text-decoration: none;
        transition: all 0.2s ease;
    }
    .view-toggle.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }
    .view-toggle:hover {
        text-decoration: none;
        color: #007bff;
        border-color: #007bff;
    }
    .view-toggle.active:hover {
        color: white;
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container-fluid mt-4">
    <!-- Header with View Toggle -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">My Booking Dashboard</h2>
                    <p class="text-muted mb-0">Manage and track your service bookings</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="?view=dashboard" class="view-toggle {% if view_type == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-th-large me-1"></i>Dashboard
                    </a>
                    <a href="?view=list" class="view-toggle {% if view_type == 'list' %}active{% endif %}">
                        <i class="fas fa-list me-1"></i>List View
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card status-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="status-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Total Bookings</div>
                            <div class="h4 mb-0 fw-bold">{{ status_counts.total }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card status-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="status-icon" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Pending</div>
                            <div class="h4 mb-0 fw-bold">{{ status_counts.pending }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card status-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="status-icon" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Confirmed</div>
                            <div class="h4 mb-0 fw-bold">{{ status_counts.confirmed }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card status-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="status-icon" style="background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Total Spent</div>
                            <div class="h4 mb-0 fw-bold">${{ total_spent|floatformat:0 }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Next Upcoming Booking -->
        <div class="col-lg-8 mb-4">
            {% if next_booking %}
            <div class="card next-booking-card">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h5 class="mb-1">
                                <i class="fas fa-star me-2"></i>Next Booking
                            </h5>
                            <p class="mb-0 opacity-75">Your upcoming appointment</p>
                        </div>
                        <span class="badge bg-light text-dark">
                            {{ next_booking.get_status_display }}
                        </span>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="mb-2">{{ next_booking.venue.venue_name }}</h6>
                            <div class="mb-2">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                {{ next_booking.venue.city }}, {{ next_booking.venue.state }}
                            </div>
                            <div class="mb-2">
                                <i class="fas fa-calendar me-2"></i>
                                {% for item in next_booking.items.all|slice:":1" %}
                                    {{ item.scheduled_date|date:"F d, Y" }} at {{ item.scheduled_time }}
                                {% endfor %}
                            </div>
                            <div>
                                <i class="fas fa-spa me-2"></i>
                                {% for item in next_booking.items.all|slice:":2" %}
                                    {{ item.service_title }}{% if not forloop.last %}, {% endif %}
                                {% endfor %}
                                {% if next_booking.items.count > 2 %}
                                    <span class="opacity-75">+{{ next_booking.items.count|add:"-2" }} more</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="h4 mb-2">${{ next_booking.total_price }}</div>
                            <a href="{% url 'booking_cart_app:booking_detail' next_booking.slug %}"
                               class="btn btn-light btn-sm">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Upcoming Bookings</h5>
                    <p class="text-muted">Browse our services to make your next booking</p>
                    <a href="/" class="btn btn-primary">Browse Services</a>
                </div>
            </div>
            {% endif %}

            <!-- Recent Bookings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>Recent Bookings
                    </h6>
                </div>
                <div class="card-body">
                    {% if upcoming_bookings %}
                        <div class="row">
                            {% for booking in upcoming_bookings|slice:":3" %}
                            <div class="col-lg-4 mb-3">
                                <div class="booking-card card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0">{{ booking.venue.venue_name|truncatechars:20 }}</h6>
                                            {% if booking.status == 'confirmed' %}
                                                <span class="status-badge bg-success text-white">{{ booking.get_status_display }}</span>
                                            {% elif booking.status == 'pending' %}
                                                <span class="status-badge bg-warning text-dark">{{ booking.get_status_display }}</span>
                                            {% elif booking.status == 'cancelled' %}
                                                <span class="status-badge bg-danger text-white">{{ booking.get_status_display }}</span>
                                            {% else %}
                                                <span class="status-badge bg-secondary text-white">{{ booking.get_status_display }}</span>
                                            {% endif %}
                                        </div>
                                        <div class="text-muted small mb-2">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ booking.booking_date|date:"M d" }}
                                        </div>
                                        <div class="text-muted small mb-3">
                                            {% for item in booking.items.all|slice:":1" %}
                                                {{ item.service_title|truncatechars:30 }}
                                            {% endfor %}
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="fw-bold">${{ booking.total_price }}</span>
                                            <a href="{% url 'booking_cart_app:booking_detail' booking.slug %}"
                                               class="btn btn-outline-primary btn-sm">View</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="?view=list" class="btn btn-outline-primary">View All Bookings</a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-calendar-alt fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No recent bookings to display</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bell me-2"></i>Recent Activity
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_status_changes %}
                        {% for change in recent_status_changes %}
                        <div class="timeline-item">
                            <div class="fw-bold small">{{ change.booking.venue.venue_name }}</div>
                            <div class="text-muted small">
                                Status changed from {{ change.old_status|title }} to {{ change.new_status|title }}
                            </div>
                            <div class="timeline-time">{{ change.changed_at|timesince }} ago</div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No recent activity</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>Browse Services
                        </a>
                        <a href="{% url 'booking_cart_app:cart_view' %}" class="btn btn-outline-success">
                            <i class="fas fa-shopping-cart me-2"></i>View Cart
                        </a>
                        <a href="?view=list&status=pending" class="btn btn-outline-warning">
                            <i class="fas fa-clock me-2"></i>Pending Bookings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
