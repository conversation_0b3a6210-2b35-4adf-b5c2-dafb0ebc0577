# Local imports
from .models import Cart
from .utils import get_cart_total


def cart_context(request):
    """
    Context processor to add cart information to all templates.
    Provides cart count and total for authenticated customers.
    """
    cart_count = 0
    cart_total = 0

    if (
        request.user.is_authenticated
        and hasattr(request.user, "is_customer")
        and request.user.is_customer
    ):

        try:
            # Get user's cart
            cart = Cart.objects.get(customer=request.user)

            # Only count if cart is not expired
            if not cart.is_expired:
                cart_count = cart.total_items
                cart_total = cart.total_price
        except Cart.DoesNotExist:
            # No cart exists for user
            pass

    return {
        "cart_count": cart_count,
        "cart_total": cart_total,
    }
