"""
Tests package for booking_cart_app.

This package contains all unit and integration tests for the booking_cart_app.
All test modules are imported here to ensure proper test discovery.
"""

from .test_forms import AddToCartFormTest, CheckoutFormTest, ServiceAvailabilityFormTest
from .test_integration import (
    AdminBookingManagementIntegrationTest,
    CrossAppIntegrationTest,
    CustomerBookingWorkflowIntegrationTest,
    ProviderBookingManagementIntegrationTest,
    RealTimeAvailabilityIntegrationTest,
    SecurityPermissionIntegrationTest,
)

# Import all test modules to ensure they are discovered by Django's test runner
from .test_models import (
    BookingItemModelTest,
    BookingModelTest,
    CartItemModelTest,
    CartModelTest,
    ServiceAvailabilityModelTest,
)
from .test_urls import BookingCartURLsTest, URLAccessTest
from .test_utils import AvailabilityUtilityTest, BookingUtilityTest, CartUtilityTest
from .test_views import CustomerViewsTest, ProviderViewsTest
