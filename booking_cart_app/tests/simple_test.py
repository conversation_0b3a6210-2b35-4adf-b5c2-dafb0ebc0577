"""
Simple test to verify that the test setup works correctly.
"""

from django.apps import apps
from django.test import TestCase


class SimpleTestCase(TestCase):
    """Simple test case to verify that the test setup works correctly."""

    def test_app_installed(self):
        """Test that the booking_cart_app is installed."""
        self.assertTrue(apps.is_installed("booking_cart_app"))

    def test_models_exist(self):
        """Test that the models exist."""
        from booking_cart_app.models import (
            Booking,
            BookingItem,
            Cart,
            CartItem,
            ServiceAvailability,
        )

        self.assertTrue(Cart)
        self.assertTrue(CartItem)
        self.assertTrue(Booking)
        self.assertTrue(BookingItem)
        self.assertTrue(ServiceAvailability)
        print("Simple test passed!")
