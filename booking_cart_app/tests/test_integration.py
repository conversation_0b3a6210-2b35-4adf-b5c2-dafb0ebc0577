"""
Integration tests for booking_cart_app.

This module contains comprehensive integration tests for complex workflows and
end-to-end functionality in the booking_cart_app, including complete customer booking flows,
provider booking management, admin oversight, and cross-app integrations.
"""

# Standard library imports
from datetime import timedelta
from decimal import Decimal
from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages

# Django imports
from django.test import Client, TestCase
from django.urls import reverse
from django.utils import timezone

# Local imports
from accounts_app.models import CustomerProfile, ServiceProviderProfile
from booking_cart_app.models import (
    Booking,
    BookingItem,
    Cart,
    CartItem,
    ServiceAvailability,
)
from venues_app.models import Category, Service, Venue, VenueCategory

User = get_user_model()


class CustomerBookingWorkflowIntegrationTest(TestCase):
    """Test the complete customer booking workflow from cart to completion."""

    def setUp(self):
        """Set up test data for customer booking workflow tests."""
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role=User.CUSTOMER,
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role=User.SERVICE_PROVIDER,
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Wellness Center",
            phone="+**********",
            contact_name="Test Provider",
            address="123 Test St",
            city="Test City",
            state="NY",
            zip_code="12345",
        )

        # Create test category and venue
        self.category = Category.objects.create(
            category_name="Spa Services",
            category_description="Relaxation and wellness services",
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Luxury Wellness Spa",
            short_description="Premium spa services",
            state="New York",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Wellness Ave",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create test service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="Relaxing full-body massage",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True,
        )

        # Create service availability
        tomorrow = timezone.now().date() + timedelta(days=1)
        from datetime import time

        self.availability = ServiceAvailability.objects.create(
            service=self.service,
            available_date=tomorrow,
            start_time=time(10, 0),  # Fixed time without microseconds
            end_time=time(11, 0),  # Fixed time without microseconds
            max_bookings=3,
            current_bookings=0,
            is_available=True,
        )

    def test_complete_customer_booking_workflow(self):
        """Test the complete customer booking workflow from cart to booking confirmation."""
        # Step 1: Customer logs in
        login_success = self.client.login(
            email="<EMAIL>", password="CustomerPass123!"
        )
        self.assertTrue(login_success)

        # Step 2: Add service to cart (simplified test)
        add_to_cart_url = reverse(
            "booking_cart_app:add_to_cart", args=[self.service.id]
        )
        cart_data = {
            "selected_date": self.availability.available_date.isoformat(),
            "selected_time_slot": self.availability.start_time.strftime("%H:%M"),
            "quantity": 1,
        }
        response = self.client.post(add_to_cart_url, cart_data)
        self.assertEqual(response.status_code, 302)  # Should redirect

        # Verify cart item was created
        cart = Cart.objects.get(customer=self.customer)
        self.assertEqual(cart.items.count(), 1)
        cart_item = cart.items.first()
        self.assertEqual(cart_item.service, self.service)
        self.assertEqual(cart_item.quantity, 1)

        # Step 3: View cart
        cart_url = reverse("booking_cart_app:cart_view")
        response = self.client.get(cart_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Swedish Massage")

        # Step 4: Proceed to checkout
        checkout_url = reverse("booking_cart_app:checkout")
        response = self.client.get(checkout_url)
        self.assertEqual(response.status_code, 200)

        # Step 5: Complete checkout
        checkout_data = {
            "notes": "Please call me 30 minutes before the appointment.",
            "accept_terms": True,
        }
        response = self.client.post(checkout_url, checkout_data)
        self.assertEqual(response.status_code, 302)  # Should redirect to confirmation

        # Verify booking was created
        booking = Booking.objects.filter(customer=self.customer).first()
        self.assertIsNotNone(booking)
        self.assertEqual(booking.venue, self.venue)
        self.assertEqual(booking.status, Booking.PENDING)
        self.assertEqual(booking.notes, checkout_data["notes"])

        # Verify booking items were created
        self.assertEqual(booking.items.count(), 1)
        booking_item = booking.items.first()
        self.assertEqual(booking_item.service, self.service)
        self.assertEqual(booking_item.quantity, 1)

        # Verify cart was cleared
        cart.refresh_from_db()
        self.assertEqual(cart.items.count(), 0)

        # Verify service availability was updated
        self.availability.refresh_from_db()
        self.assertEqual(self.availability.current_bookings, 1)

        # Step 6: View booking confirmation
        confirmation_url = reverse(
            "booking_cart_app:booking_detail", args=[booking.slug]
        )
        response = self.client.get(confirmation_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Swedish Massage")

        # Step 7: View booking list
        booking_list_url = reverse("booking_cart_app:booking_list")
        response = self.client.get(booking_list_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Swedish Massage")

        # Step 8: View booking detail
        booking_detail_url = reverse(
            "booking_cart_app:booking_detail", args=[booking.slug]
        )
        response = self.client.get(booking_detail_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, booking.notes)

        return booking  # Return for use in other tests

    def test_customer_booking_cancellation_workflow(self):
        """Test customer booking cancellation within 6-hour window."""
        # Create a booking first
        booking = self.test_complete_customer_booking_workflow()

        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Test cancellation within 6-hour window
        cancel_url = reverse("booking_cart_app:cancel_booking", args=[booking.slug])
        response = self.client.get(cancel_url)
        self.assertEqual(response.status_code, 200)

        # Submit cancellation
        cancel_data = {
            "cancellation_reason": "Need to reschedule due to emergency",
            "confirm_cancellation": True,
        }
        response = self.client.post(cancel_url, cancel_data)
        self.assertEqual(response.status_code, 302)

        # Verify booking was cancelled
        booking.refresh_from_db()
        self.assertEqual(booking.status, Booking.CANCELLED)
        self.assertEqual(
            booking.cancellation_reason, cancel_data["cancellation_reason"]
        )

        # Verify service availability was restored
        self.availability.refresh_from_db()
        self.assertEqual(self.availability.current_bookings, 0)


class ProviderBookingManagementIntegrationTest(TestCase):
    """Test provider booking management workflows."""

    def setUp(self):
        """Set up test data for provider booking management tests."""
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role=User.CUSTOMER,
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role=User.SERVICE_PROVIDER,
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Provider Wellness Center",
            phone="+**********",
            contact_name="Provider Test",
            address="456 Provider St",
            city="Provider City",
            state="CA",
            zip_code="90210",
        )

        # Create test category and venue
        self.category = Category.objects.create(
            category_name="Wellness Services",
            category_description="Health and wellness services",
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Provider Spa",
            short_description="Professional spa services",
            state="California",
            county="Los Angeles County",
            city="Los Angeles",
            street_number="456",
            street_name="Spa Boulevard",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create test service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Hot Stone Massage",
            short_description="Therapeutic hot stone massage",
            price_min=Decimal("120.00"),
            duration_minutes=90,
            is_active=True,
        )

        # Create test booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status=Booking.PENDING,
            total_price=Decimal("120.00"),
            notes="Looking forward to the massage",
        )

        # Create booking item
        tomorrow = timezone.now().date() + timedelta(days=1)
        from datetime import time

        self.booking_item = BookingItem.objects.create(
            booking=self.booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=self.service.price_min,
            quantity=1,
            scheduled_date=tomorrow,
            scheduled_time=time(15, 0),  # Fixed time without microseconds
            duration_minutes=self.service.duration_minutes,
        )

        # Create service availability
        self.availability = ServiceAvailability.objects.create(
            service=self.service,
            available_date=tomorrow,
            start_time=time(15, 0),  # Fixed time without microseconds
            end_time=time(16, 30),  # Fixed time without microseconds
            max_bookings=2,
            current_bookings=1,
            is_available=True,
        )

    def test_provider_booking_management_workflow(self):
        """Test complete provider booking management workflow."""
        # Step 1: Provider logs in
        login_success = self.client.login(
            email="<EMAIL>", password="ProviderPass123!"
        )
        self.assertTrue(login_success)

        # Step 2: View booking list
        booking_list_url = reverse("booking_cart_app:provider_booking_list")
        response = self.client.get(booking_list_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Hot Stone Massage")

        # Step 3: View booking detail
        booking_detail_url = reverse(
            "booking_cart_app:provider_booking_detail", args=[self.booking.slug]
        )
        response = self.client.get(booking_detail_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.booking.notes)

        # Step 4: Accept booking
        accept_url = reverse(
            "booking_cart_app:provider_accept_booking", args=[self.booking.slug]
        )
        response = self.client.post(accept_url)
        self.assertEqual(response.status_code, 302)

        # Verify booking status was updated
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, Booking.CONFIRMED)

    def test_provider_booking_decline_workflow(self):
        """Test provider declining a booking."""
        # Login as provider
        self.client.login(email="<EMAIL>", password="ProviderPass123!")

        # Decline booking
        decline_url = reverse(
            "booking_cart_app:provider_decline_booking", args=[self.booking.slug]
        )
        decline_data = {"decline_reason": "Therapist not available at requested time"}
        response = self.client.post(decline_url, decline_data)
        self.assertEqual(response.status_code, 302)

        # Verify booking was declined
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, Booking.DECLINED)

        # Verify service availability was restored
        self.availability.refresh_from_db()
        self.assertEqual(self.availability.current_bookings, 0)


class SecurityPermissionIntegrationTest(TestCase):
    """Test security and permission controls across different user roles."""

    def setUp(self):
        """Set up test data for security and permission tests."""
        self.client = Client()

        # Create test users with different roles
        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role=User.CUSTOMER,
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role=User.SERVICE_PROVIDER,
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Security Test Spa",
            phone="+**********",
            contact_name="Security Provider",
            address="999 Security Ave",
            city="Las Vegas",
            state="NV",
            zip_code="89101",
        )

        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="AdminPass123!",
            role=User.ADMIN,
            is_staff=True,
        )

        # Create test venue and service
        self.category = Category.objects.create(
            category_name="Security Test Services",
            category_description="Services for security testing",
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Security Test Venue",
            short_description="Venue for security testing",
            state="Nevada",
            county="Clark County",
            city="Las Vegas",
            street_number="999",
            street_name="Security Ave",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        VenueCategory.objects.create(venue=self.venue, category=self.category)

        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Security Test Service",
            short_description="Service for security testing",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True,
        )

        # Create test booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status=Booking.PENDING,
            total_price=Decimal("100.00"),
        )

    def test_customer_access_control_workflow(self):
        """Test that customers can only access their own data."""
        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Customer should be able to view their own booking
        booking_detail_url = reverse(
            "booking_cart_app:booking_detail", args=[self.booking.slug]
        )
        response = self.client.get(booking_detail_url)
        self.assertEqual(response.status_code, 200)

        # Customer should NOT be able to access provider views
        provider_booking_list_url = reverse("booking_cart_app:provider_booking_list")
        response = self.client.get(provider_booking_list_url)
        self.assertEqual(response.status_code, 302)  # Should redirect

        # Customer should NOT be able to access admin views
        admin_booking_list_url = reverse("booking_cart_app:admin_booking_list")
        response = self.client.get(admin_booking_list_url)
        self.assertEqual(response.status_code, 302)  # Should redirect

    def test_unauthenticated_access_control(self):
        """Test that unauthenticated users cannot access booking features."""
        # Ensure user is logged out
        self.client.logout()

        # Test various URLs that should require authentication
        protected_urls = [
            reverse("booking_cart_app:cart_view"),
            reverse("booking_cart_app:checkout"),
            reverse("booking_cart_app:booking_list"),
            reverse("booking_cart_app:booking_detail", args=[self.booking.slug]),
            reverse("booking_cart_app:provider_booking_list"),
            reverse("booking_cart_app:admin_booking_list"),
        ]

        for url in protected_urls:
            response = self.client.get(url)
            self.assertIn(
                response.status_code, [302, 403]
            )  # Should redirect to login or forbidden


class AdminBookingManagementIntegrationTest(TestCase):
    """Test admin booking management and dispute resolution workflows."""

    def setUp(self):
        """Set up test data for admin booking management tests."""
        self.client = Client()

        # Create test users
        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="AdminPass123!",
            role=User.ADMIN,
            is_staff=True,
            is_superuser=True,
        )

        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role=User.CUSTOMER,
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role=User.SERVICE_PROVIDER,
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Admin Test Spa",
            phone="+**********",
            contact_name="Admin Provider",
            address="789 Admin St",
            city="Houston",
            state="TX",
            zip_code="77001",
        )

        # Create test category and venue
        self.category = Category.objects.create(
            category_name="Admin Test Services",
            category_description="Services for admin testing",
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Admin Test Venue",
            short_description="Venue for admin testing",
            state="Texas",
            county="Harris County",
            city="Houston",
            street_number="789",
            street_name="Admin Street",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create test service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Admin Test Massage",
            short_description="Massage for admin testing",
            price_min=Decimal("90.00"),
            duration_minutes=60,
            is_active=True,
        )

        # Create test booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status=Booking.PENDING,
            total_price=Decimal("90.00"),
            notes="Pending booking for admin test",
        )

    def test_admin_booking_dashboard_workflow(self):
        """Test admin booking dashboard and analytics."""
        # Login as admin
        login_success = self.client.login(
            email="<EMAIL>", password="AdminPass123!"
        )
        self.assertTrue(login_success)

        # View admin dashboard
        dashboard_url = reverse("booking_cart_app:admin_booking_dashboard")
        response = self.client.get(dashboard_url)
        self.assertEqual(response.status_code, 200)

        # View analytics page
        analytics_url = reverse("booking_cart_app:admin_booking_analytics")
        response = self.client.get(analytics_url)
        self.assertEqual(response.status_code, 200)

    def test_admin_booking_list_workflow(self):
        """Test admin booking list with filtering capabilities."""
        # Login as admin
        self.client.login(email="<EMAIL>", password="AdminPass123!")

        # View all bookings
        booking_list_url = reverse("booking_cart_app:admin_booking_list")
        response = self.client.get(booking_list_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Admin Test Venue")

        # View booking detail
        booking_detail_url = reverse(
            "booking_cart_app:admin_booking_detail", args=[self.booking.slug]
        )
        response = self.client.get(booking_detail_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Pending booking for admin test")

    def test_admin_booking_status_management_workflow(self):
        """Test admin booking status management."""
        # Login as admin
        self.client.login(email="<EMAIL>", password="AdminPass123!")

        # Update pending booking to confirmed
        status_update_url = reverse(
            "booking_cart_app:admin_update_booking_status", args=[self.booking.slug]
        )
        update_data = {
            "status": "confirmed",
            "dispute_resolution_notes": "Manually confirmed by admin after verification",
        }
        response = self.client.post(status_update_url, update_data)
        self.assertEqual(response.status_code, 302)

        # Verify booking status was updated
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, Booking.CONFIRMED)


class CrossAppIntegrationTest(TestCase):
    """Test integration between booking_cart_app and other apps (venues_app, discount_app)."""

    def setUp(self):
        """Set up test data for cross-app integration tests."""
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role=User.CUSTOMER,
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role=User.SERVICE_PROVIDER,
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Cross-App Test Spa",
            phone="+**********",
            contact_name="Cross-App Provider",
            address="321 Integration Blvd",
            city="Miami",
            state="FL",
            zip_code="33101",
        )

        # Create test category and venue
        self.category = Category.objects.create(
            category_name="Cross-App Services",
            category_description="Services for cross-app testing",
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Cross-App Test Venue",
            short_description="Venue for cross-app testing",
            state="Florida",
            county="Miami-Dade County",
            city="Miami",
            street_number="321",
            street_name="Integration Blvd",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create test service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Cross-App Test Service",
            short_description="Service for cross-app testing",
            price_min=Decimal("150.00"),
            duration_minutes=75,
            is_active=True,
        )

        # Create service availability
        tomorrow = timezone.now().date() + timedelta(days=1)
        from datetime import time

        self.availability = ServiceAvailability.objects.create(
            service=self.service,
            available_date=tomorrow,
            start_time=time(11, 0),  # Fixed time without microseconds
            end_time=time(12, 15),  # Fixed time without microseconds
            max_bookings=2,
            current_bookings=0,
            is_available=True,
        )

    def test_venues_app_integration_workflow(self):
        """Test integration with venues_app for service discovery and booking."""
        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Step 1: Search for venues (venues_app functionality)
        search_url = reverse("venues_app:venue_search")
        response = self.client.get(search_url, {"query": "Cross-App"})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Cross-App Test Venue")

        # Step 2: View venue detail (venues_app functionality)
        venue_detail_url = reverse(
            "venues_app:venue_detail", kwargs={"venue_slug": self.venue.slug}
        )
        response = self.client.get(venue_detail_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Cross-App Test Service")

        # Step 3: Add service to cart from venue detail page
        add_to_cart_url = reverse(
            "booking_cart_app:add_to_cart", args=[self.service.id]
        )
        cart_data = {
            "selected_date": self.availability.available_date.isoformat(),
            "selected_time_slot": self.availability.start_time.strftime("%H:%M"),
            "quantity": 1,
        }
        response = self.client.post(add_to_cart_url, cart_data)
        self.assertEqual(response.status_code, 302)

        # Verify cart item was created with correct venue association
        cart = Cart.objects.get(customer=self.customer)
        cart_item = cart.items.first()
        self.assertEqual(cart_item.service.venue, self.venue)
        self.assertEqual(cart_item.venue, self.venue)


class SecurityPermissionIntegrationTest(TestCase):
    """Test security and permission controls across different user roles."""

    def setUp(self):
        """Set up test data for security and permission tests."""
        self.client = Client()

        # Create test users with different roles
        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role=User.CUSTOMER,
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role=User.SERVICE_PROVIDER,
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Security Test Spa",
            phone="+**********",
            contact_name="Test Provider",
            address="123 Provider St",
            city="Las Vegas",
            state="NV",
            zip_code="89101",
        )

        self.other_provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role=User.SERVICE_PROVIDER,
        )
        self.other_provider_profile = ServiceProviderProfile.objects.create(
            user=self.other_provider,
            legal_name="Other Provider Spa",
            phone="+**********",
            contact_name="Other Provider",
            address="888 Other St",
            city="Las Vegas",
            state="NV",
            zip_code="89102",
        )

        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="AdminPass123!",
            role=User.ADMIN,
            is_staff=True,
        )

        # Create test venue and service
        self.category = Category.objects.create(
            category_name="Security Test Services",
            category_description="Services for security testing",
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Security Test Venue",
            short_description="Venue for security testing",
            state="Nevada",
            county="Clark County",
            city="Las Vegas",
            street_number="999",
            street_name="Security Ave",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        VenueCategory.objects.create(venue=self.venue, category=self.category)

        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Security Test Service",
            short_description="Service for security testing",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True,
        )

        # Create test booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status=Booking.PENDING,
            total_price=Decimal("100.00"),
        )

    def test_customer_access_control_workflow(self):
        """Test that customers can only access their own data."""
        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Customer should be able to view their own booking
        booking_detail_url = reverse(
            "booking_cart_app:booking_detail", args=[self.booking.slug]
        )
        response = self.client.get(booking_detail_url)
        self.assertEqual(response.status_code, 200)

        # Customer should NOT be able to access provider views
        provider_booking_list_url = reverse("booking_cart_app:provider_booking_list")
        response = self.client.get(provider_booking_list_url)
        self.assertEqual(response.status_code, 302)  # Should redirect

        # Customer should NOT be able to access admin views
        admin_booking_list_url = reverse("booking_cart_app:admin_booking_list")
        response = self.client.get(admin_booking_list_url)
        self.assertEqual(response.status_code, 302)  # Should redirect

    def test_provider_access_control_workflow(self):
        """Test that providers can only access their own venue bookings."""
        # Login as the venue owner provider
        self.client.login(email="<EMAIL>", password="ProviderPass123!")

        # Provider should be able to view their venue's booking
        provider_booking_detail_url = reverse(
            "booking_cart_app:provider_booking_detail", args=[self.booking.slug]
        )
        response = self.client.get(provider_booking_detail_url)
        self.assertEqual(response.status_code, 200)

        # Provider should be able to manage their service availability
        availability_url = reverse(
            "booking_cart_app:provider_service_availability", args=[self.service.id]
        )
        response = self.client.get(availability_url)
        self.assertEqual(response.status_code, 200)

        # Login as different provider
        self.client.logout()
        self.client.login(
            email="<EMAIL>", password="ProviderPass123!"
        )

        # Other provider should NOT be able to view this booking
        response = self.client.get(provider_booking_detail_url)
        self.assertEqual(response.status_code, 404)  # Should not find booking

        # Other provider should NOT be able to manage this service
        response = self.client.get(availability_url)
        self.assertEqual(response.status_code, 404)  # Should not find service

        # Provider should NOT be able to access admin views
        admin_booking_list_url = reverse("booking_cart_app:admin_booking_list")
        response = self.client.get(admin_booking_list_url)
        self.assertEqual(response.status_code, 302)  # Should redirect

    def test_admin_access_control_workflow(self):
        """Test that admins can access all booking data."""
        # Login as admin
        self.client.login(email="<EMAIL>", password="AdminPass123!")

        # Admin should be able to view any booking
        admin_booking_detail_url = reverse(
            "booking_cart_app:admin_booking_detail", args=[self.booking.slug]
        )
        response = self.client.get(admin_booking_detail_url)
        self.assertEqual(response.status_code, 200)

        # Admin should be able to access admin dashboard
        admin_dashboard_url = reverse("booking_cart_app:admin_booking_dashboard")
        response = self.client.get(admin_dashboard_url)
        self.assertEqual(response.status_code, 200)

        # Admin should be able to update booking status
        status_update_url = reverse(
            "booking_cart_app:admin_update_booking_status", args=[self.booking.slug]
        )
        response = self.client.get(status_update_url)
        self.assertEqual(response.status_code, 200)

    def test_unauthenticated_access_control(self):
        """Test that unauthenticated users cannot access booking features."""
        # Ensure user is logged out
        self.client.logout()

        # Test various URLs that should require authentication
        protected_urls = [
            reverse("booking_cart_app:cart_view"),
            reverse("booking_cart_app:checkout"),
            reverse("booking_cart_app:booking_list"),
            reverse("booking_cart_app:booking_detail", args=[self.booking.slug]),
            reverse("booking_cart_app:provider_booking_list"),
            reverse("booking_cart_app:admin_booking_list"),
        ]

        for url in protected_urls:
            response = self.client.get(url)
            self.assertIn(
                response.status_code, [302, 403]
            )  # Should redirect to login or forbidden


class RealTimeAvailabilityIntegrationTest(TestCase):
    """Test real-time availability management across multiple users and concurrent bookings."""

    def setUp(self):
        """Set up test data for real-time availability tests."""
        self.client = Client()

        # Create multiple customers
        self.customer1 = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role=User.CUSTOMER,
        )
        CustomerProfile.objects.create(user=self.customer1)

        self.customer2 = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role=User.CUSTOMER,
        )
        CustomerProfile.objects.create(user=self.customer2)

        # Create provider
        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role=User.SERVICE_PROVIDER,
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Availability Test Spa",
            phone="+**********",
            contact_name="Availability Provider",
            address="555 Availability St",
            city="Portland",
            state="OR",
            zip_code="97201",
        )

        # Create test venue and service
        self.category = Category.objects.create(
            category_name="Availability Test Services",
            category_description="Services for availability testing",
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Availability Test Venue",
            short_description="Venue for availability testing",
            state="Oregon",
            county="Multnomah County",
            city="Portland",
            street_number="555",
            street_name="Availability St",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        VenueCategory.objects.create(venue=self.venue, category=self.category)

        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Availability Test Service",
            short_description="Service for availability testing",
            price_min=Decimal("80.00"),
            duration_minutes=60,
            is_active=True,
        )

        # Create limited availability (max 2 bookings)
        tomorrow = timezone.now().date() + timedelta(days=1)
        from datetime import time

        self.availability = ServiceAvailability.objects.create(
            service=self.service,
            available_date=tomorrow,
            start_time=time(14, 0),  # Fixed time without microseconds
            end_time=time(15, 0),  # Fixed time without microseconds
            max_bookings=2,
            current_bookings=0,
            is_available=True,
        )

    def test_concurrent_booking_availability_workflow(self):
        """Test availability management with concurrent bookings."""
        # Customer 1 adds service to cart
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        add_to_cart_url = reverse(
            "booking_cart_app:add_to_cart", args=[self.service.id]
        )
        cart_data = {
            "selected_date": self.availability.available_date.isoformat(),
            "selected_time_slot": self.availability.start_time.strftime("%H:%M"),
            "quantity": 1,
        }
        response = self.client.post(add_to_cart_url, cart_data)
        self.assertEqual(response.status_code, 302)

        # Customer 1 completes checkout
        checkout_url = reverse("booking_cart_app:checkout")
        response = self.client.post(
            checkout_url, {"notes": "First booking", "accept_terms": True}
        )
        self.assertEqual(response.status_code, 302)

        # Verify availability was updated
        self.availability.refresh_from_db()
        self.assertEqual(self.availability.current_bookings, 1)

        # Customer 2 adds same service to cart
        self.client.logout()
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        response = self.client.post(add_to_cart_url, cart_data)
        self.assertEqual(response.status_code, 302)

        # Customer 2 completes checkout
        response = self.client.post(
            checkout_url, {"notes": "Second booking", "accept_terms": True}
        )
        self.assertEqual(response.status_code, 302)

        # Verify availability is now fully booked
        self.availability.refresh_from_db()
        self.assertEqual(self.availability.current_bookings, 2)
        self.assertFalse(self.availability.available_spots > 0)

        # Try to add third booking (should fail or be handled gracefully)
        customer3 = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role=User.CUSTOMER,
        )
        CustomerProfile.objects.create(user=customer3)

        self.client.logout()
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        response = self.client.post(add_to_cart_url, cart_data)
        # Should either redirect with error message or handle gracefully
        self.assertIn(response.status_code, [200, 302])
