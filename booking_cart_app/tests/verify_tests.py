"""
Verification script for booking_cart_app unit tests.

This script verifies that all test modules can be imported and basic test structure is correct.
Run this to verify the test implementation before running the actual tests.
"""

import os
import sys


def verify_test_structure():
    """Verify that all test modules can be imported."""
    print("🔍 Verifying booking_cart_app test structure...")

    # Test imports
    test_modules = [
        "test_models",
        "test_forms",
        "test_views",
        "test_utils",
        "test_urls",
    ]

    success_count = 0
    total_count = len(test_modules)

    for module_name in test_modules:
        try:
            module = __import__(f"booking_cart_app.tests.{module_name}", fromlist=[""])
            print(f"✅ {module_name}.py - Import successful")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name}.py - Import failed: {e}")
        except Exception as e:
            print(f"⚠️  {module_name}.py - Other error: {e}")

    print(
        f"\n📊 Import Results: {success_count}/{total_count} modules imported successfully"
    )

    if success_count == total_count:
        print("🎉 All test modules imported successfully!")
        return True
    else:
        print("⚠️  Some test modules failed to import. Check the errors above.")
        return False


def verify_test_classes():
    """Verify that test classes exist and have proper structure."""
    print("\n🔍 Verifying test class structure...")

    expected_classes = {
        "test_models": [
            "CartModelTest",
            "CartItemModelTest",
            "BookingModelTest",
            "BookingItemModelTest",
            "ServiceAvailabilityModelTest",
        ],
        "test_forms": [
            "AddToCartFormTest",
            "CheckoutFormTest",
            "ServiceAvailabilityFormTest",
        ],
        "test_views": ["CustomerViewsTest", "ProviderViewsTest"],
        "test_utils": [
            "CartUtilityTest",
            "BookingUtilityTest",
            "AvailabilityUtilityTest",
        ],
        "test_urls": ["BookingCartURLsTest", "URLAccessTest"],
    }

    success_count = 0
    total_count = sum(len(classes) for classes in expected_classes.values())

    for module_name, class_names in expected_classes.items():
        try:
            module = __import__(f"booking_cart_app.tests.{module_name}", fromlist=[""])
            for class_name in class_names:
                if hasattr(module, class_name):
                    print(f"✅ {module_name}.{class_name} - Class exists")
                    success_count += 1
                else:
                    print(f"❌ {module_name}.{class_name} - Class not found")
        except ImportError:
            for class_name in class_names:
                print(f"❌ {module_name}.{class_name} - Module import failed")

    print(f"\n📊 Class Results: {success_count}/{total_count} test classes found")

    if success_count == total_count:
        print("🎉 All test classes found!")
        return True
    else:
        print("⚠️  Some test classes are missing. Check the errors above.")
        return False


def verify_test_methods():
    """Verify that test classes have test methods."""
    print("\n🔍 Verifying test methods...")

    try:
        from booking_cart_app.tests.test_models import CartModelTest

        # Get test methods (methods starting with 'test_')
        test_methods = [
            method for method in dir(CartModelTest) if method.startswith("test_")
        ]

        print(f"✅ CartModelTest has {len(test_methods)} test methods:")
        for method in test_methods[:5]:  # Show first 5 methods
            print(f"   - {method}")
        if len(test_methods) > 5:
            print(f"   ... and {len(test_methods) - 5} more")

        return len(test_methods) > 0

    except Exception as e:
        print(f"❌ Error checking test methods: {e}")
        return False


def main():
    """Main verification function."""
    print("=" * 60)
    print("🧪 BOOKING CART APP - UNIT TESTS VERIFICATION")
    print("=" * 60)

    # Run verification steps
    structure_ok = verify_test_structure()
    classes_ok = verify_test_classes()
    methods_ok = verify_test_methods()

    print("\n" + "=" * 60)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 60)

    if structure_ok and classes_ok and methods_ok:
        print("🎉 SUCCESS: All verifications passed!")
        print("✅ Test structure is properly implemented")
        print("✅ All test classes are present")
        print("✅ Test methods are available")
        print("\n💡 You can now run the tests using:")
        print("   python manage.py test booking_cart_app.tests")
        return True
    else:
        print("⚠️  ISSUES FOUND: Some verifications failed")
        print("❌ Please check the errors above and fix them")
        print("\n💡 Common issues:")
        print("   - Missing imports in test files")
        print("   - Syntax errors in test code")
        print("   - Missing dependencies")
        return False


if __name__ == "__main__":
    main()
