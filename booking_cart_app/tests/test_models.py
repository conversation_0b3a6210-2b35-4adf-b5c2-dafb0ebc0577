"""
Unit tests for booking_cart_app models.

This module contains comprehensive unit tests for all model classes in the booking_cart_app,
including Cart, CartItem, Booking, BookingItem, and ServiceAvailability.
"""

# Standard library imports
import uuid
from datetime import timedel<PERSON>
from decimal import Decimal

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError

# Django imports
from django.test import TestCase
from django.utils import timezone

# Local imports
from accounts_app.models import ServiceProviderProfile
from booking_cart_app.models import (
    Booking,
    BookingItem,
    Cart,
    CartItem,
    ServiceAvailability,
)
from venues_app.models import Category, Service, Venue, VenueCategory

User = get_user_model()


class CartModelTest(TestCase):
    """Test the Cart model functionality."""

    def setUp(self):
        """Set up test data."""
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa Business",
            phone="+**********",
            contact_name="Test Provider",
            address="123 Business St",
            city="New York",
            state="NY",
            zip_code="10001",
        )

    def test_create_cart(self):
        """Test creating a cart."""
        cart = Cart.objects.create(customer=self.customer)

        self.assertEqual(cart.customer, self.customer)
        self.assertIsNotNone(cart.created_at)
        self.assertIsNotNone(cart.expires_at)
        self.assertIsNotNone(cart.updated_at)
        self.assertFalse(cart.is_expired)

    def test_cart_string_representation(self):
        """Test cart string representation."""
        cart = Cart.objects.create(customer=self.customer)
        expected_str = f"{self.customer.email}'s Cart"
        self.assertEqual(str(cart), expected_str)

    def test_cart_auto_expiration_setting(self):
        """Test that cart automatically sets expiration time."""
        cart = Cart.objects.create(customer=self.customer)

        # Check that expires_at was automatically set to 24 hours from now
        time_diff = cart.expires_at - timezone.now()
        self.assertTrue(timedelta(hours=23) < time_diff < timedelta(hours=25))

    def test_cart_is_expired_property(self):
        """Test cart is_expired property."""
        cart = Cart.objects.create(customer=self.customer)

        # Cart should not be expired initially
        self.assertFalse(cart.is_expired)

        # Set expiration to the past
        cart.expires_at = timezone.now() - timedelta(hours=1)
        cart.save()

        # Cart should be expired now
        self.assertTrue(cart.is_expired)

    def test_cart_extend_expiration(self):
        """Test extending cart expiration."""
        cart = Cart.objects.create(customer=self.customer)
        original_expiration = cart.expires_at

        cart.extend_expiration()

        # Expiration should be extended
        self.assertGreater(cart.expires_at, original_expiration)

    def test_cart_one_to_one_relationship(self):
        """Test one-to-one relationship with customer."""
        cart1 = Cart.objects.create(customer=self.customer)

        # Try to create another cart for the same customer
        with self.assertRaises(IntegrityError):
            Cart.objects.create(customer=self.customer)


class CartItemModelTest(TestCase):
    """Test the CartItem model functionality."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa Business",
            phone="+**********",
            contact_name="Test Provider",
            address="123 Business St",
            city="New York",
            state="NY",
            zip_code="10001",
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa", category_description="Spa services", is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True,
        )

        # Create cart
        self.cart = Cart.objects.create(customer=self.customer)

    def test_create_cart_item(self):
        """Test creating a cart item."""
        cart_item = CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=2,
            price_per_item=Decimal("100.00"),
        )

        self.assertEqual(cart_item.cart, self.cart)
        self.assertEqual(cart_item.service, self.service)
        self.assertEqual(cart_item.quantity, 2)
        self.assertEqual(cart_item.price_per_item, Decimal("100.00"))
        self.assertIsNotNone(cart_item.added_at)

    def test_cart_item_string_representation(self):
        """Test cart item string representation."""
        cart_item = CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("100.00"),
        )
        expected_str = f"{self.service.service_title} - {cart_item.selected_date} {cart_item.selected_time_slot}"
        self.assertEqual(str(cart_item), expected_str)

    def test_cart_item_total_price_property(self):
        """Test cart item total_price property."""
        cart_item = CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=3,
            price_per_item=Decimal("50.00"),
        )

        expected_total = Decimal("150.00")  # 3 * 50.00
        self.assertEqual(cart_item.total_price, expected_total)

    def test_cart_item_venue_property(self):
        """Test cart item venue property."""
        cart_item = CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("100.00"),
        )

        self.assertEqual(cart_item.service.venue, self.venue)

    def test_cart_item_clean_validation_past_date(self):
        """Test cart item validation for past dates."""
        cart_item = CartItem(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() - timedelta(days=1),  # Past date
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("100.00"),
        )

        with self.assertRaises(ValidationError):
            cart_item.full_clean()

    def test_cart_item_clean_validation_inactive_service(self):
        """Test cart item validation for inactive service."""
        # Make service inactive
        self.service.is_active = False
        self.service.save()

        cart_item = CartItem(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("100.00"),
        )

        with self.assertRaises(ValidationError):
            cart_item.full_clean()

    def test_cart_item_clean_validation_service_provider_customer(self):
        """Test cart item validation for service provider as customer."""
        # Create cart for service provider
        provider_cart = Cart.objects.create(customer=self.provider)

        cart_item = CartItem(
            cart=provider_cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("100.00"),
        )

        with self.assertRaises(ValidationError):
            cart_item.full_clean()

    def test_cart_item_unique_constraint(self):
        """Test cart item unique constraint."""
        from datetime import time

        # Use fixed values to ensure they're identical
        test_date = timezone.now().date() + timedelta(days=1)
        test_time = time(10, 0)  # 10:00 AM

        # Create first cart item
        CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=test_date,
            selected_time_slot=test_time,
            quantity=1,
            price_per_item=Decimal("100.00"),
        )

        # Try to create duplicate cart item
        with self.assertRaises(IntegrityError):
            CartItem.objects.create(
                cart=self.cart,
                service=self.service,
                selected_date=test_date,
                selected_time_slot=test_time,
                quantity=2,
                price_per_item=Decimal("100.00"),
            )

    def test_cart_item_quantity_validators(self):
        """Test cart item quantity validators."""
        # Test minimum quantity
        cart_item = CartItem(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=0,  # Below minimum
            price_per_item=Decimal("100.00"),
        )

        with self.assertRaises(ValidationError):
            cart_item.full_clean()

        # Test maximum quantity
        cart_item.quantity = 11  # Above maximum
        with self.assertRaises(ValidationError):
            cart_item.full_clean()

    def test_cart_total_items_property(self):
        """Test cart total_items property."""
        # Create multiple cart items
        CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("100.00"),
        )

        CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=2),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("100.00"),
        )

        self.assertEqual(self.cart.total_items, 2)

    def test_cart_total_price_property(self):
        """Test cart total_price property."""
        # Create multiple cart items
        CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=1),
            selected_time_slot=timezone.now().time(),
            quantity=2,
            price_per_item=Decimal("50.00"),
        )

        CartItem.objects.create(
            cart=self.cart,
            service=self.service,
            selected_date=timezone.now().date() + timedelta(days=2),
            selected_time_slot=timezone.now().time(),
            quantity=1,
            price_per_item=Decimal("75.00"),
        )

        expected_total = Decimal("175.00")  # (2 * 50.00) + (1 * 75.00)
        self.assertEqual(self.cart.total_price, expected_total)


class BookingModelTest(TestCase):
    """Test the Booking model functionality."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa Business",
            phone="+**********",
            contact_name="Test Provider",
            address="123 Business St",
            city="New York",
            state="NY",
            zip_code="10001",
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa", category_description="Spa services", is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True,
        )

    def test_create_booking(self):
        """Test creating a booking."""
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("150.00"),
            notes="Please use lavender oil",
        )

        self.assertEqual(booking.customer, self.customer)
        self.assertEqual(booking.venue, self.venue)
        self.assertEqual(booking.total_price, Decimal("150.00"))
        self.assertEqual(booking.notes, "Please use lavender oil")
        self.assertEqual(booking.status, Booking.PENDING)
        self.assertIsNotNone(booking.booking_id)
        self.assertIsNotNone(booking.booking_date)
        self.assertIsNotNone(booking.last_status_change)

    def test_booking_string_representation(self):
        """Test booking string representation."""
        booking = Booking.objects.create(
            customer=self.customer, venue=self.venue, total_price=Decimal("100.00")
        )
        # The string representation uses friendly_id, slug, or booking_id in that order
        # Since we don't have items, it will use slug or booking_id
        expected_str = f"Booking {booking.friendly_id or booking.slug or booking.booking_id} - {self.customer.email}"
        self.assertEqual(str(booking), expected_str)

    def test_booking_uuid_generation(self):
        """Test that booking ID is automatically generated as UUID."""
        booking = Booking.objects.create(
            customer=self.customer, venue=self.venue, total_price=Decimal("100.00")
        )

        # Check that booking_id is a valid UUID
        self.assertIsInstance(booking.booking_id, uuid.UUID)

        # Check that each booking gets a unique ID
        booking2 = Booking.objects.create(
            customer=self.customer, venue=self.venue, total_price=Decimal("100.00")
        )
        self.assertNotEqual(booking.booking_id, booking2.booking_id)

    def test_booking_clean_validation_service_provider_customer(self):
        """Test booking validation for service provider as customer."""
        booking = Booking(
            customer=self.provider,  # Service provider as customer
            venue=self.venue,
            total_price=Decimal("100.00"),
        )

        with self.assertRaises(ValidationError):
            booking.full_clean()

    def test_booking_can_be_cancelled_property(self):
        """Test booking can_be_cancelled property."""
        booking = Booking.objects.create(
            customer=self.customer, venue=self.venue, total_price=Decimal("100.00")
        )

        # Create a booking item scheduled for tomorrow to allow cancellation
        future_date = timezone.now().date() + timedelta(days=1)
        future_time = timezone.now().time()
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("100.00"),
            quantity=1,
            scheduled_date=future_date,
            scheduled_time=future_time,
            duration_minutes=60,
        )

        # Booking should be cancellable when scheduled for tomorrow
        self.assertTrue(booking.can_be_cancelled)

        # Update the booking item to be scheduled in the near future (less than 6 hours)
        near_future = timezone.now() + timedelta(hours=2)
        booking_item = booking.items.first()
        booking_item.scheduled_date = near_future.date()
        booking_item.scheduled_time = near_future.time()
        booking_item.save()

        # Should still be cancellable within 2-hour grace period if booking was just made
        # But let's test when booking is old and service is soon
        booking.booking_date = timezone.now() - timedelta(hours=5)
        booking.save()
        self.assertFalse(booking.can_be_cancelled)

        # Test with cancelled status
        booking.status = Booking.CANCELLED
        booking.save()
        self.assertFalse(booking.can_be_cancelled)

    def test_booking_service_provider_property(self):
        """Test booking service_provider property."""
        booking = Booking.objects.create(
            customer=self.customer, venue=self.venue, total_price=Decimal("100.00")
        )

        self.assertEqual(booking.service_provider, self.venue.service_provider)

    def test_booking_cancel_booking_method(self):
        """Test booking cancel_booking method."""
        booking = Booking.objects.create(
            customer=self.customer, venue=self.venue, total_price=Decimal("100.00")
        )

        # Create a booking item scheduled for tomorrow to allow cancellation
        future_date = timezone.now().date() + timedelta(days=1)
        future_time = timezone.now().time()
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("100.00"),
            quantity=1,
            scheduled_date=future_date,
            scheduled_time=future_time,
            duration_minutes=60,
        )

        # Cancel booking with reason
        reason = "Change of plans"
        booking.cancel_booking(reason)

        self.assertEqual(booking.status, Booking.CANCELLED)
        self.assertEqual(booking.cancellation_reason, reason)

        # Try to cancel already cancelled booking
        with self.assertRaises(ValidationError):
            booking.cancel_booking("Another reason")

    def test_booking_confirm_booking_method(self):
        """Test booking confirm_booking method."""
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.PENDING,
        )

        # Confirm booking
        booking.confirm_booking()
        self.assertEqual(booking.status, Booking.CONFIRMED)

        # Try to confirm already confirmed booking
        with self.assertRaises(ValidationError):
            booking.confirm_booking()

    def test_booking_decline_booking_method(self):
        """Test booking decline_booking method."""
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.PENDING,
        )

        # Decline booking with reason
        reason = "Fully booked"
        booking.decline_booking(reason)

        self.assertEqual(booking.status, Booking.DECLINED)
        self.assertEqual(booking.cancellation_reason, reason)

        # Try to decline non-pending booking
        booking2 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.CONFIRMED,
        )

        with self.assertRaises(ValidationError):
            booking2.decline_booking("Cannot decline confirmed booking")

    def test_booking_file_dispute_method(self):
        """Test booking file_dispute method."""
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.CONFIRMED,
        )

        # File dispute
        reason = "Service not as described"
        booking.file_dispute(reason, "customer")

        self.assertEqual(booking.status, Booking.DISPUTED)
        self.assertEqual(booking.dispute_reason, reason)
        self.assertEqual(booking.dispute_filed_by, "customer")
        self.assertIsNotNone(booking.dispute_filed_at)

        # Try to file dispute on cancelled booking
        booking2 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.CANCELLED,
        )

        with self.assertRaises(ValidationError):
            booking2.file_dispute("Cannot dispute cancelled booking")

    def test_booking_resolve_dispute_method(self):
        """Test booking resolve_dispute method."""
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.DISPUTED,
            dispute_reason="Service issue",
            dispute_filed_by="customer",
            dispute_filed_at=timezone.now(),
        )

        # Resolve dispute
        resolution_notes = "Issue resolved with refund"
        booking.resolve_dispute(resolution_notes, Booking.COMPLETED)

        self.assertEqual(booking.status, Booking.COMPLETED)
        self.assertEqual(booking.dispute_resolution_notes, resolution_notes)
        self.assertIsNotNone(booking.dispute_resolved_at)

        # Try to resolve non-disputed booking
        booking2 = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.CONFIRMED,
        )

        with self.assertRaises(ValidationError):
            booking2.resolve_dispute("Cannot resolve non-disputed booking")

    def test_booking_is_disputed_property(self):
        """Test booking is_disputed property."""
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.CONFIRMED,
        )

        self.assertFalse(booking.is_disputed)

        booking.status = Booking.DISPUTED
        booking.save()
        self.assertTrue(booking.is_disputed)

    def test_booking_has_unresolved_dispute_property(self):
        """Test booking has_unresolved_dispute property."""
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status=Booking.DISPUTED,
            dispute_filed_at=timezone.now(),
        )

        # Unresolved dispute
        self.assertTrue(booking.has_unresolved_dispute)

        # Resolved dispute
        booking.dispute_resolved_at = timezone.now()
        booking.save()
        self.assertFalse(booking.has_unresolved_dispute)

    def test_booking_status_choices(self):
        """Test booking status choices."""
        expected_statuses = [
            Booking.PENDING,
            Booking.CONFIRMED,
            Booking.CANCELLED,
            Booking.DECLINED,
            Booking.COMPLETED,
            Booking.DISPUTED,
            Booking.NO_SHOW,
        ]

        for status in expected_statuses:
            booking = Booking.objects.create(
                customer=self.customer,
                venue=self.venue,
                total_price=Decimal("100.00"),
                status=status,
            )
            self.assertEqual(booking.status, status)

    def test_booking_price_validation(self):
        """Test booking price validation."""
        # Test minimum price
        booking = Booking(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("0.00"),  # Below minimum
        )

        with self.assertRaises(ValidationError):
            booking.full_clean()

    def test_booking_notes_max_length(self):
        """Test booking notes maximum length."""
        long_notes = "x" * 501  # Exceeds 500 character limit

        booking = Booking(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            notes=long_notes,
        )

        with self.assertRaises(ValidationError):
            booking.full_clean()


class BookingItemModelTest(TestCase):
    """Test the BookingItem model functionality."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa Business",
            phone="+**********",
            contact_name="Test Provider",
            address="123 Business St",
            city="New York",
            state="NY",
            zip_code="10001",
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa", category_description="Spa services", is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True,
        )

        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer, venue=self.venue, total_price=Decimal("200.00")
        )

    def test_create_booking_item(self):
        """Test creating a booking item."""
        booking_item = BookingItem.objects.create(
            booking=self.booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("100.00"),
            quantity=2,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=60,
        )

        self.assertEqual(booking_item.booking, self.booking)
        self.assertEqual(booking_item.service, self.service)
        self.assertEqual(booking_item.service_title, self.service.service_title)
        self.assertEqual(booking_item.service_price, Decimal("100.00"))
        self.assertEqual(booking_item.quantity, 2)
        self.assertEqual(booking_item.duration_minutes, 60)
        self.assertIsNotNone(booking_item.created_at)

    def test_booking_item_string_representation(self):
        """Test booking item string representation."""
        booking_item = BookingItem.objects.create(
            booking=self.booking,
            service=self.service,
            service_title="Deep Tissue Massage",
            service_price=Decimal("120.00"),
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=90,
        )
        expected_str = f"Deep Tissue Massage - {booking_item.scheduled_date} {booking_item.scheduled_time}"
        self.assertEqual(str(booking_item), expected_str)

    def test_booking_item_total_price_property(self):
        """Test booking item total_price property."""
        booking_item = BookingItem.objects.create(
            booking=self.booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("75.00"),
            quantity=3,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=60,
        )

        expected_total = Decimal("225.00")  # 3 * 75.00
        self.assertEqual(booking_item.total_price, expected_total)

    def test_booking_item_end_time_property(self):
        """Test booking item end_time property."""
        scheduled_time = (
            timezone.now().time().replace(hour=10, minute=0, second=0, microsecond=0)
        )
        booking_item = BookingItem.objects.create(
            booking=self.booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("100.00"),
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=scheduled_time,
            duration_minutes=90,
        )

        # End time should be 90 minutes after start time (11:30 AM)
        expected_end_time = (
            timezone.now().time().replace(hour=11, minute=30, second=0, microsecond=0)
        )
        self.assertEqual(booking_item.end_time, expected_end_time)

    def test_booking_item_clean_validation_past_date(self):
        """Test booking item validation for past dates."""
        booking_item = BookingItem(
            booking=self.booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("100.00"),
            quantity=1,
            scheduled_date=timezone.now().date() - timedelta(days=1),  # Past date
            scheduled_time=timezone.now().time(),
            duration_minutes=60,
        )

        with self.assertRaises(ValidationError):
            booking_item.full_clean()

    def test_booking_item_clean_validation_different_venue(self):
        """Test booking item validation for service from different venue."""
        # Create another service provider and venue
        other_provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        other_provider_profile = ServiceProviderProfile.objects.create(
            user=other_provider,
            legal_name="Other Spa Business",
            phone="+**********",
            contact_name="Other Provider",
            address="456 Other St",
            city="New York",
            state="NY",
            zip_code="10002",
        )

        other_venue = Venue.objects.create(
            service_provider=other_provider_profile,
            venue_name="Other Spa",
            short_description="Another spa.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="456",
            street_name="Other St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        VenueCategory.objects.create(venue=other_venue, category=self.category)

        other_service = Service.objects.create(
            venue=other_venue,
            service_title="Hot Stone Massage",
            short_description="Relaxing hot stone massage.",
            price_min=Decimal("150.00"),
            duration_minutes=90,
            is_active=True,
        )

        booking_item = BookingItem(
            booking=self.booking,  # Booking is for self.venue
            service=other_service,  # Service is from other_venue
            service_title=other_service.service_title,
            service_price=Decimal("150.00"),
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=90,
        )

        with self.assertRaises(ValidationError):
            booking_item.full_clean()

    def test_booking_item_quantity_validators(self):
        """Test booking item quantity validators."""
        # Test minimum quantity
        booking_item = BookingItem(
            booking=self.booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("100.00"),
            quantity=0,  # Below minimum
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=60,
        )

        with self.assertRaises(ValidationError):
            booking_item.full_clean()

        # Test maximum quantity
        booking_item.quantity = 11  # Above maximum
        with self.assertRaises(ValidationError):
            booking_item.full_clean()

    def test_booking_item_price_validation(self):
        """Test booking item price validation."""
        # Test minimum price
        booking_item = BookingItem(
            booking=self.booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("0.00"),  # Below minimum
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=60,
        )

        with self.assertRaises(ValidationError):
            booking_item.full_clean()


class ServiceAvailabilityModelTest(TestCase):
    """Test the ServiceAvailability model functionality."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa Business",
            phone="+**********",
            contact_name="Test Provider",
            address="123 Business St",
            city="New York",
            state="NY",
            zip_code="10001",
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name="Spa", category_description="Spa services", is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            short_description="A relaxing full-body massage.",
            price_min=Decimal("100.00"),
            duration_minutes=60,
            is_active=True,
        )

    def test_create_service_availability(self):
        """Test creating a service availability."""
        availability = ServiceAvailability.objects.create(
            service=self.service,
            available_date=timezone.now().date() + timedelta(days=1),
            start_time=timezone.now().time().replace(hour=9, minute=0),
            end_time=timezone.now().time().replace(hour=10, minute=0),
            max_bookings=5,
            current_bookings=2,
        )

        self.assertEqual(availability.service, self.service)
        self.assertEqual(availability.max_bookings, 5)
        self.assertEqual(availability.current_bookings, 2)
        self.assertTrue(availability.is_available)
        self.assertIsNotNone(availability.created_at)
        self.assertIsNotNone(availability.updated_at)

    def test_service_availability_string_representation(self):
        """Test service availability string representation."""
        availability = ServiceAvailability.objects.create(
            service=self.service,
            available_date=timezone.now().date() + timedelta(days=1),
            start_time=timezone.now().time().replace(hour=9, minute=0),
            end_time=timezone.now().time().replace(hour=10, minute=0),
            max_bookings=3,
        )
        expected_str = f"{self.service.service_title} - {availability.available_date} {availability.start_time}-{availability.end_time}"
        self.assertEqual(str(availability), expected_str)

    def test_service_availability_clean_validation_past_date(self):
        """Test service availability validation for past dates."""
        availability = ServiceAvailability(
            service=self.service,
            available_date=timezone.now().date() - timedelta(days=1),  # Past date
            start_time=timezone.now().time().replace(hour=9, minute=0),
            end_time=timezone.now().time().replace(hour=10, minute=0),
            max_bookings=3,
        )

        with self.assertRaises(ValidationError):
            availability.full_clean()

    def test_service_availability_clean_validation_end_before_start(self):
        """Test service availability validation for end time before start time."""
        availability = ServiceAvailability(
            service=self.service,
            available_date=timezone.now().date() + timedelta(days=1),
            start_time=timezone.now().time().replace(hour=10, minute=0),
            end_time=timezone.now()
            .time()
            .replace(hour=9, minute=0),  # End before start
            max_bookings=3,
        )

        with self.assertRaises(ValidationError):
            availability.full_clean()

    def test_service_availability_unique_constraint(self):
        """Test service availability unique constraint."""
        from datetime import time

        # Use fixed values to ensure they're identical
        test_date = timezone.now().date() + timedelta(days=1)
        test_start_time = time(9, 0)  # 9:00 AM
        test_end_time = time(10, 0)  # 10:00 AM

        # Create first availability
        ServiceAvailability.objects.create(
            service=self.service,
            available_date=test_date,
            start_time=test_start_time,
            end_time=test_end_time,
            max_bookings=3,
        )

        # Try to create duplicate availability
        with self.assertRaises(IntegrityError):
            ServiceAvailability.objects.create(
                service=self.service,
                available_date=test_date,
                start_time=test_start_time,  # Same date and start time
                end_time=time(11, 0),
                max_bookings=5,
            )

    def test_service_availability_max_bookings_validators(self):
        """Test service availability max_bookings validators."""
        # Test minimum max_bookings
        availability = ServiceAvailability(
            service=self.service,
            available_date=timezone.now().date() + timedelta(days=1),
            start_time=timezone.now().time().replace(hour=9, minute=0),
            end_time=timezone.now().time().replace(hour=10, minute=0),
            max_bookings=0,  # Below minimum
        )

        with self.assertRaises(ValidationError):
            availability.full_clean()

        # Test maximum max_bookings
        availability.max_bookings = 21  # Above maximum
        with self.assertRaises(ValidationError):
            availability.full_clean()

    def test_service_availability_properties(self):
        """Test service availability computed properties."""
        availability = ServiceAvailability.objects.create(
            service=self.service,
            available_date=timezone.now().date() + timedelta(days=1),
            start_time=timezone.now().time().replace(hour=9, minute=0),
            end_time=timezone.now().time().replace(hour=10, minute=0),
            max_bookings=5,
            current_bookings=2,
        )

        # Test available_spots property
        self.assertEqual(availability.available_spots, 3)  # 5 - 2

        # Test is_fully_booked property
        self.assertFalse(availability.is_fully_booked)

        # Make it fully booked
        availability.current_bookings = 5
        availability.save()
        self.assertTrue(availability.is_fully_booked)

        # Reset for next test
        availability.current_bookings = 2
        availability.save()
        self.assertFalse(availability.is_fully_booked)

    def test_service_availability_booking_methods(self):
        """Test service availability booking-related methods."""
        availability = ServiceAvailability.objects.create(
            service=self.service,
            available_date=timezone.now().date() + timedelta(days=1),
            start_time=timezone.now().time().replace(hour=9, minute=0),
            end_time=timezone.now().time().replace(hour=10, minute=0),
            max_bookings=3,
            current_bookings=1,
        )

        # Test book_slot method
        availability.book_slot()
        self.assertEqual(availability.current_bookings, 2)

        # Try to book when fully booked
        availability.current_bookings = 3
        availability.save()
        with self.assertRaises(ValidationError):
            availability.book_slot()

        # Test cancel_booking method
        availability.current_bookings = 2
        availability.save()
        availability.cancel_booking()
        self.assertEqual(availability.current_bookings, 1)

        # Try to cancel when no bookings
        availability.current_bookings = 0
        availability.save()
        with self.assertRaises(ValidationError):
            availability.cancel_booking()
