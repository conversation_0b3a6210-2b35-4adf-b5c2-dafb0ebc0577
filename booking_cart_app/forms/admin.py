# Django imports
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Local imports
from ..models import Booking


class AdminBookingStatusForm(forms.Form):
    """Form for admin to update booking status."""

    status = forms.ChoiceField(
        label=_("Booking Status"),
        widget=forms.Select(attrs={"class": "form-control"}),
        help_text=_("Select the new status for this booking"),
    )
    admin_notes = forms.CharField(
        label=_("Admin Notes"),
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 3,
                "placeholder": "Add notes about this status change (optional)",
                "maxlength": 500,
            }
        ),
        required=False,
        help_text=_("Maximum 500 characters"),
    )

    def __init__(self, booking=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if booking:
            current_status = booking.status
            all_choices = Booking.STATUS_CHOICES
            available_choices = [
                choice for choice in all_choices if choice[0] != current_status
            ]
            self.fields["status"].choices = available_choices
        else:
            self.fields["status"].choices = Booking.STATUS_CHOICES


class DisputeResolutionForm(forms.Form):
    """Form for admin to resolve booking disputes."""

    resolution_status = forms.ChoiceField(
        label=_("Resolution Status"),
        choices=[
            ("confirmed", _("Confirm Booking")),
            ("cancelled", _("Cancel Booking")),
            ("completed", _("Mark as Completed")),
            ("no_show", _("Mark as No Show")),
        ],
        widget=forms.Select(attrs={"class": "form-control"}),
        help_text=_("Select the final status after resolving the dispute"),
    )
    resolution_notes = forms.CharField(
        label=_("Resolution Notes"),
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": "Explain how the dispute was resolved and any actions taken...",
                "maxlength": 1000,
            }
        ),
        required=True,
        help_text=_("Required. Explain the resolution decision (max 1000 characters)"),
    )
    refund_amount = forms.DecimalField(
        label=_("Refund Amount (Optional)"),
        widget=forms.NumberInput(
            attrs={
                "class": "form-control",
                "min": "0.00",
                "step": "0.01",
                "placeholder": "0.00",
            }
        ),
        required=False,
        max_digits=10,
        decimal_places=2,
        help_text=_("Enter refund amount if applicable"),
    )
    notify_parties = forms.BooleanField(
        label=_("Notify All Parties"),
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
        required=False,
        initial=True,
        help_text=_(
            "Send notification to customer and service provider about the resolution"
        ),
    )

    def clean_resolution_notes(self):
        notes = self.cleaned_data.get("resolution_notes", "")
        if not notes.strip():
            raise ValidationError(_("Resolution notes are required."))
        return notes.strip()


class BookingFilterForm(forms.Form):
    """Form for filtering bookings in admin views."""

    status = forms.ChoiceField(
        label=_("Status"),
        choices=[("", "All Statuses")],
        widget=forms.Select(attrs={"class": "form-control"}),
        required=False,
    )
    venue = forms.ModelChoiceField(
        label=_("Venue"),
        queryset=None,
        widget=forms.Select(attrs={"class": "form-control"}),
        required=False,
        empty_label="All Venues",
    )
    date_from = forms.DateField(
        label=_("From Date"),
        widget=forms.DateInput(attrs={"class": "form-control", "type": "date"}),
        required=False,
    )
    date_to = forms.DateField(
        label=_("To Date"),
        widget=forms.DateInput(attrs={"class": "form-control", "type": "date"}),
        required=False,
    )
    search = forms.CharField(
        label=_("Search"),
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": "Search by booking ID, customer email, or venue name...",
            }
        ),
        required=False,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from venues_app.models import Venue

        from ..models import Booking

        status_choices = [("", "All Statuses")] + list(Booking.STATUS_CHOICES)
        self.fields["status"].choices = status_choices
        self.fields["venue"].queryset = Venue.objects.filter(
            visibility=Venue.ACTIVE
        ).order_by("venue_name")

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get("date_from")
        date_to = cleaned_data.get("date_to")
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_("From date must be before to date."))
        return cleaned_data
