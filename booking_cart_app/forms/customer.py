# Django imports
from datetime import timedelta

from django import forms
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Local imports
from ..models import Booking, CartItem


class AddToCartForm(forms.ModelForm):
    """Form for adding a service to the customer's cart with enhanced validation."""

    class Meta:
        model = CartItem
        fields = ["selected_date", "selected_time_slot", "quantity"]
        widgets = {
            "selected_date": forms.DateInput(
                attrs={
                    "class": "form-control",
                    "type": "date",
                    "min": timezone.now().date().isoformat(),
                    "max": (timezone.now().date() + timedelta(days=30)).isoformat(),
                    "id": "id_selected_date",
                    "data-validation": "required",
                    "data-validation-message": "Please select a date for your appointment",
                }
            ),
            "selected_time_slot": forms.Select(
                attrs={
                    "class": "form-control",
                    "id": "id_selected_time_slot",
                    "data-validation": "required",
                    "data-validation-message": "Please select a time slot",
                }
            ),
            "quantity": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "min": 1,
                    "max": 10,
                    "value": 1,
                    "data-validation": "required|range[1,10]",
                    "data-validation-message": "Please enter a quantity between 1 and 10",
                }
            ),
        }
        labels = {
            "selected_date": _("Select Date"),
            "selected_time_slot": _("Select Time Slot"),
            "quantity": _("Number of Appointments"),
        }
        help_texts = {
            "selected_date": _("Choose a date within the next 30 days"),
            "selected_time_slot": _(
                "Available time slots will appear after selecting a date"
            ),
            "quantity": _("Maximum 10 appointments per booking"),
        }

    def __init__(self, service=None, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = service
        self.user = user

        self.fields["selected_time_slot"].choices = [("", "Select a date first")]

        today = timezone.now().date()
        max_date = today + timedelta(days=30)
        self.fields["selected_date"].widget.attrs.update(
            {"min": today.isoformat(), "max": max_date.isoformat()}
        )

        # Add enhanced error messages
        self.fields["selected_date"].error_messages = {
            "required": _("Please select a date for your appointment."),
            "invalid": _("Please enter a valid date."),
        }

        self.fields["selected_time_slot"].error_messages = {
            "required": _("Please select a time slot for your appointment."),
            "invalid_choice": _("Please select a valid time slot."),
        }

        self.fields["quantity"].error_messages = {
            "required": _("Please specify the number of appointments."),
            "invalid": _("Please enter a valid number."),
            "min_value": _("Minimum 1 appointment required."),
            "max_value": _("Maximum 10 appointments allowed."),
        }

    def clean_selected_date(self):
        selected_date = self.cleaned_data.get("selected_date")

        if not selected_date:
            raise ValidationError(_("Please select a date for your appointment."))

        # Check if date is not in the past
        today = timezone.now().date()
        if selected_date <= today:
            raise ValidationError(
                _("Please select a future date. Today or past dates are not allowed.")
            )

        # Check if date is not too far in the future
        max_date = today + timedelta(days=30)
        if selected_date > max_date:
            raise ValidationError(_("Please select a date within the next 30 days."))

        # Check if it's not a weekend (optional business rule)
        if (
            self.service
            and hasattr(self.service, "no_weekends")
            and self.service.no_weekends
        ):
            if selected_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
                raise ValidationError(
                    _("Weekend appointments are not available for this service.")
                )

        return selected_date

    def clean_selected_time_slot(self):
        selected_time_slot = self.cleaned_data.get("selected_time_slot")

        if not selected_time_slot:
            raise ValidationError(_("Please select a time slot for your appointment."))

        return selected_time_slot

    def clean_quantity(self):
        quantity = self.cleaned_data.get("quantity")

        if not quantity:
            raise ValidationError(_("Please specify the number of appointments."))

        if quantity < 1:
            raise ValidationError(_("Minimum 1 appointment required."))

        if quantity > 10:
            raise ValidationError(_("Maximum 10 appointments allowed per booking."))

        return quantity

    def clean(self):
        cleaned_data = super().clean()
        selected_date = cleaned_data.get("selected_date")
        selected_time_slot = cleaned_data.get("selected_time_slot")
        quantity = cleaned_data.get("quantity")

        if not all([selected_date, selected_time_slot, quantity]):
            return cleaned_data

        # Additional validation for user permissions
        if (
            self.user
            and hasattr(self.user, "is_service_provider")
            and self.user.is_service_provider
        ):
            raise ValidationError(_("Service providers cannot add items to cart."))

        # Validate availability if service is provided
        if self.service:
            from ..utils import check_service_availability

            try:
                is_available, message = check_service_availability(
                    self.service, selected_date, selected_time_slot, quantity
                )
                if not is_available:
                    raise ValidationError(message)
            except Exception as e:
                raise ValidationError(
                    _("Unable to verify availability. Please try again.")
                )

        return cleaned_data

    def save(self, cart=None, commit=True):
        """Save the cart item with the provided cart and service."""
        cart_item = super().save(commit=False)

        if cart:
            cart_item.cart = cart
        if self.service:
            cart_item.service = self.service
            cart_item.price_per_item = self.service.price_min  # Use the minimum price

        if commit:
            cart_item.save()

        return cart_item


class UpdateCartItemForm(forms.ModelForm):
    """Form for updating the quantity of a cart item with enhanced validation."""

    class Meta:
        model = CartItem
        fields = ["quantity"]
        widgets = {
            "quantity": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "min": 1,
                    "max": 10,
                    "data-validation": "required|range[1,10]",
                    "data-validation-message": "Please enter a quantity between 1 and 10",
                }
            )
        }
        labels = {"quantity": _("Number of Appointments")}
        help_texts = {"quantity": _("Maximum 10 appointments per booking")}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields["quantity"].error_messages = {
            "required": _("Please specify the number of appointments."),
            "invalid": _("Please enter a valid number."),
            "min_value": _("Minimum 1 appointment required."),
            "max_value": _("Maximum 10 appointments allowed."),
        }

    def clean_quantity(self):
        quantity = self.cleaned_data.get("quantity")

        if not quantity:
            raise ValidationError(_("Please specify the number of appointments."))

        if quantity < 1:
            raise ValidationError(_("Minimum 1 appointment required."))

        if quantity > 10:
            raise ValidationError(_("Maximum 10 appointments allowed per booking."))

        # Check availability for the new quantity
        if self.instance and self.instance.service:
            from ..utils import check_service_availability

            try:
                is_available, message = check_service_availability(
                    self.instance.service,
                    self.instance.selected_date,
                    self.instance.selected_time_slot,
                    quantity,
                )
                if not is_available:
                    raise ValidationError(message)
            except Exception:
                raise ValidationError(
                    _("Unable to verify availability for this quantity.")
                )

        return quantity


class CheckoutForm(forms.ModelForm):
    """Form used during checkout with enhanced validation and user experience."""

    # Add terms acceptance field
    accept_terms = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                "class": "form-check-input",
                "data-validation": "required",
                "data-validation-message": "Please accept the terms and conditions",
            }
        ),
        label=_("I agree to the Terms and Conditions and Cancellation Policy"),
        error_messages={
            "required": _("You must accept the terms and conditions to proceed."),
        },
    )

    # Optional marketing emails
    marketing_emails = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
        label=_(
            "I would like to receive email updates about new services and special offers"
        ),
    )

    class Meta:
        model = Booking
        fields = ["notes"]
        widgets = {
            "notes": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 4,
                    "placeholder": "Any special requests or notes for the service provider? (Optional)",
                    "maxlength": 500,
                    "data-character-counter": "true",
                    "data-validation": "length[0,500]",
                    "data-validation-message": "Notes cannot exceed 500 characters",
                }
            )
        }
        labels = {"notes": _("Special Requests (Optional)")}
        help_texts = {"notes": _("Maximum 500 characters")}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields["notes"].error_messages = {
            "max_length": _("Notes cannot exceed 500 characters."),
        }

    def clean_notes(self):
        notes = self.cleaned_data.get("notes", "")

        if len(notes) > 500:
            raise ValidationError(_("Notes cannot exceed 500 characters."))

        # Clean up whitespace
        notes = notes.strip()

        # Optional: Check for inappropriate content (placeholder)
        prohibited_words = ["spam", "test", "fake"]  # Add actual prohibited words
        for word in prohibited_words:
            if word.lower() in notes.lower():
                raise ValidationError(_("Please provide appropriate booking notes."))

        return notes

    def clean_accept_terms(self):
        accept_terms = self.cleaned_data.get("accept_terms")

        if not accept_terms:
            raise ValidationError(
                _("You must accept the terms and conditions to proceed.")
            )

        return accept_terms


class BookingCancellationForm(forms.Form):
    """Form for cancelling a booking with enhanced validation."""

    CANCELLATION_REASONS = [
        ("", _("Select a reason (optional)")),
        ("schedule_conflict", _("Schedule conflict")),
        ("emergency", _("Emergency")),
        ("service_issue", _("Issue with service")),
        ("venue_issue", _("Issue with venue")),
        ("weather", _("Weather conditions")),
        ("health", _("Health reasons")),
        ("other", _("Other reason")),
    ]

    cancellation_reason_category = forms.ChoiceField(
        choices=CANCELLATION_REASONS,
        required=False,
        widget=forms.Select(
            attrs={
                "class": "form-control mb-3",
                "data-validation-message": "Please select a cancellation reason",
            }
        ),
        label=_("Reason for Cancellation"),
    )

    cancellation_reason = forms.CharField(
        label=_("Additional Details"),
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 3,
                "placeholder": "Please provide additional details about your cancellation (optional)",
                "maxlength": 500,
                "data-character-counter": "true",
                "data-validation": "length[0,500]",
                "data-validation-message": "Cancellation reason cannot exceed 500 characters",
            }
        ),
        required=False,
        help_text=_("Maximum 500 characters"),
    )

    # Confirmation checkbox
    confirm_cancellation = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                "class": "form-check-input",
                "data-validation": "required",
                "data-validation-message": "Please confirm that you want to cancel this booking",
            }
        ),
        label=_(
            "I understand that this action cannot be undone and I want to cancel this booking"
        ),
        error_messages={
            "required": _("Please confirm that you want to cancel this booking."),
        },
    )

    def clean_cancellation_reason(self):
        reason = self.cleaned_data.get("cancellation_reason", "")

        if len(reason) > 500:
            raise ValidationError(
                _("Cancellation reason cannot exceed 500 characters.")
            )

        return reason.strip()

    def clean_confirm_cancellation(self):
        confirm = self.cleaned_data.get("confirm_cancellation")

        if not confirm:
            raise ValidationError(
                _("Please confirm that you want to cancel this booking.")
            )

        return confirm


class BookingRescheduleForm(forms.Form):
    """Form for rescheduling a booking to a new date and time."""

    new_date = forms.DateField(
        widget=forms.DateInput(
            attrs={
                "class": "form-control",
                "type": "date",
                "min": timezone.now().date().isoformat(),
                "max": (timezone.now().date() + timedelta(days=30)).isoformat(),
                "data-validation": "required",
                "data-validation-message": "Please select a new date",
            }
        ),
        label=_("New Date"),
        help_text=_("Select a new date for your appointment"),
    )

    new_time_slot = forms.ChoiceField(
        choices=[("", "Select new date first")],
        widget=forms.Select(
            attrs={
                "class": "form-control",
                "data-validation": "required",
                "data-validation-message": "Please select a new time slot",
            }
        ),
        label=_("New Time Slot"),
        help_text=_("Available time slots will appear after selecting a date"),
    )

    reschedule_reason = forms.CharField(
        required=False,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 3,
                "placeholder": "Reason for rescheduling (optional)",
                "maxlength": 300,
                "data-character-counter": "true",
            }
        ),
        label=_("Reason for Rescheduling"),
        help_text=_("Optional - Maximum 300 characters"),
    )

    def __init__(self, booking=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.booking = booking

    def clean_new_date(self):
        new_date = self.cleaned_data.get("new_date")

        if not new_date:
            raise ValidationError(_("Please select a new date."))

        if new_date <= timezone.now().date():
            raise ValidationError(_("Please select a future date."))

        # Check if it's the same as the current booking date
        if self.booking and new_date == self.booking.items.first().scheduled_date:
            raise ValidationError(
                _("Please select a different date from your current booking.")
            )

        return new_date

    def clean(self):
        cleaned_data = super().clean()
        new_date = cleaned_data.get("new_date")
        new_time_slot = cleaned_data.get("new_time_slot")

        if new_date and new_time_slot and self.booking:
            # Check if the new time is the same as current booking
            current_item = self.booking.items.first()
            if new_date == current_item.scheduled_date and new_time_slot == str(
                current_item.scheduled_time
            ):
                raise ValidationError(
                    _(
                        "Please select a different date and time from your current booking."
                    )
                )

        return cleaned_data
