# CozyWish Project Configuration
# Configuration for Black, isort, and other Python tools

[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cozywish"
version = "1.0.0"
description = "CozyWish - Book Beauty & Wellness Services"
authors = [
    {name = "CozyWish Team", email = "<EMAIL>"},
]
requires-python = ">=3.10"
dependencies = [
    "Django>=5.2.3",
]

[tool.black]
line-length = 88
target-version = ['py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["*/migrations/*"]
known_django = "django"
known_first_party = [
    "accounts_app",
    "admin_app",
    "booking_cart_app",
    "dashboard_app",
    "discount_app",
    "notifications_app",
    "payments_app",
    "project_root",
    "review_app",
    "utility_app",
    "utils",
    "venues_app",
]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "DJANGO", "FIRSTPARTY", "LOCALFOLDER"]

[tool.coverage.run]
source = "."
omit = [
    "*/migrations/*",
    "*/venv/*",
    "*/ENV_*/*",
    "manage.py",
    "*/settings/*",
    "*/tests/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
]
