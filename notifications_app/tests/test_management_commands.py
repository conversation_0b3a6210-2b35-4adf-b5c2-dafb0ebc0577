from io import String<PERSON>

from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.test import TestCase

from notifications_app.models import AdminAnnouncement, Notification

User = get_user_model()


class TestNotificationManagementCommands(TestCase):
    def test_test_notifications_command(self):
        out = StringIO()
        call_command("test_notifications", stdout=out)
        output = out.getvalue()
        self.assertIn("All notification tests completed successfully", output)
        self.assertTrue(Notification.objects.exists())
        self.assertTrue(AdminAnnouncement.objects.exists())
        # cleanup run
        call_command("test_notifications", "--cleanup", stdout=out)
