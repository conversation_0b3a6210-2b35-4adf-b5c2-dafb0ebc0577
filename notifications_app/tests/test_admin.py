"""
Unit tests for notifications_app Django admin interface.

This module contains comprehensive unit tests for the Django admin interface
for all models in the notifications_app.
"""

from django.contrib import admin
from django.contrib.admin.sites import AdminSite
from django.contrib.auth import get_user_model

# Django imports
from django.test import Client, TestCase
from django.urls import reverse
from django.utils import timezone

from notifications_app.admin import (
    AdminAnnouncementAdmin,
    NotificationAdmin,
    NotificationPreferenceAdmin,
)

# Local imports
from notifications_app.models import (
    AdminAnnouncement,
    Notification,
    NotificationPreference,
)

User = get_user_model()


class MockRequest:
    """Mock request for admin testing."""

    def __init__(self, user=None):
        self.user = user


class AdminAnnouncementAdminTest(TestCase):
    """Test the Django admin for AdminAnnouncement model."""

    def setUp(self):
        """Set up test data."""
        self.site = AdminSite()
        self.admin = AdminAnnouncementAdmin(AdminAnnouncement, self.site)

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            is_staff=True,
            is_superuser=True,
        )

        self.announcement = AdminAnnouncement.objects.create(
            title="Test Announcement",
            announcement_text="This is a test announcement",
            target_audience=AdminAnnouncement.ALL_USERS,
            created_by=self.admin_user,
        )

    def test_list_display(self):
        """Test the list display configuration."""
        expected_fields = (
            "title",
            "slug",
            "target_audience",
            "status",
            "created_by",
            "created_at",
            "sent_at",
            "total_recipients",
            "get_status_display_colored",
        )
        self.assertEqual(self.admin.list_display, expected_fields)

    def test_search_fields(self):
        """Test the search fields configuration."""
        expected_fields = ("title", "slug", "announcement_text", "created_by__email")
        self.assertEqual(self.admin.search_fields, expected_fields)

    def test_admin_str_representation(self):
        """Test the string representation in admin."""
        expected_str = f"{self.announcement.title} - {self.announcement.get_target_audience_display()}"
        self.assertEqual(str(self.announcement), expected_str)

    def test_admin_changelist_view(self):
        """Test the admin changelist view."""
        client = Client()
        client.login(email="<EMAIL>", password="testpass123")

        url = reverse("admin:notifications_app_adminannouncement_changelist")
        response = client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Announcement")

    def test_admin_change_view(self):
        """Test the admin change view."""
        client = Client()
        client.login(email="<EMAIL>", password="testpass123")

        url = reverse(
            "admin:notifications_app_adminannouncement_change",
            args=[self.announcement.id],
        )
        response = client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Announcement")

    def test_admin_add_view(self):
        """Test the admin add view."""
        client = Client()
        client.login(email="<EMAIL>", password="testpass123")

        url = reverse("admin:notifications_app_adminannouncement_add")
        response = client.get(url)

        self.assertEqual(response.status_code, 200)


class NotificationAdminTest(TestCase):
    """Test the Django admin for Notification model."""

    def setUp(self):
        """Set up test data."""
        self.site = AdminSite()
        self.admin = NotificationAdmin(Notification, self.site)

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            is_staff=True,
            is_superuser=True,
        )

        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        self.notification = Notification.objects.create(
            user=self.user,
            notification_type=Notification.BOOKING,
            title="Test Notification",
            message="Test message",
        )

    def test_list_display(self):
        """Test the list display configuration."""
        expected_fields = (
            "user",
            "title",
            "notification_type",
            "read_status",
            "created_at",
            "is_recent_notification",
            "get_message_preview",
        )
        self.assertEqual(self.admin.list_display, expected_fields)

    def test_list_filter(self):
        """Test the list filter configuration."""
        expected_filters = (
            "notification_type",
            "read_status",
            "created_at",
            ("created_at", admin.DateFieldListFilter),
        )
        self.assertEqual(self.admin.list_filter, expected_filters)

    def test_search_fields(self):
        """Test the search fields configuration."""
        expected_fields = (
            "user__email",
            "title",
            "message",
            "user__first_name",
            "user__last_name",
        )
        self.assertEqual(self.admin.search_fields, expected_fields)

    def test_date_hierarchy(self):
        """Test the date hierarchy configuration."""
        self.assertEqual(self.admin.date_hierarchy, "created_at")

    def test_readonly_fields(self):
        """Test the readonly fields configuration."""
        expected_fields = ("created_at", "read_at")
        self.assertEqual(self.admin.readonly_fields, expected_fields)

    def test_admin_str_representation(self):
        """Test the string representation in admin."""
        expected_str = f"{self.user.email} - {self.notification.title}"
        self.assertEqual(str(self.notification), expected_str)

    def test_admin_changelist_view(self):
        """Test the admin changelist view."""
        client = Client()
        client.login(email="<EMAIL>", password="testpass123")

        url = reverse("admin:notifications_app_notification_changelist")
        response = client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Notification")

    def test_admin_change_view(self):
        """Test the admin change view."""
        client = Client()
        client.login(email="<EMAIL>", password="testpass123")

        url = reverse(
            "admin:notifications_app_notification_change", args=[self.notification.id]
        )
        response = client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Notification")

    def test_admin_filtering(self):
        """Test admin filtering functionality."""
        client = Client()
        client.login(email="<EMAIL>", password="testpass123")

        # Test filtering by notification type
        url = reverse("admin:notifications_app_notification_changelist")
        response = client.get(url, {"notification_type__exact": Notification.BOOKING})

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Notification")

    def test_admin_search(self):
        """Test admin search functionality."""
        client = Client()
        client.login(email="<EMAIL>", password="testpass123")

        url = reverse("admin:notifications_app_notification_changelist")
        response = client.get(url, {"q": "Test"})

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Notification")


class NotificationPreferenceAdminTest(TestCase):
    """Test the Django admin for NotificationPreference model."""

    def setUp(self):
        """Set up test data."""
        self.site = AdminSite()
        self.admin = NotificationPreferenceAdmin(NotificationPreference, self.site)

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            is_staff=True,
            is_superuser=True,
        )

        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        self.preference = NotificationPreference.objects.create(
            user=self.user,
            notification_type=Notification.BOOKING,
            channel=NotificationPreference.EMAIL,
            is_enabled=True,
        )

    def test_list_display(self):
        """Test the list display configuration."""
        expected_fields = ("user", "notification_type", "channel", "is_enabled")
        self.assertEqual(self.admin.list_display, expected_fields)

    def test_list_filter(self):
        """Test the list filter configuration."""
        expected_filters = ("notification_type", "channel", "is_enabled")
        self.assertEqual(self.admin.list_filter, expected_filters)

    def test_search_fields(self):
        """Test the search fields configuration."""
        expected_fields = ("user__email",)
        self.assertEqual(self.admin.search_fields, expected_fields)

    def test_admin_str_representation(self):
        """Test the string representation in admin."""
        expected_str = f"{self.user.email} - {self.preference.notification_type} - {self.preference.channel}"
        self.assertEqual(str(self.preference), expected_str)

    def test_admin_changelist_view(self):
        """Test the admin changelist view."""
        client = Client()
        client.login(email="<EMAIL>", password="testpass123")

        url = reverse("admin:notifications_app_notificationpreference_changelist")
        response = client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.email)

    def test_admin_filtering_by_channel(self):
        """Test admin filtering by channel."""
        client = Client()
        client.login(email="<EMAIL>", password="testpass123")

        url = reverse("admin:notifications_app_notificationpreference_changelist")
        response = client.get(url, {"channel__exact": NotificationPreference.EMAIL})

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.email)
