"""
Unit tests for notifications_app views.

This module contains comprehensive unit tests for all view classes and functions in the notifications_app,
including customer, provider, and admin notification views.
"""

from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.http import JsonResponse

# Django imports
from django.test import Client, TestCase
from django.urls import reverse
from django.utils import timezone

from accounts_app.models import CustomUser, ServiceProviderProfile

# Local imports
from notifications_app.models import (
    AdminAnnouncement,
    Notification,
    NotificationPreference,
)
from notifications_app.utils import create_notification

User = get_user_model()


class CustomerNotificationViewsTest(TestCase):
    """Test customer notification views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=CustomUser.CUSTOMER,
        )

    def test_notification_list_view_authenticated(self):
        """Test notification list view for authenticated customer."""
        self.client.login(email="<EMAIL>", password="testpass123")

        # Create some notifications
        create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Test Notification 1",
            message="Test message 1",
            related_object_id=1,
            related_object_type="Booking",
        )

        create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Test Notification 2",
            message="Test message 2",
            related_object_id=2,
            related_object_type="Booking",
        )

        response = self.client.get(reverse("notifications_app:notification_list"))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Notification 1")
        self.assertContains(response, "Test Notification 2")
        self.assertIn("notifications", response.context)

    def test_notification_list_view_unauthenticated(self):
        """Test notification list view for unauthenticated user."""
        response = self.client.get(reverse("notifications_app:notification_list"))

        # Should redirect to login
        self.assertEqual(response.status_code, 302)
        self.assertIn("/accounts/", response.url)

    def test_notification_detail_view(self):
        """Test notification detail view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        notification = create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Test Notification",
            message="Test message",
        )

        response = self.client.get(
            reverse("notifications_app:notification_detail", args=[notification.id])
        )

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Notification")
        self.assertContains(response, "Test message")

        # Check that notification is marked as read
        notification.refresh_from_db()
        self.assertTrue(notification.is_read)
        self.assertIsNotNone(notification.read_at)

    def test_notification_detail_view_wrong_user(self):
        """Test notification detail view with wrong user."""
        # Create another user
        other_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=CustomUser.CUSTOMER
        )

        self.client.login(email="<EMAIL>", password="testpass123")

        notification = create_notification(
            user=other_user,
            notification_type=Notification.BOOKING,
            title="Test Notification",
            message="Test message",
        )

        response = self.client.get(
            reverse("notifications_app:notification_detail", args=[notification.id])
        )

        # Should redirect to notification list for security (don't reveal if notification exists)
        self.assertEqual(response.status_code, 302)
        self.assertIn("notifications", response.url)

    def test_mark_as_read_view(self):
        """Test mark as read view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        notification = create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Test Notification",
            message="Test message",
        )

        response = self.client.post(
            reverse("notifications_app:mark_notification_read", args=[notification.id])
        )

        self.assertEqual(response.status_code, 302)  # Redirect after marking as read

        # Check that notification is marked as read
        notification.refresh_from_db()
        self.assertTrue(notification.is_read)
        self.assertIsNotNone(notification.read_at)

    def test_mark_all_as_read_view(self):
        """Test mark all as read view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        # Create multiple notifications
        create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Test Notification 1",
            message="Test message 1",
            related_object_id=3,
            related_object_type="Booking",
        )

        create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Test Notification 2",
            message="Test message 2",
            related_object_id=4,
            related_object_type="Booking",
        )

        response = self.client.post(
            reverse("notifications_app:mark_all_notifications_read")
        )

        self.assertEqual(
            response.status_code, 302
        )  # Redirect after marking all as read

        # Check that all notifications are marked as read
        notifications = Notification.objects.filter(user=self.customer)
        for notification in notifications:
            self.assertTrue(notification.is_read)
            self.assertIsNotNone(notification.read_at)

    # TODO: Fix notification preferences template syntax error
    # def test_notification_preferences_view_get(self):
    #     """Test notification preferences view GET request."""
    #     self.client.login(email='<EMAIL>', password='testpass123')
    #
    #     response = self.client.get(reverse('notifications_app:notification_preferences'))
    #
    #     self.assertEqual(response.status_code, 200)
    #     self.assertIn('preferences', response.context)

    # def test_notification_preferences_view_post(self):
    #     """Test notification preferences view POST request."""
    #     self.client.login(email='<EMAIL>', password='testpass123')
    #
    #     form_data = {
    #         f'preference_{Notification.BOOKING}_email': 'on',
    #         f'preference_{Notification.BOOKING}_dashboard': 'on',
    #     }
    #
    #     response = self.client.post(
    #         reverse('notifications_app:notification_preferences'),
    #         data=form_data
    #     )
    #
    #     self.assertEqual(response.status_code, 302)  # Redirect after successful save
    #
    #     # Check that preferences were created
    #     email_preference = NotificationPreference.objects.get(
    #         user=self.customer,
    #         notification_type=Notification.BOOKING,
    #         channel='email'
    #     )
    #     self.assertTrue(email_preference.is_enabled)

    def test_unread_count_api_view(self):
        """Test unread count API view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        # Create some notifications
        create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Test Notification 1",
            message="Test message 1",
            related_object_id=5,
            related_object_type="Booking",
        )

        create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Test Notification 2",
            message="Test message 2",
            related_object_id=6,
            related_object_type="Booking",
        )

        response = self.client.get(
            reverse("notifications_app:get_unread_notifications")
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Type"], "application/json")

        data = response.json()
        self.assertEqual(data["unread_count"], 2)


class ProviderNotificationViewsTest(TestCase):
    """Test provider notification views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=CustomUser.SERVICE_PROVIDER,
        )

        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Business",
            phone="+**********",
            contact_name="John Doe",
            address="123 Test Street",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

    def test_provider_notification_list_view(self):
        """Test provider notification list view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        # Create some notifications
        create_notification(
            user=self.provider,
            notification_type=Notification.BOOKING,
            title="New Booking Request",
            message="You have a new booking request",
        )

        response = self.client.get(reverse("notifications_app:notification_list"))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "New Booking Request")
        self.assertIn("notifications", response.context)

    def test_provider_notification_list_view_unauthenticated(self):
        """Test provider notification list view for unauthenticated user."""
        response = self.client.get(reverse("notifications_app:notification_list"))

        # Should redirect to login
        self.assertEqual(response.status_code, 302)
        self.assertIn("/accounts/", response.url)

    def test_provider_notification_list_view_wrong_user_type(self):
        """Test provider notification list view with wrong user type."""
        customer = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=CustomUser.CUSTOMER,
        )

        self.client.login(email="<EMAIL>", password="testpass123")

        response = self.client.get(reverse("notifications_app:notification_list"))

        # Should return 200 but with no notifications (since customer has no notifications)
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(
            response, "New Booking Request"
        )  # Provider's notification shouldn't be visible


class AdminNotificationViewsTest(TestCase):
    """Test admin notification views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=CustomUser.ADMIN,
            is_staff=True,
            is_superuser=True,
        )

        self.regular_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=CustomUser.CUSTOMER
        )

    def test_admin_notification_dashboard_view(self):
        """Test admin notification dashboard view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        response = self.client.get(
            reverse("notifications_app:admin_notification_dashboard")
        )

        self.assertEqual(response.status_code, 200)
        self.assertIn("total_notifications", response.context)
        self.assertIn("unread_notifications", response.context)

    def test_admin_notification_dashboard_view_unauthorized(self):
        """Test admin notification dashboard view for unauthorized user."""
        self.client.login(email="<EMAIL>", password="testpass123")

        response = self.client.get(
            reverse("notifications_app:admin_notification_dashboard")
        )

        # Should return 403 or redirect
        self.assertIn(response.status_code, [403, 302])

    def test_admin_create_announcement_view_get(self):
        """Test admin create announcement view GET request."""
        self.client.login(email="<EMAIL>", password="testpass123")

        response = self.client.get(
            reverse("notifications_app:admin_create_announcement")
        )

        self.assertEqual(response.status_code, 200)
        self.assertIn("form", response.context)

    def test_admin_create_announcement_view_post(self):
        """Test admin create announcement view POST request."""
        self.client.login(email="<EMAIL>", password="testpass123")

        form_data = {
            "title": "System Maintenance",
            "announcement_text": "The system will be under maintenance from 2-4 AM.",
            "target_audience": AdminAnnouncement.ALL_USERS,
        }

        response = self.client.post(
            reverse("notifications_app:admin_create_announcement"), data=form_data
        )

        self.assertEqual(
            response.status_code, 302
        )  # Redirect after successful creation

        # Check that announcement was created
        announcement = AdminAnnouncement.objects.get(title="System Maintenance")
        self.assertEqual(announcement.target_audience, AdminAnnouncement.ALL_USERS)

    def test_admin_notification_list_view(self):
        """Test admin notification list view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        # Create some notifications
        create_notification(
            user=self.regular_user,
            notification_type=Notification.ANNOUNCEMENT,
            title="System Announcement",
            message="System message",
        )

        response = self.client.get(reverse("notifications_app:admin_notification_list"))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "System Announcement")
        self.assertIn("notifications", response.context)

    def test_admin_notification_detail_view(self):
        """Test admin notification detail view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        notification = create_notification(
            user=self.regular_user,
            notification_type=Notification.ANNOUNCEMENT,
            title="System Announcement",
            message="System message",
        )

        response = self.client.get(
            reverse(
                "notifications_app:admin_notification_detail", args=[notification.id]
            )
        )

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "System Announcement")
        self.assertContains(response, "System message")
