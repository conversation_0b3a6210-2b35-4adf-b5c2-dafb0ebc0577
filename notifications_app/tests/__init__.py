"""
Tests package for notifications_app.

This package contains all unit and integration tests for the notifications_app.
All test modules are imported here to ensure proper test discovery.
"""

from .simple_test import SimpleTestCase
from .test_forms import (
    AdminAnnouncementFormTest,
    BulkNotificationFormTest,
    NotificationFilterFormTest,
    NotificationFormTest,
)
from .test_integration import (
    AdminNotificationIntegrationTest,
    CrossAppIntegrationTest,
    NotificationAjaxIntegrationTest,
    NotificationSecurityIntegrationTest,
    NotificationWorkflowIntegrationTest,
)
from .test_management_commands import TestNotificationManagementCommands

# Import all test modules to ensure they are discovered by Django's test runner
from .test_models import (
    AdminAnnouncementModelTest,
    NotificationMethodsTest,
    NotificationModelTest,
)
from .test_utils import NotificationUtilsTest

__all__ = [
    # Model tests
    "NotificationModelTest",
    "NotificationMethodsTest",
    "AdminAnnouncementModelTest",
    # Form tests
    "NotificationFormTest",
    "AdminAnnouncementFormTest",
    "NotificationFilterFormTest",
    "BulkNotificationFormTest",
    # Utility tests
    "NotificationUtilsTest",
    # Simple tests
    "SimpleTestCase",
    # Integration tests
    "NotificationWorkflowIntegrationTest",
    "CrossAppIntegrationTest",
    "AdminNotificationIntegrationTest",
    "NotificationAjaxIntegrationTest",
    "NotificationSecurityIntegrationTest",
    "TestNotificationManagementCommands",
]
