"""
Unit tests for notifications_app signal handlers.

This module contains comprehensive unit tests for all signal handlers in the notifications_app,
including booking, review, and system notification signals.
"""

# Standard library imports
from unittest.mock import MagicMock, patch

from django.contrib.auth import get_user_model
from django.db.models.signals import post_save

# Django imports
from django.test import TestCase
from django.utils import timezone

from accounts_app.models import ServiceProviderProfile
from booking_cart_app.models import Booking

# Local imports
from notifications_app.models import AdminAnnouncement, Notification
from notifications_app.signals import (
    handle_booking_notifications,
    handle_provider_notifications,
    handle_review_notifications,
    handle_review_response_notifications,
)
from review_app.models import Review, ReviewResponse
from venues_app.models import Category, Venue

User = get_user_model()


class NotificationSignalsTest(TestCase):
    """Test basic notification signal functionality."""

    def setUp(self):
        """Set up test data."""
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

    @patch("notifications_app.signals.notify_new_booking")
    def test_signal_handler_called_on_booking_creation(self, mock_notify):
        """Test that signal handler is called when booking is created."""
        # Create a mock booking
        booking = MagicMock()
        booking.customer = self.customer
        booking.venue.service_provider.user = self.provider

        # Manually call the signal handler
        handle_booking_notifications(sender=None, instance=booking, created=True)

        # Check that the notification function was called
        mock_notify.assert_called_once_with(booking)

    @patch("notifications_app.signals.notify_booking_status_changed")
    def test_signal_handler_called_on_booking_status_change(self, mock_notify):
        """Test that signal handler is called when booking status changes."""
        # Create a mock booking
        booking = MagicMock()
        booking.status = "confirmed"
        booking._old_status = "pending"

        # Manually call the signal handler
        handle_booking_notifications(sender=None, instance=booking, created=False)

        # Check that the notification function was called
        mock_notify.assert_called_once_with(booking, "pending")

    @patch("notifications_app.signals.notify_booking_cancellation")
    def test_signal_handler_called_on_booking_cancellation(self, mock_notify):
        """Test that signal handler is called when booking is cancelled."""
        # Create a mock booking
        booking = MagicMock()
        booking.status = "cancelled"
        booking._old_status = "confirmed"

        # Manually call the signal handler
        handle_booking_notifications(sender=None, instance=booking, created=False)

        # Check that the notification function was called
        mock_notify.assert_called_once_with(booking)

    @patch("notifications_app.signals.notify_new_review")
    def test_signal_handler_called_on_review_creation(self, mock_notify):
        """Test that signal handler is called when review is created."""
        # Create a mock review
        review = MagicMock()
        review.customer = self.customer
        review.venue.service_provider.user = self.provider

        # Manually call the signal handler
        handle_review_notifications(sender=None, instance=review, created=True)

        # Check that the notification function was called
        mock_notify.assert_called_once_with(review)

    @patch("notifications_app.signals.notify_review_response")
    def test_signal_handler_called_on_review_response_creation(self, mock_notify):
        """Test that signal handler is called when review response is created."""
        # Create a mock review response
        review_response = MagicMock()
        review_response.review.customer = self.customer

        # Manually call the signal handler
        handle_review_response_notifications(
            sender=None, instance=review_response, created=True
        )

        # Check that the notification function was called
        mock_notify.assert_called_once_with(review_response.review, review_response)


class BookingNotificationSignalsTest(TestCase):
    """Test booking-related notification signals."""

    def setUp(self):
        """Set up test data."""
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa Business",
            contact_name="Test Provider",
            phone="**********",
            address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create venue category and venue
        self.venue_category = Category.objects.create(
            name="Spa", description="Spa services"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="Test spa description",
            state="CA",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
        )


class ReviewNotificationSignalsTest(TestCase):
    """Test review-related notification signals."""

    def setUp(self):
        """Set up test data."""
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa Business",
            contact_name="Test Provider",
            phone="**********",
            address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create venue category and venue
        self.venue_category = Category.objects.create(
            name="Spa", description="Spa services"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="Test spa description",
            state="CA",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
        )


class SystemNotificationSignalsTest(TestCase):
    """Test system-related notification signals."""

    def setUp(self):
        """Set up test data."""
        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

    @patch("notifications_app.utils.create_notification")
    def test_service_provider_approved_signal_creates_notification(
        self, mock_create_notification
    ):
        """Test that service provider approval signal is disabled (no notification created)."""
        # Create a service provider profile
        provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Business",
            contact_name="Test Contact",
            phone="**********",
            address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Manually trigger the signal handler
        handle_provider_notifications(
            sender=None, instance=provider_profile, created=True
        )

        # Check that create_notification was NOT called (signal handler is disabled)
        mock_create_notification.assert_not_called()

    @patch("notifications_app.utils.create_notification")
    def test_service_provider_rejected_signal_creates_notification(
        self, mock_create_notification
    ):
        """Test that service provider rejection signal is disabled (no notification created)."""
        # Create a service provider profile
        provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Business",
            contact_name="Test Contact",
            phone="**********",
            address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Manually trigger the signal handler
        handle_provider_notifications(
            sender=None, instance=provider_profile, created=True
        )

        # Check that create_notification was NOT called (signal handler is disabled)
        mock_create_notification.assert_not_called()

    def test_signal_handlers_exist(self):
        """Test that all required signal handlers exist."""
        from notifications_app import signals

        # Check that signal handler functions exist
        self.assertTrue(hasattr(signals, "handle_booking_notifications"))
        self.assertTrue(hasattr(signals, "handle_review_notifications"))
        self.assertTrue(hasattr(signals, "handle_review_response_notifications"))
        self.assertTrue(hasattr(signals, "handle_provider_notifications"))
        self.assertTrue(hasattr(signals, "handle_user_activation"))
        self.assertTrue(hasattr(signals, "handle_venue_notifications"))
        self.assertTrue(hasattr(signals, "handle_payment_notifications"))

    def test_signal_handlers_are_callable(self):
        """Test that all signal handlers are callable."""
        from notifications_app import signals

        handlers = [
            signals.handle_booking_notifications,
            signals.handle_review_notifications,
            signals.handle_review_response_notifications,
            signals.handle_provider_notifications,
            signals.handle_user_activation,
            signals.handle_venue_notifications,
            signals.handle_payment_notifications,
        ]

        for handler in handlers:
            self.assertTrue(callable(handler))
