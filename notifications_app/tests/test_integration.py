"""
Integration tests for notifications_app.

This module contains comprehensive integration tests for complex workflows and
end-to-end functionality in the notifications_app, including notification creation,
cross-app integration, admin functionality, and security testing.
"""

from datetime import timedelta
from decimal import Decimal
from unittest.mock import Mock, patch

from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages
from django.core import mail

# Django imports
from django.test import Client, TestCase, override_settings
from django.urls import reverse
from django.utils import timezone

from accounts_app.models import CustomerProfile, ServiceProviderProfile
from booking_cart_app.models import Booking, BookingItem, Cart, CartItem

# Local imports
from notifications_app.models import AdminAnnouncement, Notification
from notifications_app.utils import (
    create_notification,
    notify_booking_status_changed,
    notify_new_booking,
    notify_new_review,
    notify_payment_successful,
    notify_review_response,
    notify_service_provider_approval,
)
from payments_app.models import Payment
from review_app.models import Review, ReviewResponse
from venues_app.models import Category, Service, Venue

User = get_user_model()


@override_settings(SECURE_SSL_REDIRECT=False)
class NotificationWorkflowIntegrationTest(TestCase):
    """Test complete notification workflows from creation to user interaction."""

    def setUp(self):
        """Set up test data for notification workflow tests."""
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role="customer",
            first_name="John",
            last_name="Customer",
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role="service_provider",
            first_name="Jane",
            last_name="Provider",
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa Business",
            description="A test business",
            phone="555-0123",
            contact_name="Jane Provider",
            address="123 Test Street",
            city="Test City",
            state="CA",
            county="Test County",
            zip_code="12345",
        )

        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="AdminPass123!",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        # Create test venue and service
        self.category = Category.objects.create(
            category_name="Spa Services",
            category_description="Relaxation and wellness services",
        )

        # Create test service category
        from venues_app.models import ServiceCategory

        self.service_category = ServiceCategory.objects.create(
            name="Massage Services", description="Professional massage services"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Integration Test Spa",
            short_description="A spa for integration testing",
            state="TS",
            county="Test County",
            city="Test City",
            street_number="456",
            street_name="Spa Avenue",
            approval_status="approved",
            visibility="active",
        )

        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Relaxation Massage",
            short_description="A relaxing full-body massage",
            service_category=self.service_category,
            price_min=Decimal("80.00"),
            price_max=Decimal("120.00"),
            duration_minutes=60,
            is_active=True,
        )

    def test_complete_notification_creation_and_viewing_workflow(self):
        """Test the complete workflow of creating and viewing notifications."""
        # Step 1: Create a notification
        notification = create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Test Booking Notification",
            message="Your booking has been confirmed for Integration Test Spa.",
            related_object_id=123,
            related_object_type="Booking",
            action_url="/booking/123/",
        )

        self.assertIsNotNone(notification)
        self.assertEqual(notification.user, self.customer)
        self.assertEqual(notification.read_status, Notification.UNREAD)

        # Step 2: Login as customer
        login_success = self.client.login(
            email="<EMAIL>", password="CustomerPass123!"
        )
        self.assertTrue(login_success)

        # Step 3: View notification list
        response = self.client.get(reverse("notifications_app:notification_list"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Booking Notification")
        self.assertContains(response, "Integration Test Spa")

        # Verify unread count
        self.assertEqual(response.context["unread_count"], 1)

        # Step 4: View notification detail (should mark as read)
        response = self.client.get(
            reverse("notifications_app:notification_detail", args=[notification.id])
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Booking Notification")

        # Verify notification is now marked as read
        notification.refresh_from_db()
        self.assertEqual(notification.read_status, Notification.READ)
        self.assertIsNotNone(notification.read_at)

        # Step 5: Verify unread count is now 0
        response = self.client.get(reverse("notifications_app:notification_list"))
        self.assertEqual(response.context["unread_count"], 0)

    def test_mark_all_notifications_read_workflow(self):
        """Test marking all notifications as read workflow."""
        # Create multiple notifications
        notifications = []
        for i in range(3):
            notification = create_notification(
                user=self.customer,
                notification_type=Notification.SYSTEM,
                title=f"Test Notification {i+1}",
                message=f"This is test notification number {i+1}.",
                related_object_id=i + 1,  # Unique ID to avoid constraint violation
                related_object_type="test_object",
            )
            notifications.append(notification)

        # Verify all are unread
        unread_count = Notification.get_unread_count_for_user(self.customer)
        self.assertEqual(unread_count, 3)

        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Mark all as read
        response = self.client.post(
            reverse("notifications_app:mark_all_notifications_read")
        )
        self.assertEqual(response.status_code, 302)  # Should redirect

        # Verify all notifications are now read
        unread_count = Notification.get_unread_count_for_user(self.customer)
        self.assertEqual(unread_count, 0)

        # Verify all notifications have read status and read_at timestamp
        for notification in notifications:
            notification.refresh_from_db()
            self.assertEqual(notification.read_status, Notification.READ)
            self.assertIsNotNone(notification.read_at)

    def test_notification_filtering_workflow(self):
        """Test notification filtering functionality."""
        # Create notifications of different types
        booking_notification = create_notification(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Booking Notification",
            message="Your booking has been confirmed.",
            related_object_id=100,
            related_object_type="booking",
        )

        payment_notification = create_notification(
            user=self.customer,
            notification_type=Notification.PAYMENT,
            title="Payment Notification",
            message="Your payment has been processed.",
            related_object_id=200,
            related_object_type="payment",
        )

        review_notification = create_notification(
            user=self.customer,
            notification_type=Notification.REVIEW,
            title="Review Notification",
            message="You have received a new review.",
            related_object_id=300,
            related_object_type="review",
        )

        # Mark one as read
        payment_notification.mark_as_read()

        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Test filtering by type
        response = self.client.get(
            reverse("notifications_app:notification_list"),
            {"notification_type": Notification.BOOKING},
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Booking Notification")
        self.assertNotContains(response, "Payment Notification")
        self.assertNotContains(response, "Review Notification")

        # Test filtering by read status
        response = self.client.get(
            reverse("notifications_app:notification_list"), {"read_status": "unread"}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Booking Notification")
        self.assertNotContains(response, "Payment Notification")  # This one is read
        self.assertContains(response, "Review Notification")

        # Test filtering by read status (read)
        response = self.client.get(
            reverse("notifications_app:notification_list"), {"read_status": "read"}
        )
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, "Booking Notification")
        self.assertContains(response, "Payment Notification")  # This one is read
        self.assertNotContains(response, "Review Notification")


@override_settings(SECURE_SSL_REDIRECT=False)
class CrossAppIntegrationTest(TestCase):
    """Test integration between notifications_app and other apps."""

    def setUp(self):
        """Set up test data for cross-app integration tests."""
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role="customer",
            first_name="John",
            last_name="Customer",
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role="service_provider",
            first_name="Jane",
            last_name="Provider",
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Cross-App Test Spa",
            description="A test spa for cross-app integration",
            phone="555-0123",
            contact_name="Jane Provider",
            address="123 Cross-App Street",
            city="Test City",
            state="CA",
            county="Test County",
            zip_code="12345",
        )

        # Create test venue and service
        self.category = Category.objects.create(
            category_name="Spa Services",
            category_description="Relaxation and wellness services",
        )

        # Create test service category
        from venues_app.models import ServiceCategory

        self.service_category = ServiceCategory.objects.create(
            name="Massage Services", description="Professional massage services"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Cross-App Integration Spa",
            short_description="A spa for cross-app integration testing",
            state="TS",
            county="Test County",
            city="Test City",
            street_number="456",
            street_name="Integration Avenue",
            approval_status="approved",
            visibility="active",
        )

        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Cross-App Test Service",
            short_description="A service for cross-app testing",
            service_category=self.service_category,
            price_min=Decimal("100.00"),
            price_max=Decimal("150.00"),
            duration_minutes=90,
            is_active=True,
        )

    def test_booking_cart_app_integration_workflow(self):
        """Test integration with booking_cart_app for booking notifications."""
        # Create a booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="pending",
            notes="Integration test booking",
        )

        # Create booking item
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal("100.00"),
            quantity=1,
            scheduled_date=timezone.now().date(),
            scheduled_time=timezone.now().time(),
            duration_minutes=60,
        )

        # Test that booking creation triggered notifications via signal
        # The signal should have created notifications when the booking was created
        notification_count = Notification.objects.count()
        self.assertEqual(
            notification_count, 2
        )  # Should have 2 notifications from signal

        # Verify customer notification
        customer_notification = Notification.objects.filter(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Booking Submitted",
        ).first()
        self.assertIsNotNone(customer_notification)
        self.assertIn("Cross-App Integration Spa", customer_notification.message)
        self.assertIn(str(booking.booking_id), customer_notification.message)

        # Verify provider notification
        provider_notification = Notification.objects.filter(
            user=self.provider,
            notification_type=Notification.BOOKING,
            title="New Booking Received",
        ).first()
        self.assertIsNotNone(provider_notification)
        self.assertIn("Cross-App Integration Spa", provider_notification.message)
        self.assertIn("John Customer", provider_notification.message)

        # Test booking status change notification
        # Manually set the old status since the pre_save signal might not be working in tests
        booking._old_status = "pending"
        booking.status = "confirmed"
        booking.save()

        # Should create notification for customer via signal
        status_notification = Notification.objects.filter(
            user=self.customer,
            notification_type=Notification.BOOKING,
            title="Booking Confirmed",
        ).first()
        self.assertIsNotNone(status_notification)
        self.assertIn("confirmed", status_notification.message)
        self.assertIn(str(booking.booking_id), status_notification.message)

    def test_accounts_app_integration_workflow(self):
        """Test integration with accounts_app for service provider approval notifications."""
        # Create a new provider profile (pending approval)
        new_provider = User.objects.create_user(
            email="<EMAIL>",
            password="NewProviderPass123!",
            role="service_provider",
            first_name="New",
            last_name="Provider",
        )

        new_provider_profile = ServiceProviderProfile.objects.create(
            user=new_provider,
            legal_name="New Test Business",
            description="A new business for testing",
            phone="555-9999",
            contact_name="New Provider",
            address="999 New Street",
            city="New City",
            state="NC",
            county="Test County",
            zip_code="99999",
        )

        # Test service provider approval notification
        initial_notification_count = Notification.objects.count()
        notify_service_provider_approval(new_provider_profile)

        # Should create notification for the new provider
        new_notification_count = Notification.objects.count()
        self.assertEqual(new_notification_count, initial_notification_count + 1)

        # Verify provider notification
        approval_notification = Notification.objects.filter(
            user=new_provider,
            notification_type=Notification.SYSTEM,
            title="Welcome to CozyWish!",
        ).first()
        self.assertIsNotNone(approval_notification)
        self.assertIn(
            "service provider account has been set up", approval_notification.message
        )
        self.assertIn("start adding venues", approval_notification.message)


@override_settings(SECURE_SSL_REDIRECT=False)
class AdminNotificationIntegrationTest(TestCase):
    """Test admin-specific notification functionality and workflows."""

    def setUp(self):
        """Set up test data for admin notification tests."""
        self.client = Client()

        # Create admin user
        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="AdminPass123!",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        # Create test users for announcements
        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role="customer",
            first_name="John",
            last_name="Customer",
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role="service_provider",
            first_name="Jane",
            last_name="Provider",
        )
        ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Provider Business",
            description="A test business",
            phone="555-0123",
            contact_name="Jane Provider",
            address="123 Test Street",
            city="Test City",
            state="CA",
            county="Test County",
            zip_code="12345",
        )

    def test_admin_announcement_creation_workflow(self):
        """Test the complete admin announcement creation and delivery workflow."""
        # Login as admin
        login_success = self.client.login(
            email="<EMAIL>", password="AdminPass123!"
        )
        self.assertTrue(login_success)

        # Access admin notification dashboard
        response = self.client.get(
            reverse("notifications_app:admin_notification_dashboard")
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Notification Management")

        # Create an announcement for all users
        announcement_data = {
            "title": "System Maintenance Notice",
            "announcement_text": "The system will be down for maintenance on Saturday from 2-4 AM.",
            "target_audience": AdminAnnouncement.ALL_USERS,
        }

        response = self.client.post(
            reverse("notifications_app:admin_create_announcement"), announcement_data
        )
        self.assertEqual(response.status_code, 302)  # Should redirect

        # Verify announcement was created and sent
        announcement = AdminAnnouncement.objects.filter(
            title="System Maintenance Notice"
        ).first()
        self.assertIsNotNone(announcement)
        self.assertEqual(announcement.created_by, self.admin)
        self.assertEqual(announcement.status, AdminAnnouncement.SENT)

        # Verify notifications were created for all users
        announcement_notifications = Notification.objects.filter(
            notification_type=Notification.ANNOUNCEMENT,
            title="System Maintenance Notice",
        )
        expected_recipients = 3  # admin, customer, provider
        self.assertEqual(announcement_notifications.count(), expected_recipients)

        # Verify announcement status updated
        announcement.refresh_from_db()
        self.assertEqual(announcement.status, AdminAnnouncement.SENT)
        self.assertIsNotNone(announcement.sent_at)
        self.assertEqual(announcement.total_recipients, expected_recipients)

        # Verify notifications were created for each user
        for user in [self.admin, self.customer, self.provider]:
            notification = Notification.objects.filter(
                user=user,
                notification_type=Notification.ANNOUNCEMENT,
                title="System Maintenance Notice",
            ).first()
            self.assertIsNotNone(notification)
            self.assertIn("maintenance", notification.message)

    def test_admin_targeted_announcement_workflow(self):
        """Test creating announcements targeted to specific user groups."""
        # Login as admin
        self.client.login(email="<EMAIL>", password="AdminPass123!")

        # Create announcement for customers only
        customer_announcement_data = {
            "title": "New Customer Features",
            "announcement_text": "We have added new features for customers!",
            "target_audience": AdminAnnouncement.CUSTOMERS,
        }

        response = self.client.post(
            reverse("notifications_app:admin_create_announcement"),
            customer_announcement_data,
        )
        self.assertEqual(response.status_code, 302)

        # Verify the announcement was created and sent
        customer_announcement = AdminAnnouncement.objects.filter(
            title="New Customer Features"
        ).first()
        self.assertIsNotNone(customer_announcement)
        self.assertEqual(customer_announcement.status, AdminAnnouncement.SENT)

        # Verify only customer received notification
        customer_notifications = Notification.objects.filter(
            notification_type=Notification.ANNOUNCEMENT, title="New Customer Features"
        )
        self.assertEqual(customer_notifications.count(), 1)

        # Verify customer notification
        customer_notification = Notification.objects.filter(
            user=self.customer,
            notification_type=Notification.ANNOUNCEMENT,
            title="New Customer Features",
        ).first()
        self.assertIsNotNone(customer_notification)

        # Verify provider did not receive notification
        provider_notification = Notification.objects.filter(
            user=self.provider,
            notification_type=Notification.ANNOUNCEMENT,
            title="New Customer Features",
        ).first()
        self.assertIsNone(provider_notification)

        # Create announcement for providers only
        provider_announcement_data = {
            "title": "Provider Policy Update",
            "announcement_text": "Important policy updates for service providers.",
            "target_audience": AdminAnnouncement.PROVIDERS,
        }

        response = self.client.post(
            reverse("notifications_app:admin_create_announcement"),
            provider_announcement_data,
        )
        self.assertEqual(response.status_code, 302)

        # Verify the provider announcement was created and sent
        provider_announcement = AdminAnnouncement.objects.filter(
            title="Provider Policy Update"
        ).first()
        self.assertIsNotNone(provider_announcement)
        self.assertEqual(provider_announcement.status, AdminAnnouncement.SENT)

        # Verify only provider received notification
        provider_notifications = Notification.objects.filter(
            notification_type=Notification.ANNOUNCEMENT, title="Provider Policy Update"
        )
        self.assertEqual(provider_notifications.count(), 1)

        # Verify provider notification
        provider_notification = Notification.objects.filter(
            user=self.provider,
            notification_type=Notification.ANNOUNCEMENT,
            title="Provider Policy Update",
        ).first()
        self.assertIsNotNone(provider_notification)

        # Verify customer did not receive notification
        customer_notification = Notification.objects.filter(
            user=self.customer,
            notification_type=Notification.ANNOUNCEMENT,
            title="Provider Policy Update",
        ).first()
        self.assertIsNone(customer_notification)


@override_settings(SECURE_SSL_REDIRECT=False)
class NotificationAjaxIntegrationTest(TestCase):
    """Test AJAX endpoints and dynamic notification features."""

    def setUp(self):
        """Set up test data for AJAX notification tests."""
        self.client = Client()

        # Create test user
        self.customer = User.objects.create_user(
            email="<EMAIL>",
            password="CustomerPass123!",
            role="customer",
            first_name="John",
            last_name="Customer",
        )
        CustomerProfile.objects.create(user=self.customer)

        # Create test notifications
        self.notifications = []
        for i in range(5):
            notification = create_notification(
                user=self.customer,
                notification_type=Notification.SYSTEM,
                title=f"AJAX Test Notification {i+1}",
                message=f"This is AJAX test notification number {i+1}.",
                related_object_id=i + 1,  # Make each notification unique
                related_object_type="TestObject",
            )
            self.notifications.append(notification)

        # Mark some as read
        self.notifications[0].mark_as_read()
        self.notifications[1].mark_as_read()

    def test_get_unread_notifications_ajax_endpoint(self):
        """Test the AJAX endpoint for getting unread notifications."""
        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Make AJAX request for unread notifications
        response = self.client.get(
            reverse("notifications_app:get_unread_notifications"),
            HTTP_X_REQUESTED_WITH="XMLHttpRequest",
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Type"], "application/json")

        # Parse JSON response
        data = response.json()
        self.assertTrue(data["success"])
        self.assertEqual(data["unread_count"], 3)  # 3 unread notifications
        self.assertEqual(len(data["notifications"]), 3)

        # Verify notification data structure
        for notification_data in data["notifications"]:
            self.assertIn("id", notification_data)
            self.assertIn("title", notification_data)
            self.assertIn("message", notification_data)
            self.assertIn("created_at", notification_data)
            self.assertIn(
                "type", notification_data
            )  # The field is 'type', not 'notification_type'

    def test_mark_notification_read_ajax_endpoint(self):
        """Test the AJAX endpoint for marking notifications as read."""
        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Get an unread notification
        unread_notification = self.notifications[2]  # Should be unread
        self.assertEqual(unread_notification.read_status, Notification.UNREAD)

        # Make AJAX request to mark as read
        response = self.client.post(
            reverse(
                "notifications_app:mark_notification_read",
                args=[unread_notification.id],
            ),
            HTTP_X_REQUESTED_WITH="XMLHttpRequest",
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Type"], "application/json")

        # Parse JSON response
        data = response.json()
        self.assertTrue(data["success"])
        self.assertIn("marked as read", data["message"])
        self.assertEqual(data["unread_count"], 2)  # Should be 2 now

        # Verify notification is marked as read
        unread_notification.refresh_from_db()
        self.assertEqual(unread_notification.read_status, Notification.READ)
        self.assertIsNotNone(unread_notification.read_at)

    def test_mark_all_notifications_read_ajax_endpoint(self):
        """Test the AJAX endpoint for marking all notifications as read."""
        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Verify initial unread count
        initial_unread_count = Notification.get_unread_count_for_user(self.customer)
        self.assertEqual(initial_unread_count, 3)

        # Make AJAX request to mark all as read
        response = self.client.post(
            reverse("notifications_app:mark_all_notifications_read"),
            HTTP_X_REQUESTED_WITH="XMLHttpRequest",
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Type"], "application/json")

        # Parse JSON response
        data = response.json()
        self.assertTrue(data["success"])
        self.assertEqual(data["marked_count"], 3)  # 3 notifications were marked as read
        self.assertEqual(data["unread_count"], 0)  # Should be 0 now

        # Verify all notifications are marked as read
        final_unread_count = Notification.get_unread_count_for_user(self.customer)
        self.assertEqual(final_unread_count, 0)

    def test_delete_notification_ajax_endpoint(self):
        """Test the AJAX endpoint for deleting notifications."""
        # Login as customer
        self.client.login(email="<EMAIL>", password="CustomerPass123!")

        # Get a notification to delete
        notification_to_delete = self.notifications[0]
        notification_id = notification_to_delete.id

        # Verify notification exists
        self.assertTrue(Notification.objects.filter(id=notification_id).exists())

        # Make AJAX request to delete notification
        response = self.client.post(
            reverse("notifications_app:delete_notification", args=[notification_id]),
            HTTP_X_REQUESTED_WITH="XMLHttpRequest",
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Type"], "application/json")

        # Parse JSON response
        data = response.json()
        self.assertTrue(data["success"])
        self.assertIn("deleted successfully", data["message"])

        # Verify notification was deleted
        self.assertFalse(Notification.objects.filter(id=notification_id).exists())


@override_settings(SECURE_SSL_REDIRECT=False)
class NotificationSecurityIntegrationTest(TestCase):
    """Test security and access control for notifications."""

    def setUp(self):
        """Set up test data for security tests."""
        self.client = Client()

        # Create test users
        self.customer1 = User.objects.create_user(
            email="<EMAIL>",
            password="Customer1Pass123!",
            role="customer",
            first_name="John",
            last_name="Customer1",
        )
        CustomerProfile.objects.create(user=self.customer1)

        self.customer2 = User.objects.create_user(
            email="<EMAIL>",
            password="Customer2Pass123!",
            role="customer",
            first_name="Jane",
            last_name="Customer2",
        )
        CustomerProfile.objects.create(user=self.customer2)

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="ProviderPass123!",
            role="service_provider",
            first_name="Provider",
            last_name="User",
        )
        ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Security Test Business",
            description="A test business for security testing",
            phone="555-0123",
            contact_name="Provider User",
            address="123 Security Street",
            city="Test City",
            state="CA",
            county="Test County",
            zip_code="12345",
        )

        # Create notifications for each user
        self.customer1_notification = create_notification(
            user=self.customer1,
            notification_type=Notification.SYSTEM,
            title="Customer 1 Notification",
            message="This notification belongs to customer 1.",
        )

        self.customer2_notification = create_notification(
            user=self.customer2,
            notification_type=Notification.SYSTEM,
            title="Customer 2 Notification",
            message="This notification belongs to customer 2.",
        )

        self.provider_notification = create_notification(
            user=self.provider,
            notification_type=Notification.SYSTEM,
            title="Provider Notification",
            message="This notification belongs to the provider.",
        )

    def test_notification_access_control(self):
        """Test that users can only access their own notifications."""
        # Login as customer1
        self.client.login(email="<EMAIL>", password="Customer1Pass123!")

        # Customer1 should be able to access their own notification
        response = self.client.get(
            reverse(
                "notifications_app:notification_detail",
                args=[self.customer1_notification.id],
            )
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Customer 1 Notification")

        # Customer1 should NOT be able to access customer2's notification (should redirect)
        response = self.client.get(
            reverse(
                "notifications_app:notification_detail",
                args=[self.customer2_notification.id],
            )
        )
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse("notifications_app:notification_list"))

        # Customer1 should NOT be able to access provider's notification (should redirect)
        response = self.client.get(
            reverse(
                "notifications_app:notification_detail",
                args=[self.provider_notification.id],
            )
        )
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse("notifications_app:notification_list"))

    def test_notification_list_isolation(self):
        """Test that notification lists only show user's own notifications."""
        # Login as customer1
        self.client.login(email="<EMAIL>", password="Customer1Pass123!")

        # Get notification list
        response = self.client.get(reverse("notifications_app:notification_list"))
        self.assertEqual(response.status_code, 200)

        # Should contain customer1's notification
        self.assertContains(response, "Customer 1 Notification")

        # Should NOT contain other users' notifications
        self.assertNotContains(response, "Customer 2 Notification")
        self.assertNotContains(response, "Provider Notification")

        # Verify context data
        notifications = response.context["notifications"]
        self.assertEqual(len(notifications), 1)
        self.assertEqual(notifications[0].user, self.customer1)

    def test_unauthorized_notification_modification(self):
        """Test that users cannot modify other users' notifications."""
        # Login as customer1
        self.client.login(email="<EMAIL>", password="Customer1Pass123!")

        # Try to mark customer2's notification as read (should redirect)
        response = self.client.post(
            reverse(
                "notifications_app:mark_notification_read",
                args=[self.customer2_notification.id],
            )
        )
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse("notifications_app:notification_list"))

        # Verify customer2's notification is still unread
        self.customer2_notification.refresh_from_db()
        self.assertEqual(self.customer2_notification.read_status, Notification.UNREAD)

        # Try to delete provider's notification (should redirect)
        response = self.client.post(
            reverse(
                "notifications_app:delete_notification",
                args=[self.provider_notification.id],
            )
        )
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse("notifications_app:notification_list"))

        # Verify provider's notification still exists
        self.assertTrue(
            Notification.objects.filter(id=self.provider_notification.id).exists()
        )

    def test_admin_access_restrictions(self):
        """Test that non-admin users cannot access admin notification features."""
        # Login as customer
        self.client.login(email="<EMAIL>", password="Customer1Pass123!")

        # Try to access admin dashboard
        response = self.client.get(
            reverse("notifications_app:admin_notification_dashboard")
        )
        self.assertEqual(response.status_code, 302)  # Should redirect (access denied)

        # Try to create admin announcement
        response = self.client.get(
            reverse("notifications_app:admin_create_announcement")
        )
        self.assertEqual(response.status_code, 302)  # Should redirect (access denied)

        # Try to post admin announcement
        announcement_data = {
            "title": "Unauthorized Announcement",
            "announcement_text": "This should not be allowed.",
            "target_audience": AdminAnnouncement.ALL_USERS,
        }
        response = self.client.post(
            reverse("notifications_app:admin_create_announcement"), announcement_data
        )
        self.assertEqual(response.status_code, 302)  # Should redirect (access denied)

        # Verify no announcement was created
        self.assertFalse(
            AdminAnnouncement.objects.filter(title="Unauthorized Announcement").exists()
        )
