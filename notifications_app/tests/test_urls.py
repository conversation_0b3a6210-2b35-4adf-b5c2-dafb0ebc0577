"""
Unit tests for notifications_app URL routing.

This module contains comprehensive unit tests for URL routing in the notifications_app,
including URL pattern resolution, parameter handling, and access control.
"""

from django.contrib.auth import get_user_model

# Django imports
from django.test import Client, SimpleTestCase, TestCase
from django.urls import resolve, reverse

# Local imports
try:
    from notifications_app import views
except ImportError:
    views = None

User = get_user_model()


class NotificationURLsTest(SimpleTestCase):
    """Test URL routing for notifications_app."""

    def test_notification_list_url_resolves(self):
        """Test that notification list URL resolves correctly."""
        url = reverse("notifications_app:notification_list")
        self.assertEqual(resolve(url).func, views.notification_list)

    def test_notification_detail_url_resolves(self):
        """Test that notification detail URL resolves correctly."""
        url = reverse("notifications_app:notification_detail", args=[1])
        self.assertEqual(resolve(url).func, views.notification_detail)

    def test_mark_notification_read_url_resolves(self):
        """Test that mark notification read URL resolves correctly."""
        url = reverse("notifications_app:mark_notification_read", args=[1])
        self.assertEqual(resolve(url).func, views.mark_notification_read)

    def test_mark_all_notifications_read_url_resolves(self):
        """Test that mark all notifications read URL resolves correctly."""
        url = reverse("notifications_app:mark_all_notifications_read")
        self.assertEqual(resolve(url).func, views.mark_all_notifications_read)

    def test_notification_preferences_url_resolves(self):
        """Test that notification preferences URL resolves correctly."""
        url = reverse("notifications_app:notification_preferences")
        self.assertEqual(resolve(url).func, views.notification_preferences_view)

    def test_get_unread_notifications_url_resolves(self):
        """Test that get unread notifications URL resolves correctly."""
        url = reverse("notifications_app:get_unread_notifications")
        self.assertEqual(resolve(url).func, views.get_unread_notifications)

    def test_admin_notification_dashboard_url_resolves(self):
        """Test that admin notification dashboard URL resolves correctly."""
        url = reverse("notifications_app:admin_notification_dashboard")
        self.assertEqual(resolve(url).func, views.admin_notification_dashboard)

    def test_admin_create_announcement_url_resolves(self):
        """Test that admin create announcement URL resolves correctly."""
        url = reverse("notifications_app:admin_create_announcement")
        self.assertEqual(resolve(url).func, views.admin_create_announcement)

    def test_admin_notification_list_url_resolves(self):
        """Test that admin notification list URL resolves correctly."""
        url = reverse("notifications_app:admin_notification_list")
        self.assertEqual(resolve(url).func, views.admin_notification_list)

    def test_admin_notification_detail_url_resolves(self):
        """Test that admin notification detail URL resolves correctly."""
        url = reverse("notifications_app:admin_notification_detail", args=[1])
        self.assertEqual(resolve(url).func, views.admin_notification_detail)

    def test_announcement_detail_url_resolves(self):
        """Test that announcement detail URL resolves correctly."""
        url = reverse("notifications_app:announcement_detail", args=["test-slug"])
        self.assertEqual(resolve(url).func, views.announcement_detail)

    def test_url_patterns_have_names(self):
        """Test that all URL patterns have proper names."""
        # URLs that don't require parameters
        url_names_no_params = [
            "notification_list",
            "mark_all_notifications_read",
            "mark_all_notifications_unread",
            "bulk_mark_notifications_read",
            "bulk_mark_notifications_unread",
            "notification_preferences",
            "get_unread_notifications",
            "admin_notification_dashboard",
            "admin_create_announcement",
            "admin_notification_list",
        ]

        for url_name in url_names_no_params:
            try:
                url = reverse(f"notifications_app:{url_name}")
                self.assertIsNotNone(url)
            except Exception as e:
                self.fail(f"URL name '{url_name}' failed to reverse: {e}")

        # URLs that require parameters - test with dummy values
        url_names_with_params = [
            ("notification_detail", [1]),
            ("mark_notification_read", [1]),
            ("mark_notification_unread", [1]),
            ("delete_notification", [1]),
            ("announcement_detail", ["test-slug"]),
            ("admin_notification_detail", [1]),
        ]

        for url_name, args in url_names_with_params:
            try:
                url = reverse(f"notifications_app:{url_name}", args=args)
                self.assertIsNotNone(url)
            except Exception as e:
                self.fail(
                    f"URL name '{url_name}' with args {args} failed to reverse: {e}"
                )

    def test_url_patterns_with_parameters(self):
        """Test URL patterns that require parameters."""
        # Test URLs that require ID parameters
        parametrized_urls = [
            ("notification_detail", [1]),
            ("mark_notification_read", [1]),
            ("mark_notification_unread", [1]),
            ("delete_notification", [1]),
            ("admin_notification_detail", [1]),
            ("announcement_detail", ["test-slug"]),
        ]

        for url_name, args in parametrized_urls:
            try:
                url = reverse(f"notifications_app:{url_name}", args=args)
                self.assertIsNotNone(url)
                self.assertIn(str(args[0]), url)
            except Exception as e:
                self.fail(
                    f"URL name '{url_name}' with args {args} failed to reverse: {e}"
                )

    def test_url_namespace(self):
        """Test that URLs are properly namespaced."""
        url = reverse("notifications_app:notification_list")
        self.assertIn("/notifications/", url)

    def test_trailing_slash_consistency(self):
        """Test that URLs have consistent trailing slash behavior."""
        # Most URLs should not require trailing slashes for GET requests
        url_names_without_params = [
            "notification_list",
            "mark_all_notifications_read",
            "mark_all_notifications_unread",
            "bulk_mark_notifications_read",
            "bulk_mark_notifications_unread",
            "notification_preferences",
            "get_unread_notifications",
            "admin_notification_dashboard",
            "admin_create_announcement",
            "admin_notification_list",
        ]

        for url_name in url_names_without_params:
            url = reverse(f"notifications_app:{url_name}")
            # URLs should be consistent in their trailing slash usage
            self.assertIsNotNone(url)


class URLAccessTest(TestCase):
    """Test URL access control and authentication requirements."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

    def test_customer_urls_require_authentication(self):
        """Test that customer URLs require authentication."""
        customer_urls = [
            "notifications_app:notification_list",
            "notifications_app:notification_preferences",
            "notifications_app:get_unread_notifications",
        ]

        for url_name in customer_urls:
            response = self.client.get(reverse(url_name))
            # Should redirect to login or return 401/403
            self.assertIn(response.status_code, [302, 401, 403])

    def test_admin_urls_require_admin_access(self):
        """Test that admin URLs require admin access."""
        admin_urls = [
            "notifications_app:admin_notification_dashboard",
            "notifications_app:admin_create_announcement",
            "notifications_app:admin_notification_list",
        ]

        # Test unauthenticated access
        for url_name in admin_urls:
            response = self.client.get(reverse(url_name))
            self.assertIn(response.status_code, [302, 401, 403])

        # Test regular user access
        self.client.login(email="<EMAIL>", password="testpass123")
        for url_name in admin_urls:
            response = self.client.get(reverse(url_name))
            self.assertIn(response.status_code, [302, 403])

    def test_customer_authenticated_access(self):
        """Test that authenticated customers can access customer URLs."""
        self.client.login(email="<EMAIL>", password="testpass123")

        customer_urls = [
            "notifications_app:notification_list",
            "notifications_app:notification_preferences",
            "notifications_app:get_unread_notifications",
        ]

        for url_name in customer_urls:
            response = self.client.get(reverse(url_name))
            self.assertEqual(response.status_code, 200)

    def test_admin_authenticated_access(self):
        """Test that authenticated admins can access admin URLs."""
        self.client.login(email="<EMAIL>", password="testpass123")

        admin_urls = [
            "notifications_app:admin_notification_dashboard",
            "notifications_app:admin_create_announcement",
            "notifications_app:admin_notification_list",
        ]

        for url_name in admin_urls:
            response = self.client.get(reverse(url_name))
            self.assertEqual(response.status_code, 200)

    def test_cross_user_type_access_restrictions(self):
        """Test that users cannot access URLs meant for other user types."""
        # Customer trying to access admin URLs
        self.client.login(email="<EMAIL>", password="testpass123")
        response = self.client.get(
            reverse("notifications_app:admin_notification_dashboard")
        )
        self.assertIn(response.status_code, [302, 403])

        # Provider trying to access admin URLs
        self.client.login(email="<EMAIL>", password="testpass123")
        response = self.client.get(
            reverse("notifications_app:admin_notification_dashboard")
        )
        self.assertIn(response.status_code, [302, 403])
