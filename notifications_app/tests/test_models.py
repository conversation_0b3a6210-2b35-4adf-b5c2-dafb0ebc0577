"""
Unit tests for notifications_app models.

This module contains comprehensive unit tests for all model classes in the ``notifications_app``,
including ``Notification``, ``AdminAnnouncement`` and ``NotificationPreference``.
"""

# Standard library imports
from datetime import timedelta
from unittest.mock import Mock, patch

from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.db import IntegrityError

# Django imports
from django.test import TestCase, override_settings
from django.utils import timezone

# Local imports
from notifications_app.models import AdminAnnouncement, Notification

User = get_user_model()


class NotificationModelTest(TestCase):
    """Test the Notification model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        self.notification_data = {
            "user": self.user,
            "notification_type": Notification.BOOKING,
            "title": "New Booking Confirmation",
            "message": "Your booking has been confirmed.",
        }

    def test_create_notification(self):
        """Test creating a notification."""
        notification = Notification.objects.create(**self.notification_data)

        self.assertEqual(notification.title, "New Booking Confirmation")
        self.assertEqual(notification.message, "Your booking has been confirmed.")
        self.assertEqual(notification.notification_type, Notification.BOOKING)
        self.assertEqual(notification.user, self.user)
        self.assertEqual(notification.read_status, Notification.UNREAD)

    def test_notification_str_representation(self):
        """Test the string representation of Notification."""
        notification = Notification.objects.create(**self.notification_data)
        expected_str = f"{self.user.email} - {notification.title}"
        self.assertEqual(str(notification), expected_str)

    def test_notification_type_choices(self):
        """Test notification type choices."""
        valid_types = [
            Notification.BOOKING,
            Notification.PAYMENT,
            Notification.REVIEW,
            Notification.ANNOUNCEMENT,
            Notification.SYSTEM,
        ]

        for notification_type in valid_types:
            notification_data = self.notification_data.copy()
            notification_data["notification_type"] = notification_type
            notification = Notification.objects.create(**notification_data)
            self.assertEqual(notification.notification_type, notification_type)

    def test_notification_read_status_choices(self):
        """Test notification read status choices."""
        notification = Notification.objects.create(**self.notification_data)

        # Test default status
        self.assertEqual(notification.read_status, Notification.UNREAD)

        # Test changing status
        notification.read_status = Notification.READ
        notification.save()
        self.assertEqual(notification.read_status, Notification.READ)

    def test_notification_with_action_url(self):
        """Test notification with action URL."""
        notification_data = self.notification_data.copy()
        notification_data["action_url"] = "https://example.com/booking/123"

        notification = Notification.objects.create(**notification_data)
        self.assertEqual(notification.action_url, "https://example.com/booking/123")

    def test_notification_with_related_object(self):
        """Test notification with related object fields."""
        notification_data = self.notification_data.copy()
        notification_data["related_object_id"] = 123
        notification_data["related_object_type"] = "Booking"

        notification = Notification.objects.create(**notification_data)
        self.assertEqual(notification.related_object_id, 123)
        self.assertEqual(notification.related_object_type, "Booking")

    def test_notification_ordering(self):
        """Test the default ordering of notifications."""
        # Create notifications with different timestamps
        notification1 = Notification.objects.create(
            user=self.user,
            notification_type=Notification.BOOKING,
            title="First Notification",
            message="First message",
        )

        notification2 = Notification.objects.create(
            user=self.user,
            notification_type=Notification.BOOKING,
            title="Second Notification",
            message="Second message",
        )

        notifications = list(Notification.objects.all())
        # Should be ordered by created_at descending (newest first)
        self.assertEqual(notifications[0], notification2)
        self.assertEqual(notifications[1], notification1)


class NotificationMethodsTest(TestCase):
    """Test the Notification model methods and properties."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        self.notification = Notification.objects.create(
            user=self.user,
            notification_type=Notification.BOOKING,
            title="Test Notification",
            message="Test message",
        )

    def test_mark_as_read(self):
        """Test marking a notification as read."""
        # Initially should be unread
        self.assertEqual(self.notification.read_status, Notification.UNREAD)
        self.assertIsNone(self.notification.read_at)

        # Mark as read
        self.notification.mark_as_read()

        # Should now be read
        self.assertEqual(self.notification.read_status, Notification.READ)
        self.assertIsNotNone(self.notification.read_at)

    def test_mark_as_unread(self):
        """Test marking a notification as unread."""
        # First mark as read
        self.notification.mark_as_read()
        self.assertEqual(self.notification.read_status, Notification.READ)

        # Then mark as unread
        self.notification.mark_as_unread()

        # Should now be unread
        self.assertEqual(self.notification.read_status, Notification.UNREAD)
        self.assertIsNone(self.notification.read_at)

    def test_is_read_property(self):
        """Test the is_read property."""
        # Initially should be False
        self.assertFalse(self.notification.is_read)

        # Mark as read
        self.notification.mark_as_read()

        # Should now be True
        self.assertTrue(self.notification.is_read)

    def test_is_recent_property(self):
        """Test the is_recent property."""
        # Newly created notification should be recent
        self.assertTrue(self.notification.is_recent)

        # Create an old notification
        old_notification = Notification.objects.create(
            user=self.user,
            notification_type=Notification.BOOKING,
            title="Old Notification",
            message="Old message",
        )

        # Manually set created_at to 2 days ago
        old_notification.created_at = timezone.now() - timedelta(days=2)
        old_notification.save()

        # Should not be recent
        self.assertFalse(old_notification.is_recent)

    def test_get_unread_count_for_user(self):
        """Test the get_unread_count_for_user class method."""
        # Initially should be 1 (the notification created in setUp)
        self.assertEqual(Notification.get_unread_count_for_user(self.user), 1)

        # Create another notification
        Notification.objects.create(
            user=self.user,
            notification_type=Notification.PAYMENT,
            title="Payment Notification",
            message="Payment message",
        )

        # Should now be 2
        self.assertEqual(Notification.get_unread_count_for_user(self.user), 2)

        # Mark one as read
        self.notification.mark_as_read()

        # Should now be 1
        self.assertEqual(Notification.get_unread_count_for_user(self.user), 1)

    def test_mark_all_as_read_for_user(self):
        """Test the mark_all_as_read_for_user class method."""
        # Create multiple notifications
        Notification.objects.create(
            user=self.user,
            notification_type=Notification.PAYMENT,
            title="Payment Notification",
            message="Payment message",
        )

        # Should have 2 unread notifications
        self.assertEqual(Notification.get_unread_count_for_user(self.user), 2)

        # Mark all as read
        Notification.mark_all_as_read_for_user(self.user)

        # Should now have 0 unread notifications
        self.assertEqual(Notification.get_unread_count_for_user(self.user), 0)


class AdminAnnouncementModelTest(TestCase):
    """Test the AdminAnnouncement model."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.ADMIN,
            is_staff=True,
        )

        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        self.announcement_data = {
            "title": "System Maintenance",
            "announcement_text": "The system will be under maintenance from 2-4 AM.",
            "target_audience": AdminAnnouncement.ALL_USERS,
            "created_by": self.admin_user,
        }

    def test_create_admin_announcement(self):
        """Test creating an admin announcement."""
        announcement = AdminAnnouncement.objects.create(**self.announcement_data)

        self.assertEqual(announcement.title, "System Maintenance")
        self.assertEqual(
            announcement.announcement_text,
            "The system will be under maintenance from 2-4 AM.",
        )
        self.assertEqual(announcement.target_audience, AdminAnnouncement.ALL_USERS)
        self.assertEqual(announcement.created_by, self.admin_user)
        self.assertEqual(announcement.status, AdminAnnouncement.PENDING)

    def test_admin_announcement_str_representation(self):
        """Test the string representation of AdminAnnouncement."""
        announcement = AdminAnnouncement.objects.create(**self.announcement_data)
        expected_str = (
            f"{announcement.title} - {announcement.get_target_audience_display()}"
        )
        self.assertEqual(str(announcement), expected_str)

    def test_target_audience_choices(self):
        """Test target audience choices."""
        valid_audiences = [
            AdminAnnouncement.ALL_USERS,
            AdminAnnouncement.CUSTOMERS,
            AdminAnnouncement.PROVIDERS,
            AdminAnnouncement.ADMINS,
        ]

        for audience in valid_audiences:
            announcement_data = self.announcement_data.copy()
            announcement_data["target_audience"] = audience
            announcement = AdminAnnouncement.objects.create(**announcement_data)
            self.assertEqual(announcement.target_audience, audience)

    def test_status_choices(self):
        """Test status choices."""
        announcement = AdminAnnouncement.objects.create(**self.announcement_data)

        # Test default status
        self.assertEqual(announcement.status, AdminAnnouncement.PENDING)

        # Test changing status
        announcement.status = AdminAnnouncement.SENT
        announcement.save()
        self.assertEqual(announcement.status, AdminAnnouncement.SENT)

    def test_can_be_sent_property(self):
        """Test the can_be_sent property."""
        announcement = AdminAnnouncement.objects.create(**self.announcement_data)

        # Initially should be True (pending status)
        self.assertTrue(announcement.can_be_sent)

        # Change status to sent
        announcement.status = AdminAnnouncement.SENT
        announcement.save()

        # Should now be False
        self.assertFalse(announcement.can_be_sent)

    def test_is_sent_property(self):
        """Test the is_sent property."""
        announcement = AdminAnnouncement.objects.create(**self.announcement_data)

        # Initially should be False
        self.assertFalse(announcement.is_sent)

        # Change status to sent
        announcement.status = AdminAnnouncement.SENT
        announcement.save()

        # Should now be True
        self.assertTrue(announcement.is_sent)

    def test_cancel_announcement(self):
        """Test cancelling an announcement."""
        announcement = AdminAnnouncement.objects.create(**self.announcement_data)

        # Should be able to cancel pending announcement
        result = announcement.cancel_announcement()
        self.assertTrue(result)
        self.assertEqual(announcement.status, AdminAnnouncement.CANCELLED)

        # Should not be able to cancel already cancelled announcement
        result = announcement.cancel_announcement()
        self.assertFalse(result)

    def test_get_pending_count(self):
        """Test the get_pending_count class method."""
        # Initially should be 0
        self.assertEqual(AdminAnnouncement.get_pending_count(), 0)

        # Create a pending announcement
        AdminAnnouncement.objects.create(**self.announcement_data)

        # Should now be 1
        self.assertEqual(AdminAnnouncement.get_pending_count(), 1)

        # Create a sent announcement
        sent_announcement_data = self.announcement_data.copy()
        sent_announcement_data["title"] = "Sent Announcement"
        sent_announcement_data["status"] = AdminAnnouncement.SENT
        AdminAnnouncement.objects.create(**sent_announcement_data)

        # Should still be 1 (only pending announcements counted)
        self.assertEqual(AdminAnnouncement.get_pending_count(), 1)


# End of test_models.py
