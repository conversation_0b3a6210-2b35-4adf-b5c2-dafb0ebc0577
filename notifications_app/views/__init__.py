"""View package for notifications_app.

This module exposes all view functions for backward compatibility."""

from .admin import (
    admin_create_announcement,
    admin_notification_dashboard,
    admin_notification_detail,
    admin_notification_list,
)

# --- Local Imports ---
from .common import get_client_ip, is_admin_user
from .user import (
    announcement_detail,
    bulk_mark_notifications_read,
    bulk_mark_notifications_unread,
    delete_notification,
    get_unread_notifications,
    mark_all_notifications_read,
    mark_all_notifications_unread,
    mark_notification_read,
    mark_notification_unread,
    notification_detail,
    notification_list,
    notification_preferences_view,
    test_view,
)

__all__ = [
    "get_client_ip",
    "is_admin_user",
    "notification_list",
    "notification_detail",
    "mark_notification_read",
    "mark_notification_unread",
    "delete_notification",
    "mark_all_notifications_read",
    "mark_all_notifications_unread",
    "bulk_mark_notifications_read",
    "bulk_mark_notifications_unread",
    "get_unread_notifications",
    "notification_preferences_view",
    "announcement_detail",
    "test_view",
    "admin_notification_dashboard",
    "admin_create_announcement",
    "admin_notification_list",
    "admin_notification_detail",
]
