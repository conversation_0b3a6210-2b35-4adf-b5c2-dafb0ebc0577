"""Common utilities and shared functions for notifications_app views."""

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model

# --- Local Imports ---
from ..models import AdminAnnouncement, Notification, NotificationPreference

# Import logging utilities
try:
    from utils.logging_utils import (
        get_app_logger,
        log_error,
        log_security_event,
        log_user_activity,
    )

    from ..logging_utils import (
        log_notification_created,
        log_notification_deleted,
        log_notification_delivery_status,
        log_notification_error,
        log_notification_preferences_updated,
        log_notification_read,
        log_notification_validation_error,
        log_notifications_marked_read,
        log_system_announcement_created,
        log_unauthorized_notification_access,
        performance_monitor,
    )

    LOGGING_ENABLED = True
except ImportError:  # pragma: no cover - fallback if logging utils missing
    LOGGING_ENABLED = False

    def log_notification_created(*args, **kwargs):
        pass

    def log_notification_read(*args, **kwargs):
        pass

    def log_notification_deleted(*args, **kwargs):
        pass

    def log_notifications_marked_read(*args, **kwargs):
        pass

    def log_notification_preferences_updated(*args, **kwargs):
        pass

    def log_system_announcement_created(*args, **kwargs):
        pass

    def log_notification_delivery_status(*args, **kwargs):
        pass

    def log_unauthorized_notification_access(*args, **kwargs):
        pass

    def log_notification_error(*args, **kwargs):
        pass

    def log_notification_validation_error(*args, **kwargs):
        pass

    def performance_monitor(operation_name):
        def decorator(func):
            return func

        return decorator

    def get_app_logger(app_name, logger_type=""):
        import logging

        return logging.getLogger(app_name)

    def log_user_activity(
        app_name, activity_type, user=None, request=None, details=None
    ):
        pass

    def log_error(
        app_name,
        error_type,
        error_message,
        user=None,
        request=None,
        exception=None,
        details=None,
    ):
        pass

    def log_security_event(
        app_name, event_type, user_email=None, request=None, details=None
    ):
        pass


# Get the user model
User = get_user_model()

# Get loggers
logger = get_app_logger("notifications_app")
activity_logger = get_app_logger("notifications_app", "activity")
security_logger = get_app_logger("notifications_app", "security")
error_logger = get_app_logger("notifications_app", "errors")


# ===== UTILITY FUNCTIONS =====


def is_admin_user(user):
    """Check if user is admin."""
    return user.is_authenticated and (
        user.is_superuser or getattr(user, "role", None) == "admin"
    )


def get_client_ip(request):
    """Get client IP address from request."""
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip
