"""
Management command to seed notifications_app with realistic test data.
Creates notifications and admin announcements.
"""

import random
from datetime import timed<PERSON><PERSON>

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone

from booking_cart_app.models import Booking
from notifications_app.models import AdminAnnouncement, Notification
from notifications_app.utils import create_notification
from payments_app.models import Payment
from review_app.models import Review

User = get_user_model()


class Command(BaseCommand):
    """Seed notifications_app with realistic test data."""

    help = "Seed notifications_app with notifications and announcements"

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing notification data before seeding",
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS("🌱 Starting notifications_app data seeding...")
        )

        if options["clear"]:
            self.clear_existing_data()

        with transaction.atomic():
            self.create_booking_notifications()
            self.create_review_notifications()
            self.create_payment_notifications()
            self.create_system_notifications()
            self.create_admin_announcements()

        self.stdout.write(
            self.style.SUCCESS(
                "✅ Notifications app data seeding completed successfully!"
            )
        )

    def clear_existing_data(self):
        """Clear existing notification data."""
        self.stdout.write("🧹 Clearing existing notification data...")

        try:
            Notification.objects.all().delete()
            AdminAnnouncement.objects.all().delete()
            self.stdout.write("   ✅ Existing notification data cleared")
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"   ⚠️ Warning during data clearing: {str(e)}")
            )

    def create_booking_notifications(self):
        """Create booking-related notifications."""
        self.stdout.write("📅 Creating booking notifications...")

        bookings = list(Booking.objects.all())

        if not bookings:
            self.stdout.write("   ⚠️ No bookings found, skipping booking notifications")
            return

        notification_count = 0

        for booking in bookings:
            # Customer notifications
            if booking.status == Booking.CONFIRMED:
                create_notification(
                    user=booking.customer,
                    notification_type=Notification.BOOKING,
                    title="Booking Confirmed",
                    message=f"Your booking at {booking.venue.venue_name} has been confirmed.",
                    related_object_type="Booking",
                    related_object_id=booking.id,
                )
                notification_count += 1

                # Provider notification
                create_notification(
                    user=booking.venue.service_provider.user,
                    notification_type=Notification.BOOKING,
                    title="New Booking Received",
                    message=f"You have a new booking from {booking.customer.email}.",
                    related_object_type="Booking",
                    related_object_id=booking.id,
                )
                notification_count += 1

            elif booking.status == Booking.CANCELLED:
                create_notification(
                    user=booking.customer,
                    notification_type=Notification.BOOKING,
                    title="Booking Cancelled",
                    message=f"Your booking at {booking.venue.venue_name} has been cancelled.",
                    related_object_type="Booking",
                    related_object_id=booking.id,
                )
                notification_count += 1

                # Provider notification
                create_notification(
                    user=booking.venue.service_provider.user,
                    notification_type=Notification.BOOKING,
                    title="Booking Cancelled",
                    message=f"Booking from {booking.customer.email} has been cancelled.",
                    related_object_type="Booking",
                    related_object_id=booking.id,
                )
                notification_count += 1

            elif booking.status == Booking.COMPLETED:
                create_notification(
                    user=booking.customer,
                    notification_type=Notification.BOOKING,
                    title="Service Completed",
                    message=f"Thank you for visiting {booking.venue.venue_name}! Please consider leaving a review.",
                    related_object_type="Booking",
                    related_object_id=booking.id,
                )
                notification_count += 1

        self.stdout.write(f"   ✅ Created {notification_count} booking notifications")

    def create_review_notifications(self):
        """Create review-related notifications."""
        self.stdout.write("⭐ Creating review notifications...")

        reviews = list(Review.objects.filter(is_approved=True))

        if not reviews:
            self.stdout.write(
                "   ⚠️ No approved reviews found, skipping review notifications"
            )
            return

        notification_count = 0

        for review in reviews:
            # Notify provider about new review
            create_notification(
                user=review.venue.service_provider.user,
                notification_type=Notification.REVIEW,
                title="New Review Received",
                message=f"You received a {review.rating}-star review from {review.customer.email}.",
                related_object_type="Review",
                related_object_id=review.id,
            )
            notification_count += 1

            # If review has a response, notify customer
            if hasattr(review, "response"):
                create_notification(
                    user=review.customer,
                    notification_type=Notification.REVIEW,
                    title="Provider Responded to Your Review",
                    message=f"{review.venue.venue_name} has responded to your review.",
                    related_object_type="Review",
                    related_object_id=review.id,
                )
                notification_count += 1

        self.stdout.write(f"   ✅ Created {notification_count} review notifications")

    def create_payment_notifications(self):
        """Create payment-related notifications."""
        self.stdout.write("💳 Creating payment notifications...")

        payments = list(Payment.objects.all())

        if not payments:
            self.stdout.write("   ⚠️ No payments found, skipping payment notifications")
            return

        notification_count = 0

        for payment in payments:
            if payment.payment_status == Payment.SUCCEEDED:
                # Customer notification
                create_notification(
                    user=payment.customer,
                    notification_type=Notification.PAYMENT,
                    title="Payment Successful",
                    message=f"Your payment of ${payment.amount_paid} has been processed successfully.",
                    related_object_type="Payment",
                    related_object_id=payment.id,
                )
                notification_count += 1

                # Provider notification
                create_notification(
                    user=payment.provider,
                    notification_type=Notification.PAYMENT,
                    title="Payment Received",
                    message=f"You received a payment of ${payment.amount_paid} from {payment.customer.email}.",
                    related_object_type="Payment",
                    related_object_id=payment.id,
                )
                notification_count += 1

            elif payment.payment_status == Payment.FAILED:
                create_notification(
                    user=payment.customer,
                    notification_type=Notification.PAYMENT,
                    title="Payment Failed",
                    message=f"Your payment of ${payment.amount_paid} could not be processed. Please try again.",
                    related_object_type="Payment",
                    related_object_id=payment.id,
                )
                notification_count += 1

            elif payment.payment_status in [
                Payment.REFUNDED,
                Payment.PARTIALLY_REFUNDED,
            ]:
                refund_amount = payment.refunded_amount
                create_notification(
                    user=payment.customer,
                    notification_type=Notification.PAYMENT,
                    title="Refund Processed",
                    message=f"A refund of ${refund_amount} has been processed to your account.",
                    related_object_type="Payment",
                    related_object_id=payment.id,
                )
                notification_count += 1

        self.stdout.write(f"   ✅ Created {notification_count} payment notifications")

    def create_system_notifications(self):
        """Create system and promotional notifications."""
        self.stdout.write("🔔 Creating system notifications...")

        users = list(User.objects.filter(is_active=True))

        if not users:
            self.stdout.write(
                "   ⚠️ No active users found, skipping system notifications"
            )
            return

        system_notifications = [
            {
                "title": "Welcome to CozyWish!",
                "message": "Thank you for joining CozyWish. Discover amazing spa and wellness services near you.",
                "type": Notification.SYSTEM,
                "user_filter": "customers",
            },
            {
                "title": "Profile Setup Complete",
                "message": "Your provider profile has been set up successfully. Start adding your services!",
                "type": Notification.SYSTEM,
                "user_filter": "providers",
            },
            {
                "title": "New Features Available",
                "message": "Check out our new booking features and enhanced search capabilities.",
                "type": Notification.SYSTEM,
                "user_filter": "all",
            },
            {
                "title": "Weekend Special Offers",
                "message": "Don't miss our weekend special offers! Up to 25% off selected services.",
                "type": Notification.SYSTEM,
                "user_filter": "customers",
            },
            {
                "title": "Provider Dashboard Updates",
                "message": "New analytics and reporting features are now available in your dashboard.",
                "type": Notification.SYSTEM,
                "user_filter": "providers",
            },
        ]

        notification_count = 0

        for notif_data in system_notifications:
            # Filter users based on notification target
            if notif_data["user_filter"] == "customers":
                target_users = [u for u in users if u.role == User.CUSTOMER]
            elif notif_data["user_filter"] == "providers":
                target_users = [u for u in users if u.role == User.SERVICE_PROVIDER]
            else:
                target_users = users

            # Send to random subset of users (simulate gradual rollout)
            num_recipients = max(1, len(target_users) // 3)
            recipients = random.sample(
                target_users, min(num_recipients, len(target_users))
            )

            for user in recipients:
                create_notification(
                    user=user,
                    notification_type=notif_data["type"],
                    title=notif_data["title"],
                    message=notif_data["message"],
                )
                notification_count += 1

        self.stdout.write(f"   ✅ Created {notification_count} system notifications")

    def create_admin_announcements(self):
        """Create admin announcements."""
        self.stdout.write("📢 Creating admin announcements...")

        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.filter(role=User.ADMIN).first()

        if not admin_user:
            self.stdout.write("   ⚠️ No admin user found, skipping announcements")
            return

        announcements = [
            {
                "title": "Platform Maintenance Scheduled",
                "announcement_text": "We will be performing scheduled maintenance on Sunday from 2 AM to 4 AM EST. The platform may be temporarily unavailable during this time.",
                "target_audience": AdminAnnouncement.ALL_USERS,
                "status": AdminAnnouncement.PENDING,
            },
            {
                "title": "New Provider Onboarding Process",
                "announcement_text": "We have updated our provider onboarding process to make it easier and faster. New providers can now get approved within 24 hours.",
                "target_audience": AdminAnnouncement.PROVIDERS,
                "status": AdminAnnouncement.SENT,
            },
            {
                "title": "Holiday Season Promotions",
                "announcement_text": "Special holiday promotions are now live! Customers can enjoy up to 30% off on selected spa services throughout December.",
                "target_audience": AdminAnnouncement.CUSTOMERS,
                "status": AdminAnnouncement.SENT,
            },
            {
                "title": "Security Enhancement Notice",
                "announcement_text": "We have implemented additional security measures to protect your account. Please update your password if you haven't done so recently.",
                "target_audience": AdminAnnouncement.ALL_USERS,
                "status": AdminAnnouncement.CANCELLED,
            },
        ]

        for announcement_data in announcements:
            announcement = AdminAnnouncement.objects.create(
                created_by=admin_user, **announcement_data
            )

            status_emoji = {
                AdminAnnouncement.PENDING: "⏳",
                AdminAnnouncement.SENT: "✅",
                AdminAnnouncement.CANCELLED: "❌",
            }.get(announcement.status, "❓")
            self.stdout.write(
                f"   {status_emoji} {announcement.get_status_display()}: {announcement.title}"
            )

        self.stdout.write(f"   ✅ Created {len(announcements)} admin announcements")
