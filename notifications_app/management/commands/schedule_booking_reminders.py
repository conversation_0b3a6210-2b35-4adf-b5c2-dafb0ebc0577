"""Management command to schedule booking reminder notifications."""

# --- Standard Library Imports ---
from datetime import timedelta

# --- Third-Party Imports ---
from django.core.management.base import BaseCommand
from django.utils import timezone

# --- Local Imports ---
from notifications_app.tasks import schedule_booking_reminders


class Command(BaseCommand):
    """Schedule booking reminder notifications for upcoming appointments."""

    help = "Schedule 2-hour reminder notifications for upcoming bookings"

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be scheduled without actually scheduling",
        )
        parser.add_argument(
            "--hours-ahead",
            type=int,
            default=2,
            help="Hours before appointment to send reminder (default: 2)",
        )

    def handle(self, *args, **options):
        """Execute the command."""
        dry_run = options["dry_run"]
        hours_ahead = options["hours_ahead"]

        self.stdout.write(
            self.style.SUCCESS(
                f"🔔 Scheduling booking reminders ({hours_ahead} hours ahead)..."
            )
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    "   ⚠️ DRY RUN MODE - No actual scheduling will occur"
                )
            )

        try:
            if dry_run:
                # In dry run mode, show what would be scheduled
                from booking_cart_app.models import Booking

                now = timezone.now()
                reminder_start = now + timedelta(hours=hours_ahead)
                reminder_end = now + timedelta(hours=hours_ahead + 1)

                bookings_to_remind = Booking.objects.filter(
                    status__in=[Booking.CONFIRMED, Booking.PENDING],
                    items__scheduled_date=reminder_start.date(),
                    items__scheduled_time__range=(
                        reminder_start.time(),
                        reminder_end.time(),
                    ),
                ).distinct()

                self.stdout.write(
                    f"   📅 Found {bookings_to_remind.count()} bookings that would need reminders"
                )

                for booking in bookings_to_remind:
                    next_appointment = (
                        booking.items.filter(scheduled_date__gte=now.date())
                        .order_by("scheduled_date", "scheduled_time")
                        .first()
                    )

                    if next_appointment:
                        appointment_time = timezone.make_aware(
                            timezone.datetime.combine(
                                next_appointment.scheduled_date,
                                next_appointment.scheduled_time,
                            )
                        )
                        reminder_time = appointment_time - timedelta(hours=hours_ahead)

                        self.stdout.write(
                            f"     • Booking {booking.friendly_id or booking.booking_id} "
                            f"for {booking.customer.email} at {booking.venue.venue_name} "
                            f'(reminder would be sent at {reminder_time.strftime("%Y-%m-%d %H:%M")})'
                        )

                scheduled_count = bookings_to_remind.count()

            else:
                # Actually schedule the reminders
                scheduled_count = schedule_booking_reminders.delay().get()

            if scheduled_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'   ✅ {"Would schedule" if dry_run else "Scheduled"} '
                        f"{scheduled_count} booking reminder(s)"
                    )
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'   ℹ️ No booking reminders {"would be" if dry_run else "were"} scheduled '
                        f"(no appointments found in the {hours_ahead}-hour window)"
                    )
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"   ❌ Error scheduling booking reminders: {str(e)}")
            )
            raise

        self.stdout.write(
            self.style.SUCCESS(
                f'✅ Booking reminder scheduling {"simulation" if dry_run else "task"} completed!'
            )
        )
