"""Management command to purge stale notifications."""

# --- Standard Library Imports ---
from datetime import timedelta

# --- Third-Party Imports ---
from django.core.management.base import BaseCommand
from django.utils import timezone

# --- Local Imports ---
from notifications_app.models import Notification


class Command(BaseCommand):
    """Delete notifications older than a specified number of days."""

    help = "Purge notifications older than the given number of days"

    def add_arguments(self, parser):
        parser.add_argument(
            "--days", type=int, default=90, help="Number of days to keep notifications"
        )

    def handle(self, *args, **options):
        days = options["days"]
        cutoff = timezone.now() - timedelta(days=days)
        deleted, _ = Notification.objects.filter(created_at__lt=cutoff).delete()
        self.stdout.write(
            self.style.SUCCESS(
                f"Deleted {deleted} notifications older than {days} days."
            )
        )
