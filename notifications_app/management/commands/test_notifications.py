"""Management command to test notification functionality."""

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db import transaction

# --- Local Imports ---
from notifications_app.models import Notification
from notifications_app.utils import (
    create_notification,
    create_system_announcement,
    notify_new_booking,
)

# Get the user model
User = get_user_model()


class Command(BaseCommand):
    """Management command to test notification functionality."""

    help = "Test notification signals and functionality"

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            "--cleanup",
            action="store_true",
            help="Clean up test notifications after testing",
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS("Starting notification functionality tests...")
        )

        try:
            with transaction.atomic():
                self.test_basic_notification_creation()
                self.test_system_announcement()
                self.test_notification_utilities()

                if options["cleanup"]:
                    self.cleanup_test_data()

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error during testing: {str(e)}"))
            raise

        self.stdout.write(
            self.style.SUCCESS("All notification tests completed successfully!")
        )

    def test_basic_notification_creation(self):
        """Test basic notification creation."""
        self.stdout.write("Testing basic notification creation...")

        # Get or create a test user
        user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "role": "customer",
                "is_active": True,
                "first_name": "Test",
                "last_name": "User",
            },
        )

        # Create a test notification
        notification = create_notification(
            user=user,
            notification_type=Notification.SYSTEM,
            title="Test Notification",
            message="This is a test notification created by the management command.",
            related_object_id=1,
            related_object_type="TestCommand",
        )

        self.stdout.write(
            self.style.SUCCESS(
                f"✓ Created notification {notification.id} for {user.email}"
            )
        )

        # Test notification methods - ensure we can toggle read status
        notification.refresh_from_db()
        initial_read_status = notification.is_read

        # Test marking as read
        notification.mark_as_read()
        notification.refresh_from_db()
        self.assertEqual(notification.is_read, True)

        # Test marking as unread
        notification.mark_as_unread()
        notification.refresh_from_db()
        self.assertEqual(notification.is_read, False)

        self.stdout.write(
            self.style.SUCCESS("✓ Notification read/unread functionality working")
        )

    def test_system_announcement(self):
        """Test system announcement functionality."""
        self.stdout.write("Testing system announcement creation...")

        # Get an admin user or create one
        admin_user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "role": "admin",
                "is_active": True,
                "is_staff": True,
                "first_name": "Admin",
                "last_name": "User",
            },
        )

        # Count notifications before
        notifications_before = Notification.objects.count()

        # Create a system announcement (don't auto-send to avoid Celery issues)
        announcement = create_system_announcement(
            title="Test System Announcement",
            message="This is a test system announcement.",
            target_audience="all",
            created_by=admin_user,
            auto_send=False,
        )

        # Use synchronous sending for testing (avoid Celery issues)
        announcement.send_announcement_sync()

        # Count notifications after
        notifications_after = Notification.objects.count()
        new_notifications = notifications_after - notifications_before

        self.stdout.write(
            self.style.SUCCESS(
                f"✓ Created system announcement {announcement.id} "
                f"and {new_notifications} notifications"
            )
        )

    def test_notification_utilities(self):
        """Test notification utility functions."""
        self.stdout.write("Testing notification utility functions...")

        # Get a test user
        user = User.objects.filter(email="<EMAIL>").first()
        if not user:
            user = User.objects.create(
                email="<EMAIL>", role="customer", is_active=True
            )

        # Test unread count
        unread_count_before = Notification.get_unread_count_for_user(user)

        # Create some test notifications
        for i in range(3):
            create_notification(
                user=user,
                notification_type=Notification.SYSTEM,
                title=f"Test Notification {i+1}",
                message=f"This is test notification number {i+1}.",
                related_object_id=i
                + 2,  # Start from 2 to avoid conflict with first notification
                related_object_type="TestCommand",
            )

        unread_count_after = Notification.get_unread_count_for_user(user)

        self.stdout.write(
            self.style.SUCCESS(
                f"✓ Unread count: {unread_count_before} → {unread_count_after} "
                f"(+{unread_count_after - unread_count_before})"
            )
        )

        # Test mark all as read
        marked_count = Notification.mark_all_as_read_for_user(user)
        unread_count_final = Notification.get_unread_count_for_user(user)

        self.stdout.write(
            self.style.SUCCESS(
                f"✓ Marked {marked_count} notifications as read. "
                f"Final unread count: {unread_count_final}"
            )
        )

    def cleanup_test_data(self):
        """Clean up test data created during testing."""
        self.stdout.write("Cleaning up test data...")

        # Delete test notifications
        test_notifications = Notification.objects.filter(title__icontains="test")
        notification_count = test_notifications.count()
        test_notifications.delete()

        # Delete test users
        test_users = User.objects.filter(
            email__in=["<EMAIL>", "<EMAIL>"]
        )
        user_count = test_users.count()
        test_users.delete()

        self.stdout.write(
            self.style.SUCCESS(
                f"✓ Cleaned up {notification_count} notifications and {user_count} users"
            )
        )

    def assertEqual(self, first, second):
        """Simple assertion helper."""
        if first != second:
            raise AssertionError(f"{first} != {second}")
