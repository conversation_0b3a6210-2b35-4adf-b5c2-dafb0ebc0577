"""Celery tasks used by :mod:`notifications_app`."""

# --- Third-Party Imports ---
from datetime import datetime, timedelta

from celery import shared_task

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone

# --- Local Imports ---
from .models import AdminAnnouncement, Notification

User = get_user_model()


@shared_task
def send_admin_announcement_task(announcement_id):
    try:
        announcement = AdminAnnouncement.objects.get(id=announcement_id)
    except AdminAnnouncement.DoesNotExist:
        return 0

    target_users = announcement.get_target_users()
    notifications = [
        Notification(
            user=user,
            notification_type=Notification.ANNOUNCEMENT,
            title=announcement.title,
            message=announcement.announcement_text,
            related_object_id=announcement.id,
            related_object_type="AdminAnnouncement",
        )
        for user in target_users
    ]
    Notification.objects.bulk_create(notifications)

    announcement.status = AdminAnnouncement.SENT
    announcement.sent_at = timezone.now()
    announcement.total_recipients = len(notifications)
    announcement.save(update_fields=["status", "sent_at", "total_recipients"])
    return announcement.total_recipients


@shared_task
def send_daily_digest_task(user_id):
    """Send a daily notification digest email to a user."""
    try:
        user = User.objects.get(id=user_id, is_active=True)
    except User.DoesNotExist:
        return 0

    since = timezone.now() - timezone.timedelta(days=1)
    notifications = Notification.objects.filter(
        user=user,
        created_at__gte=since,
        read_status=Notification.UNREAD,
    ).order_by("-created_at")

    count = notifications.count()
    if count == 0:
        return 0

    context = {
        "user": user,
        "notifications": notifications,
        "count": count,
    }
    message = render_to_string("notifications_app/emails/digest_email.txt", context)

    send_mail(
        subject="CozyWish Daily Notification Digest",
        message=message,
        from_email=settings.DEFAULT_FROM_EMAIL,
        recipient_list=[user.email],
        fail_silently=False,
    )

    return count


@shared_task
def send_booking_reminder_task(booking_id):
    """Send booking reminder notification 2 hours before appointment."""
    try:
        from django.urls import reverse

        from booking_cart_app.models import Booking

        from .utils import create_notification, send_notification_email_async

        booking = Booking.objects.get(id=booking_id)

        # Check if booking is still active (not cancelled, completed, etc.)
        if booking.status not in [Booking.CONFIRMED, Booking.PENDING]:
            return 0

        # Get upcoming appointment time
        upcoming_items = booking.items.filter(
            scheduled_date__gte=timezone.now().date()
        ).order_by("scheduled_date", "scheduled_time")

        if not upcoming_items.exists():
            return 0

        next_appointment = upcoming_items.first()
        appointment_datetime = timezone.make_aware(
            datetime.combine(
                next_appointment.scheduled_date, next_appointment.scheduled_time
            )
        )

        # Double-check timing (should be about 2 hours before)
        time_until_appointment = appointment_datetime - timezone.now()
        if time_until_appointment.total_seconds() < 0:
            # Appointment has passed
            return 0

        # Send reminder to customer
        customer_title = "Appointment Reminder"
        customer_message = (
            f"This is a reminder that you have an appointment at {booking.venue.venue_name} "
            f"in 2 hours ({next_appointment.scheduled_time.strftime('%I:%M %p')} on "
            f"{next_appointment.scheduled_date.strftime('%B %d, %Y')}). "
            f"Booking ID: {booking.friendly_id or booking.booking_id}."
        )
        customer_action_url = reverse(
            "booking_cart_app:booking_detail", args=[booking.slug]
        )

        create_notification(
            user=booking.customer,
            notification_type=Notification.BOOKING,
            title=customer_title,
            message=customer_message,
            related_object_id=booking.id,
            related_object_type="Booking",
            action_url=customer_action_url,
        )

        send_notification_email_async(
            booking.customer, customer_title, customer_message, customer_action_url
        )

        # Send reminder to service provider
        provider = booking.venue.service_provider.user
        provider_title = "Upcoming Appointment"
        provider_message = (
            f"You have an upcoming appointment with {booking.customer.get_full_name() or booking.customer.email} "
            f"in 2 hours ({next_appointment.scheduled_time.strftime('%I:%M %p')} on "
            f"{next_appointment.scheduled_date.strftime('%B %d, %Y')}). "
            f"Service: {next_appointment.service_title}. "
            f"Booking ID: {booking.friendly_id or booking.booking_id}."
        )
        provider_action_url = reverse(
            "booking_cart_app:provider_booking_detail", args=[booking.slug]
        )

        create_notification(
            user=provider,
            notification_type=Notification.BOOKING,
            title=provider_title,
            message=provider_message,
            related_object_id=booking.id,
            related_object_type="Booking",
            action_url=provider_action_url,
        )

        send_notification_email_async(
            provider, provider_title, provider_message, provider_action_url
        )

        return 2  # Sent to 2 users (customer + provider)

    except Exception as e:
        # Log error but don't raise to avoid breaking the queue
        import logging

        logger = logging.getLogger("notifications_app.tasks")
        logger.error(
            f"Error sending booking reminder for booking {booking_id}: {str(e)}"
        )
        return 0


@shared_task
def schedule_booking_reminders():
    """
    Scheduled task to find bookings that need 2-hour reminders and schedule them.
    Should be run every hour.
    """
    try:
        from celery import current_app

        from booking_cart_app.models import Booking

        # Find bookings with appointments in 2-3 hours (to account for scheduling delays)
        now = timezone.now()
        reminder_start = now + timedelta(hours=2)
        reminder_end = now + timedelta(hours=3)

        # Get bookings with appointments in the reminder window
        bookings_to_remind = Booking.objects.filter(
            status__in=[Booking.CONFIRMED, Booking.PENDING],
            items__scheduled_date=reminder_start.date(),
            items__scheduled_time__range=(reminder_start.time(), reminder_end.time()),
        ).distinct()

        scheduled_count = 0
        for booking in bookings_to_remind:
            # Calculate exact reminder time (2 hours before first appointment)
            next_appointment = (
                booking.items.filter(scheduled_date__gte=now.date())
                .order_by("scheduled_date", "scheduled_time")
                .first()
            )

            if next_appointment:
                appointment_datetime = timezone.make_aware(
                    datetime.combine(
                        next_appointment.scheduled_date, next_appointment.scheduled_time
                    )
                )
                reminder_time = appointment_datetime - timedelta(hours=2)

                # Only schedule if reminder time is in the future
                if reminder_time > now:
                    # Check if reminder already scheduled (avoid duplicates)
                    scheduled_tasks = current_app.control.inspect().scheduled()
                    task_id = f"booking_reminder_{booking.id}"

                    # Schedule the reminder
                    send_booking_reminder_task.apply_async(
                        args=[booking.id], eta=reminder_time, task_id=task_id
                    )
                    scheduled_count += 1

        return scheduled_count

    except Exception as e:
        import logging

        logger = logging.getLogger("notifications_app.tasks")
        logger.error(f"Error scheduling booking reminders: {str(e)}")
        return 0
