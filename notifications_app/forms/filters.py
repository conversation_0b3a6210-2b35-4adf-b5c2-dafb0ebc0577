"""Forms for filtering notifications in views."""

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from ..models import Notification


class NotificationFilterForm(forms.Form):
    """Form for filtering notifications in views."""

    notification_type = forms.ChoiceField(
        choices=[("", "All Types")] + Notification.TYPE_CHOICES,
        required=False,
        label=_("Type"),
        widget=forms.Select(attrs={"class": "form-select"}),
    )

    read_status = forms.ChoiceField(
        choices=[("", "All"), ("unread", "Unread Only"), ("read", "Read Only")],
        required=False,
        label=_("Status"),
        widget=forms.Select(attrs={"class": "form-select"}),
    )

    date_from = forms.DateField(
        required=False,
        label=_("From Date"),
        widget=forms.DateInput(attrs={"class": "form-control", "type": "date"}),
    )

    date_to = forms.DateField(
        required=False,
        label=_("To Date"),
        widget=forms.DateInput(attrs={"class": "form-control", "type": "date"}),
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get("date_from")
        date_to = cleaned_data.get("date_to")

        if date_from and date_to and date_from > date_to:
            raise ValidationError(_("From date cannot be later than to date."))

        return cleaned_data
