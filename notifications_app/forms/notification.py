"""Forms for creating notifications."""

# --- Third-Party Imports ---
from django import forms
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from ..models import Notification

User = get_user_model()


class NotificationForm(forms.ModelForm):
    """Form for creating and editing notifications."""

    user = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        label=_("Recipient"),
        widget=forms.Select(attrs={"class": "form-select"}),
        help_text=_("Select the user who will receive this notification"),
    )

    notification_type = forms.ChoiceField(
        choices=Notification.TYPE_CHOICES,
        label=_("Notification Type"),
        widget=forms.Select(attrs={"class": "form-select"}),
        help_text=_("Type of notification"),
    )

    title = forms.CharField(
        label=_("Title"),
        max_length=255,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": "Enter notification title",
                "minlength": "3",
            }
        ),
        help_text=_("Brief title for the notification"),
    )

    message = forms.CharField(
        label=_("Message"),
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": "Enter notification message",
                "minlength": "10",
            }
        ),
        help_text=_("Detailed message content"),
    )

    action_url = forms.URLField(
        label=_("Action URL"),
        required=False,
        widget=forms.URLInput(
            attrs={"class": "form-control", "placeholder": "https://example.com/action"}
        ),
        help_text=_("Optional URL for notification action"),
    )

    class Meta:
        model = Notification
        fields = ["user", "notification_type", "title", "message", "action_url"]

    def clean_title(self):
        title = self.cleaned_data.get("title")
        if title and len(title.strip()) < 3:
            raise ValidationError(_("Title must be at least 3 characters long."))
        return title.strip() if title else title

    def clean_message(self):
        message = self.cleaned_data.get("message")
        if message and len(message.strip()) < 10:
            raise ValidationError(_("Message must be at least 10 characters long."))
        return message.strip() if message else message


class BulkNotificationForm(forms.Form):
    """Form for sending bulk notifications to multiple users."""

    target_users = forms.ModelMultipleChoiceField(
        queryset=User.objects.filter(is_active=True),
        label=_("Recipients"),
        widget=forms.CheckboxSelectMultiple(attrs={"class": "form-check-input"}),
        help_text=_("Select users who will receive this notification"),
    )

    notification_type = forms.ChoiceField(
        choices=Notification.TYPE_CHOICES,
        label=_("Notification Type"),
        widget=forms.Select(attrs={"class": "form-select"}),
    )

    title = forms.CharField(
        label=_("Title"),
        max_length=255,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": "Enter notification title",
                "minlength": "3",
            }
        ),
    )

    message = forms.CharField(
        label=_("Message"),
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": "Enter notification message",
                "minlength": "10",
            }
        ),
    )

    def clean_target_users(self):
        users = self.cleaned_data.get("target_users")
        if not users:
            raise ValidationError(_("Please select at least one recipient."))
        if len(users) > 100:
            raise ValidationError(_("Cannot send to more than 100 users at once."))
        return users

    def clean_title(self):
        title = self.cleaned_data.get("title")
        if title and len(title.strip()) < 3:
            raise ValidationError(_("Title must be at least 3 characters long."))
        return title.strip() if title else title

    def clean_message(self):
        message = self.cleaned_data.get("message")
        if message and len(message.strip()) < 10:
            raise ValidationError(_("Message must be at least 10 characters long."))
        return message.strip() if message else message
