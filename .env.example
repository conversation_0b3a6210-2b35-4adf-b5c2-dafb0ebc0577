# CozyWish Environment Configuration Template
# ==========================================
# Copy this file to .env and fill in your values
# This file shows all available environment variables

# ===== ENVIRONMENT SELECTION =====
# Determines which settings module to use
# Options: development, production, staging, testing
# Default: development
DJANGO_ENVIRONMENT=development

# ===== CORE DJANGO SETTINGS =====
# Required: Django secret key for cryptographic signing
# Generate with: python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
SECRET_KEY=your-secret-key-here

# Debug mode (True for development, False for production)
# Default: False
DEBUG=True

# ===== DATABASE CONFIGURATION =====
# Database URL for PostgreSQL (leave empty for SQLite in development)
# Format: postgres://user:password@host:port/database
# Example: postgres://cozywish:password@localhost:5432/cozywish_db
DATABASE_URL=

# ===== EMAIL CONFIGURATION (SendGrid) =====
# SMTP server settings
EMAIL_HOST=smtp.sendgrid.net
EMAIL_HOST_USER=apikey
EMAIL_HOST_PASSWORD=your-sendgrid-api-key-here
EMAIL_PORT=587
EMAIL_USE_TLS=True

# Email addresses
DEFAULT_FROM_EMAIL=<EMAIL>
SERVER_EMAIL=<EMAIL>

# Force email sending in development (True/False)
# Set to True to test real email sending in development
FORCE_EMAIL_BACKEND=False

# ===== AWS S3 CONFIGURATION =====
# Required for production file storage
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=your-cloudfront-domain.cloudfront.net

# ===== REDIS CONFIGURATION =====
# Redis URLs for caching and Celery
# Format: redis://host:port/db
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# ===== APPLICATION SETTINGS =====
# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Platform fee rate (decimal, e.g., 0.05 for 5%)
PLATFORM_FEE_RATE=0.05

# Cache timeouts (in seconds)
DASHBOARD_CACHE_TIMEOUT=300
NOTIFICATION_CACHE_TIMEOUT=60

# ===== DEPLOYMENT SETTINGS =====
# External hostname for Render deployment
RENDER_EXTERNAL_HOSTNAME=

# Web server concurrency
WEB_CONCURRENCY=4

# ===== SECURITY SETTINGS =====
# Additional allowed hosts (comma-separated)
ADDITIONAL_ALLOWED_HOSTS=

# Additional CSRF trusted origins (comma-separated)
ADDITIONAL_CSRF_ORIGINS=

# ===== DEVELOPMENT SETTINGS =====
# Enable Django Debug Toolbar (True/False)
ENABLE_DEBUG_TOOLBAR=False

# ===== MONITORING & ANALYTICS =====
# Sentry DSN for error tracking
SENTRY_DSN=

# Google Analytics tracking ID
GA_TRACKING_ID=

# ===== THIRD-PARTY INTEGRATIONS =====
# Stripe API keys for payments
STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

# Social authentication keys
GOOGLE_OAUTH2_CLIENT_ID=
GOOGLE_OAUTH2_CLIENT_SECRET=
FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=
