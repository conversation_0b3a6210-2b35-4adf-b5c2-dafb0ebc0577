/*
 * CozyWish Notifications App - Professional Animations & Micro-interactions
 * Black & White Design Theme with Smooth Transitions
 */

/* ===== GLOBAL ANIMATION VARIABLES ===== */
:root {
    --animation-fast: 0.2s;
    --animation-normal: 0.3s;
    --animation-slow: 0.5s;
    --easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --easing-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* ===== LOADING ANIMATIONS ===== */
@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100px);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(0, 0, 0, 0.1); }
    50% { box-shadow: 0 0 20px rgba(0, 0, 0, 0.2), 0 0 30px rgba(0, 0, 0, 0.1); }
}

/* ===== NOTIFICATION CARD ANIMATIONS ===== */
.notification-card {
    animation: fadeInUp var(--animation-normal) var(--easing-smooth);
}

.notification-card.removing {
    animation: slideOutRight var(--animation-normal) var(--easing-smooth) forwards;
}

.notification-card:hover {
    animation: none;
    transition: all var(--animation-normal) var(--easing-smooth);
}

.notification-card.unread {
    animation: fadeInScale var(--animation-slow) var(--easing-bounce);
}

/* ===== BUTTON ANIMATIONS ===== */
.btn, .action-btn, .quick-action-btn {
    position: relative;
    overflow: hidden;
    transition: all var(--animation-normal) var(--easing-smooth);
}

.btn:hover, .action-btn:hover, .quick-action-btn:hover {
    animation: pulse var(--animation-fast) ease-in-out;
}

.btn:active, .action-btn:active, .quick-action-btn:active {
    transform: scale(0.98);
    transition: transform var(--animation-fast) var(--easing-smooth);
}

/* Ripple effect for buttons */
.btn::after, .action-btn::after, .quick-action-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width var(--animation-normal) ease, height var(--animation-normal) ease;
}

.btn:active::after, .action-btn:active::after, .quick-action-btn:active::after {
    width: 300px;
    height: 300px;
}

/* ===== ICON ANIMATIONS ===== */
.notification-icon, .notification-item-icon, .category-icon {
    transition: all var(--animation-normal) var(--easing-smooth);
}

.notification-icon:hover, .notification-item-icon:hover, .category-icon:hover {
    animation: bounce var(--animation-slow) var(--easing-bounce);
}

/* ===== TOGGLE SWITCH ANIMATIONS ===== */
.toggle-slider {
    transition: all var(--animation-normal) var(--easing-smooth);
}

.toggle-slider:before {
    transition: all var(--animation-normal) var(--easing-elastic);
}

.toggle-switch:hover .toggle-slider {
    animation: glow 1s ease-in-out infinite;
}

/* ===== BADGE ANIMATIONS ===== */
.badge, .category-badge, .unread-count-badge {
    transition: all var(--animation-normal) var(--easing-smooth);
}

.badge.new, .unread-count-badge {
    animation: bounce var(--animation-slow) var(--easing-bounce);
}

/* ===== DROPDOWN ANIMATIONS ===== */
.dropdown-menu {
    animation: fadeInScale var(--animation-normal) var(--easing-smooth);
    transform-origin: top right;
}

.professional-notification-item {
    transition: all var(--animation-normal) var(--easing-smooth);
}

.professional-notification-item:hover {
    animation: slideInRight var(--animation-fast) var(--easing-smooth);
}

/* ===== FORM ANIMATIONS ===== */
.form-control, .form-select {
    transition: all var(--animation-normal) var(--easing-smooth);
}

.form-control:focus, .form-select:focus {
    animation: glow var(--animation-slow) ease-in-out;
}

/* ===== LOADING STATES ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== NOTIFICATION SPECIFIC ANIMATIONS ===== */
.notification-new {
    animation: fadeInScale var(--animation-slow) var(--easing-bounce);
}

.notification-read {
    animation: fadeInUp var(--animation-normal) var(--easing-smooth);
}

.notification-deleted {
    animation: slideOutRight var(--animation-normal) var(--easing-smooth) forwards;
}

/* ===== STAGGER ANIMATIONS ===== */
.notification-list .notification-card:nth-child(1) { animation-delay: 0.1s; }
.notification-list .notification-card:nth-child(2) { animation-delay: 0.2s; }
.notification-list .notification-card:nth-child(3) { animation-delay: 0.3s; }
.notification-list .notification-card:nth-child(4) { animation-delay: 0.4s; }
.notification-list .notification-card:nth-child(5) { animation-delay: 0.5s; }

/* ===== HOVER EFFECTS ===== */
.hover-lift {
    transition: transform var(--animation-normal) var(--easing-smooth);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-scale {
    transition: transform var(--animation-normal) var(--easing-smooth);
}

.hover-scale:hover {
    transform: scale(1.02);
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== RESPONSIVE ANIMATIONS ===== */
@media (max-width: 768px) {
    :root {
        --animation-fast: 0.15s;
        --animation-normal: 0.25s;
        --animation-slow: 0.4s;
    }

    .notification-card {
        animation-duration: var(--animation-fast);
    }
}
