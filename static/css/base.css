/**
 * Base Template Styles
 * Professional typography and layout styles for CozyWish base template
 */

/* CSS Custom Properties (Variables) */
:root {
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segue UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --color-primary: black;
    --color-secondary: black;
    --color-success: black;
    --color-danger: black;
    --color-warning: black;
    --color-info: black;
    --color-light: white;
    --color-dark: black;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;

    /* CozyWish Design System Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;
    --cw-secondary-950: #2f1f18;
    --cw-neutral-600: #525252;
    --cw-neutral-200: #e5e5e5;
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Global Typography */
body {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: -0.01em;
    color: var(--color-dark);
}

/* Heading Styles */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.3;
    margin-bottom: 0.75rem;
}

/* Navigation Brand */
.navbar-brand {
    font-family: var(--font-heading);
    font-weight: 700;
    letter-spacing: -0.02em;
    font-size: 1.5rem;
    color: var(--color-primary) !important;
    text-decoration: none;
    transition: var(--transition);
}

.navbar-brand:hover {
    color: var(--color-primary) !important;
    opacity: 0.8;
}

/* Button Styles */
.btn {
    font-family: var(--font-primary);
    font-weight: 500;
    letter-spacing: 0.025em;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

/* Dropdown Styles */
.dropdown-item {
    font-family: var(--font-primary);
    font-weight: 400;
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: white;
    color: black;
}

.dropdown-item i {
    width: 1.25rem;
    text-align: center;
}

/* Enhanced Navigation Specific Styles */
.navbar {
    padding: 1rem 0;
    transition: var(--transition);
    font-family: var(--font-primary);
}

.navbar-expand-lg .navbar-nav .nav-link {
    padding: 0.75rem 1rem;
    font-weight: 600;
    letter-spacing: 0.025em;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
}

.navbar-expand-lg .navbar-nav .nav-link:hover {
    background: rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
}

.nav-buttons .btn {
    margin-left: 0.5rem;
}

.nav-buttons .btn:first-child {
    margin-left: 0;
}

/* Hero Section Styles */
.radial-gradient {
    background: white;
    min-height: 100vh;
    position: relative;
}

.radial-gradient .navbar {
    background: white;
}

.radial-gradient .navbar-brand,
.radial-gradient .btn {
    color: black !important;
}

/* Toast Notifications */
.toast-container {
    z-index: 1100;
}

.toast {
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Main Content */
main {
    min-height: calc(100vh - 200px);
}

/* Footer Styles - CozyWish Design System */
.footer-cw {
    padding: 4rem 0 2rem;
    margin-top: 4rem;
    border-top: 1px solid var(--cw-neutral-200) !important;
    background-color: white;
}

.footer-cw h5,
.footer-cw h6 {
    font-family: var(--font-heading);
    font-weight: 600;
}

.footer-cw .footer-link {
    transition: var(--transition);
    font-size: 0.9rem;
    line-height: 1.6;
}

.footer-cw .footer-link:hover {
    color: var(--cw-brand-primary) !important;
    text-decoration: underline !important;
}

.footer-cw .social-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-color: var(--cw-neutral-600);
    color: var(--cw-neutral-600);
    transition: var(--transition);
}

.footer-cw .social-icon:hover {
    background-color: var(--cw-brand-primary);
    border-color: var(--cw-brand-primary);
    color: white;
    transform: translateY(-2px);
}

.footer-cw .contact-info {
    font-size: 0.875rem;
    line-height: 1.5;
}

.footer-cw .contact-info i {
    color: var(--cw-brand-primary);
    width: 16px;
}

/* CozyWish Design System Text Classes */
.text-secondary-cw {
    color: var(--cw-secondary-950) !important;
}

.text-neutral-cw {
    color: var(--cw-neutral-600) !important;
}

/* Responsive Footer Adjustments */
@media (max-width: 768px) {
    .footer-cw {
        padding: 3rem 0 2rem;
    }

    .footer-cw .row > div {
        margin-bottom: 2rem !important;
    }

    .footer-cw .social-icon {
        width: 36px;
        height: 36px;
    }
}

/* Utility Classes */
.text-muted {
    color: var(--cw-neutral-600) !important;
}

.active {
    color: var(--color-primary) !important;
    font-weight: 600;
}

/* Global Alert Overrides for Proper Visibility */
.alert {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
    border-radius: 0.5rem !important;
}

.alert-success {
    background-color: white !important;
    color: black !important;
    border-color: black !important;
}

.alert-danger {
    background-color: white !important;
    color: black !important;
    border-color: black !important;
}

.alert-warning {
    background-color: white !important;
    color: black !important;
    border-color: black !important;
}

.alert-info {
    background-color: white !important;
    color: black !important;
    border-color: black !important;
}

/* Global Badge Overrides for Proper Visibility */
.badge.bg-primary {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
}

.badge.bg-success {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
}

.badge.bg-danger {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
}

.badge.bg-warning {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
}

.badge.bg-info {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
}

.badge.bg-secondary {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
}

.badge.bg-light {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
}

.badge.bg-dark {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
}

/* Global Table Overrides */
.table-light {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
}

.table-light th,
.table-light td {
    color: black !important;
    border-color: black !important;
}

/* Global Text Color Overrides */
.text-success,
.text-danger,
.text-info,
.text-warning {
    color: black !important;
}

/* Enhanced Responsive Design */
@media (max-width: 991.98px) {
    .navbar-brand {
        font-size: 1.5rem;
    }

    .nav-buttons {
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .nav-buttons .btn {
        padding: 0.625rem 1rem;
        font-size: 0.8125rem;
    }
}

@media (max-width: 767.98px) {
    .navbar-brand {
        font-size: 1.375rem;
        letter-spacing: -0.025em;
    }

    .nav-buttons {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.75rem;
        justify-content: center;
    }

    .nav-buttons .btn {
        margin-left: 0;
        flex: 1;
        max-width: 120px;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .footer-cw .row > div {
        margin-bottom: 2rem;
    }

    .dropdown-menu {
        min-width: 220px;
        margin-top: 0.5rem;
    }
}

@media (max-width: 575.98px) {
    .navbar {
        padding: 0.75rem 0;
    }

    .navbar-brand {
        font-size: 1.25rem;
    }

    .nav-buttons .btn {
        padding: 0.5rem 0.625rem;
        font-size: 0.6875rem;
        max-width: 100px;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles for Better Accessibility */
.btn:focus,
.dropdown-item:focus,
.navbar-brand:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .navbar,
    footer,
    .toast-container {
        display: none !important;
    }

    main {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
}
