/**
 * Enhanced Contact Form Styles
 * Improved layout, validation feedback, and UX for contact information forms
 */

/* Section Header Enhancements */
.section-header-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--cw-brand-accent);
}

.section-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.section-actions .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    white-space: nowrap;
}

.section-toggle {
    display: flex;
    align-items: center;
}

/* Contact Subsections */
.contact-subsection {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--cw-accent-light);
    border: 1px solid var(--cw-brand-accent);
    border-radius: 0.75rem;
    transition: all 0.2s ease;
}

.contact-subsection:hover {
    border-color: var(--cw-brand-primary);
    box-shadow: var(--cw-shadow-sm);
}

.subsection-title {
    font-family: var(--cw-font-heading);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Enhanced Form Fields */
.enhanced-field {
    position: relative;
    margin-bottom: 1.5rem;
}

.enhanced-field .form-label-cw {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.input-group-enhanced {
    position: relative;
}

.input-group-enhanced .input-group {
    margin-bottom: 0.5rem;
}

/* Validation Feedback */
.input-feedback {
    margin-top: 0.5rem;
    min-height: 1.5rem;
}

.validation-status {
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
    transition: all 0.2s ease;
}

.validation-status.success {
    color: #22c55e;
}

.validation-status.warning {
    color: #f59e0b;
}

.validation-status.error {
    color: #ef4444;
}

.validation-status i {
    font-size: 0.875rem;
}

.format-hint {
    font-size: 0.75rem;
    color: var(--cw-neutral-600);
    font-style: italic;
    margin-top: 0.25rem;
    transition: opacity 0.2s ease;
}

.format-hint:empty {
    display: none;
}

/* Action Button Stacks */
.action-buttons-stack {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.action-buttons-stack .btn {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
}

.action-buttons-stack .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--cw-shadow-sm);
}

/* Social Media Grid */
.social-media-grid {
    padding: 1.5rem;
    background: white;
    border-radius: 0.5rem;
    border: 1px solid var(--cw-neutral-300);
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.social-media-grid.show {
    max-height: none;
    opacity: 1;
}

/* Form Field Focus Enhancements */
.enhanced-field .form-control-cw:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--cw-brand-primary-rgb), 0.25);
    transform: translateY(-1px);
}

.enhanced-field .form-control-cw:valid:not(:placeholder-shown) {
    border-color: #22c55e;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2322c55e' d='m2.3 6.73.94-.94 1.06 1.06-1.13 1.13-2.19-2.19L2.3 4.4l.56.56c1.13 1.13 1.13 1.13 1.13 1.13z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.enhanced-field .form-control-cw:invalid:not(:placeholder-shown) {
    border-color: #ef4444;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ef4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.2 2.4 2.4m0-2.4L5.8 6.6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Contact Messages */
.contact-message {
    position: fixed;
    top: 2rem;
    right: 2rem;
    min-width: 300px;
    max-width: 400px;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 3000;
    transform: translateX(calc(100% + 2rem));
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.contact-message.show {
    transform: translateX(0);
}

.message-success {
    background: rgba(34, 197, 94, 0.9);
    border: 1px solid #22c55e;
    color: white;
}

.message-error {
    background: rgba(239, 68, 68, 0.9);
    border: 1px solid #ef4444;
    color: white;
}

.message-warning {
    background: rgba(251, 191, 36, 0.9);
    border: 1px solid #f59e0b;
    color: white;
}

.message-info {
    background: rgba(59, 130, 246, 0.9);
    border: 1px solid #3b82f6;
    color: white;
}

/* Contact Method Warning */
.contact-method-warning {
    border-left: 4px solid #f59e0b;
    background: rgba(251, 191, 36, 0.1);
    border-color: #f59e0b;
    border-radius: 0.5rem;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Input Group Enhancements */
.input-group .form-control-cw {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
}

/* Loading States */
.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Badge Enhancements */
.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.badge.bg-success {
    background-color: #22c55e !important;
}

.badge.bg-warning {
    background-color: #f59e0b !important;
    color: white !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-header-enhanced {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .section-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .contact-message {
        left: 1rem;
        right: 1rem;
        min-width: auto;
        max-width: calc(100vw - 2rem);
        transform: translateY(-100vh);
        top: 1rem;
    }

    .contact-message.show {
        transform: translateY(0);
    }

    .action-buttons-stack .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .contact-subsection {
        padding: 1rem;
    }

    .subsection-title {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .section-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .section-actions .btn {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }

    .social-media-grid {
        padding: 1rem;
    }

    .enhanced-field .form-label-cw {
        font-size: 0.9rem;
    }

    .validation-status {
        font-size: 0.8rem;
    }

    .format-hint {
        font-size: 0.7rem;
    }
}

/* Dark Mode Support (if implemented) */
@media (prefers-color-scheme: dark) {
    .contact-subsection {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
    }

    .social-media-grid {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
    }

    .format-hint {
        color: rgba(255, 255, 255, 0.6);
    }
}

/* Accessibility Enhancements */
.enhanced-field:focus-within .form-label-cw {
    color: var(--cw-brand-primary);
}

.validation-status[role="alert"] {
    animation: fadeIn 0.2s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .validation-status.success {
        color: #16a34a;
        font-weight: 600;
    }

    .validation-status.error {
        color: #dc2626;
        font-weight: 600;
    }

    .validation-status.warning {
        color: #d97706;
        font-weight: 600;
    }

    .contact-subsection {
        border-width: 2px;
    }

    .enhanced-field .form-control-cw:focus {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .contact-message,
    .enhanced-field .form-control-cw,
    .action-buttons-stack .btn,
    .contact-subsection {
        transition: none;
    }

    .btn .fa-spinner {
        animation: none;
    }

    @keyframes spin {
        0%, 100% { transform: rotate(0deg); }
    }
}
