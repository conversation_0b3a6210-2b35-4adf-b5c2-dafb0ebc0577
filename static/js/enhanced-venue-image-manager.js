/**
 * Enhanced Venue Image Manager
 * Modern image upload and management system with queue management,
 * drag-and-drop, multiple file selection, and real-time progress tracking.
 */

class EnhancedVenueImageManager {
    constructor() {
        this.uploadQueue = new Map();
        this.activeUploads = new Set();
        this.maxConcurrentUploads = 3;
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.maxImages = 5; // Will be updated from server
        this.currentImageCount = 0;
        this.allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
        this.csrfToken = null;

        // Queue management
        this.queueId = 0;
        this.totalProgress = 0;
        this.completedUploads = 0;

        this.init();
    }

    init() {
        this.csrfToken = this.getCsrfToken();
        this.currentImageCount = this.getCurrentImageCount();
        this.maxImages = this.getMaxImages();

        this.setupUploadZone();
        this.setupFileInput();
        this.setupQueueControls();
        this.setupImageActions();
        this.bindEvents();
    }

    setupUploadZone() {
        const uploadZone = document.getElementById('enhanced-upload-zone');
        const fileInput = document.getElementById('multiple-file-input');
        const browseButton = document.getElementById('browse-button');

        if (!uploadZone || !fileInput || !browseButton) return;

        // Drag and drop events
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadZone.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadZone.addEventListener(eventName, () => {
                uploadZone.classList.add('drag-over');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadZone.addEventListener(eventName, () => {
                uploadZone.classList.remove('drag-over');
            }, false);
        });

        uploadZone.addEventListener('drop', (e) => {
            const files = Array.from(e.dataTransfer.files);
            this.handleFileSelection(files);
        });

        // Click to browse
        uploadZone.addEventListener('click', () => {
            fileInput.click();
        });

        browseButton.addEventListener('click', (e) => {
            e.stopPropagation();
            fileInput.click();
        });
    }

    setupFileInput() {
        const fileInput = document.getElementById('multiple-file-input');
        if (!fileInput) return;

        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleFileSelection(files);
            e.target.value = ''; // Reset input for reuse
        });
    }

    setupQueueControls() {
        const startUploadBtn = document.getElementById('start-upload');
        const clearQueueBtn = document.getElementById('clear-queue');

        if (startUploadBtn) {
            startUploadBtn.addEventListener('click', () => this.startQueuedUploads());
        }

        if (clearQueueBtn) {
            clearQueueBtn.addEventListener('click', () => this.clearQueue());
        }
    }

    setupImageActions() {
        // Delegate events for dynamically added elements
        document.addEventListener('click', (e) => {
            if (e.target.closest('.set-primary-btn')) {
                this.handleSetPrimary(e);
            } else if (e.target.closest('.delete-image-btn')) {
                this.handleDeleteImage(e);
            } else if (e.target.closest('.move-up-btn')) {
                this.handleMoveImage(e, 'up');
            } else if (e.target.closest('.move-down-btn')) {
                this.handleMoveImage(e, 'down');
            }
        });
    }

    bindEvents() {
        // Window events
        window.downloadImage = this.downloadImage.bind(this);
        window.hideProgressTracker = this.hideProgressTracker.bind(this);
    }

    async handleFileSelection(files) {
        if (!files || files.length === 0) return;

        const validFiles = [];
        const errors = [];

        // Validate each file
        for (const file of files) {
            const validation = this.validateFile(file);
            if (validation.valid) {
                validFiles.push(file);
            } else {
                errors.push(`${file.name}: ${validation.error}`);
            }
        }

        // Check if adding files would exceed the limit
        const totalFiles = this.uploadQueue.size + validFiles.length + this.currentImageCount;
        if (totalFiles > this.maxImages) {
            const allowed = this.maxImages - this.uploadQueue.size - this.currentImageCount;
            if (allowed <= 0) {
                this.showMessage('Maximum image limit reached. Cannot add more images.', 'warning');
                return;
            }

            validFiles.splice(allowed);
            this.showMessage(`Only ${allowed} images can be added due to limit restrictions.`, 'warning');
        }

        // Show validation errors
        if (errors.length > 0) {
            this.showMessage(`Some files were rejected:\n${errors.join('\n')}`, 'danger');
        }

        // Add valid files to queue
        for (const file of validFiles) {
            this.addToQueue(file);
        }

        this.updateQueueDisplay();
    }

    validateFile(file) {
        // Check file type
        if (!this.allowedTypes.includes(file.type)) {
            return {
                valid: false,
                error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
            };
        }

        // Check file size
        if (file.size > this.maxFileSize) {
            const sizeMB = (file.size / (1024 * 1024)).toFixed(1);
            const maxSizeMB = (this.maxFileSize / (1024 * 1024)).toFixed(1);
            return {
                valid: false,
                error: `File size (${sizeMB}MB) exceeds maximum limit of ${maxSizeMB}MB.`
            };
        }

        return { valid: true };
    }

    addToQueue(file) {
        const queueItem = {
            id: ++this.queueId,
            file: file,
            status: 'pending', // pending, uploading, success, error
            progress: 0,
            error: null,
            preview: null
        };

        // Generate preview
        this.generatePreview(file).then(preview => {
            queueItem.preview = preview;
            this.updateQueueItemDisplay(queueItem);
        });

        this.uploadQueue.set(queueItem.id, queueItem);
    }

    async generatePreview(file) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => resolve(null);
            reader.readAsDataURL(file);
        });
    }

    updateQueueDisplay() {
        const queueContainer = document.getElementById('upload-queue');
        const queueItemsContainer = document.getElementById('queue-items');

        if (!queueContainer || !queueItemsContainer) return;

        if (this.uploadQueue.size === 0) {
            queueContainer.style.display = 'none';
            return;
        }

        queueContainer.style.display = 'block';
        queueItemsContainer.innerHTML = '';

        this.uploadQueue.forEach(item => {
            const queueItemElement = this.createQueueItemElement(item);
            queueItemsContainer.appendChild(queueItemElement);
        });

        this.updateQueueControls();
    }

    createQueueItemElement(item) {
        const element = document.createElement('div');
        element.className = `queue-item ${item.status}`;
        element.dataset.itemId = item.id;

        const sizeText = this.formatFileSize(item.file.size);
        const statusIcon = this.getStatusIcon(item.status);

        element.innerHTML = `
            <img src="${item.preview || '/static/images/placeholder.png'}" alt="Preview" class="queue-item-preview">
            <div class="queue-item-info">
                <div class="queue-item-name">${item.file.name}</div>
                <div class="queue-item-details">${sizeText} • ${item.file.type.split('/')[1].toUpperCase()}</div>
                <div class="queue-item-progress">
                    <div class="queue-item-progress-bar" style="width: ${item.progress}%"></div>
                </div>
                <div class="queue-item-status">
                    ${statusIcon} ${this.getStatusText(item)}
                </div>
            </div>
            <div class="queue-item-actions">
                ${this.getItemActions(item)}
            </div>
        `;

        return element;
    }

    getStatusIcon(status) {
        const icons = {
            pending: '<i class="fas fa-clock text-muted"></i>',
            uploading: '<i class="fas fa-spinner fa-spin text-info"></i>',
            success: '<i class="fas fa-check-circle text-success"></i>',
            error: '<i class="fas fa-exclamation-circle text-danger"></i>'
        };
        return icons[status] || icons.pending;
    }

    getStatusText(item) {
        switch (item.status) {
            case 'pending':
                return 'Waiting to upload';
            case 'uploading':
                return `Uploading... ${item.progress}%`;
            case 'success':
                return 'Upload complete';
            case 'error':
                return item.error || 'Upload failed';
            default:
                return 'Unknown status';
        }
    }

    getItemActions(item) {
        switch (item.status) {
            case 'pending':
                return `
                    <button type="button" class="queue-item-btn btn-outline-danger" onclick="enhancedImageManager.removeFromQueue(${item.id})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
            case 'uploading':
                return `
                    <button type="button" class="queue-item-btn btn-outline-warning" onclick="enhancedImageManager.cancelUpload(${item.id})">
                        <i class="fas fa-stop"></i>
                    </button>
                `;
            case 'error':
                return `
                    <button type="button" class="queue-item-btn btn-outline-info" onclick="enhancedImageManager.retryUpload(${item.id})">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button type="button" class="queue-item-btn btn-outline-danger" onclick="enhancedImageManager.removeFromQueue(${item.id})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
            case 'success':
                return `
                    <button type="button" class="queue-item-btn btn-outline-success" disabled>
                        <i class="fas fa-check"></i>
                    </button>
                `;
            default:
                return '';
        }
    }

    updateQueueControls() {
        const startBtn = document.getElementById('start-upload');
        const clearBtn = document.getElementById('clear-queue');

        if (!startBtn || !clearBtn) return;

        const hasPendingItems = Array.from(this.uploadQueue.values()).some(item => item.status === 'pending');
        const hasActiveUploads = this.activeUploads.size > 0;

        startBtn.disabled = !hasPendingItems || hasActiveUploads;
        clearBtn.disabled = hasActiveUploads;

        if (hasActiveUploads) {
            startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
        } else {
            startBtn.innerHTML = '<i class="fas fa-play me-1"></i>Start Upload';
        }
    }

    async startQueuedUploads() {
        const pendingItems = Array.from(this.uploadQueue.values()).filter(item => item.status === 'pending');

        if (pendingItems.length === 0) return;

        this.showProgressTracker();
        this.updateOverallProgress();

        // Start uploads with concurrency limit
        const uploadPromises = [];
        for (let i = 0; i < Math.min(pendingItems.length, this.maxConcurrentUploads); i++) {
            uploadPromises.push(this.processNextUpload());
        }

        await Promise.all(uploadPromises);
    }

    async processNextUpload() {
        while (true) {
            const nextItem = Array.from(this.uploadQueue.values()).find(item => item.status === 'pending');
            if (!nextItem) break;

            await this.uploadFile(nextItem);

            // Check if there are more pending items
            const stillPending = Array.from(this.uploadQueue.values()).some(item => item.status === 'pending');
            if (!stillPending) break;
        }
    }

    async uploadFile(item) {
        try {
            item.status = 'uploading';
            this.activeUploads.add(item.id);
            this.updateQueueItemDisplay(item);
            this.updateQueueControls();

            const formData = new FormData();
            formData.append('image', item.file);
            formData.append('csrfmiddlewaretoken', this.csrfToken);

            const response = await fetch('/venues/provider/images/upload/', {
                method: 'POST',
                body: formData,
                onUploadProgress: (progressEvent) => {
                    if (progressEvent.lengthComputable) {
                        item.progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        this.updateQueueItemDisplay(item);
                        this.updateOverallProgress();
                    }
                }
            });

            const data = await response.json();

            if (data.success) {
                item.status = 'success';
                item.progress = 100;
                this.completedUploads++;
                this.currentImageCount++;

                // Add the new image to the grid
                this.addImageToGrid(data);
                this.showMessage('Image uploaded successfully!', 'success');
            } else {
                throw new Error(data.error || 'Upload failed');
            }

        } catch (error) {
            item.status = 'error';
            item.error = error.message;
            this.showMessage(`Upload failed for ${item.file.name}: ${error.message}`, 'danger');
        } finally {
            this.activeUploads.delete(item.id);
            this.updateQueueItemDisplay(item);
            this.updateQueueControls();
            this.updateOverallProgress();
        }
    }

    updateQueueItemDisplay(item) {
        const element = document.querySelector(`[data-item-id="${item.id}"]`);
        if (!element) return;

        element.className = `queue-item ${item.status}`;

        const progressBar = element.querySelector('.queue-item-progress-bar');
        if (progressBar) {
            progressBar.style.width = `${item.progress}%`;
        }

        const statusElement = element.querySelector('.queue-item-status');
        if (statusElement) {
            statusElement.innerHTML = `${this.getStatusIcon(item.status)} ${this.getStatusText(item)}`;
        }

        const actionsElement = element.querySelector('.queue-item-actions');
        if (actionsElement) {
            actionsElement.innerHTML = this.getItemActions(item);
        }
    }

    showProgressTracker() {
        const tracker = document.getElementById('progress-tracker');
        if (tracker) {
            tracker.style.display = 'block';
            tracker.style.animation = 'slideIn 0.3s ease-out';
        }
    }

    hideProgressTracker() {
        const tracker = document.getElementById('progress-tracker');
        if (tracker) {
            tracker.style.display = 'none';
        }
    }

    updateOverallProgress() {
        const totalItems = this.uploadQueue.size;
        if (totalItems === 0) return;

        let totalProgress = 0;
        this.uploadQueue.forEach(item => {
            if (item.status === 'success') {
                totalProgress += 100;
            } else if (item.status === 'uploading') {
                totalProgress += item.progress;
            }
        });

        const overallPercent = Math.round(totalProgress / totalItems);

        const fillElement = document.getElementById('overall-progress-fill');
        const percentElement = document.getElementById('overall-percentage');

        if (fillElement) fillElement.style.width = `${overallPercent}%`;
        if (percentElement) percentElement.textContent = `${overallPercent}%`;

        // Hide tracker when all uploads are complete
        if (overallPercent === 100 && this.activeUploads.size === 0) {
            setTimeout(() => {
                this.hideProgressTracker();
                this.clearSuccessfulUploads();
            }, 2000);
        }
    }

    clearSuccessfulUploads() {
        const itemsToRemove = [];
        this.uploadQueue.forEach((item, id) => {
            if (item.status === 'success') {
                itemsToRemove.push(id);
            }
        });

        itemsToRemove.forEach(id => {
            this.uploadQueue.delete(id);
        });

        this.updateQueueDisplay();
        this.updateImageCountDisplay();
    }

    removeFromQueue(itemId) {
        this.uploadQueue.delete(itemId);
        this.updateQueueDisplay();
    }

    clearQueue() {
        this.uploadQueue.clear();
        this.updateQueueDisplay();
        this.hideProgressTracker();
    }

    async retryUpload(itemId) {
        const item = this.uploadQueue.get(itemId);
        if (!item) return;

        item.status = 'pending';
        item.progress = 0;
        item.error = null;

        this.updateQueueItemDisplay(item);
        await this.uploadFile(item);
    }

    async cancelUpload(itemId) {
        // Note: Actual cancellation would require AbortController support
        const item = this.uploadQueue.get(itemId);
        if (!item) return;

        item.status = 'error';
        item.error = 'Upload cancelled by user';
        this.activeUploads.delete(itemId);

        this.updateQueueItemDisplay(item);
        this.updateQueueControls();
    }

    addImageToGrid(imageData) {
        const grid = document.getElementById('images-grid');
        if (!grid) return;

        // Remove empty state if it exists
        const emptyState = grid.querySelector('.enhanced-empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        // Create new image card
        const imageCard = this.createImageCardElement(imageData);
        grid.appendChild(imageCard);

        // Animate in
        imageCard.style.animation = 'slideIn 0.5s ease-out';
    }

    createImageCardElement(imageData) {
        const card = document.createElement('div');
        card.className = `enhanced-image-card ${imageData.is_primary ? 'primary' : ''}`;
        card.dataset.imageId = imageData.image_id;
        card.dataset.order = imageData.order;

        card.innerHTML = `
            <div class="image-card-header">
                <img src="${imageData.image_url}" class="image-card-img" alt="Venue image">
                <div class="image-card-overlay">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-light btn-sm" onclick="window.open('${imageData.image_url}', '_blank')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-light btn-sm" onclick="downloadImage('${imageData.image_url}', 'venue-image')">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="image-card-badges">
                    <div>
                        ${imageData.is_primary ? '<span class="image-card-badge primary"><i class="fas fa-star me-1"></i>Primary</span>' : ''}
                    </div>
                    <span class="image-card-badge order">${imageData.order}</span>
                </div>
            </div>
            <div class="image-card-body">
                <h6 class="image-card-title">${imageData.caption || `Image ${imageData.order}`}</h6>
                <div class="image-card-actions">
                    <button type="button" class="image-card-btn btn-outline-primary move-up-btn" data-image-id="${imageData.image_id}">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button type="button" class="image-card-btn btn-outline-primary move-down-btn" data-image-id="${imageData.image_id}">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    ${!imageData.is_primary ?
                        `<button type="button" class="image-card-btn btn-outline-warning set-primary-btn" data-image-id="${imageData.image_id}">
                            <i class="fas fa-star me-1"></i>Set Primary
                        </button>` :
                        `<button type="button" class="image-card-btn btn-warning" disabled>
                            <i class="fas fa-star me-1"></i>Primary
                        </button>`
                    }
                    <button type="button" class="image-card-btn btn-outline-danger delete-image-btn" data-image-id="${imageData.image_id}" data-image-caption="Image ${imageData.order}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    // Image management actions
    async handleSetPrimary(e) {
        const btn = e.target.closest('.set-primary-btn');
        const imageId = btn.dataset.imageId;

        try {
            const response = await fetch(`/venues/provider/images/${imageId}/set-primary/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showMessage('Primary image updated successfully!', 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            this.showMessage(`Failed to set primary image: ${error.message}`, 'danger');
        }
    }

    async handleDeleteImage(e) {
        const btn = e.target.closest('.delete-image-btn');
        const imageId = btn.dataset.imageId;
        const caption = btn.dataset.imageCaption;

        if (!confirm(`Are you sure you want to delete "${caption}"?`)) {
            return;
        }

        try {
            const response = await fetch(`/venues/provider/images/${imageId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showMessage('Image deleted successfully!', 'success');

                // Remove from DOM with animation
                const imageCard = document.querySelector(`[data-image-id="${imageId}"]`);
                if (imageCard) {
                    imageCard.style.animation = 'slideOut 0.3s ease-in forwards';
                    setTimeout(() => {
                        imageCard.remove();
                        this.currentImageCount--;
                        this.updateImageCountDisplay();
                    }, 300);
                }
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            this.showMessage(`Failed to delete image: ${error.message}`, 'danger');
        }
    }

    async handleMoveImage(e, direction) {
        const btn = e.target.closest(`.move-${direction}-btn`);
        const imageId = btn.dataset.imageId;

        try {
            const response = await fetch(`/venues/provider/images/${imageId}/reorder/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `direction=${direction}`
            });

            const data = await response.json();

            if (data.success) {
                this.showMessage(data.message, 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                this.showMessage(data.message || 'Reorder operation completed', 'info');
            }
        } catch (error) {
            this.showMessage(`Failed to reorder image: ${error.message}`, 'danger');
        }
    }

    downloadImage(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Utility methods
    updateImageCountDisplay() {
        const countElement = document.getElementById('current-count');
        if (countElement) {
            countElement.textContent = this.currentImageCount;
        }

        // Update progress bar
        const progressBar = document.querySelector('.image-limit-progress .progress-bar');
        if (progressBar) {
            const percentage = (this.currentImageCount / this.maxImages) * 100;
            progressBar.style.width = `${percentage}%`;
        }
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showMessage(message, type = 'info', duration = 5000) {
        // Create or get message container
        let container = document.getElementById('message-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'message-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1060;
                min-width: 300px;
                max-width: 500px;
            `;
            document.body.appendChild(container);
        }

        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `alert alert-${type} alert-dismissible fade show`;
        messageElement.style.cssText = `
            margin-bottom: 0.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease-out;
        `;

        messageElement.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        container.appendChild(messageElement);

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.style.animation = 'slideOut 0.3s ease-in forwards';
                    setTimeout(() => messageElement.remove(), 300);
                }
            }, duration);
        }
    }

    getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }

    getCurrentImageCount() {
        const countElement = document.getElementById('current-count');
        return countElement ? parseInt(countElement.textContent) : 0;
    }

    getMaxImages() {
        const progressBar = document.querySelector('.image-limit-progress .progress-bar');
        return progressBar ? parseInt(progressBar.getAttribute('aria-valuemax')) : 5;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.enhancedImageManager = new EnhancedVenueImageManager();
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-20px);
        }
    }
`;
document.head.appendChild(style);
