/**
 * CozyWish Notifications App - Professional User Feedback System
 * Handles loading states, error messages, success notifications, and user interactions
 */

class NotificationFeedback {
    constructor() {
        this.toastContainer = null;
        this.loadingOverlay = null;
        this.init();
    }

    init() {
        this.createToastContainer();
        this.createLoadingOverlay();
        this.setupGlobalErrorHandling();
    }

    // ===== TOAST NOTIFICATION SYSTEM =====
    createToastContainer() {
        if (document.querySelector('.toast-container')) return;

        this.toastContainer = document.createElement('div');
        this.toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        this.toastContainer.style.zIndex = '1100';
        document.body.appendChild(this.toastContainer);
    }

    showToast(message, type = 'success', duration = 5000) {
        const toastId = 'toast-' + Date.now();
        const iconMap = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };

        const colorMap = {
            success: 'text-bg-success',
            error: 'text-bg-danger',
            warning: 'text-bg-warning',
            info: 'text-bg-info'
        };

        const toastHTML = `
            <div id="${toastId}" class="toast ${colorMap[type]} mb-2 professional-toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body d-flex align-items-center">
                        <i class="fas ${iconMap[type]} me-2"></i>
                        <span>${message}</span>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        this.toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: duration });

        // Add animation classes
        toastElement.classList.add('fade-in');
        toast.show();

        // Auto-remove after hiding
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });

        return toast;
    }

    // ===== LOADING OVERLAY SYSTEM =====
    createLoadingOverlay() {
        if (document.querySelector('.loading-overlay')) return;

        this.loadingOverlay = document.createElement('div');
        this.loadingOverlay.className = 'loading-overlay';
        this.loadingOverlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner">
                    <div class="spinner-border text-dark" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <div class="loading-text">Processing...</div>
            </div>
        `;
        document.body.appendChild(this.loadingOverlay);
    }

    showLoading(message = 'Processing...') {
        const loadingText = this.loadingOverlay.querySelector('.loading-text');
        loadingText.textContent = message;
        this.loadingOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    hideLoading() {
        this.loadingOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    // ===== BUTTON LOADING STATES =====
    setButtonLoading(button, loading = true, originalText = null) {
        if (loading) {
            if (!button.dataset.originalText) {
                button.dataset.originalText = button.innerHTML;
            }
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            button.disabled = true;
            button.classList.add('loading');
        } else {
            button.innerHTML = originalText || button.dataset.originalText || button.innerHTML;
            button.disabled = false;
            button.classList.remove('loading');
            delete button.dataset.originalText;
        }
    }

    // ===== FORM VALIDATION FEEDBACK =====
    showFieldError(field, message) {
        this.clearFieldError(field);

        field.classList.add('is-invalid');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback d-block';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }
    }

    clearAllFieldErrors(form) {
        const fields = form.querySelectorAll('.is-invalid');
        fields.forEach(field => this.clearFieldError(field));
    }

    // ===== AJAX ERROR HANDLING =====
    handleAjaxError(error, context = 'Operation') {
        console.error('Ajax Error:', error);

        let message = `${context} failed. Please try again.`;

        if (error.responseJSON && error.responseJSON.message) {
            message = error.responseJSON.message;
        } else if (error.status === 403) {
            message = 'You do not have permission to perform this action.';
        } else if (error.status === 404) {
            message = 'The requested resource was not found.';
        } else if (error.status === 500) {
            message = 'A server error occurred. Please try again later.';
        } else if (error.status === 0) {
            message = 'Network error. Please check your connection.';
        }

        this.showToast(message, 'error');
    }

    // ===== CONFIRMATION DIALOGS =====
    showConfirmDialog(message, onConfirm, onCancel = null) {
        const modalId = 'confirm-modal-' + Date.now();
        const modalHTML = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content professional-modal">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-question-circle me-2"></i>Confirm Action
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p class="mb-0">${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary confirm-btn">Confirm</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById(modalId));

        // Handle confirm button
        document.querySelector(`#${modalId} .confirm-btn`).addEventListener('click', () => {
            modal.hide();
            if (onConfirm) onConfirm();
        });

        // Handle cancel
        document.getElementById(modalId).addEventListener('hidden.bs.modal', () => {
            document.getElementById(modalId).remove();
            if (onCancel) onCancel();
        });

        modal.show();
        return modal;
    }

    // ===== GLOBAL ERROR HANDLING =====
    setupGlobalErrorHandling() {
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.showToast('An unexpected error occurred.', 'error');
        });

        // Handle JavaScript errors
        window.addEventListener('error', (event) => {
            console.error('JavaScript error:', event.error);
            // Only show user-friendly message for critical errors
            if (event.error && event.error.message && !event.error.message.includes('Script error')) {
                this.showToast('An error occurred. Please refresh the page.', 'error');
            }
        });
    }

    // ===== UTILITY METHODS =====
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialize the feedback system
const notificationFeedback = new NotificationFeedback();

// Export for use in other scripts
window.NotificationFeedback = notificationFeedback;

// ===== CSS STYLES FOR FEEDBACK COMPONENTS =====
const feedbackStyles = `
<style>
/* Professional Toast Notifications */
.professional-toast {
    border: 2px solid black;
    border-radius: 1rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.professional-toast .toast-body {
    font-weight: 500;
    font-size: 0.95rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    text-align: center;
    background: white;
    border: 3px solid black;
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.loading-spinner {
    margin-bottom: 1rem;
}

.loading-text {
    font-weight: 600;
    color: black;
    font-size: 1.1rem;
}

/* Professional Modal */
.professional-modal {
    border: 3px solid black;
    border-radius: 1.5rem;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.professional-modal .modal-header {
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
}

.professional-modal .modal-footer {
    border-top: 2px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #f8f9fa 0%, white 100%);
}

/* Button Loading State */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* Form Validation */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.invalid-feedback {
    color: #dc3545;
    font-weight: 500;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}
</style>
`;

// Inject styles into the document
if (!document.querySelector('#feedback-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'feedback-styles';
    styleElement.innerHTML = feedbackStyles;
    document.head.appendChild(styleElement);
}
