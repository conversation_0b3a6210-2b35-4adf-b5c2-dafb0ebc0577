/**
 * Enhanced Venue Description Management
 * Provides rich text editing, templates, and SEO analysis for venue descriptions
 */

class VenueDescriptionManager {
    constructor() {
        this.descriptionField = null;
        this.seoPanel = null;
        this.templatesPanel = null;
        this.editor = null;
        this.currentSeoScore = 0;

        this.init();
    }

    init() {
        this.descriptionField = document.getElementById('id_short_description');
        if (!this.descriptionField) return;

        this.setupRichTextEditor();
        this.setupSeoAnalysis();
        this.setupTemplates();
        this.setupCharacterCounter();
        this.setupEventListeners();
    }

    setupRichTextEditor() {
        // Create toolbar
        const toolbar = this.createToolbar();
        this.descriptionField.parentNode.insertBefore(toolbar, this.descriptionField);

        // Wrap description field for better styling
        const wrapper = document.createElement('div');
        wrapper.className = 'rich-text-wrapper';
        this.descriptionField.parentNode.insertBefore(wrapper, this.descriptionField);
        wrapper.appendChild(this.descriptionField);

        // Add rich text styling
        this.descriptionField.classList.add('rich-text-editor');
        this.descriptionField.style.minHeight = '120px';
        this.descriptionField.style.resize = 'vertical';
    }

    createToolbar() {
        const toolbar = document.createElement('div');
        toolbar.className = 'description-toolbar';
        toolbar.innerHTML = `
            <div class="toolbar-section">
                <button type="button" class="toolbar-btn" data-action="bold" title="Bold">
                    <i class="fas fa-bold"></i>
                </button>
                <button type="button" class="toolbar-btn" data-action="italic" title="Italic">
                    <i class="fas fa-italic"></i>
                </button>
                <button type="button" class="toolbar-btn" data-action="underline" title="Underline">
                    <i class="fas fa-underline"></i>
                </button>
            </div>
            <div class="toolbar-section">
                <button type="button" class="toolbar-btn" data-action="templates" title="Description Templates">
                    <i class="fas fa-file-alt"></i> Templates
                </button>
                <button type="button" class="toolbar-btn" data-action="seo" title="SEO Analysis">
                    <i class="fas fa-search"></i> SEO
                </button>
            </div>
        `;

        // Add event listeners
        toolbar.addEventListener('click', (e) => {
            const btn = e.target.closest('.toolbar-btn');
            if (!btn) return;

            const action = btn.getAttribute('data-action');
            this.handleToolbarAction(action);
            e.preventDefault();
        });

        return toolbar;
    }

    handleToolbarAction(action) {
        switch (action) {
            case 'bold':
            case 'italic':
            case 'underline':
                this.applyFormatting(action);
                break;
            case 'templates':
                this.toggleTemplatesPanel();
                break;
            case 'seo':
                this.toggleSeoPanel();
                break;
        }
    }

    applyFormatting(format) {
        const start = this.descriptionField.selectionStart;
        const end = this.descriptionField.selectionEnd;
        const text = this.descriptionField.value;
        const selectedText = text.substring(start, end);

        if (selectedText) {
            let formattedText = selectedText;
            switch (format) {
                case 'bold':
                    formattedText = `**${selectedText}**`;
                    break;
                case 'italic':
                    formattedText = `*${selectedText}*`;
                    break;
                case 'underline':
                    formattedText = `_${selectedText}_`;
                    break;
            }

            const newText = text.substring(0, start) + formattedText + text.substring(end);
            this.descriptionField.value = newText;
            this.descriptionField.setSelectionRange(start, start + formattedText.length);
            this.updateSeoAnalysis();
        }
    }

    setupSeoAnalysis() {
        this.seoPanel = this.createSeoPanel();
        this.descriptionField.parentNode.appendChild(this.seoPanel);
    }

    createSeoPanel() {
        const panel = document.createElement('div');
        panel.className = 'seo-analysis-panel';
        panel.style.display = 'none';
        panel.innerHTML = `
            <div class="seo-header">
                <h4><i class="fas fa-search"></i> SEO Analysis</h4>
                <button type="button" class="close-panel" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="seo-content">
                <div class="seo-score">
                    <div class="score-circle">
                        <span class="score-number">0</span>
                        <span class="score-label">Score</span>
                    </div>
                    <div class="score-status">
                        <span class="status-text">Add description for analysis</span>
                    </div>
                </div>
                <div class="seo-metrics">
                    <div class="metric">
                        <span class="metric-label">Character Count:</span>
                        <span class="metric-value" id="char-count">0</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Word Count:</span>
                        <span class="metric-value" id="word-count">0</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Keywords Found:</span>
                        <span class="metric-value" id="keywords-count">0</span>
                    </div>
                </div>
                <div class="seo-suggestions">
                    <h5>Suggestions:</h5>
                    <ul class="suggestions-list"></ul>
                </div>
                <div class="seo-keywords">
                    <h5>Keywords Found:</h5>
                    <div class="keywords-list"></div>
                </div>
            </div>
        `;

        // Add close event listener
        const closeBtn = panel.querySelector('.close-panel');
        closeBtn.addEventListener('click', () => {
            panel.style.display = 'none';
        });

        return panel;
    }

    setupTemplates() {
        this.templatesPanel = this.createTemplatesPanel();
        this.descriptionField.parentNode.appendChild(this.templatesPanel);
        this.loadTemplates();
    }

    createTemplatesPanel() {
        const panel = document.createElement('div');
        panel.className = 'templates-panel';
        panel.style.display = 'none';
        panel.innerHTML = `
            <div class="templates-header">
                <h4><i class="fas fa-file-alt"></i> Description Templates</h4>
                <button type="button" class="close-panel" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="templates-content">
                <p class="templates-help">Choose a template to get started, then customize it for your venue.</p>
                <div class="templates-grid"></div>
            </div>
        `;

        // Add close event listener
        const closeBtn = panel.querySelector('.close-panel');
        closeBtn.addEventListener('click', () => {
            panel.style.display = 'none';
        });

        return panel;
    }

    async loadTemplates() {
        try {
            // In a real implementation, this would fetch from the server
            // For now, we'll use a local template set
            const templates = this.getLocalTemplates();
            this.renderTemplates(templates);
        } catch (error) {
            console.error('Error loading templates:', error);
        }
    }

    getLocalTemplates() {
        return [
            {
                title: 'Luxury Spa Experience',
                template: 'Experience ultimate relaxation at our luxury spa, where expert therapists provide rejuvenating treatments in a serene environment. We offer a full range of services including massages, facials, body treatments, and wellness packages designed to restore your mind, body, and spirit.',
                keywords: ['luxury spa', 'massage', 'facials', 'relaxation', 'wellness']
            },
            {
                title: 'Full-Service Hair Salon',
                template: 'Transform your look at our full-service salon, where skilled stylists provide expert cuts, coloring, styling, and treatments. We use premium products and stay current with the latest trends to help you achieve your perfect style.',
                keywords: ['hair salon', 'hair cuts', 'hair coloring', 'styling', 'premium products']
            },
            {
                title: 'Therapeutic Massage Center',
                template: 'Find relief and relaxation at our therapeutic massage center, where licensed therapists specialize in Swedish, deep tissue, sports, and specialized massage techniques. Each session is tailored to address your specific needs and wellness goals.',
                keywords: ['therapeutic massage', 'licensed therapists', 'Swedish massage', 'deep tissue']
            },
            {
                title: 'Complete Fitness Center',
                template: 'Achieve your fitness goals at our state-of-the-art facility featuring modern equipment, group classes, and personal training. Whether you\'re a beginner or advanced athlete, our supportive environment helps you succeed.',
                keywords: ['fitness center', 'modern equipment', 'group classes', 'personal training']
            },
            {
                title: 'Integrative Wellness Center',
                template: 'Our integrative wellness center combines traditional and alternative healing approaches to support your complete well-being. We offer personalized consultations, holistic treatments, and educational wellness programs.',
                keywords: ['integrative wellness', 'holistic treatments', 'alternative healing', 'personalized consultations']
            }
        ];
    }

    renderTemplates(templates) {
        const grid = this.templatesPanel.querySelector('.templates-grid');
        grid.innerHTML = '';

        templates.forEach((template, index) => {
            const templateCard = document.createElement('div');
            templateCard.className = 'template-card';
            templateCard.innerHTML = `
                <h5 class="template-title">${template.title}</h5>
                <p class="template-preview">${template.template.substring(0, 100)}...</p>
                <div class="template-keywords">
                    ${template.keywords.map(keyword => `<span class="keyword-tag">${keyword}</span>`).join('')}
                </div>
                <button type="button" class="btn-use-template" data-index="${index}">
                    Use This Template
                </button>
            `;

            templateCard.querySelector('.btn-use-template').addEventListener('click', () => {
                this.applyTemplate(template);
            });

            grid.appendChild(templateCard);
        });
    }

    applyTemplate(template) {
        this.descriptionField.value = template.template;
        this.templatesPanel.style.display = 'none';
        this.updateSeoAnalysis();
        this.descriptionField.focus();

        // Show success message
        this.showNotification('Template applied successfully! Customize it for your venue.', 'success');
    }

    setupCharacterCounter() {
        const counter = document.createElement('div');
        counter.className = 'character-counter-enhanced';
        counter.innerHTML = `
            <div class="counter-info">
                <span class="char-count">0</span>
                <span class="char-limit">/ 500</span>
                <span class="counter-status">characters</span>
            </div>
            <div class="counter-bar">
                <div class="counter-progress"></div>
            </div>
        `;
        this.descriptionField.parentNode.appendChild(counter);
    }

    setupEventListeners() {
        this.descriptionField.addEventListener('input', () => {
            this.updateCharacterCounter();
            this.debounce(this.updateSeoAnalysis.bind(this), 500)();
        });

        this.descriptionField.addEventListener('paste', () => {
            setTimeout(() => {
                this.updateCharacterCounter();
                this.updateSeoAnalysis();
            }, 100);
        });
    }

    updateCharacterCounter() {
        const text = this.descriptionField.value;
        const length = text.length;
        const maxLength = 500;
        const percentage = (length / maxLength) * 100;

        const counter = document.querySelector('.character-counter-enhanced');
        if (!counter) return;

        const charCount = counter.querySelector('.char-count');
        const progress = counter.querySelector('.counter-progress');
        const status = counter.querySelector('.counter-status');

        charCount.textContent = length;
        progress.style.width = `${Math.min(percentage, 100)}%`;

        // Update status and styling
        if (length === 0) {
            status.textContent = 'characters';
            progress.className = 'counter-progress';
        } else if (length < 10) {
            status.textContent = 'characters (too short)';
            progress.className = 'counter-progress warning';
        } else if (length > maxLength) {
            status.textContent = 'characters (too long)';
            progress.className = 'counter-progress error';
        } else if (length > maxLength * 0.9) {
            status.textContent = 'characters (approaching limit)';
            progress.className = 'counter-progress warning';
        } else {
            status.textContent = 'characters (good length)';
            progress.className = 'counter-progress success';
        }
    }

    updateSeoAnalysis() {
        const description = this.descriptionField.value;
        const venueName = document.getElementById('id_venue_name')?.value || '';

        const analysis = this.analyzeSeo(description, venueName);
        this.renderSeoAnalysis(analysis);
    }

    analyzeSeo(description, venueName = '') {
        if (!description) {
            return {
                score: 0,
                overall: 'needs_improvement',
                suggestions: ['Add a description to improve SEO'],
                keywords: [],
                wordCount: 0,
                charCount: 0
            };
        }

        const wordCount = description.split(/\s+/).filter(word => word.length > 0).length;
        const charCount = description.length;
        let score = 0;
        const suggestions = [];
        const keywords = [];

        // Length analysis
        if (charCount < 100) {
            suggestions.push('Description should be at least 100 characters for better SEO');
        } else if (charCount > 400) {
            suggestions.push('Consider shortening description for better readability');
            score += 10;
        } else {
            score += 20;
        }

        // Venue name inclusion
        if (venueName && description.toLowerCase().includes(venueName.toLowerCase())) {
            score += 15;
        } else if (venueName) {
            suggestions.push(`Consider including your venue name "${venueName}" in the description`);
        }

        // Action words
        const actionWords = ['experience', 'discover', 'enjoy', 'relax', 'transform', 'achieve', 'expert', 'professional', 'quality'];
        const foundActions = actionWords.filter(word => description.toLowerCase().includes(word));
        if (foundActions.length > 0) {
            score += foundActions.length * 5;
            keywords.push(...foundActions);
        } else {
            suggestions.push('Add action words like "experience", "discover", or "enjoy"');
        }

        // Service-related keywords
        const serviceWords = ['massage', 'spa', 'salon', 'fitness', 'wellness', 'therapy', 'treatment', 'service'];
        const foundServices = serviceWords.filter(word => description.toLowerCase().includes(word));
        keywords.push(...foundServices);
        score += foundServices.length * 3;

        // Readability
        const sentences = (description.match(/[.!?]+/g) || []).length;
        if (sentences > 0 && wordCount / sentences <= 20) {
            score += 10;
        } else if (sentences === 0) {
            suggestions.push('Add punctuation to improve readability');
        } else {
            suggestions.push('Consider breaking up long sentences');
        }

        // Final scoring
        score = Math.min(score, 100);
        let overall;
        if (score >= 80) overall = 'excellent';
        else if (score >= 60) overall = 'good';
        else if (score >= 40) overall = 'fair';
        else overall = 'needs_improvement';

        if (suggestions.length === 0) {
            suggestions.push('Great! Your description is well-optimized for SEO.');
        }

        return {
            score,
            overall,
            suggestions,
            keywords: [...new Set(keywords)],
            wordCount,
            charCount
        };
    }

    renderSeoAnalysis(analysis) {
        if (!this.seoPanel || this.seoPanel.style.display === 'none') return;

        const scoreNumber = this.seoPanel.querySelector('.score-number');
        const statusText = this.seoPanel.querySelector('.status-text');
        const charCount = this.seoPanel.querySelector('#char-count');
        const wordCount = this.seoPanel.querySelector('#word-count');
        const keywordsCount = this.seoPanel.querySelector('#keywords-count');
        const suggestionsList = this.seoPanel.querySelector('.suggestions-list');
        const keywordsList = this.seoPanel.querySelector('.keywords-list');

        // Update score
        scoreNumber.textContent = analysis.score;
        scoreNumber.className = `score-number ${analysis.overall}`;

        // Update status
        const statusMap = {
            excellent: 'Excellent SEO!',
            good: 'Good SEO',
            fair: 'Fair SEO',
            needs_improvement: 'Needs Improvement'
        };
        statusText.textContent = statusMap[analysis.overall] || 'Unknown';
        statusText.className = `status-text ${analysis.overall}`;

        // Update metrics
        charCount.textContent = analysis.charCount;
        wordCount.textContent = analysis.wordCount;
        keywordsCount.textContent = analysis.keywords.length;

        // Update suggestions
        suggestionsList.innerHTML = '';
        analysis.suggestions.forEach(suggestion => {
            const li = document.createElement('li');
            li.textContent = suggestion;
            suggestionsList.appendChild(li);
        });

        // Update keywords
        keywordsList.innerHTML = '';
        analysis.keywords.forEach(keyword => {
            const span = document.createElement('span');
            span.className = 'keyword-tag found';
            span.textContent = keyword;
            keywordsList.appendChild(span);
        });

        this.currentSeoScore = analysis.score;
    }

    toggleSeoPanel() {
        const isVisible = this.seoPanel.style.display !== 'none';
        this.seoPanel.style.display = isVisible ? 'none' : 'block';

        if (!isVisible) {
            this.templatesPanel.style.display = 'none';
            this.updateSeoAnalysis();
        }
    }

    toggleTemplatesPanel() {
        const isVisible = this.templatesPanel.style.display !== 'none';
        this.templatesPanel.style.display = isVisible ? 'none' : 'block';

        if (!isVisible) {
            this.seoPanel.style.display = 'none';
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        `;

        document.body.appendChild(notification);

        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('[data-rich-text="true"]')) {
        new VenueDescriptionManager();
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VenueDescriptionManager;
}
