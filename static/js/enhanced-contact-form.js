/**
 * Enhanced Contact Form Validation
 * Provides real-time validation, auto-formatting, and improved UX for contact information forms
 */

class ContactFormManager {
    constructor() {
        this.validationRules = {
            phone: {
                pattern: /^[\+]?[1-9][\d]{0,15}$/,
                format: /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
                cleanPattern: /[\s\-\(\)]/g
            },
            email: {
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            },
            url: {
                pattern: /^https?:\/\/.+\..+/,
                social: {
                    instagram: /(?:instagram\.com\/|@)([a-zA-Z0-9_.]+)/,
                    facebook: /(?:facebook\.com\/|fb\.com\/)([a-zA-Z0-9_.]+)/,
                    twitter: /(?:twitter\.com\/|x\.com\/|@)([a-zA-Z0-9_]+)/,
                    linkedin: /linkedin\.com\/(in|company)\/([a-zA-Z0-9-]+)/
                }
            }
        };

        this.init();
    }

    init() {
        this.setupRealTimeValidation();
        this.setupSocialMediaToggle();
        this.setupQuickActions();
        this.setupContactMethodDetection();
    }

    setupRealTimeValidation() {
        // Enhanced fields with real-time validation
        const enhancedFields = document.querySelectorAll('.enhanced-field input');

        enhancedFields.forEach(field => {
            const fieldName = this.getFieldType(field);
            const feedback = document.getElementById(`${fieldName}-feedback`);

            if (feedback) {
                // Set up validation on input
                field.addEventListener('input', this.debounce(() => {
                    this.validateField(field, fieldName, feedback);
                }, 300));

                // Validate on blur for immediate feedback
                field.addEventListener('blur', () => {
                    this.validateField(field, fieldName, feedback, true);
                });

                // Initial validation if field has value
                if (field.value.trim()) {
                    this.validateField(field, fieldName, feedback);
                }
            }
        });
    }

    getFieldType(field) {
        const name = field.name || field.id;
        if (name.includes('phone')) return 'phone';
        if (name.includes('email')) return 'email';
        if (name.includes('website')) return 'website';
        if (name.includes('instagram')) return 'instagram';
        if (name.includes('facebook')) return 'facebook';
        if (name.includes('twitter')) return 'twitter';
        if (name.includes('linkedin')) return 'linkedin';
        return 'generic';
    }

    async validateField(field, fieldType, feedback, showErrors = false) {
        const value = field.value.trim();
        const validationStatus = feedback.querySelector('.validation-status');
        const formatHint = feedback.querySelector('.format-hint');

        // Clear previous status
        validationStatus.className = 'validation-status';
        validationStatus.innerHTML = '';

        if (!value) {
            formatHint.style.display = 'block';
            return { isValid: true, isEmpty: true };
        }

        let isValid = false;
        let message = '';
        let isWarning = false;

        switch (fieldType) {
            case 'phone':
                const result = this.validatePhone(value);
                isValid = result.isValid;
                message = result.message;
                isWarning = result.isWarning;

                // Auto-format phone if valid
                if (result.formatted && result.formatted !== value) {
                    field.value = result.formatted;
                }
                break;

            case 'email':
                isValid = this.validationRules.email.pattern.test(value);
                message = isValid ? 'Valid email address' : 'Invalid email format';
                break;

            case 'website':
                isValid = this.validationRules.url.pattern.test(value);
                message = isValid ? 'Valid website URL' : 'Please enter a complete URL (http:// or https://)';

                // Auto-add https if missing
                if (!isValid && !value.startsWith('http')) {
                    const withHttps = 'https://' + value;
                    if (this.validationRules.url.pattern.test(withHttps)) {
                        field.value = withHttps;
                        isValid = true;
                        message = 'Added https:// prefix';
                        isWarning = true;
                    }
                }
                break;

            case 'instagram':
            case 'facebook':
            case 'twitter':
            case 'linkedin':
                const socialResult = this.validateSocialMedia(value, fieldType);
                isValid = socialResult.isValid;
                message = socialResult.message;
                isWarning = socialResult.isWarning;

                if (socialResult.formatted && socialResult.formatted !== value) {
                    field.value = socialResult.formatted;
                }
                break;
        }

        // Update UI
        this.updateValidationUI(validationStatus, formatHint, {
            isValid,
            message,
            isWarning,
            showErrors
        });

        return { isValid, message };
    }

    validatePhone(phone) {
        const cleaned = phone.replace(this.validationRules.phone.cleanPattern, '');

        if (cleaned.length < 10) {
            return {
                isValid: false,
                message: 'Phone number too short',
                formatted: phone
            };
        }

        if (cleaned.length > 15) {
            return {
                isValid: false,
                message: 'Phone number too long',
                formatted: phone
            };
        }

        // US phone number formatting
        if (cleaned.length === 10 && /^\d{10}$/.test(cleaned)) {
            const formatted = `(${cleaned.slice(0,3)}) ${cleaned.slice(3,6)}-${cleaned.slice(6)}`;
            return {
                isValid: true,
                message: 'Valid US phone number',
                formatted: formatted
            };
        }

        // International format
        if (this.validationRules.phone.pattern.test(cleaned)) {
            return {
                isValid: true,
                message: 'Valid phone number',
                formatted: phone,
                isWarning: true
            };
        }

        return {
            isValid: false,
            message: 'Invalid phone number format',
            formatted: phone
        };
    }

    validateSocialMedia(value, platform) {
        // Handle @username format
        if (value.startsWith('@')) {
            const username = value.slice(1);
            if (username.length > 0) {
                const urls = {
                    instagram: `https://instagram.com/${username}`,
                    facebook: `https://facebook.com/${username}`,
                    twitter: `https://twitter.com/${username}`,
                    linkedin: `https://linkedin.com/in/${username}`
                };

                return {
                    isValid: true,
                    message: 'Converted to full URL',
                    formatted: urls[platform],
                    isWarning: true
                };
            }
        }

        // Validate URL format
        if (this.validationRules.url.pattern.test(value)) {
            const pattern = this.validationRules.url.social[platform];
            if (pattern && pattern.test(value)) {
                return {
                    isValid: true,
                    message: `Valid ${platform} URL`,
                    formatted: value
                };
            } else {
                return {
                    isValid: false,
                    message: `This doesn't appear to be a ${platform} URL`,
                    formatted: value
                };
            }
        }

        // Try to construct URL from username
        if (/^[a-zA-Z0-9_.]+$/.test(value)) {
            const urls = {
                instagram: `https://instagram.com/${value}`,
                facebook: `https://facebook.com/${value}`,
                twitter: `https://twitter.com/${value}`,
                linkedin: `https://linkedin.com/in/${value}`
            };

            return {
                isValid: true,
                message: 'Converted username to URL',
                formatted: urls[platform],
                isWarning: true
            };
        }

        return {
            isValid: false,
            message: 'Please enter a username or valid URL',
            formatted: value
        };
    }

    updateValidationUI(statusElement, hintElement, result) {
        const { isValid, message, isWarning, showErrors } = result;

        if (isValid) {
            statusElement.className = `validation-status ${isWarning ? 'warning' : 'success'}`;
            statusElement.innerHTML = `
                <i class="fas fa-${isWarning ? 'exclamation-triangle' : 'check'}"></i>
                ${message}
            `;
            hintElement.style.display = 'none';
        } else if (showErrors) {
            statusElement.className = 'validation-status error';
            statusElement.innerHTML = `
                <i class="fas fa-times"></i>
                ${message}
            `;
            hintElement.style.display = 'none';
        } else {
            statusElement.innerHTML = '';
            hintElement.style.display = 'block';
        }
    }

    setupSocialMediaToggle() {
        const toggleBtn = document.getElementById('toggle-social-section');
        const socialContent = document.getElementById('social-media-content');

        if (toggleBtn && socialContent) {
            // Check if any social media fields have values
            const socialFields = socialContent.querySelectorAll('input');
            const hasValues = Array.from(socialFields).some(field => field.value.trim());

            if (hasValues) {
                socialContent.style.display = 'block';
                toggleBtn.innerHTML = '<i class="fas fa-chevron-up"></i> Hide Options';
            }

            toggleBtn.addEventListener('click', () => {
                const isVisible = socialContent.style.display !== 'none';
                socialContent.style.display = isVisible ? 'none' : 'block';
                toggleBtn.innerHTML = isVisible ?
                    '<i class="fas fa-chevron-down"></i> Show Options' :
                    '<i class="fas fa-chevron-up"></i> Hide Options';
            });
        }
    }

    setupQuickActions() {
        // Validate all button
        const validateBtn = document.getElementById('validate-all-btn');
        if (validateBtn) {
            validateBtn.addEventListener('click', this.validateAllFields.bind(this));
        }

        // Test website button
        const testWebsiteBtn = document.getElementById('test-website-btn');
        if (testWebsiteBtn) {
            testWebsiteBtn.addEventListener('click', this.testWebsite.bind(this));
        }
    }

    async validateAllFields() {
        const validateBtn = document.getElementById('validate-all-btn');
        const originalContent = validateBtn.innerHTML;

        validateBtn.disabled = true;
        validateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Validating...';

        const enhancedFields = document.querySelectorAll('.enhanced-field input');
        let validCount = 0;
        let totalCount = 0;

        for (const field of enhancedFields) {
            if (field.value.trim()) {
                totalCount++;
                const fieldType = this.getFieldType(field);
                const feedback = document.getElementById(`${fieldType}-feedback`);

                if (feedback) {
                    const result = await this.validateField(field, fieldType, feedback, true);
                    if (result.isValid) {
                        validCount++;
                    }
                }
            }
        }

        setTimeout(() => {
            validateBtn.disabled = false;
            validateBtn.innerHTML = originalContent;

            if (totalCount === 0) {
                this.showMessage('No contact information to validate.', 'info');
            } else {
                this.showMessage(`Validation complete: ${validCount}/${totalCount} fields valid.`,
                    validCount === totalCount ? 'success' : 'warning');
            }
        }, 1000);
    }

    testWebsite() {
        const websiteField = document.querySelector('[name*="website"]');
        if (websiteField && websiteField.value.trim()) {
            const url = websiteField.value.trim();
            if (this.validationRules.url.pattern.test(url)) {
                window.open(url, '_blank', 'noopener,noreferrer');
                this.showMessage('Website opened in new tab.', 'info');
            } else {
                this.showMessage('Please enter a valid website URL first.', 'warning');
            }
        } else {
            this.showMessage('Please enter a website URL first.', 'warning');
        }
    }

    setupContactMethodDetection() {
        // Detect if user has at least one contact method
        const contactFields = ['phone', 'email', 'website_url'];
        const inputs = contactFields.map(field =>
            document.querySelector(`[name*="${field}"]`)
        ).filter(Boolean);

        const checkContactMethods = () => {
            const hasContact = inputs.some(input => input.value.trim());
            const warning = document.querySelector('.contact-method-warning');

            if (!hasContact && !warning) {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-warning contact-method-warning mt-3';
                warningDiv.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Recommendation:</strong> Add at least one contact method to help customers reach you.
                `;

                const contactSection = document.querySelector('.contact-subsection');
                if (contactSection) {
                    contactSection.appendChild(warningDiv);
                }
            } else if (hasContact && warning) {
                warning.remove();
            }
        };

        inputs.forEach(input => {
            input.addEventListener('input', this.debounce(checkContactMethods, 500));
        });

        // Initial check
        checkContactMethods();
    }

    showMessage(text, type = 'info') {
        const message = document.createElement('div');
        message.className = `contact-message message-${type}`;
        message.innerHTML = `
            <i class="fas fa-${this.getMessageIcon(type)}"></i>
            ${text}
        `;

        document.body.appendChild(message);

        setTimeout(() => {
            message.classList.add('show');
        }, 100);

        setTimeout(() => {
            message.classList.remove('show');
            setTimeout(() => message.remove(), 300);
        }, 4000);
    }

    getMessageIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Auto-initialize if DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        if (typeof window.contactFormManager === 'undefined') {
            window.contactFormManager = new ContactFormManager();
        }
    });
} else {
    if (typeof window.contactFormManager === 'undefined') {
        window.contactFormManager = new ContactFormManager();
    }
}
