/**
 * Homepage Search Functionality
 * Handles location autocomplete, category selection, and current location features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Location autocomplete functionality
    const locationInput = document.getElementById('id_location');
    const locationList = document.getElementById('locationList');

    if (locationInput && locationList) {
        let debounceTimer;

        locationInput.addEventListener('input', function() {
            const query = this.value.trim();

            // Clear previous timer
            clearTimeout(debounceTimer);

            // Don't search for queries less than 2 characters
            if (query.length < 2) {
                locationList.innerHTML = '';
                return;
            }

            // Debounce the API call
            debounceTimer = setTimeout(() => {
                fetch(`/venues/api/location-autocomplete/?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        locationList.innerHTML = '';

                        if (data.suggestions && data.suggestions.length > 0) {
                            data.suggestions.forEach(suggestion => {
                                const option = document.createElement('option');
                                option.value = suggestion.label;
                                locationList.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Location autocomplete error:', error);
                        locationList.innerHTML = '';
                    });
            }, 300); // 300ms debounce
        });
    }

    // Category dropdown functionality
    const categoryDropdownItems = document.querySelectorAll('.treatment-dropdown .dropdown-item');
    const categoryInput = document.querySelector('input[name="query"]');

    categoryDropdownItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            const categoryText = this.textContent.trim();
            const categoryValue = this.dataset.category || '';

            // Update the input field
            if (categoryInput) {
                if (categoryText === 'All treatments') {
                    categoryInput.value = '';
                    categoryInput.placeholder = 'categories';
                } else {
                    categoryInput.value = categoryText;
                    categoryInput.placeholder = categoryText;
                }
            }

            // Store category ID in hidden field if it exists
            const categoryHiddenInput = document.querySelector('input[name="category"]');
            if (categoryHiddenInput) {
                categoryHiddenInput.value = categoryValue;
            }

            // Close the dropdown
            const dropdown = bootstrap.Dropdown.getInstance(categoryInput);
            if (dropdown) {
                dropdown.hide();
            }
        });
    });

    // Use current location functionality
    const useCurrentLocationBtn = document.getElementById('use-current-location');

    if (useCurrentLocationBtn) {
        useCurrentLocationBtn.addEventListener('click', function(e) {
            e.preventDefault();

            if (!navigator.geolocation) {
                alert('Geolocation is not supported by this browser.');
                return;
            }

            // Show loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting location...';
            this.style.pointerEvents = 'none';

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;

                    // Use reverse geocoding to get location name
                    // For now, we'll just set a placeholder - in production you'd use a geocoding service
                    if (locationInput) {
                        locationInput.value = 'Current Location';
                        locationInput.placeholder = 'Current Location';
                    }

                    // Reset button
                    this.innerHTML = originalText;
                    this.style.pointerEvents = 'auto';

                    // Close the dropdown
                    const dropdown = bootstrap.Dropdown.getInstance(locationInput);
                    if (dropdown) {
                        dropdown.hide();
                    }
                },
                (error) => {
                    console.error('Geolocation error:', error);
                    alert('Unable to get your location. Please enter it manually.');

                    // Reset button
                    this.innerHTML = originalText;
                    this.style.pointerEvents = 'auto';
                }
            );
        });
    }

    // Form validation before submit
    const searchForm = document.querySelector('form[action*="venue_search"]');

    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const queryInput = this.querySelector('input[name="query"]');
            const locationInput = this.querySelector('input[name="location"]');

            // Check if at least one field has a value
            const hasQuery = queryInput && queryInput.value.trim();
            const hasLocation = locationInput && locationInput.value.trim();

            if (!hasQuery && !hasLocation) {
                e.preventDefault();
                alert('Please enter a search term or location to find venues.');
                return false;
            }
        });
    }

    // Clear search functionality
    const clearSearchBtn = document.getElementById('clear-search');

    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Clear all search inputs
            const queryInput = document.querySelector('input[name="query"]');
            const locationInput = document.querySelector('input[name="location"]');
            const categoryHiddenInput = document.querySelector('input[name="category"]');

            if (queryInput) {
                queryInput.value = '';
                queryInput.placeholder = 'categories';
            }

            if (locationInput) {
                locationInput.value = '';
                locationInput.placeholder = 'City, State, or County';
            }

            if (categoryHiddenInput) {
                categoryHiddenInput.value = '';
            }

            // Clear location datalist
            if (locationList) {
                locationList.innerHTML = '';
            }
        });
    }
});
