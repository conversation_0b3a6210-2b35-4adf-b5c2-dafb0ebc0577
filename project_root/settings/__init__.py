"""
CozyWish Settings Package

This package contains modular Django settings for different environments.
The appropriate settings module is automatically selected based on the
DJANGO_ENVIRONMENT variable.

Environment Settings:
- development: Local development settings (default)
- production: Production deployment settings
- testing: Test environment settings
- staging: Staging environment settings

Usage:
    Set DJANGO_ENVIRONMENT in your environment or use default development settings.
    
    export DJANGO_ENVIRONMENT=production
    python manage.py runserver
"""

import os
import sys
from django.core.exceptions import ImproperlyConfigured

# Determine which settings module to use
environment = os.environ.get('DJANGO_ENVIRONMENT', 'development')

# Map environment names to settings modules
SETTINGS_MODULES = {
    'development': 'project_root.settings.development',
    'production': 'project_root.settings.production',
    'testing': 'project_root.settings.testing',
    'staging': 'project_root.settings.staging',
}

# Auto-detect testing environment
if 'test' in sys.argv or 'pytest' in sys.modules:
    environment = 'testing'

# Get the appropriate settings module
settings_module = SETTINGS_MODULES.get(environment)

if not settings_module:
    available_envs = ', '.join(SETTINGS_MODULES.keys())
    raise ImproperlyConfigured(
        f"Unknown environment '{environment}'. "
        f"Available environments: {available_envs}"
    )

# Import all settings from the appropriate module
try:
    exec(f"from {settings_module} import *")
except ImportError as e:
    raise ImproperlyConfigured(
        f"Could not import settings module '{settings_module}': {e}"
    )
