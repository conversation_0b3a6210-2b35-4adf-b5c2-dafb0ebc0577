"""
Development Settings for CozyWish

This module contains settings specific to the development environment.
It inherits from base.py and overrides settings for local development.

Features:
- Debug mode enabled
- Console email backend
- SQLite database
- Local file storage
- Relaxed security settings
- Development-specific middleware
"""

from .base import *
import os

# --- Development Environment Override ---
DEBUG = True
ENABLE_TEST_VIEW = True

# --- Development Hosts ---
ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'testserver', '0.0.0.0']

# --- Development CSRF Settings ---
CSRF_TRUSTED_ORIGINS = [
    'http://localhost:8000',
    'http://127.0.0.1:8000',
]

# --- Development-only Apps ---
INSTALLED_APPS += [
    'debug_toolbar',
    'django_extensions',
    'silk',
]

# --- Development Middleware ---
# Insert debug toolbar and silk middleware at the beginning
MIDDLEWARE.insert(0, 'debug_toolbar.middleware.DebugToolbarMiddleware')
MIDDLEWARE.insert(1, 'silk.middleware.SilkyMiddleware')

# --- Development Database ---
# Force SQLite for development regardless of DATABASE_URL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# --- Development Email Backend ---
# Always use console backend in development unless forced
FORCE_EMAIL_BACKEND = env('FORCE_EMAIL_BACKEND', default=False)
if FORCE_EMAIL_BACKEND:
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD', default='')
else:
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# --- Development Media Files ---
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# --- Development Storage Configuration ---
STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

# --- Development Cache ---
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'cozywish-dev-cache',
        'TIMEOUT': 60,  # Shorter timeout for development
        'OPTIONS': {
            'MAX_ENTRIES': 100,
            'CULL_FREQUENCY': 3,
        }
    }
}

# --- Development Security Settings ---
# Relaxed security for development
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# --- Development CSP Configuration ---
# Relaxed CSP for development
CSP_DEFAULT_SRC = ("'self'", "'unsafe-inline'", "'unsafe-eval'")
CSP_SCRIPT_SRC = (
    "'self'",
    "'unsafe-inline'",
    "'unsafe-eval'",  # Allow eval for development tools
    "https://cdn.jsdelivr.net",
    "https://cdnjs.cloudflare.com",
    "http://localhost:*",  # Local development servers
    "http://127.0.0.1:*",  # Local development servers
)
CSP_STYLE_SRC = (
    "'self'",
    "'unsafe-inline'",
    "https://fonts.googleapis.com",
    "https://cdn.jsdelivr.net",
    "https://cdnjs.cloudflare.com",
    "http://localhost:*",
    "http://127.0.0.1:*",
)
CSP_CONNECT_SRC = (
    "'self'",
    "http://localhost:*",
    "http://127.0.0.1:*",
    "ws://localhost:*",  # WebSocket connections for development
    "ws://127.0.0.1:*",
)
CSP_IMG_SRC = (
    "'self'",
    "data:",
    "https:",
    "http:",  # Allow HTTP images in development
)

# Disable CSP reporting in development
CSP_REPORT_ONLY = True  # Only report, don't enforce


# --- Development Rate Limiting ---
# More relaxed rate limiting for development
RATELIMIT_ENABLE = True  # Keep enabled to test functionality
RATELIMIT_LOGIN_ATTEMPTS = '20/5m'  # More lenient for testing
RATELIMIT_API_CALLS = '1000/h'      # Higher limit for development
RATELIMIT_FORM_SUBMISSIONS = '50/m'  # Higher limit for testing
RATELIMIT_PASSWORD_RESET = '10/h'   # More attempts for testing
RATELIMIT_REGISTRATION = '20/h'     # More attempts for testing


# --- Development CORS Configuration ---
# Permissive CORS for development
CORS_ALLOW_ALL_ORIGINS = True  # Allow all origins in development
CORS_ALLOW_CREDENTIALS = True

# Additional headers for development tools
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'x-debug-toolbar',  # For Django Debug Toolbar
]

# --- Django Debug Toolbar Configuration ---
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

# Debug toolbar panels
DEBUG_TOOLBAR_PANELS = [
    'debug_toolbar.panels.history.HistoryPanel',
    'debug_toolbar.panels.versions.VersionsPanel',
    'debug_toolbar.panels.timer.TimerPanel',
    'debug_toolbar.panels.settings.SettingsPanel',
    'debug_toolbar.panels.headers.HeadersPanel',
    'debug_toolbar.panels.request.RequestPanel',
    'debug_toolbar.panels.sql.SQLPanel',
    'debug_toolbar.panels.staticfiles.StaticFilesPanel',
    'debug_toolbar.panels.templates.TemplatesPanel',
    'debug_toolbar.panels.cache.CachePanel',
    'debug_toolbar.panels.signals.SignalsPanel',
    'debug_toolbar.panels.redirects.RedirectsPanel',
    'debug_toolbar.panels.profiling.ProfilingPanel',
]

DEBUG_TOOLBAR_CONFIG = {
    'SHOW_TOOLBAR_CALLBACK': lambda request: DEBUG,
    'SHOW_COLLAPSED': True,
}

# --- Django Silk Configuration ---
SILKY_PYTHON_PROFILER = True
SILKY_PYTHON_PROFILER_BINARY = True
SILKY_AUTHENTICATION = True  # Require login to view
SILKY_AUTHORISATION = True
SILKY_PERMISSIONS = lambda user: user.is_superuser


# --- Development Logging ---
LOGGING['loggers']['django']['level'] = 'INFO'
# Set debug level for all app loggers
for app in ['accounts_app', 'venues_app', 'utility_app', 'utils', 'booking_cart_app',
           'payments_app', 'dashboard_app', 'review_app', 'admin_app',
           'discount_app', 'notifications_app']:
    if app in LOGGING['loggers']:
        LOGGING['loggers'][app]['level'] = 'DEBUG'

# --- Development Celery ---
# Use eager execution for development
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# --- Development Debug Toolbar Configuration ---
# Debug toolbar is already added to INSTALLED_APPS and MIDDLEWARE above
# Additional configuration is handled in the main configuration section

# --- Development Environment Indicator ---
ENVIRONMENT_NAME = 'Development'
ENVIRONMENT_COLOR = '#28a745'  # Green
