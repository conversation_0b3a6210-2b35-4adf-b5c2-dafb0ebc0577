"""
Testing Settings for CozyWish

This module contains settings specific to the testing environment.
It inherits from base.py and overrides settings for running tests.

Features:
- Debug mode disabled
- In-memory database
- Console email backend
- Local file storage
- Disabled caching
- Fast test execution
- Minimal logging
"""

from .base import *
import os

# --- Testing Environment Override ---
DEBUG = False
TESTING = True
ENABLE_TEST_VIEW = False

# --- Testing Hosts ---
ALLOWED_HOSTS = ['testserver', 'localhost', '127.0.0.1']

# --- Testing Database ---
# Use in-memory SQLite for fast tests
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'OPTIONS': {
            'timeout': 20,
        },
    }
}

# --- Testing Email Backend ---
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# --- Testing Media Files ---
MEDIA_URL = '/test-media/'
MEDIA_ROOT = BASE_DIR / 'test_media'

# --- Testing Storage Configuration ---
STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

# --- Testing Cache ---
# Disable caching for tests
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# --- Testing Security Settings ---
# Relaxed security for testing
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# --- Testing Middleware ---
# Remove whitenoise during tests
MIDDLEWARE = [m for m in MIDDLEWARE if 'whitenoise' not in m.lower()]

# --- Testing Logging ---
# Minimal logging for tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'cozywish': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
    },
}

# --- Testing Celery ---
# Use eager execution for tests
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# --- Testing Password Hashers ---
# Use fast password hasher for tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# --- Testing File Upload ---
# Smaller file upload limits for tests
FILE_UPLOAD_MAX_MEMORY_SIZE = 1024 * 1024  # 1MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 1024 * 1024  # 1MB

# --- Testing Environment Indicator ---
ENVIRONMENT_NAME = 'Testing'
ENVIRONMENT_COLOR = '#ffc107'  # Yellow
