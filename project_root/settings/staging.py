"""
Staging Settings for CozyWish

This module contains settings specific to the staging environment.
It inherits from base.py and overrides settings for staging deployment.

Features:
- Debug mode disabled
- SMTP email backend
- PostgreSQL database
- AWS S3 storage (staging bucket)
- Moderate security settings
- Staging-specific configurations
- Enhanced logging for debugging
"""

from .base import *
import os

# --- Staging Environment Override ---
DEBUG = False
ENABLE_TEST_VIEW = True  # Allow test views in staging

# --- Staging Hosts ---
ALLOWED_HOSTS = ['.staging.cozywish.com', 'cozywish-staging.onrender.com']
RENDER_EXTERNAL_HOSTNAME = env('RENDER_EXTERNAL_HOSTNAME', default=None)
if RENDER_EXTERNAL_HOSTNAME:
    ALLOWED_HOSTS.append(RENDER_EXTERNAL_HOSTNAME)

# --- Staging CSRF Settings ---
CSRF_TRUSTED_ORIGINS = [
    'https://staging.cozywish.com',
    'https://cozywish-staging.onrender.com'
]

# --- Staging Database ---
DATABASE_URL = env('DATABASE_URL', default=None)
if DATABASE_URL:
    DATABASES = {'default': dj_database_url.parse(DATABASE_URL, conn_max_age=600)}
else:
    # Fallback to SQLite for staging if no DATABASE_URL
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'staging.sqlite3',
        }
    }

# --- Staging Email Backend ---
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD', default='')

# Override email recipients for staging
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL', default='<EMAIL>')
SERVER_EMAIL = env('SERVER_EMAIL', default='<EMAIL>')

# --- Staging Media Files ---
# Use staging S3 bucket if available, otherwise local storage
AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID', default=None)
AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY', default=None)
AWS_STORAGE_BUCKET_NAME = env('AWS_STORAGE_BUCKET_NAME', default=None)

if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY and AWS_STORAGE_BUCKET_NAME:
    # Use S3 for staging
    STORAGES = {
        "default": {
            "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
            "OPTIONS": {
                "access_key": AWS_ACCESS_KEY_ID,
                "secret_key": AWS_SECRET_ACCESS_KEY,
                "bucket_name": AWS_STORAGE_BUCKET_NAME,
                "region_name": env('AWS_S3_REGION_NAME', default='us-east-1'),
                "custom_domain": env('AWS_S3_CUSTOM_DOMAIN', default=None),
                "file_overwrite": False,
                "default_acl": None,
                "signature_version": "s3v4",
                "addressing_style": "virtual",
                "use_ssl": True,
                "verify": True,
                "object_parameters": {
                    "CacheControl": "max-age=3600",  # Shorter cache for staging
                },
                "querystring_auth": True,
                "querystring_expire": 3600,
            },
        },
        "staticfiles": {
            "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
        },
    }
else:
    # Fallback to local storage
    MEDIA_URL = '/media/'
    MEDIA_ROOT = BASE_DIR / 'staging_media'
    STORAGES = {
        "default": {
            "BACKEND": "django.core.files.storage.FileSystemStorage",
        },
        "staticfiles": {
            "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
        },
    }

# --- Staging Static Files ---
WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = True

# --- Staging Cache ---
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'cozywish-staging-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 500,
            'CULL_FREQUENCY': 3,
        }
    }
}

# --- Staging Security Settings ---
# Moderate security for staging
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 3600  # 1 hour
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# --- Staging CSP Configuration ---
# Moderate CSP for staging (between development and production)
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = (
    "'self'",
    "'unsafe-inline'",  # More permissive for debugging
    "https://cdn.jsdelivr.net",
    "https://cdnjs.cloudflare.com",
)
CSP_STYLE_SRC = (
    "'self'",
    "'unsafe-inline'",
    "https://fonts.googleapis.com",
    "https://cdn.jsdelivr.net",
    "https://cdnjs.cloudflare.com",
)
CSP_CONNECT_SRC = (
    "'self'",
    # Add staging API endpoints here
)
CSP_IMG_SRC = (
    "'self'",
    "data:",
    "https:",
    # Add staging S3 bucket or CDN here
)

# Report-only mode for staging to test CSP without breaking functionality
CSP_REPORT_ONLY = True
# CSP_REPORT_URI = '/csp-report/'


# --- Staging Rate Limiting ---
# Moderate rate limiting for staging
RATELIMIT_ENABLE = True
RATELIMIT_LOGIN_ATTEMPTS = '5/5m'   # Same as base settings
RATELIMIT_API_CALLS = '80/h'        # Moderate API limits
RATELIMIT_FORM_SUBMISSIONS = '8/m'   # Moderate form limits
RATELIMIT_PASSWORD_RESET = '3/h'    # Same as base settings
RATELIMIT_REGISTRATION = '5/h'      # Same as base settings


# --- Staging CORS Configuration ---
# Moderate CORS for staging
CORS_ALLOW_ALL_ORIGINS = False
CORS_ALLOWED_ORIGINS = [
    # Add your staging frontend domains here
    # "https://staging.yourdomain.com",
    # "https://staging-app.yourdomain.com",
]

# Allow localhost for staging testing
CORS_ALLOWED_ORIGIN_REGEXES = [
    r"^http://localhost:\d+$",
    r"^http://127\.0\.0\.1:\d+$",
]

# Staging-specific CORS headers (more permissive than production)
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]


# --- Staging Logging ---
# Enhanced logging for staging debugging
LOGGING['loggers']['django']['level'] = 'INFO'
# Set debug level for all app loggers in staging
for app in ['accounts_app', 'venues_app', 'utility_app', 'utils', 'booking_cart_app',
           'payments_app', 'dashboard_app', 'review_app', 'admin_app',
           'discount_app', 'notifications_app']:
    if app in LOGGING['loggers']:
        LOGGING['loggers'][app]['level'] = 'DEBUG'

# --- Staging Celery ---
CELERY_TASK_ALWAYS_EAGER = False
CELERY_TASK_EAGER_PROPAGATES = False

# --- Staging Environment Indicator ---
ENVIRONMENT_NAME = 'Staging'
ENVIRONMENT_COLOR = '#17a2b8'  # Blue
