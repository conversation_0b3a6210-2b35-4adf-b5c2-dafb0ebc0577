{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}
{% load math_filters %}

{% block title %}Revenue Tracking - Admin Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/admin_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'payments_app:admin_payment_list' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-credit-card"></i> Transactions
    </a>
    <a href="{% url 'admin:index' %}" class="btn btn-sm btn-outline-secondary">
        <i class="fas fa-cog"></i> Django Admin
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'dashboard_app:admin_dashboard' %}">Dashboard</a></li>
    <li class="breadcrumb-item active" aria-current="page">Revenue Tracking</li>
  </ol>
</nav>
<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar"></i> Date Range Filter</h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3" aria-label="Revenue tracking filter">
                    <div class="col-md-3">
                        {{ form.period.label_tag }}
                        {{ form.period }}
                    </div>
                    <div class="col-md-3">
                        {{ form.start_date.label_tag }}
                        {{ form.start_date }}
                    </div>
                    <div class="col-md-3">
                        {{ form.end_date.label_tag }}
                        {{ form.end_date }}
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter"></i> Apply Filter
                        </button>
                        <a href="{% url 'dashboard_app:admin_revenue_tracking' %}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-times"></i> Clear
                        </a>
                        <a href="{% url 'dashboard_app:admin_revenue_export' %}?start_date={{ start_date }}&end_date={{ end_date }}" class="btn btn-outline-success spinner-button">
                            <i class="fas fa-file-csv"></i> {% trans "Export CSV" %}
                        </a>
                    </div>
                </form>
                <div class="mt-2">
                    <small class="text-muted">
                        Showing data from {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Overview -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                <h3 class="text-success">${{ total_revenue|floatformat:2 }}</h3>
                <p class="mb-0">Total Revenue</p>
                <small class="text-muted">From {{ bookings.count }} bookings</small>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                <h3 class="text-info">
                    {% if bookings.count > 0 %}
                        ${{ total_revenue|div:bookings.count|floatformat:2 }}
                    {% else %}
                        $0.00
                    {% endif %}
                </h3>
                <p class="mb-0">Average Booking</p>
                <small class="text-muted">Per booking value</small>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check fa-2x text-warning mb-2"></i>
                <h3 class="text-warning">{{ bookings.count }}</h3>
                <p class="mb-0">Total Bookings</p>
                <small class="text-muted">In selected period</small>
            </div>
        </div>
    </div>
</div>

<!-- Daily Revenue Chart -->
{% if daily_revenue %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Daily Revenue Trends</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm" id="recent-bookings-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Revenue</th>
                                <th>Visual</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for date, revenue in daily_revenue.items %}
                            <tr>
                                <td>{{ date }}</td>
                                <td>
                                    <span class="badge bg-success">${{ revenue|floatformat:2 }}</span>
                                </td>
                                <td>
                                    <div class="progress" style="height: 15px; width: 200px;">
                                        <div class="progress-bar bg-success" role="progressbar"
                                             style="width: {% if revenue > 0 %}{% widthratio revenue 1000 100 %}{% else %}0{% endif %}%">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Revenue by Category -->
{% if sorted_categories %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Revenue by Category</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for category in sorted_categories %}
                    <div class="col-md-4 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="text-primary">{{ category.category }}</h6>
                            <h4 class="text-success">${{ category.revenue|floatformat:2 }}</h4>
                            <div class="progress mt-2" style="height: 10px;">
                                <div class="progress-bar bg-success" role="progressbar"
                                     style="width: {% widthratio category.revenue total_revenue 100 %}%">
                                </div>
                            </div>
                            <small class="text-muted">
                                {% widthratio category.revenue total_revenue 100 %}% of total
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Top Performing Venues -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-trophy"></i> Top Venues by Revenue</h5>
                <small class="text-muted">This period</small>
            </div>
            <div class="card-body">
                {% if top_venues %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Venue</th>
                                <th>Provider</th>
                                <th>Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for venue in top_venues %}
                            <tr>
                                <td>{{ venue.name|truncatechars:25 }}</td>
                                <td>{{ venue.service_provider.user.full_name|default:venue.service_provider.user.email|truncatechars:20 }}</td>
                                <td>
                                    <span class="badge bg-success">${{ venue.revenue|floatformat:2 }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">No venue revenue data available for this period.</p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-star"></i> Top Services by Revenue</h5>
                <small class="text-muted">This period</small>
            </div>
            <div class="card-body">
                {% if top_services %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Service</th>
                                <th>Venue</th>
                                <th>Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for service in top_services %}
                            <tr>
                                <td>{{ service.name|truncatechars:25 }}</td>
                                <td>{{ service.venue.name|truncatechars:20 }}</td>
                                <td>
                                    <span class="badge bg-success">${{ service.revenue|floatformat:2 }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">No service revenue data available for this period.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent High-Value Bookings -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-money-bill-wave"></i> Recent Bookings</h5>
                <small class="text-muted">From selected period</small>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <input type="search" id="recent-bookings-search" class="form-control" placeholder="Quick search..." aria-label="Search bookings">
                </div>
                {% if bookings %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Venue</th>
                                <th>Status</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for booking in bookings|slice:":20" %}
                            <tr>
                                <td>{{ booking.booking_date|date:"M d, Y" }}</td>
                                <td>{{ booking.customer.get_full_name|default:booking.customer.email|truncatechars:20 }}</td>
                                <td>{{ booking.venue.name|truncatechars:25 }}</td>
                                <td>
                                    <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'completed' %}info{% else %}warning{% endif %}">
                                        {{ booking.status|title }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-success">${{ booking.total_price|floatformat:2 }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% if bookings.count > 20 %}
                <div class="text-center mt-3">
                    <small class="text-muted">Showing first 20 of {{ bookings.count }} bookings</small>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <p class="text-muted">{% trans "No bookings found for the selected period." %}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
