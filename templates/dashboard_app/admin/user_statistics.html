{% extends 'dashboard_app/base_dashboard.html' %}

{% block title %}User Statistics - Admin Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/admin_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'admin_app:user_list' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-users-cog"></i> Manage Users
    </a>
    <a href="{% url 'admin:index' %}" class="btn btn-sm btn-outline-secondary">
        <i class="fas fa-cog"></i> Django Admin
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'dashboard_app:admin_dashboard' %}">Dashboard</a></li>
    <li class="breadcrumb-item active" aria-current="page">User Statistics</li>
  </ol>
</nav>
<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar"></i> Date Range Filter</h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3" aria-label="User statistics filter">
                    <div class="col-md-3">
                        {{ form.period.label_tag }}
                        {{ form.period }}
                    </div>
                    <div class="col-md-3">
                        {{ form.start_date.label_tag }}
                        {{ form.start_date }}
                    </div>
                    <div class="col-md-3">
                        {{ form.end_date.label_tag }}
                        {{ form.end_date }}
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter"></i> Apply Filter
                        </button>
                        <a href="{% url 'dashboard_app:admin_user_statistics' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
                <div class="mt-2">
                    <small class="text-muted">
                        Showing data from {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Overview -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h3 class="text-primary">{{ total_users }}</h3>
                <p class="mb-0">Total Users</p>
                <small class="text-success">+{{ new_users_count }} this period</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-user-tag fa-2x text-info mb-2"></i>
                <h3 class="text-info">{{ customer_count }}</h3>
                <p class="mb-0">Customers</p>
                <small class="text-success">+{{ new_customers }} this period</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-store fa-2x text-warning mb-2"></i>
                <h3 class="text-warning">{{ provider_count }}</h3>
                <p class="mb-0">Providers</p>
                <small class="text-success">+{{ new_providers }} this period</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                <h3 class="text-success">{{ active_users_count }}</h3>
                <p class="mb-0">Active Users</p>
                <small class="text-muted">Last 30 days</small>
            </div>
        </div>
    </div>
</div>

<!-- User Type Distribution -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> User Type Distribution</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h4 class="text-info">{{ customer_count }}</h4>
                            <p class="mb-0">Customers</p>
                            <small class="text-muted">
                                {% widthratio customer_count total_users 100 %}% of total
                            </small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h4 class="text-warning">{{ provider_count }}</h4>
                            <p class="mb-0">Providers</p>
                            <small class="text-muted">
                                {% widthratio provider_count total_users 100 %}% of total
                            </small>
                        </div>
                    </div>
                </div>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar bg-info" role="progressbar"
                         style="width: {% widthratio customer_count total_users 100 %}%">
                        Customers ({{ customer_count }})
                    </div>
                    <div class="progress-bar bg-warning" role="progressbar"
                         style="width: {% widthratio provider_count total_users 100 %}%">
                        Providers ({{ provider_count }})
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> New User Growth</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-4 mb-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ new_users_count }}</h4>
                            <p class="mb-0">Total New</p>
                        </div>
                    </div>
                    <div class="col-4 mb-3">
                        <div class="text-center">
                            <h4 class="text-info">{{ new_customers }}</h4>
                            <p class="mb-0">New Customers</p>
                        </div>
                    </div>
                    <div class="col-4 mb-3">
                        <div class="text-center">
                            <h4 class="text-warning">{{ new_providers }}</h4>
                            <p class="mb-0">New Providers</p>
                        </div>
                    </div>
                </div>
                {% if new_users_count > 0 %}
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar bg-info" role="progressbar"
                         style="width: {% widthratio new_customers new_users_count 100 %}%">
                        Customers
                    </div>
                    <div class="progress-bar bg-warning" role="progressbar"
                         style="width: {% widthratio new_providers new_users_count 100 %}%">
                        Providers
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Daily Registration Trends -->
{% if daily_registrations %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Daily Registration Trends</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>New Registrations</th>
                                <th>Visual</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for date, count in daily_registrations.items %}
                            <tr>
                                <td>{{ date }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ count }}</span>
                                </td>
                                <td>
                                    <div class="progress" style="height: 15px; width: 200px;">
                                        <div class="progress-bar bg-primary" role="progressbar"
                                             style="width: {% if count > 0 %}{% widthratio count 10 100 %}{% else %}0{% endif %}%">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Top Users -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-trophy"></i> Top Customers</h5>
                <small class="text-muted">By booking count</small>
            </div>
            <div class="card-body">
                {% if top_customers %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Email</th>
                                <th>Bookings</th>
                                <th>Joined</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in top_customers %}
                            <tr>
                                <td>{{ customer.get_full_name|default:"N/A" }}</td>
                                <td>{{ customer.email|truncatechars:25 }}</td>
                                <td>
                                    <span class="badge bg-success">{{ customer.booking_count }}</span>
                                </td>
                                <td>{{ customer.date_joined|date:"M d, Y" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">No customer data available.</p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-star"></i> Top Providers</h5>
                <small class="text-muted">By booking count</small>
            </div>
            <div class="card-body">
                {% if top_providers %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Provider</th>
                                <th>Email</th>
                                <th>Venues</th>
                                <th>Bookings</th>
                                <th>Joined</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for provider in top_providers %}
                            <tr>
                                <td>{{ provider.get_full_name|default:"N/A" }}</td>
                                <td>{{ provider.email|truncatechars:20 }}</td>
                                <td>
                                    <span class="badge bg-info">{{ provider.venue_count }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ provider.booking_count }}</span>
                                </td>
                                <td>{{ provider.date_joined|date:"M d, Y" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">No provider data available.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
