{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}Today's Bookings - Provider Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Today's Bookings{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-list"></i> All Bookings
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'dashboard_app:provider_dashboard' %}">Dashboard</a></li>
    <li class="breadcrumb-item active" aria-current="page">Today's Bookings</li>
  </ol>
</nav>



<!-- Filter and Stats -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Bookings</h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3" aria-label="Today's bookings filter">
                    <div class="col-md-6">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select" aria-label="Status filter">
                            <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Status</option>
                            <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                            <option value="confirmed" {% if status_filter == 'confirmed' %}selected{% endif %}>Confirmed</option>
                            <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                            <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2" aria-label="Filter bookings">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn btn-outline-secondary" aria-label="Clear filters">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Today's Summary</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ total_count }}</h4>
                        <small class="text-muted">Total</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">{{ pending_count }}</h4>
                        <small class="text-muted">Pending</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-success">{{ confirmed_count }}</h4>
                        <small class="text-muted">Confirmed</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ completed_count }}</h4>
                        <small class="text-muted">Completed</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Bookings by Time Slot -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    Bookings for {{ today|date:"F d, Y" }}
                </h5>
                <span class="badge bg-primary">{{ total_count }} booking{{ total_count|pluralize }}</span>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <input type="search" id="booking-search" class="form-control" placeholder="Quick search..." aria-label="Search bookings">
                </div>
                {% include 'booking_cart_app/includes/booking_skeleton.html' %}
                <div id="bookings-results" style="display:none;">
                {% if bookings_by_time %}
                    {% for time_slot, booking_items in bookings_by_time.items %}
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2">
                                <i class="fas fa-clock me-2"></i>{{ time_slot }}
                            </h6>
                            <div class="row">
                                {% for item in booking_items %}
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-start border-4 border-{% if item.booking.status == 'confirmed' %}success{% elif item.booking.status == 'pending' %}warning{% elif item.booking.status == 'completed' %}info{% else %}secondary{% endif %}">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="card-title mb-1">{{ item.service_title }}</h6>
                                                        <p class="card-text text-muted mb-1">
                                                            <i class="fas fa-user me-1"></i>
                                                            {{ item.booking.customer.get_full_name|default:item.booking.customer.email }}
                                                        </p>
                                                        <p class="card-text text-muted mb-1">
                                                            <i class="fas fa-dollar-sign me-1"></i>
                                                            ${{ item.service_price|floatformat:2 }}
                                                        </p>
                                                        {% if item.booking.notes %}
                                                            <p class="card-text">
                                                                <small class="text-muted">
                                                                    <i class="fas fa-sticky-note me-1"></i>
                                                                    {{ item.booking.notes|truncatechars:50 }}
                                                                </small>
                                                            </p>
                                                        {% endif %}
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-{% if item.booking.status == 'confirmed' %}success{% elif item.booking.status == 'pending' %}warning{% elif item.booking.status == 'completed' %}info{% else %}secondary{% endif %}">
                                                            {{ item.booking.get_status_display }}
                                                        </span>
                                                        <div class="mt-2">
                                                            <a href="{% url 'booking_cart_app:booking_detail' item.booking.booking_id %}"
                                                               class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye"></i> View
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">No bookings found for today</h5>
                        {% if status_filter != 'all' %}
                            <p class="text-muted">Try changing the status filter to see more bookings.</p>
                            <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn btn-outline-primary" aria-label="Clear filter">
                                <i class="fas fa-times"></i> Clear Filter
                            </a>
                        {% else %}
                            <p class="text-muted">Your schedule is clear for today!</p>
                        {% endif %}
                    </div>
                {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
{% if bookings_by_time %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <a href="{% url 'booking_cart_app:provider_booking_list' %}?status=pending" class="btn btn-outline-warning">
                        <i class="fas fa-clock"></i> Review Pending Bookings
                    </a>
                    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-outline-info">
                        <i class="fas fa-spa"></i> Manage Services
                    </a>
                    <a href="{% url 'dashboard_app:provider_earnings_reports' %}" class="btn btn-outline-success">
                        <i class="fas fa-chart-line"></i> View Earnings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
{% block extra_js %}<script src="{% static 'js/bookings.js' %}"></script>{% endblock %}
