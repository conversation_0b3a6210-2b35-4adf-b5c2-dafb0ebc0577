{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load math_filters %}

{% block title %}Service Performance - Provider Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Service Performance{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-spa"></i> Manage Services
    </a>
    <a href="{% url 'venues_app:service_create' %}" class="btn btn-sm btn-outline-success">
        <i class="fas fa-plus"></i> Add Service
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'dashboard_app:provider_dashboard' %}">Dashboard</a></li>
    <li class="breadcrumb-item active" aria-current="page">Service Performance</li>
  </ol>
</nav>



<!-- Performance Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Service Performance Overview</h5>
            </div>
            <div class="card-body">
                {% if service_performance %}
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">{{ service_performance|length }}</h4>
                            <small class="text-muted">Active Services</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">
                                ${% for perf in service_performance %}{{ perf.total_revenue|add:0 }}{% if not forloop.last %}{% endif %}{% endfor %}
                            </h4>
                            <small class="text-muted">Total Revenue</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">
                                {% for perf in service_performance %}{{ perf.total_bookings|add:0 }}{% if not forloop.last %}{% endif %}{% endfor %}
                            </h4>
                            <small class="text-muted">Total Bookings</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">
                                {% if service_performance.0 %}{{ service_performance.0.service.service_title|truncatechars:15 }}{% else %}N/A{% endif %}
                            </h4>
                            <small class="text-muted">Top Performer</small>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No services found. Add your first service to see performance data.</p>
                        <a href="{% url 'venues_app:service_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Service
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Service Performance Cards -->
{% if service_performance %}
<div class="row">
    {% for perf in service_performance %}
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-spa me-2"></i>{{ perf.service.service_title }}
                    </h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{% url 'venues_app:service_edit' perf.service.id %}">
                                    <i class="fas fa-edit me-2"></i>Edit Service
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'booking_cart_app:provider_booking_list' %}?service={{ perf.service.id }}">
                                    <i class="fas fa-list me-2"></i>View Bookings
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Service Stats -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success mb-1">${{ perf.total_revenue|floatformat:2 }}</h4>
                                <small class="text-muted">Total Revenue</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-info mb-1">{{ perf.total_bookings }}</h4>
                                <small class="text-muted">Total Bookings</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-warning mb-1">${{ perf.revenue_per_booking|floatformat:2 }}</h4>
                                <small class="text-muted">Avg per Booking</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary mb-1">{{ perf.recent_bookings }}</h4>
                                <small class="text-muted">Last 30 Days</small>
                            </div>
                        </div>
                    </div>

                    <!-- Service Details -->
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <p class="text-muted mb-2">
                                <i class="fas fa-clock me-2"></i>
                                Duration: {{ perf.service.duration_minutes }} minutes
                            </p>
                            <p class="text-muted mb-2">
                                <i class="fas fa-dollar-sign me-2"></i>
                                Price: ${{ perf.service.price_min|floatformat:2 }}
                                {% if perf.service.price_max and perf.service.price_max != perf.service.price_min %}
                                    - ${{ perf.service.price_max|floatformat:2 }}
                                {% endif %}
                            </p>
                            {% if perf.avg_rating %}
                                <p class="text-muted mb-2">
                                    <i class="fas fa-star me-2"></i>
                                    Rating: {{ perf.avg_rating|floatformat:1 }}/5.0
                                </p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Performance Indicator -->
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small class="text-muted">Performance</small>
                            <small class="text-muted">
                                {% if service_performance.0.total_revenue > 0 %}
                                    {{ perf.total_revenue|mul:100|div:service_performance.0.total_revenue|floatformat:0 }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </small>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-success"
                                 style="width: {% if service_performance.0.total_revenue > 0 %}{{ perf.total_revenue|mul:100|div:service_performance.0.total_revenue }}{% else %}0{% endif %}%">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">
                            Status:
                            <span class="badge bg-{% if perf.service.is_active %}success{% else %}secondary{% endif %}">
                                {% if perf.service.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                        </small>
                        <small class="text-muted">
                            {% if perf.recent_bookings > 0 %}
                                <i class="fas fa-trending-up text-success"></i> Active
                            {% else %}
                                <i class="fas fa-trending-down text-warning"></i> Needs Attention
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Performance Insights -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Performance Insights</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-trophy me-2"></i>Best Performer</h6>
                            <p class="mb-0">
                                <strong>{{ service_performance.0.service.service_title }}</strong><br>
                                <small>${{ service_performance.0.total_revenue|floatformat:2 }} total revenue</small>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-chart-line me-2"></i>Most Popular</h6>
                            {% with most_booked=service_performance|dictsort:"total_bookings"|last %}
                            <p class="mb-0">
                                <strong>{{ most_booked.service.service_title }}</strong><br>
                                <small>{{ most_booked.total_bookings }} total bookings</small>
                            </p>
                            {% endwith %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Needs Attention</h6>
                            {% with least_recent=service_performance|dictsort:"recent_bookings"|first %}
                            {% if least_recent.recent_bookings == 0 %}
                                <p class="mb-0">
                                    <strong>{{ least_recent.service.service_title }}</strong><br>
                                    <small>No bookings in last 30 days</small>
                                </p>
                            {% else %}
                                <p class="mb-0">All services performing well!</p>
                            {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
