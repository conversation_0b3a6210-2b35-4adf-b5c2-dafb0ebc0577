{% extends 'notifications_app/base_notifications.html' %}
{% load static %}

{% block title %}Notifications - CozyWish{% endblock %}

{% block notifications_extra_css %}
<style>
    /* Minimal Notification List Design */

    /* Page Container */
    .notifications-wrapper {
        background: white;
        min-height: 100vh;
        padding: 2rem 0;
    }

    .notifications-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Page Header */
    .notifications-page-header {
        text-align: left;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--cw-neutral-200);
    }

    .notifications-page-title {
        font-family: var(--cw-font-heading);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-secondary-950);
        margin: 0;
        letter-spacing: -0.02em;
        text-transform: uppercase;
    }

    /* Notification Cards */
    .notification-item {
        background: var(--cw-gradient-card-subtle);
        border-radius: 0.75rem;
        margin-bottom: 1rem;
        border: 1px solid var(--cw-neutral-200);
        transition: all 0.2s ease;
        overflow: hidden;
    }

    .notification-item:hover {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 2px 8px rgba(47, 22, 15, 0.1);
    }

    .notification-item.unread {
        border-left: 3px solid var(--cw-brand-primary);
    }

    .notification-content {
        padding: 1.5rem;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    /* Avatar/Icon */
    .notification-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: var(--cw-neutral-100);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        border: 2px solid var(--cw-neutral-200);
        font-size: 1.2rem;
        color: var(--cw-neutral-600);
    }

    .notification-avatar.booking {
        background: var(--cw-success);
        color: white;
        border-color: var(--cw-success);
    }

    .notification-avatar.payment {
        background: var(--cw-info);
        color: white;
        border-color: var(--cw-info);
    }

    .notification-avatar.review {
        background: var(--cw-warning);
        color: white;
        border-color: var(--cw-warning);
    }

    .notification-avatar.announcement {
        background: var(--cw-error);
        color: white;
        border-color: var(--cw-error);
    }

    .notification-avatar.message {
        background: #6366f1;
        color: white;
        border-color: #6366f1;
    }

    .notification-avatar.comment {
        background: #8b5cf6;
        color: white;
        border-color: #8b5cf6;
    }

    .notification-avatar.connect {
        background: #06b6d4;
        color: white;
        border-color: #06b6d4;
    }

    /* Main Content */
    .notification-main {
        flex: 1;
        min-width: 0;
    }

    /* Category Badge */
    .notification-category {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        background: var(--cw-neutral-100);
        color: var(--cw-neutral-700);
    }

    .notification-category.booking {
        background: rgba(5, 150, 105, 0.1);
        color: var(--cw-success);
    }

    .notification-category.payment {
        background: rgba(2, 132, 199, 0.1);
        color: var(--cw-info);
    }

    .notification-category.review {
        background: rgba(217, 119, 6, 0.1);
        color: var(--cw-warning);
    }

    .notification-category.announcement {
        background: rgba(220, 38, 38, 0.1);
        color: var(--cw-error);
    }

    .notification-category.message {
        background: rgba(99, 102, 241, 0.1);
        color: #6366f1;
    }

    .notification-category.comment {
        background: rgba(139, 92, 246, 0.1);
        color: #8b5cf6;
    }

    .notification-category.connect {
        background: rgba(6, 182, 212, 0.1);
        color: #06b6d4;
    }

    /* Title and Description */
    .notification-title {
        font-size: 1rem;
        font-weight: 600;
        color: var(--cw-secondary-950);
        margin-bottom: 0.25rem;
        line-height: 1.4;
        font-family: var(--cw-font-heading);
    }

    .notification-description {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 0.5rem;
    }

    .notification-user {
        color: var(--cw-brand-primary);
        font-weight: 600;
        font-size: 0.875rem;
    }

    /* Timestamp */
    .notification-timestamp {
        flex-shrink: 0;
        color: var(--cw-neutral-500);
        font-size: 0.75rem;
        text-align: right;
        margin-left: auto;
        padding-left: 1rem;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 0.75rem;
        border: 1px solid var(--cw-neutral-200);
    }

    .empty-state-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--cw-neutral-100);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: var(--cw-neutral-500);
        font-size: 2rem;
    }

    .empty-state h3 {
        color: var(--cw-secondary-950);
        font-family: var(--cw-font-heading);
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .empty-state p {
        color: var(--cw-neutral-600);
        margin-bottom: 2rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .notifications-page-title {
            font-size: 2rem;
        }

        .notification-content {
            padding: 1rem;
            gap: 0.75rem;
        }

        .notification-avatar {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .notification-timestamp {
            padding-left: 0.5rem;
        }
    }

    @media (max-width: 480px) {
        .notifications-wrapper {
            padding: 1rem 0;
        }

        .notification-content {
            flex-direction: column;
            text-align: center;
        }

        .notification-timestamp {
            margin-left: 0;
            padding-left: 0;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block notifications_content %}
<div class="notifications-wrapper">
    <div class="notifications-container">
        <!-- Page Header -->
        <div class="notifications-page-header">
            <h1 class="notifications-page-title">NOTIFICATIONS</h1>
        </div>

        <!-- System Messages -->
        {% if messages %}
        <div class="mb-3">
            {% for message in messages %}
            <div class="alert alert-info alert-dismissible fade show" role="alert" style="border-radius: 0.75rem; border: 1px solid var(--cw-brand-accent); background: var(--cw-accent-light); color: var(--cw-brand-primary);">
                <i class="fas fa-info-circle me-2"></i>{{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Notification List -->
        {% if notifications %}
        <div class="notification-list">
            {% for notification in notifications %}
            <div class="notification-item {% if notification.read_status == 'unread' %}unread{% endif %}"
                 data-notification-id="{{ notification.id }}">
                <div class="notification-content">
                    <!-- Avatar/Icon -->
                    <div class="notification-avatar {{ notification.notification_type }}">
                        {% if notification.notification_type == 'booking' %}
                            <i class="fas fa-calendar-check"></i>
                        {% elif notification.notification_type == 'payment' %}
                            <i class="fas fa-credit-card"></i>
                        {% elif notification.notification_type == 'review' %}
                            <i class="fas fa-star"></i>
                        {% elif notification.notification_type == 'announcement' %}
                            <i class="fas fa-bullhorn"></i>
                        {% elif notification.notification_type == 'message' %}
                            <i class="fas fa-envelope"></i>
                        {% elif notification.notification_type == 'comment' %}
                            <i class="fas fa-comment"></i>
                        {% elif notification.notification_type == 'connect' %}
                            <i class="fas fa-user-plus"></i>
                        {% else %}
                            <i class="fas fa-bell"></i>
                        {% endif %}
                    </div>

                    <!-- Main Content -->
                    <div class="notification-main">
                        <!-- Category Badge -->
                        <span class="notification-category {{ notification.notification_type }}">
                            {% if notification.notification_type == 'booking' %}
                                Booking Update
                            {% elif notification.notification_type == 'payment' %}
                                Payment
                            {% elif notification.notification_type == 'review' %}
                                Review
                            {% elif notification.notification_type == 'announcement' %}
                                Announcement
                            {% elif notification.notification_type == 'message' %}
                                Message
                            {% elif notification.notification_type == 'comment' %}
                                Comment
                            {% elif notification.notification_type == 'connect' %}
                                Connect
                            {% else %}
                                {{ notification.get_notification_type_display }}
                            {% endif %}
                        </span>

                        <!-- Title -->
                        <div class="notification-title">{{ notification.title }}</div>

                        <!-- Description -->
                        <div class="notification-description">
                            {{ notification.message|truncatechars:150 }}
                        </div>

                        <!-- User -->
                        {% if notification.user.first_name or notification.user.last_name %}
                        <div class="notification-user">
                            {{ notification.user.first_name }} {{ notification.user.last_name }}
                        </div>
                        {% elif notification.user.email %}
                        <div class="notification-user">
                            {{ notification.user.email }}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Timestamp -->
                    <div class="notification-timestamp">
                        {{ notification.created_at|date:"d M Y" }} at {{ notification.created_at|date:"g:i A" }}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if notifications.has_other_pages %}
        <nav aria-label="Notification pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if notifications.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ notifications.previous_page_number }}" style="color: var(--cw-brand-primary); border-color: var(--cw-neutral-300);">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                {% endif %}

                {% for i in notifications.paginator.page_range %}
                {% if notifications.number == i %}
                <li class="page-item active">
                    <a class="page-link" href="#" style="background: var(--cw-brand-primary); border-color: var(--cw-brand-primary);">{{ i }}</a>
                </li>
                {% elif i > notifications.number|add:'-3' and i < notifications.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ i }}" style="color: var(--cw-brand-primary); border-color: var(--cw-neutral-300);">{{ i }}</a>
                </li>
                {% endif %}
                {% endfor %}

                {% if notifications.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ notifications.next_page_number }}" style="color: var(--cw-brand-primary); border-color: var(--cw-neutral-300);">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <!-- Empty State -->
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-bell-slash"></i>
            </div>
            <h3>No Notifications Yet</h3>
            <p>You're all caught up! When you have new activities, they'll appear here.</p>
            {% if user.role == 'customer' %}
            <a href="{% url 'venues_app:venue_list' %}" class="btn btn-cw-primary">
                <i class="fas fa-search me-2"></i>Browse Venues
            </a>
            {% elif user.role == 'service_provider' %}
            <a href="{% url 'dashboard_app:provider_dashboard' %}" class="btn btn-cw-primary">
                <i class="fas fa-tachometer-alt me-2"></i>Provider Dashboard
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript for enhanced interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handler for notification items
    const notificationItems = document.querySelectorAll('.notification-item');

    notificationItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Don't navigate if clicking on buttons or links
            if (e.target.closest('button') || e.target.closest('a')) return;

            const notificationId = this.dataset.notificationId;
            if (notificationId) {
                // Navigate to notification detail page
                window.location.href = `{% url 'notifications_app:notification_detail' 0 %}`.replace('0', notificationId);
            }
        });

        // Add cursor pointer style
        item.style.cursor = 'pointer';
    });
});
</script>
{% endblock %}
