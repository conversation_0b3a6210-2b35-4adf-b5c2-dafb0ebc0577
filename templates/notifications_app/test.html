{% extends 'notifications_app/base_notifications.html' %}

{% block title %}Notifications Test{% endblock %}

{% block notifications_content %}
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h4 class="mb-0"><i class="fas fa-flask me-2"></i>Notifications App Test</h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Development Mode Only:</strong> This test page is only available when DEBUG=True.
                    </div>

                    <p class="lead">Test the notifications system by creating sample notifications.</p>

                    <!-- User Stats -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">{{ notification_count }}</h5>
                                    <p class="card-text text-muted">Total Notifications</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">{{ unread_count }}</h5>
                                    <p class="card-text text-muted">Unread Notifications</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">{{ user.role|title }}</h5>
                                    <p class="card-text text-muted">Account Type</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Create Test Notification -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-plus me-2"></i>Create Test Notification</h6>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-bell me-2"></i>Create Test Notification
                                    </button>
                                </div>
                            </form>
                            <small class="text-muted">This will create a sample system notification for testing purposes.</small>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <h6 class="mb-3"><i class="fas fa-link me-2"></i>Quick Links</h6>
                    <div class="list-group">
                        <a href="{% url 'notifications_app:notification_list' %}" class="list-group-item list-group-item-action">
                            <i class="fas fa-list me-2"></i>View My Notifications
                        </a>
                        {% if user.role == 'admin' or user.is_superuser %}
                            <a href="{% url 'notifications_app:admin_notification_dashboard' %}" class="list-group-item list-group-item-action">
                                <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                            </a>
                            <a href="{% url 'notifications_app:admin_create_announcement' %}" class="list-group-item list-group-item-action">
                                <i class="fas fa-bullhorn me-2"></i>Create Announcement
                            </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-1"></i>Back to Home
                        </a>
                        {% if user.role == 'customer' %}
                            <a href="{% url 'dashboard_app:customer_dashboard' %}" class="btn btn-outline-primary">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        {% elif user.role == 'service_provider' %}
                            <a href="{% url 'dashboard_app:provider_dashboard' %}" class="btn btn-outline-primary">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        {% elif user.role == 'admin' or user.is_superuser %}
                            <a href="{% url 'dashboard_app:admin_dashboard' %}" class="btn btn-outline-primary">
                                <i class="fas fa-tachometer-alt me-1"></i>Admin Dashboard
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide success messages after 3 seconds
    const alerts = document.querySelectorAll('.alert-success');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 3000);
    });
});
</script>
{% endblock %}
