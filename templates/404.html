{% extends 'base.html' %}
{% load static %}

{% block title %}Page Not Found - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        min-height: 60vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem 0;
    }

    .error-content {
        max-width: 600px;
        margin: 0 auto;
    }

    .error-code {
        font-size: 8rem;
        font-weight: bold;
        color: #6c757d;
        line-height: 1;
        margin-bottom: 1rem;
    }

    .error-title {
        font-size: 2rem;
        font-weight: 600;
        color: #212529;
        margin-bottom: 1rem;
    }

    .error-message {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-primary {
        background-color: #000;
        border-color: #000;
        padding: 0.75rem 2rem;
        font-weight: 500;
    }

    .btn-primary:hover {
        background-color: #333;
        border-color: #333;
    }

    .btn-outline-secondary {
        color: #6c757d;
        border-color: #6c757d;
        padding: 0.75rem 2rem;
        font-weight: 500;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }

    .error-icon {
        font-size: 4rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .error-code {
            font-size: 6rem;
        }

        .error-title {
            font-size: 1.5rem;
        }

        .error-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn {
            width: 100%;
            max-width: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>

        <div class="error-code">404</div>

        <h1 class="error-title">Page Not Found</h1>

        <p class="error-message">
            Sorry, the page you're looking for doesn't exist or has been moved.
            This might happen if you followed an old link or typed the URL incorrectly.
        </p>

        <div class="error-actions">
            <a href="{% url 'home' %}" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>Go to Homepage
            </a>
            <a href="{% url 'venues_app:venue_search' %}" class="btn btn-outline-secondary">
                <i class="fas fa-search me-2"></i>Search Venues
            </a>
        </div>

        {% if request_path %}
        <div class="mt-4">
            <small class="text-muted">
                Requested path: <code>{{ request_path }}</code>
            </small>
        </div>
        {% endif %}

        <div class="mt-3">
            <small class="text-muted">
                Need help? Contact us at
                <a href="mailto:{{ support_email|default:'<EMAIL>' }}" class="text-decoration-none">
                    {{ support_email|default:'<EMAIL>' }}
                </a>
            </small>
        </div>
    </div>
</div>
{% endblock %}
