{% extends 'admin_app/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ title }} - CozyWish Admin{% endblock %}

{% block breadcrumbs %}
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'admin_app:user_list' %}">Users</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
</nav>
{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">{{ title }}</h1>
    <a href="{% url 'admin_app:user_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Users
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>User Information</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                Email Address <span class="text-danger">*</span>
                            </label>
                            {{ form.email|add_class:"form-control" }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.role.id_for_label }}" class="form-label">
                                User Role <span class="text-danger">*</span>
                            </label>
                            {{ form.role|add_class:"form-select" }}
                            {% if form.role.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.role.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Password Fields (for new users) -->
                    {% if 'Create' in title %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">
                                Password <span class="text-danger">*</span>
                            </label>
                            {{ form.password1|add_class:"form-control" }}
                            {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password1.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Password must be at least 8 characters long.
                            </small>
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">
                                Confirm Password <span class="text-danger">*</span>
                            </label>
                            {{ form.password2|add_class:"form-control" }}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Account Status -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-check">
                                {{ form.is_active|add_class:"form-check-input" }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Account is active
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                {{ form.is_staff|add_class:"form-check-input" }}
                                <label class="form-check-label" for="{{ form.is_staff.id_for_label }}">
                                    Staff status (can access admin)
                                </label>
                            </div>
                            {% if form.is_staff.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_staff.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Groups and Permissions -->
                    {% if form.groups %}
                    <div class="mb-4">
                        <label for="{{ form.groups.id_for_label }}" class="form-label">
                            User Groups
                        </label>
                        {{ form.groups|add_class:"form-select" }}
                        {% if form.groups.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.groups.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Hold Ctrl/Cmd to select multiple groups.
                        </small>
                    </div>
                    {% endif %}

                    <!-- Additional Notes -->
                    <div class="mb-4">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                  placeholder="Internal notes about this user (not visible to user)">{{ form.admin_notes.value|default:'' }}</textarea>
                        <small class="form-text text-muted">
                            These notes are only visible to admin users.
                        </small>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {% if 'Create' in title %}Create User{% else %}Save Changes{% endif %}
                            </button>
                            <a href="{% url 'admin_app:user_list' %}" class="btn btn-outline-secondary ms-2">
                                Cancel
                            </a>
                        </div>
                        {% if 'Edit' in title %}
                        <div>
                            <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#resetPasswordModal">
                                <i class="fas fa-key me-1"></i>
                                Reset Password
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Help and Guidelines -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Guidelines</h5>
            </div>
            <div class="card-body">
                <h6>User Roles:</h6>
                <ul class="list-unstyled">
                    <li><strong>Customer:</strong> Can book services and leave reviews</li>
                    <li><strong>Service Provider:</strong> Can create venues and manage bookings</li>
                    <li><strong>Staff:</strong> Has administrative access</li>
                </ul>

                <hr>

                <h6>Account Status:</h6>
                <ul class="list-unstyled">
                    <li><strong>Active:</strong> User can log in and use the platform</li>
                    <li><strong>Inactive:</strong> User account is disabled</li>
                    <li><strong>Staff:</strong> User can access admin panel</li>
                </ul>

                <hr>

                <h6>Password Requirements:</h6>
                <ul class="list-unstyled">
                    <li>• Minimum 8 characters</li>
                    <li>• Cannot be too common</li>
                    <li>• Cannot be entirely numeric</li>
                    <li>• Cannot be too similar to user information</li>
                </ul>
            </div>
        </div>

        {% if 'Edit' in title %}
        <!-- User Statistics -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>User Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <h6 class="text-muted">Member Since</h6>
                        <p class="mb-0">{{ user.date_joined|date:"F d, Y" }}</p>
                    </div>
                    <div class="col-12 mb-3">
                        <h6 class="text-muted">Last Login</h6>
                        <p class="mb-0">
                            {% if user.last_login %}
                                {{ user.last_login|timesince }} ago
                            {% else %}
                                Never
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-12">
                        <h6 class="text-muted">Failed Login Attempts</h6>
                        <p class="mb-0">{{ user.failed_login_count|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% if 'Edit' in title %}
<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">Reset User Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reset this user's password? A new temporary password will be generated and sent to their email address.</p>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action cannot be undone. The user will need to use the new password to log in.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="#" style="display: inline;">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="reset_password">
                    <button type="submit" class="btn btn-warning">Reset Password</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block admin_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation feedback
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });

    // Password strength indicator (for new users)
    const password1 = document.getElementById('{{ form.password1.id_for_label }}');
    if (password1) {
        password1.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;

            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            // Update visual feedback based on strength
            this.classList.remove('is-valid', 'is-invalid');
            if (strength >= 3) {
                this.classList.add('is-valid');
            } else if (password.length > 0) {
                this.classList.add('is-invalid');
            }
        });
    }
});
</script>
{% endblock %}
