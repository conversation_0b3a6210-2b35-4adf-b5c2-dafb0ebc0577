{% extends 'admin_app/base.html' %}
{% load static admin_extras %}

{% block title %}User Management - CozyWish Admin{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">User Management</h1>
    <div>
        <a href="{% url 'admin_app:bulk_user_actions' %}" class="btn btn-warning me-2">
            <i class="fas fa-tasks me-1"></i>
            Bulk Actions
        </a>
        <a href="#" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Add User
        </a>
    </div>
</div>

<!-- Filters and Search -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ request.GET.search }}" placeholder="Email, name...">
            </div>
            <div class="col-md-2">
                <label for="role" class="form-label">Role</label>
                <select class="form-select" id="role" name="role">
                    <option value="">All Roles</option>
                    <option value="customer" {% if request.GET.role == 'customer' %}selected{% endif %}>Customer</option>
                    <option value="service_provider" {% if request.GET.role == 'service_provider' %}selected{% endif %}>Provider</option>
                    <option value="staff" {% if request.GET.role == 'staff' %}selected{% endif %}>Staff</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_joined" class="form-label">Joined</label>
                <select class="form-select" id="date_joined" name="date_joined">
                    <option value="">All Time</option>
                    <option value="today" {% if request.GET.date_joined == 'today' %}selected{% endif %}>Today</option>
                    <option value="week" {% if request.GET.date_joined == 'week' %}selected{% endif %}>This Week</option>
                    <option value="month" {% if request.GET.date_joined == 'month' %}selected{% endif %}>This Month</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>
                    Filter
                </button>
                <a href="{% url 'admin_app:user_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            Users ({{ page_obj.paginator.count }} total)
        </h6>
    </div>
    <div class="card-body">
        {% if users %}
            <div class="table-responsive">
                <table class="table table-hover table-sticky">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>User</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input user-checkbox"
                                       value="{{ user.id }}">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-3">
                                        {% if user.is_customer and user.customer_profile.profile_picture %}
                                            <img src="{{ user.customer_profile.profile_picture.url }}"
                                                 class="rounded-circle" width="40" height="40"
                                                 style="object-fit: cover;">
                                        {% elif user.is_service_provider and user.service_provider_profile.business_logo %}
                                            <img src="{{ user.service_provider_profile.business_logo.url }}"
                                                 class="rounded-circle" width="40" height="40"
                                                 style="object-fit: cover;">
                                        {% else %}
                                            <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center"
                                                 style="width: 40px; height: 40px; font-size:0.9rem;">
                                                {{ user|initials }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ user.get_full_name|default:user.email }}</div>
                                        <small class="text-muted">{{ user.email }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if user.is_customer %}
                                    <span class="badge bg-primary">Customer</span>
                                {% elif user.is_service_provider %}
                                    <span class="badge bg-success">Provider</span>
                                {% elif user.is_staff %}
                                    <span class="badge bg-warning">Staff</span>
                                {% else %}
                                    <span class="badge bg-secondary">Unknown</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ user.date_joined|date:"M d, Y" }}</small>
                            </td>
                            <td>
                                <small>
                                    {% if user.last_login %}
                                        {{ user.last_login|timesince }} ago
                                    {% else %}
                                        Never
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'admin_app:user_detail' user_id=user.id %}"
                                       class="btn btn-sm btn-outline-primary" title="View Details" data-bs-toggle="tooltip">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'admin_app:user_edit' user_id=user.id %}"
                                       class="btn btn-sm btn-outline-secondary" title="Edit" data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if user.is_service_provider and not user.service_provider_profile.is_approved %}
                                    <a href="{% url 'admin_app:provider_approval' user_id=user.id %}"
                                       class="btn btn-sm btn-outline-warning" title="Review Provider" data-bs-toggle="tooltip">
                                        <i class="fas fa-check"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="User pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.role %}&role={{ request.GET.role }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_joined %}&date_joined={{ request.GET.date_joined }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.role %}&role={{ request.GET.role }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_joined %}&date_joined={{ request.GET.date_joined }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.role %}&role={{ request.GET.role }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_joined %}&date_joined={{ request.GET.date_joined }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.role %}&role={{ request.GET.role }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_joined %}&date_joined={{ request.GET.date_joined }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No users found</h5>
                <p class="text-muted">Try adjusting your search criteria or add a new user.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');

    selectAllCheckbox.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Update select all when individual checkboxes change
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === userCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < userCheckboxes.length;
        });
    });
});
</script>
{% endblock %}
