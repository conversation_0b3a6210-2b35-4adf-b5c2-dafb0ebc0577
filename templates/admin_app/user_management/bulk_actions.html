{% extends 'admin_app/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Bulk User Actions - CozyWish Admin{% endblock %}

{% block breadcrumbs %}
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'admin_app:user_list' %}">Users</a></li>
        <li class="breadcrumb-item active">Bulk Actions</li>
    </ol>
</nav>
{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Bulk User Actions</h1>
    <a href="{% url 'admin_app:user_list' %}" class="btn btn-outline-secondary" title="Back" data-bs-toggle="tooltip">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Users
    </a>
</div>

<div class="row section-spacing">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Select Action and Users</h5>
            </div>
            <div class="card-body">
                <form method="post" id="bulkActionForm" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger" role="alert">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Action Selection -->
                    <div class="mb-4">
                        <label for="{{ form.action.id_for_label }}" class="form-label">
                            Select Action <span class="text-danger">*</span>
                        </label>
                        {{ form.action|add_class:"form-select" }}
                        {% if form.action.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.action.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Choose the action to perform on selected users.
                        </small>
                    </div>

                    <!-- User Selection -->
                    <div class="mb-4">
                        <label class="form-label">
                            Select Users <span class="text-danger">*</span>
                        </label>

                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="userSearch"
                                       placeholder="Search users by email or name...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="roleFilter">
                                    <option value="">All Roles</option>
                                    <option value="customer">Customer</option>
                                    <option value="service_provider">Provider</option>
                                    <option value="staff">Staff</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>

                        <!-- User List -->
                        <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="selectAllUsers">
                                <label class="form-check-label fw-bold" for="selectAllUsers">
                                    Select All Users
                                </label>
                            </div>
                            <hr>

                            <div id="userList">
                                {% for user in users %}
                                <div class="form-check mb-2 user-item"
                                     data-email="{{ user.email|lower }}"
                                     data-name="{{ user.get_full_name|lower }}"
                                     data-role="{% if user.is_customer %}customer{% elif user.is_service_provider %}service_provider{% elif user.is_staff %}staff{% endif %}"
                                     data-status="{% if user.is_active %}active{% else %}inactive{% endif %}">
                                    <input class="form-check-input user-checkbox" type="checkbox"
                                           name="selected_users" value="{{ user.id }}" id="user_{{ user.id }}">
                                    <label class="form-check-label d-flex align-items-center" for="user_{{ user.id }}">
                                        <div class="me-3">
                                            {% if user.is_customer and user.customer_profile.profile_picture %}
                                                <img src="{{ user.customer_profile.profile_picture.url }}"
                                                     class="rounded-circle" width="30" height="30"
                                                     style="object-fit: cover;">
                                            {% elif user.is_service_provider and user.service_provider_profile.business_logo %}
                                                <img src="{{ user.service_provider_profile.business_logo.url }}"
                                                     class="rounded-circle" width="30" height="30"
                                                     style="object-fit: cover;">
                                            {% else %}
                                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                                     style="width: 30px; height: 30px;">
                                                    <i class="fas fa-user text-muted"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">{{ user.get_full_name|default:user.email }}</div>
                                            <small class="text-muted">{{ user.email }}</small>
                                            <div class="mt-1">
                                                {% if user.is_customer %}
                                                    <span class="badge bg-primary">Customer</span>
                                                {% elif user.is_service_provider %}
                                                    <span class="badge bg-success">Provider</span>
                                                {% elif user.is_staff %}
                                                    <span class="badge bg-warning">Staff</span>
                                                {% endif %}

                                                {% if user.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        {% if form.selected_users.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.selected_users.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <small class="form-text text-muted">
                            <span id="selectedCount">0</span> users selected
                        </small>
                    </div>

                    <!-- Reason/Notes -->
                    <div class="mb-4">
                        <label for="{{ form.reason.id_for_label }}" class="form-label">
                            Reason/Notes
                        </label>
                        {{ form.reason|add_class:"form-control" }}
                        {% if form.reason.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.reason.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Provide a reason for this bulk action (optional but recommended).
                        </small>
                    </div>

                    <!-- Confirmation -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmAction" required>
                            <label class="form-check-label" for="confirmAction">
                                I understand that this action will affect multiple users and cannot be easily undone.
                            </label>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-warning" id="executeButton" disabled>
                                <i class="fas fa-play me-1"></i>
                                Execute Bulk Action
                            </button>
                            <a href="{% url 'admin_app:user_list' %}" class="btn btn-outline-secondary ms-2">
                                Cancel
                            </a>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#previewModal">
                                <i class="fas fa-eye me-1"></i>
                                Preview Changes
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Action Information -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Action Information</h5>
            </div>
            <div class="card-body">
                <div id="actionInfo">
                    <p class="text-muted">Select an action to see details.</p>
                </div>
            </div>
        </div>

        <!-- Recent Bulk Actions -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Bulk Actions</h5>
            </div>
            <div class="card-body">
                {% if recent_bulk_actions %}
                    <div class="list-group list-group-flush">
                        {% for action in recent_bulk_actions %}
                        <div class="list-group-item">
                            <div class="fw-bold">{{ action.get_action_type_display }}</div>
                            <small class="text-muted">
                                {{ action.affected_count }} users - {{ action.executed_at|timesince }} ago
                                <br>by {{ action.executed_by.email }}
                            </small>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-history fa-2x mb-2"></i>
                        <p class="mb-0">No recent bulk actions</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Preview Bulk Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <p>Select an action and users to preview changes.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const actionSelect = document.getElementById('{{ form.action.id_for_label }}');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllUsers');
    const confirmCheckbox = document.getElementById('confirmAction');
    const executeButton = document.getElementById('executeButton');
    const selectedCountSpan = document.getElementById('selectedCount');
    const userSearch = document.getElementById('userSearch');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const userItems = document.querySelectorAll('.user-item');

    // Action information
    const actionInfo = {
        'activate': {
            title: 'Activate Users',
            description: 'Selected users will be activated and able to log in.',
            warning: 'This will allow users to access their accounts.'
        },
        'deactivate': {
            title: 'Deactivate Users',
            description: 'Selected users will be deactivated and unable to log in.',
            warning: 'This will prevent users from accessing their accounts.'
        },
        'delete': {
            title: 'Delete Users',
            description: 'Selected users will be permanently deleted.',
            warning: 'This action cannot be undone. All user data will be lost.'
        },
        'reset_password': {
            title: 'Reset Passwords',
            description: 'New temporary passwords will be generated and sent to users.',
            warning: 'Users will need to use the new passwords to log in.'
        }
    };

    // Update action information
    actionSelect.addEventListener('change', function() {
        const info = actionInfo[this.value];
        const actionInfoDiv = document.getElementById('actionInfo');

        if (info) {
            actionInfoDiv.innerHTML = `
                <h6>${info.title}</h6>
                <p>${info.description}</p>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${info.warning}
                </div>
            `;
        } else {
            actionInfoDiv.innerHTML = '<p class="text-muted">Select an action to see details.</p>';
        }

        updateExecuteButton();
    });

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        const visibleCheckboxes = Array.from(userCheckboxes).filter(cb =>
            !cb.closest('.user-item').style.display ||
            cb.closest('.user-item').style.display !== 'none'
        );

        visibleCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });

        updateSelectedCount();
        updateExecuteButton();
    });

    // Individual checkbox changes
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();
            updateExecuteButton();

            // Update select all checkbox
            const visibleCheckboxes = Array.from(userCheckboxes).filter(cb =>
                !cb.closest('.user-item').style.display ||
                cb.closest('.user-item').style.display !== 'none'
            );
            const checkedCount = visibleCheckboxes.filter(cb => cb.checked).length;

            selectAllCheckbox.checked = checkedCount === visibleCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < visibleCheckboxes.length;
        });
    });

    // Confirmation checkbox
    confirmCheckbox.addEventListener('change', updateExecuteButton);

    // Search and filter functionality
    function filterUsers() {
        const searchTerm = userSearch.value.toLowerCase();
        const roleFilter = document.getElementById('roleFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        userItems.forEach(item => {
            const email = item.dataset.email;
            const name = item.dataset.name;
            const role = item.dataset.role;
            const status = item.dataset.status;

            const matchesSearch = !searchTerm || email.includes(searchTerm) || name.includes(searchTerm);
            const matchesRole = !roleFilter || role === roleFilter;
            const matchesStatus = !statusFilter || status === statusFilter;

            if (matchesSearch && matchesRole && matchesStatus) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
                // Uncheck hidden items
                const checkbox = item.querySelector('.user-checkbox');
                if (checkbox.checked) {
                    checkbox.checked = false;
                }
            }
        });

        updateSelectedCount();
        updateExecuteButton();
    }

    userSearch.addEventListener('input', filterUsers);
    document.getElementById('roleFilter').addEventListener('change', filterUsers);
    document.getElementById('statusFilter').addEventListener('change', filterUsers);

    function updateSelectedCount() {
        const checkedCount = Array.from(userCheckboxes).filter(cb => cb.checked).length;
        selectedCountSpan.textContent = checkedCount;
    }

    function updateExecuteButton() {
        const hasAction = actionSelect.value !== '';
        const hasUsers = Array.from(userCheckboxes).some(cb => cb.checked);
        const isConfirmed = confirmCheckbox.checked;

        executeButton.disabled = !(hasAction && hasUsers && isConfirmed);
    }

    // Preview functionality
    document.querySelector('[data-bs-target="#previewModal"]').addEventListener('click', function() {
        const action = actionSelect.value;
        const selectedUsers = Array.from(userCheckboxes).filter(cb => cb.checked);
        const previewContent = document.getElementById('previewContent');

        if (!action || selectedUsers.length === 0) {
            previewContent.innerHTML = '<p>Select an action and users to preview changes.</p>';
            return;
        }

        const info = actionInfo[action];
        let html = `
            <h6>${info.title}</h6>
            <p>${info.description}</p>
            <div class="alert alert-info">
                <strong>Selected Users (${selectedUsers.length}):</strong>
            </div>
            <div class="list-group">
        `;

        selectedUsers.forEach(checkbox => {
            const userItem = checkbox.closest('.user-item');
            const label = userItem.querySelector('label');
            const userName = label.querySelector('.fw-bold').textContent;
            const userEmail = label.querySelector('.text-muted').textContent;

            html += `
                <div class="list-group-item">
                    <div class="fw-bold">${userName}</div>
                    <small class="text-muted">${userEmail}</small>
                </div>
            `;
        });

        html += '</div>';
        previewContent.innerHTML = html;
    });

    // Initialize
    updateSelectedCount();
    updateExecuteButton();
});
</script>
{% endblock %}
