{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Create Static Page - Admin Panel{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Create Static Page</h1>
    <a href="{% url 'admin_app:static_page_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Pages
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">Title</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger small">{{ form.title.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.slug.id_for_label }}" class="form-label">Slug</label>
                        {{ form.slug }}
                        <div class="form-text">URL-friendly version of the title (auto-generated if empty)</div>
                        {% if form.slug.errors %}
                            <div class="text-danger small">{{ form.slug.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.content.id_for_label }}" class="form-label">Content</label>
                        {{ form.content }}
                        {% if form.content.errors %}
                            <div class="text-danger small">{{ form.content.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                        {{ form.status }}
                        {% if form.status.errors %}
                            <div class="text-danger small">{{ form.status.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_featured }}
                            <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                Featured Page
                            </label>
                        </div>
                        <div class="form-text">Display prominently in navigation</div>
                        {% if form.is_featured.errors %}
                            <div class="text-danger small">{{ form.is_featured.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.featured_image.id_for_label }}" class="form-label">Featured Image</label>
                        {{ form.featured_image }}
                        <div class="form-text">JPG/PNG only, max 5MB</div>
                        {% if form.featured_image.errors %}
                            <div class="text-danger small">{{ form.featured_image.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- SEO Section -->
            <hr>
            <h5>SEO Settings</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.meta_title.id_for_label }}" class="form-label">Meta Title</label>
                        {{ form.meta_title }}
                        <div class="form-text">Max 60 characters (auto-generated if empty)</div>
                        {% if form.meta_title.errors %}
                            <div class="text-danger small">{{ form.meta_title.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.meta_keywords.id_for_label }}" class="form-label">Meta Keywords</label>
                        {{ form.meta_keywords }}
                        <div class="form-text">Comma-separated keywords</div>
                        {% if form.meta_keywords.errors %}
                            <div class="text-danger small">{{ form.meta_keywords.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.meta_description.id_for_label }}" class="form-label">Meta Description</label>
                {{ form.meta_description }}
                <div class="form-text">Max 160 characters (auto-generated if empty)</div>
                {% if form.meta_description.errors %}
                    <div class="text-danger small">{{ form.meta_description.errors.0 }}</div>
                {% endif %}
            </div>

            <div class="d-flex justify-content-end">
                <a href="{% url 'admin_app:static_page_list' %}" class="btn btn-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Create Page
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from title
    const titleField = document.getElementById('{{ form.title.id_for_label }}');
    const slugField = document.getElementById('{{ form.slug.id_for_label }}');

    if (titleField && slugField) {
        titleField.addEventListener('input', function() {
            if (!slugField.value) {
                const slug = this.value.toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .trim('-');
                slugField.value = slug;
            }
        });
    }
});
</script>
{% endblock %}
