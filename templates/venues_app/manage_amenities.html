{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}Manage Amenities - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap');

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: #2F160F;
        font-size: 2.5rem;
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(250, 225, 215, 0.2);
        border-radius: 0.75rem;
        flex-shrink: 0;
    }

    /* Form labels */
    .form-label {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    /* Form inputs */
    .form-control {
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: 'Inter', sans-serif;
        color: #2F160F;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
    }

    /* Button styling */
    .btn-cw-primary {
        background: white;
        border: 2px solid #2F160F;
        color: #2F160F;
        padding: 0.75rem 3rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-cw-primary:hover {
        background: #2F160F;
        border-color: #2F160F;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
        text-decoration: none;
    }

    .btn-cw-danger {
        background: white;
        border: 2px solid #dc3545;
        color: #dc3545;
        padding: 0.5rem 1.5rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .btn-cw-danger:hover {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Error messages */
    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        margin-top: 0.25rem;
    }

    /* Amenity items */
    .amenity-item {
        background: white;
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .amenity-item:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .amenity-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }

    .amenity-info h5 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin: 0;
        font-size: 1.125rem;
    }

    .amenity-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .no-amenities-message {
        text-align: center;
        padding: 3rem 2rem;
        color: #6b7280;
        background: rgba(250, 225, 215, 0.1);
        border-radius: 0.5rem;
        border: 1px dashed #fae1d7;
    }

    .no-amenities-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #d1d5db;
    }

    /* Amenity limit info */
    .amenity-limit-info {
        background: rgba(250, 225, 215, 0.3);
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
        color: #525252;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .amenity-limit-info i {
        color: #2F160F;
        font-size: 1.125rem;
    }

    .amenity-limit-info.warning {
        background: #fef3cd;
        border-color: #ffd60a;
        color: #856404;
    }

    .amenity-limit-info.warning i {
        color: #856404;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .amenity-content {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .amenity-actions {
            align-self: flex-start;
        }

        .form-section {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

    <!-- Current Amenities Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-star me-2"></i>Current Amenities
        </h4>

        <div class="amenity-limit-info">
            <i class="fas fa-info-circle"></i>
            You have {{ amenities.count }} of {{ max_amenities }} amenities added.
        </div>

        {% if amenities %}
            {% for amenity in amenities %}
                <div class="amenity-item">
                    <div class="amenity-content">
                        <div class="amenity-info">
                            <h5>{{ amenity.get_amenity_type_display }}</h5>
                        </div>
                        <div class="amenity-actions">
                            <a href="{% url 'venues_app:delete_amenity' amenity.id %}" class="btn-cw-danger">
                                <i class="fas fa-trash"></i> Remove
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="no-amenities-message">
                <i class="fas fa-star"></i>
                <p>{% trans "No amenities added yet. Add your first amenity below." %}</p>
            </div>
        {% endif %}
    </div>

    <!-- Add New Amenity Section -->
    {% if can_add_amenity %}
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-plus me-2"></i>Add New Amenity
        </h4>

        <form method="post" novalidate>
            {% csrf_token %}

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.amenity_type.id_for_label }}" class="form-label fw-bold">
                        Amenity Type <span class="text-danger">*</span>
                    </label>
                    {{ form.amenity_type }}
                    {% if form.amenity_type.errors %}
                        {% for error in form.amenity_type.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>

            <button type="submit" class="btn-cw-primary">
                <i class="fas fa-plus me-2"></i>Add Amenity
            </button>
        </form>
    </div>
    {% else %}
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-exclamation-triangle me-2"></i>Amenity Limit Reached
        </h4>

        <div class="amenity-limit-info warning">
            <i class="fas fa-exclamation-triangle"></i>
            You have reached the maximum limit of {{ max_amenities }} amenities for your venue.
            To add new amenities, please remove some existing ones first.
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}
