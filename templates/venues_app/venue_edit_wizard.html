{% extends 'base.html' %}

{% block title %}Edit {{ venue.venue_name }} - Step {{ step_number }} of {{ total_steps }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Load external CSS files -->
<link rel="stylesheet" href="{% static 'css/venues_app/venue_enhanced.css' %}">

<!-- Enhanced Venue Edit Wizard Styles -->
<style>
    :root {
        --cw-primary: #2F160F;
        --cw-accent: #fae1d7;
        --cw-success: #10b981;
        --cw-warning: #f59e0b;
        --cw-error: #ef4444;
        --cw-info: #3b82f6;
        --cw-light-gray: #f8fafc;
        --cw-border: #e5e7eb;
    }

    /* Enhanced Progress Bar */
    .wizard-progress {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .progress-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-primary);
    }

    .progress-percentage {
        background: var(--cw-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        position: relative;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        height: 3px;
        background: #e5e7eb;
        z-index: 1;
        border-radius: 2px;
    }

    .progress-line {
        position: absolute;
        top: 20px;
        left: 20px;
        height: 3px;
        background: linear-gradient(90deg, var(--cw-success) 0%, var(--cw-primary) 100%);
        z-index: 2;
        border-radius: 2px;
        transition: width 0.6s ease;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 3;
        flex: 1;
        text-align: center;
    }

    .step-circle {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        background: #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #6b7280;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
        border: 3px solid #e5e7eb;
        position: relative;
    }

    .step-circle.completed {
        background: var(--cw-success);
        color: white;
        border-color: var(--cw-success);
    }

    .step-circle.active {
        background: var(--cw-brand-primary) !important;
        background-color: #2F160F !important;
        color: white !important;
        border-color: var(--cw-brand-primary) !important;
        border-color: #2F160F !important;
        box-shadow: 0 0 0 4px rgba(47, 22, 15, 0.1);
        font-weight: 700 !important;
    }

    .step-circle.active,
    .step-circle.active * {
        color: white !important;
    }

    .step-circle.in-progress {
        background: var(--cw-warning);
        color: white;
        border-color: var(--cw-warning);
    }

    .step-circle.blocked {
        background: #fca5a5;
        color: white;
        border-color: #ef4444;
    }

    /* Step completion indicator */
    .step-completion-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        font-size: 0.625rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .step-completion-badge.complete {
        background: var(--cw-success);
        color: white;
    }

    .step-completion-badge.partial {
        background: var(--cw-warning);
        color: white;
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
        transition: all 0.3s ease;
    }

    .step-title.active {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .step-title.completed {
        color: var(--cw-success);
    }

    /* Step requirements tooltip */
    .step-requirements {
        position: absolute;
        bottom: -120px;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        border: 1px solid var(--cw-border);
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        width: 250px;
        font-size: 0.875rem;
        z-index: 10;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .progress-step:hover .step-requirements {
        opacity: 1;
        visibility: visible;
    }

    .step-requirements h4 {
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--cw-primary);
    }

    .step-requirements ul {
        margin: 0;
        padding-left: 1rem;
    }

    .step-requirements li {
        margin-bottom: 0.25rem;
        color: #6b7280;
    }

    /* Form Container */
    .wizard-form-container {
        background: white;
        border-radius: 1rem;
        padding: 2.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .form-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .form-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-primary);
        margin-bottom: 0.5rem;
    }

    .form-subtitle {
        color: #6b7280;
        font-size: 1rem;
    }

    /* Form Styling */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--cw-primary);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        border: 2px solid var(--cw-border);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        width: 100%;
    }

    .form-control:focus {
        border-color: var(--cw-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control.is-invalid {
        border-color: var(--cw-error);
    }

    .invalid-feedback {
        color: var(--cw-error);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-text {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Button Styling */
    .wizard-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--cw-border);
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
    }

    .btn-primary {
        background: var(--cw-primary);
        color: white;
    }

    .btn-primary:hover {
        background: #1a0f09;
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
        transform: translateY(-1px);
    }

    .btn-outline {
        background: transparent;
        color: var(--cw-primary);
        border: 2px solid var(--cw-primary);
    }

    .btn-outline:hover {
        background: var(--cw-primary);
        color: white;
    }

    /* Venue Info Card */
    .venue-info-card {
        background: var(--cw-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid var(--cw-border);
    }

    .venue-info-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .venue-icon {
        width: 48px;
        height: 48px;
        background: var(--cw-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
    }

    .venue-info h3 {
        margin: 0;
        color: var(--cw-primary);
        font-weight: 700;
    }

    .venue-info p {
        margin: 0;
        color: #6b7280;
        font-size: 0.875rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .wizard-progress {
            padding: 1rem;
        }

        .progress-steps {
            flex-direction: column;
            gap: 1rem;
        }

        .progress-steps::before {
            display: none;
        }

        .progress-line {
            display: none;
        }

        .wizard-form-container {
            padding: 1.5rem;
        }

        .wizard-buttons {
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }

    /* Loading States */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Success/Error Messages */
    .alert {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }

    .alert-success {
        background: #d1fae5;
        color: #065f46;
        border: 1px solid #a7f3d0;
    }

    .alert-error {
        background: #fee2e2;
        color: #991b1b;
        border: 1px solid #fca5a5;
    }

    .alert-info {
        background: #dbeafe;
        color: #1e40af;
        border: 1px solid #93c5fd;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="text-decoration-none">
                    My Venue
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                Edit Venue - Step {{ step_number }}
            </li>
        </ol>
    </nav>

    <!-- Venue Info Card -->
    <div class="venue-info-card">
        <div class="venue-info-header">
            <div class="venue-icon">
                <i class="fas fa-store"></i>
            </div>
            <div class="venue-info">
                <h3>{{ venue.venue_name }}</h3>
                <p>Current Status:
                    <span class="badge {% if venue.approval_status == 'approved' %}bg-success{% elif venue.approval_status == 'pending' %}bg-warning{% else %}bg-secondary{% endif %}">
                        {{ venue.get_approval_status_display }}
                    </span>
                </p>
            </div>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline btn-sm">
                <i class="fas fa-eye"></i> View Details
            </a>
            <a href="{% url 'venues_app:venue_edit' %}" class="btn btn-outline btn-sm">
                <i class="fas fa-edit"></i> Quick Edit
            </a>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="wizard-progress">
        <div class="progress-header">
            <div class="progress-title">
                <i class="fas fa-edit me-2"></i>
                Edit Progress
            </div>
            <div class="progress-percentage">
                {{ progress_percentage }}% Complete
            </div>
        </div>

        <div class="progress-steps">
            <div class="progress-line" style="width: {{ progress_percentage }}%;"></div>

            {% for step_code, step_name in step_choices %}
            <div class="progress-step">
                <div class="step-circle {% if step_code == current_step %}active{% elif step_code in completed_steps %}completed{% endif %}">
                    {% if step_code in completed_steps %}
                        <i class="fas fa-check"></i>
                        <div class="step-completion-badge complete">
                            <i class="fas fa-check"></i>
                        </div>
                    {% else %}
                        {{ forloop.counter }}
                    {% endif %}
                </div>
                <div class="step-title {% if step_code == current_step %}active{% elif step_code in completed_steps %}completed{% endif %}">
                    {{ step_name }}
                </div>

                <!-- Step Requirements Tooltip -->
                <div class="step-requirements">
                    <h4>{{ step_requirements.description }}</h4>
                    <ul>
                        {% for detail in step_requirements.details %}
                        <li>{{ detail }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Form Container -->
    <div class="wizard-form-container">
        <div class="form-header">
            <h2 class="form-title">Edit Your Venue</h2>
            <p class="form-subtitle">{{ current_step_title }} - Step {{ step_number }} of {{ total_steps }}</p>
        </div>

        <!-- Display Messages -->
        {% if messages %}
            {% for message in messages %}
            <div class="alert alert-{% if message.tags == 'error' %}error{% elif message.tags == 'success' %}success{% else %}info{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
        {% endif %}

        <!-- Form -->
        <form method="post" enctype="multipart/form-data" id="wizard-form">
            {% csrf_token %}

            <!-- Step-specific form content -->
            {% if current_step == 'basic' %}
                <!-- Basic Information Step -->
                <div class="form-group">
                    <label for="{{ form.venue_name.id_for_label }}" class="form-label">
                        Venue Name <span class="text-danger">*</span>
                    </label>
                    {{ form.venue_name }}
                    {% if form.venue_name.errors %}
                        <div class="invalid-feedback">{{ form.venue_name.errors.0 }}</div>
                    {% endif %}
                    {% if form.venue_name.help_text %}
                        <div class="form-text">{{ form.venue_name.help_text }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.short_description.id_for_label }}" class="form-label">
                        Description <span class="text-danger">*</span>
                    </label>
                    {{ form.short_description }}
                    {% if form.short_description.errors %}
                        <div class="invalid-feedback">{{ form.short_description.errors.0 }}</div>
                    {% endif %}
                    {% if form.short_description.help_text %}
                        <div class="form-text">{{ form.short_description.help_text }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label class="form-label">
                        Categories <span class="text-danger">*</span>
                    </label>
                    <div class="row">
                        {% for choice in form.categories %}
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                {{ choice.tag }}
                                <label class="form-check-label" for="{{ choice.id_for_label }}">
                                    {{ choice.choice_label }}
                                </label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% if form.categories.errors %}
                        <div class="invalid-feedback">{{ form.categories.errors.0 }}</div>
                    {% endif %}
                    {% if form.categories.help_text %}
                        <div class="form-text">{{ form.categories.help_text }}</div>
                    {% endif %}
                </div>

            {% elif current_step == 'location' %}
                <!-- Location & Contact Step -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.state.id_for_label }}" class="form-label">
                                State <span class="text-danger">*</span>
                            </label>
                            {{ form.state }}
                            {% if form.state.errors %}
                                <div class="invalid-feedback">{{ form.state.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.county.id_for_label }}" class="form-label">
                                County <span class="text-danger">*</span>
                            </label>
                            {{ form.county }}
                            {% if form.county.errors %}
                                <div class="invalid-feedback">{{ form.county.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.city.id_for_label }}" class="form-label">
                                City <span class="text-danger">*</span>
                            </label>
                            {{ form.city }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback">{{ form.city.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.zip_code.id_for_label }}" class="form-label">
                                ZIP Code <span class="text-danger">*</span>
                            </label>
                            {{ form.zip_code }}
                            {% if form.zip_code.errors %}
                                <div class="invalid-feedback">{{ form.zip_code.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="{{ form.street_number.id_for_label }}" class="form-label">
                                Street Number
                            </label>
                            {{ form.street_number }}
                            {% if form.street_number.errors %}
                                <div class="invalid-feedback">{{ form.street_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="form-group">
                            <label for="{{ form.street_name.id_for_label }}" class="form-label">
                                Street Name <span class="text-danger">*</span>
                            </label>
                            {{ form.street_name }}
                            {% if form.street_name.errors %}
                                <div class="invalid-feedback">{{ form.street_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <h4 class="mt-4 mb-3">Contact Information</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">Phone</label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">{{ form.phone.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">{{ form.email.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="{{ form.website_url.id_for_label }}" class="form-label">Website</label>
                    {{ form.website_url }}
                    {% if form.website_url.errors %}
                        <div class="invalid-feedback">{{ form.website_url.errors.0 }}</div>
                    {% endif %}
                </div>

                <!-- Social Media -->
                <h4 class="mt-4 mb-3">Social Media</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.instagram_url.id_for_label }}" class="form-label">Instagram</label>
                            {{ form.instagram_url }}
                            {% if form.instagram_url.errors %}
                                <div class="invalid-feedback">{{ form.instagram_url.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.facebook_url.id_for_label }}" class="form-label">Facebook</label>
                            {{ form.facebook_url }}
                            {% if form.facebook_url.errors %}
                                <div class="invalid-feedback">{{ form.facebook_url.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.twitter_url.id_for_label }}" class="form-label">Twitter</label>
                            {{ form.twitter_url }}
                            {% if form.twitter_url.errors %}
                                <div class="invalid-feedback">{{ form.twitter_url.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.linkedin_url.id_for_label }}" class="form-label">LinkedIn</label>
                            {{ form.linkedin_url }}
                            {% if form.linkedin_url.errors %}
                                <div class="invalid-feedback">{{ form.linkedin_url.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Hidden coordinates -->
                {{ form.latitude }}
                {{ form.longitude }}

            {% elif current_step == 'services' %}
                <!-- Services Step -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Services management is available in the main venue management area.
                    You can add, edit, and manage your services there.
                </div>
                <div class="text-center">
                    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-primary">
                        <i class="fas fa-cog"></i> Manage Services
                    </a>
                </div>

            {% elif current_step == 'gallery' %}
                <!-- Gallery Step -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Image management is available in the main venue management area.
                    You can upload, reorder, and manage your venue images there.
                </div>
                <div class="text-center">
                    <a href="{% url 'venues_app:manage_venue_images' %}" class="btn btn-primary">
                        <i class="fas fa-images"></i> Manage Images
                    </a>
                </div>

            {% elif current_step == 'details' %}
                <!-- Details & Policies Step -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.cancellation_policy.id_for_label }}" class="form-label">
                                Cancellation Policy
                            </label>
                            {{ form.cancellation_policy }}
                            {% if form.cancellation_policy.errors %}
                                <div class="invalid-feedback">{{ form.cancellation_policy.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.booking_policy.id_for_label }}" class="form-label">
                                Booking Policy
                            </label>
                            {{ form.booking_policy }}
                            {% if form.booking_policy.errors %}
                                <div class="invalid-feedback">{{ form.booking_policy.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="{{ form.special_instructions.id_for_label }}" class="form-label">
                        Special Instructions
                    </label>
                    {{ form.special_instructions }}
                    {% if form.special_instructions.errors %}
                        <div class="invalid-feedback">{{ form.special_instructions.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label class="form-label">Venue Status</label>
                    <div class="row">
                        {% for choice in form.venue_status %}
                        <div class="col-md-6 mb-2">
                            <div class="form-check">
                                {{ choice.tag }}
                                <label class="form-check-label" for="{{ choice.id_for_label }}">
                                    {{ choice.choice_label }}
                                </label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% if form.venue_status.errors %}
                        <div class="invalid-feedback">{{ form.venue_status.errors.0 }}</div>
                    {% endif %}
                </div>

                <!-- Hidden fields for complex data -->
                {{ form.operating_hours }}
                {{ form.amenities }}
                {{ form.faqs }}

            {% endif %}

            <!-- Navigation Buttons -->
            <div class="wizard-buttons">
                <div>
                    {% if not is_first_step %}
                        <a href="{% url 'venues_app:venue_edit_wizard' step=previous_step %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Previous
                        </a>
                    {% endif %}
                </div>

                <div class="d-flex gap-2">
                    {% if not is_final_step %}
                        <button type="submit" class="btn btn-primary">
                            Next <i class="fas fa-arrow-right"></i>
                        </button>
                    {% else %}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    {% endif %}

                    <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/venue-management.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-save progress
    const form = document.getElementById('wizard-form');
    const formData = new FormData(form);

    // Auto-save on form changes
    form.addEventListener('change', function() {
        saveProgress();
    });

    function saveProgress() {
        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        fetch('{% url "venues_app:venue_edit_wizard" step=current_step %}', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                action: 'save_progress',
                form_data: data
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update progress bar
                const progressLine = document.querySelector('.progress-line');
                if (progressLine) {
                    progressLine.style.width = data.progress_percentage + '%';
                }

                const progressPercentage = document.querySelector('.progress-percentage');
                if (progressPercentage) {
                    progressPercentage.textContent = data.progress_percentage + '% Complete';
                }
            }
        })
        .catch(error => {
            console.error('Error saving progress:', error);
        });
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner"></span> Saving...';

        // Save progress before submitting
        saveProgress();
    });
});
</script>
{% endblock %}
