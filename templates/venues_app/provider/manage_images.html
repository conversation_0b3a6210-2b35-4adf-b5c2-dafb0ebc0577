{% extends 'venues_app/base_venues.html' %}
{% load math_filters %}
{% load static %}

{% block title %}Manage Venue Images - CozyWish{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Image Management Styles */
    .image-count-badge .badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
    }

    .image-limit-progress {
        min-width: 120px;
    }

    .image-limit-progress .progress {
        border-radius: 10px;
        background-color: rgba(0,0,0,0.1);
    }

    .image-limit-progress .progress-bar {
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .premium-upgrade .btn {
        border-radius: 20px;
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
    }

    /* Enhanced Drag and Drop Upload Zone */
    .enhanced-upload-zone {
        border: 3px dashed #e0e6ed;
        border-radius: 16px;
        padding: 2rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        overflow: hidden;
    }

    .enhanced-upload-zone:hover {
        border-color: #007bff;
        background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
    }

    .enhanced-upload-zone.drag-over {
        border-color: #28a745;
        background: linear-gradient(135deg, #d4f8d4 0%, #a8e6a8 100%);
        transform: scale(1.05);
        box-shadow: 0 12px 30px rgba(40, 167, 69, 0.25);
    }

    .enhanced-upload-zone.uploading {
        border-color: #ffc107;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        pointer-events: none;
    }

    .upload-zone-content {
        text-align: center;
        z-index: 2;
        position: relative;
    }

    .upload-zone-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .enhanced-upload-zone:hover .upload-zone-icon {
        color: #007bff;
        transform: scale(1.1);
    }

    .enhanced-upload-zone.drag-over .upload-zone-icon {
        color: #28a745;
        transform: scale(1.2) rotate(5deg);
    }

    .upload-zone-text {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .upload-zone-subtext {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .upload-zone-button {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .upload-zone-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }

    /* Upload Queue Management */
    .upload-queue {
        margin-top: 1.5rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
    }

    .queue-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .queue-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: white;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .queue-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .queue-item.uploading {
        border-color: #007bff;
        background: linear-gradient(90deg, #e7f3ff 0%, #ffffff 100%);
    }

    .queue-item.success {
        border-color: #28a745;
        background: linear-gradient(90deg, #d4f8d4 0%, #ffffff 100%);
    }

    .queue-item.error {
        border-color: #dc3545;
        background: linear-gradient(90deg, #f8d7da 0%, #ffffff 100%);
    }

    .queue-item-preview {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        object-fit: cover;
        margin-right: 1rem;
        border: 2px solid #e9ecef;
    }

    .queue-item-info {
        flex-grow: 1;
    }

    .queue-item-name {
        font-weight: 600;
        color: #212529;
        margin-bottom: 0.25rem;
    }

    .queue-item-details {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }

    .queue-item-progress {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 0.25rem;
    }

    .queue-item-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
        border-radius: 2px;
        transition: width 0.3s ease;
    }

    .queue-item-actions {
        display: flex;
        gap: 0.5rem;
    }

    .queue-item-btn {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        transition: all 0.3s ease;
    }

    .queue-item-btn:hover {
        transform: scale(1.1);
    }

    /* Enhanced Image Preview Grid */
    .enhanced-image-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
    }

    .enhanced-image-card {
        background: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid #e9ecef;
    }

    .enhanced-image-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }

    .enhanced-image-card.primary {
        border: 2px solid #ffd700;
        box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
    }

    .image-card-header {
        position: relative;
        overflow: hidden;
    }

    .image-card-img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .enhanced-image-card:hover .image-card-img {
        transform: scale(1.05);
    }

    .image-card-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .enhanced-image-card:hover .image-card-overlay {
        opacity: 1;
    }

    .image-card-badges {
        position: absolute;
        top: 0.75rem;
        left: 0.75rem;
        right: 0.75rem;
        display: flex;
        justify-content: between;
        align-items: flex-start;
    }

    .image-card-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        backdrop-filter: blur(10px);
        color: white;
    }

    .image-card-badge.primary {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4a 100%);
        color: #212529;
        box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
    }

    .image-card-badge.order {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
    }

    .image-card-body {
        padding: 1.25rem;
    }

    .image-card-title {
        font-size: 1rem;
        font-weight: 600;
        color: #212529;
        margin-bottom: 0.75rem;
        line-height: 1.4;
    }

    .image-card-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .image-card-btn {
        flex: 1;
        min-width: 80px;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 1px solid;
    }

    .image-card-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    /* Real-time Progress Tracker */
    .progress-tracker {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1050;
        min-width: 320px;
        max-width: 400px;
    }

    .progress-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
        border: 1px solid #e9ecef;
        overflow: hidden;
    }

    .progress-header {
        padding: 1rem;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .progress-body {
        padding: 1rem;
        max-height: 300px;
        overflow-y: auto;
    }

    .overall-progress {
        margin-bottom: 1rem;
    }

    .overall-progress-bar {
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .overall-progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        border-radius: 4px;
        transition: width 0.5s ease;
    }

    /* Empty State Enhancement */
    .enhanced-empty-state {
        text-align: center;
        padding: 3rem 2rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 16px;
        border: 2px dashed #dee2e6;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #6c757d;
        margin-bottom: 1.5rem;
        opacity: 0.7;
    }

    .empty-state-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.75rem;
    }

    .empty-state-description {
        font-size: 1rem;
        color: #6c757d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .empty-state-button {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .empty-state-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }

    /* Enhanced Image Badge Styles */
    .image-card-badge.gallery {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
    }

    .image-card-badge.quality {
        margin-left: 0.5rem;
        color: white;
        font-size: 0.7rem;
    }

    .image-card-badge.quality-90,
    .image-card-badge.quality-100 {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .image-card-badge.quality-70,
    .image-card-badge.quality-80 {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    }

    .image-card-badge.quality-50,
    .image-card-badge.quality-60 {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    }

    .image-card-badge.quality-30,
    .image-card-badge.quality-40 {
        background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
    }

    /* Enhanced Metadata Display Styles */
    .image-metadata {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 1rem;
        border-left: 4px solid #007bff;
        font-size: 0.85rem;
    }

    .metadata-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .metadata-row:last-child {
        margin-bottom: 0;
    }

    .metadata-label {
        color: #6c757d;
        font-weight: 500;
        display: flex;
        align-items: center;
        flex: 1;
    }

    .metadata-value {
        color: #495057;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
    }

    /* Optimization Suggestions Styles */
    .optimization-suggestions {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .suggestions-header {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        color: #856404;
        font-weight: 600;
        font-size: 0.85rem;
    }

    .suggestion-item {
        margin-bottom: 0.25rem;
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .suggestion-item:last-child {
        margin-bottom: 0;
    }

    .suggestion-warning {
        color: #856404;
    }

    .suggestion-info {
        color: #0c5460;
    }

    .suggestion-success {
        color: #155724;
    }

    /* Image Management Tips Styles */
    .image-management-tips {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 12px;
        padding: 1.5rem;
        border: 1px solid #e1f5fe;
        margin-top: 2rem;
    }

    .tips-header h6 {
        color: #1976d2;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .tip-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0.75rem;
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .tip-item:last-child {
        margin-bottom: 0;
    }

    .tip-item i {
        margin-top: 0.25rem;
        flex-shrink: 0;
        width: 20px;
    }

    /* Enhanced responsive design */
    @media (max-width: 768px) {
        .enhanced-image-grid {
            grid-template-columns: 1fr;
        }

        .image-card-actions {
            flex-direction: column;
        }

        .image-card-btn {
            width: 100%;
            margin-bottom: 0.25rem;
        }

        .image-management-tips .row {
            flex-direction: column;
        }

        .metadata-row {
            font-size: 0.8rem;
        }
    }

    /* Notification System Styles */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1060;
        min-width: 300px;
        max-width: 400px;
        padding: 1rem;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
        transform: translateX(100%);
        animation: slideInFromRight 0.3s ease forwards;
    }

    .notification-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .notification-error {
        background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
        border: 1px solid #f1aeb5;
        color: #721c24;
    }

    .notification-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        border: 1px solid #bee5eb;
        color: #0c5460;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 500;
    }

    .notification-content i {
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .notification-fade-out {
        animation: slideOutToRight 0.3s ease forwards;
    }

    /* Image Details Modal Styles */
    .image-details-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
        z-index: 1050;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.3s ease;
    }

    .image-details-content {
        background: white;
        border-radius: 16px;
        max-width: 800px;
        max-height: 90vh;
        width: 90%;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        animation: modalSlideIn 0.3s ease;
    }

    .image-details-header {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .image-details-header h5 {
        margin: 0;
        color: #495057;
        font-weight: 600;
    }

    .btn-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #6c757d;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .btn-close:hover {
        background: #e9ecef;
        color: #495057;
    }

    .image-details-body {
        padding: 2rem;
        max-height: 70vh;
        overflow-y: auto;
    }

    /* Enhanced Animations */
    @keyframes slideInFromRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutToRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    @keyframes slideOut {
        from {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
        to {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
</style>
{% endblock %}

{% block venues_content %}
    <!-- Header -->
    <div class="venues-header d-flex align-items-center mb-4">
        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div>
            <h2>Manage Venue Images</h2>
            <p class="mb-0">Upload and organize images for {{ venue.venue_name }}</p>
        </div>
    </div>

    <!-- Enhanced Upload Section -->
    {% if can_add_image %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cloud-upload-alt me-2"></i>
                        Upload New Images
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Enhanced Drag & Drop Upload Zone -->
                    <div class="enhanced-upload-zone" id="enhanced-upload-zone">
                        <div class="upload-zone-content">
                            <div class="upload-zone-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-zone-text">
                                Drag & Drop Images Here
                            </div>
                            <div class="upload-zone-subtext">
                                or click to browse and select multiple files
                            </div>
                            <button type="button" class="upload-zone-button" id="browse-button">
                                <i class="fas fa-folder-open me-2"></i>
                                Browse Files
                            </button>
                            <input type="file" id="multiple-file-input" multiple accept="image/jpeg,image/jpg,image/png,image/webp" style="display: none;">
                        </div>
                    </div>

                    <!-- Upload Queue -->
                    <div class="upload-queue" id="upload-queue" style="display: none;">
                        <div class="queue-header">
                            <h6 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                Upload Queue
                            </h6>
                            <div class="queue-actions">
                                <button type="button" class="btn btn-sm btn-outline-success" id="start-upload">
                                    <i class="fas fa-play me-1"></i>Start Upload
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" id="clear-queue">
                                    <i class="fas fa-trash me-1"></i>Clear Queue
                                </button>
                            </div>
                        </div>
                        <div id="queue-items"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                You have reached the maximum limit of {{ max_images }} images per venue.
                {% if not user.service_provider_profile.is_premium %}
                <a href="{% url 'accounts_app:premium_upgrade' %}" class="btn btn-sm btn-outline-warning ms-2">
                    <i class="fas fa-crown me-1"></i>Upgrade for More
                </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Enhanced Current Images Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-images me-2"></i>
                        Current Images
                    </h5>
                    <div class="d-flex align-items-center">
                        <!-- Enhanced Image Count Indicator -->
                        <div class="image-count-badge me-3">
                            <span class="badge bg-primary fs-6 px-3 py-2">
                                <i class="fas fa-images me-2"></i>
                                <span id="current-count">{{ images.count }}</span>/{{ max_images }}
                                {% if images.count >= max_images %}
                                <span class="text-warning ms-1">
                                    <i class="fas fa-exclamation-triangle" title="Maximum reached"></i>
                                </span>
                                {% endif %}
                            </span>
                        </div>

                        <!-- Progress Bar for Image Limit -->
                        <div class="image-limit-progress me-3" style="width: 120px;">
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar {% if images.count >= max_images %}bg-warning{% elif images.count >= 4 %}bg-info{% else %}bg-success{% endif %}"
                                     role="progressbar"
                                     style="width: {% widthratio images.count max_images 100 %}%"
                                     aria-valuenow="{{ images.count }}"
                                     aria-valuemin="0"
                                     aria-valuemax="{{ max_images }}">
                                </div>
                            </div>
                            <small class="text-muted d-block text-center mt-1">
                                {% if images.count < max_images %}
                                    {{ max_images|sub:images.count }} slots left
                                {% else %}
                                    Limit reached
                                {% endif %}
                            </small>
                        </div>

                        <!-- Premium Upgrade Button (if applicable) -->
                        {% if images.count >= max_images and not user.service_provider_profile.is_premium %}
                        <div class="premium-upgrade me-3">
                            <a href="{% url 'accounts_app:premium_upgrade' %}" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-crown me-1"></i>
                                Upgrade for More Images
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if images %}
                    <div class="enhanced-image-grid" id="images-grid">
                        {% for image in images %}
                        <div class="enhanced-image-card {% if image.is_primary %}primary{% endif %}" data-image-id="{{ image.id }}" data-order="{{ image.order }}">
                            <div class="image-card-header">
                                <img src="{{ image.image.url }}" class="image-card-img" alt="{{ image.caption|default:'Venue image' }}">

                                <!-- Image Card Overlay -->
                                <div class="image-card-overlay">
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-light btn-sm" onclick="window.open('{{ image.image.url }}', '_blank')" title="View full size">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-light btn-sm" onclick="downloadImage('{{ image.image.url }}', '{{ image.caption|default:'venue-image' }}')" title="Download image">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button type="button" class="btn btn-light btn-sm" onclick="showImageDetails('{{ image.id }}')" title="Show image details">
                                            <i class="fas fa-info-circle"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Badges -->
                                <div class="image-card-badges">
                                    <div>
                                        {% if image.is_primary %}
                                        <span class="image-card-badge primary" title="This is your primary image - it appears first in search results and listings">
                                            <i class="fas fa-star me-1"></i>Primary
                                        </span>
                                        {% else %}
                                        <span class="image-card-badge gallery" title="Gallery image - appears in your venue's photo gallery">
                                            <i class="fas fa-images me-1"></i>Gallery
                                        </span>
                                        {% endif %}

                                        <!-- Quality indicator -->
                                        {% if image.get_quality_score %}
                                        <span class="image-card-badge quality quality-{{ image.get_quality_score|floatformat:0 }}"
                                              title="Image quality score: {{ image.get_quality_score|floatformat:0 }}/100">
                                            <i class="fas fa-gem me-1"></i>{{ image.get_quality_score|floatformat:0 }}%
                                        </span>
                                        {% endif %}
                                    </div>
                                    <span class="image-card-badge order" title="Display order">{{ image.order }}</span>
                                </div>
                            </div>

                            <div class="image-card-body">
                                <h6 class="image-card-title">
                                    {{ image.caption|default:"Image "|add:image.order }}
                                </h6>

                                <!-- Enhanced Image Metadata Display -->
                                <div class="image-metadata">
                                    <div class="metadata-row">
                                        <span class="metadata-label">
                                            <i class="fas fa-expand-arrows-alt me-1"></i>
                                            Dimensions:
                                        </span>
                                        <span class="metadata-value">{{ image.dimensions_display }}</span>
                                    </div>

                                    {% if image.aspect_ratio_display %}
                                    <div class="metadata-row">
                                        <span class="metadata-label">
                                            <i class="fas fa-crop me-1"></i>
                                            Aspect Ratio:
                                        </span>
                                        <span class="metadata-value">{{ image.aspect_ratio_display }}</span>
                                    </div>
                                    {% endif %}

                                    {% if image.file_size %}
                                    <div class="metadata-row">
                                        <span class="metadata-label">
                                            <i class="fas fa-file-alt me-1"></i>
                                            File Size:
                                        </span>
                                        <span class="metadata-value">
                                            {% if image.file_size_mb >= 1 %}
                                                {{ image.file_size_mb }}MB
                                            {% else %}
                                                {{ image.file_size_kb }}KB
                                            {% endif %}
                                        </span>
                                    </div>
                                    {% endif %}

                                    {% if image.format %}
                                    <div class="metadata-row">
                                        <span class="metadata-label">
                                            <i class="fas fa-file-image me-1"></i>
                                            Format:
                                        </span>
                                        <span class="metadata-value">{{ image.format }}</span>
                                    </div>
                                    {% endif %}

                                    <div class="metadata-row">
                                        <span class="metadata-label">
                                            <i class="fas fa-clock me-1"></i>
                                            Uploaded:
                                        </span>
                                        <span class="metadata-value" title="{{ image.created_at }}">{{ image.upload_age_display }} ago</span>
                                    </div>
                                </div>

                                <!-- Optimization Suggestions -->
                                {% if image.get_optimization_suggestions %}
                                <div class="optimization-suggestions">
                                    <div class="suggestions-header">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        <small class="text-muted">Suggestions:</small>
                                    </div>
                                    {% for suggestion in image.get_optimization_suggestions %}
                                    <div class="suggestion-item suggestion-{{ suggestion.type }}">
                                        <small>
                                            <i class="fas fa-{% if suggestion.type == 'warning' %}exclamation-triangle{% elif suggestion.type == 'info' %}info-circle{% else %}check-circle{% endif %} me-1"></i>
                                            {{ suggestion.message }}
                                        </small>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}

                                <div class="image-card-actions">
                                    <!-- Reordering controls -->
                                    <div class="btn-group" role="group" aria-label="Reorder image">
                                        <button type="button" class="image-card-btn btn-outline-primary move-up-btn"
                                                data-image-id="{{ image.id }}"
                                                {% if forloop.first %}disabled{% endif %}
                                                title="Move up in display order">
                                            <i class="fas fa-arrow-up"></i>
                                        </button>
                                        <button type="button" class="image-card-btn btn-outline-primary move-down-btn"
                                                data-image-id="{{ image.id }}"
                                                {% if forloop.last %}disabled{% endif %}
                                                title="Move down in display order">
                                            <i class="fas fa-arrow-down"></i>
                                        </button>
                                    </div>

                                    <!-- Primary image control -->
                                    {% if not image.is_primary %}
                                    <button type="button" class="image-card-btn btn-outline-warning set-primary-btn"
                                            data-image-id="{{ image.id }}"
                                            title="Set as primary image - this will be the main image shown in search results">
                                        <i class="fas fa-star me-1"></i>Set Primary
                                    </button>
                                    {% else %}
                                    <button type="button" class="image-card-btn btn-warning" disabled
                                            title="This is your primary image">
                                        <i class="fas fa-star me-1"></i>Primary
                                    </button>
                                    {% endif %}

                                    <!-- Delete button with confirmation -->
                                    <button type="button" class="image-card-btn btn-outline-danger delete-image-btn"
                                            data-image-id="{{ image.id }}"
                                            data-image-caption="{{ image.caption|default:'Image '|add:image.order }}"
                                            data-is-primary="{{ image.is_primary|yesno:'true,false' }}"
                                            title="Delete this image - this action cannot be undone">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Image Management Tips -->
                    <div class="image-management-tips mt-4">
                        <div class="tips-header">
                            <h6><i class="fas fa-lightbulb me-2"></i>Image Management Tips</h6>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="tip-item">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    <strong>Primary Image:</strong> This is your main image that appears in search results and listings. Choose your best, most representative photo.
                                </div>
                                <div class="tip-item">
                                    <i class="fas fa-images text-info me-2"></i>
                                    <strong>Gallery Images:</strong> These showcase different aspects of your venue. Arrange them in order of importance.
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="tip-item">
                                    <i class="fas fa-compress-arrows-alt text-success me-2"></i>
                                    <strong>Best Dimensions:</strong> Use images at least 1000×600 pixels for crisp display on all devices.
                                </div>
                                <div class="tip-item">
                                    <i class="fas fa-file-image text-primary me-2"></i>
                                    <strong>Recommended Format:</strong> WebP provides the best quality and smallest file size. JPEG is also good.
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="enhanced-empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="empty-state-title">No Images Yet</div>
                        <div class="empty-state-description">
                            Showcase your venue with beautiful photos. Upload your first image to get started and attract more customers.
                        </div>
                        {% if can_add_image %}
                        <button type="button" class="empty-state-button" onclick="document.getElementById('multiple-file-input').click()">
                            <i class="fas fa-upload me-2"></i>Upload Your First Image
                        </button>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Progress Tracker -->
    <div class="progress-tracker" id="progress-tracker" style="display: none;">
        <div class="progress-card">
            <div class="progress-header">
                <h6 class="mb-0">
                    <i class="fas fa-cloud-upload-alt me-2"></i>
                    Upload Progress
                </h6>
                <button type="button" class="btn btn-sm btn-outline-light" onclick="hideProgressTracker()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="progress-body">
                <div class="overall-progress">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">Overall Progress</small>
                        <small class="text-muted" id="overall-percentage">0%</small>
                    </div>
                    <div class="overall-progress-bar">
                        <div class="overall-progress-fill" id="overall-progress-fill" style="width: 0%"></div>
                    </div>
                </div>
                <div id="progress-items"></div>
            </div>
        </div>
    </div>

    <!-- Back to venue management -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Venue Management
            </a>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<!-- Enhanced Venue Image Management JavaScript -->
<script src="{% static 'js/enhanced-venue-image-manager.js' %}"></script>

<!-- Additional JavaScript for enhanced functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    // Enhanced image management functionality
    setupImageManagement();

    function setupImageManagement() {
        // Set up existing image actions if they exist
        document.querySelectorAll('.move-up-btn').forEach(btn => {
            btn.addEventListener('click', handleMoveImage);
        });

        document.querySelectorAll('.move-down-btn').forEach(btn => {
            btn.addEventListener('click', handleMoveImage);
        });

        document.querySelectorAll('.set-primary-btn').forEach(btn => {
            btn.addEventListener('click', handleSetPrimary);
        });

        document.querySelectorAll('.delete-image-btn').forEach(btn => {
            btn.addEventListener('click', handleDeleteImage);
        });
    }

    async function handleMoveImage(e) {
        const btn = e.target.closest('button');
        const imageId = btn.dataset.imageId;
        const direction = btn.classList.contains('move-up-btn') ? 'up' : 'down';

        try {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            const response = await fetch(`/venues/provider/images/${imageId}/reorder/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `direction=${direction}`
            });

            const data = await response.json();

            if (data.success) {
                showNotification('Image reordered successfully!', 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showNotification(data.message || 'Reorder completed', 'info');
            }
        } catch (error) {
            showNotification(`Failed to reorder image: ${error.message}`, 'danger');
        } finally {
            btn.disabled = false;
            btn.innerHTML = direction === 'up' ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';
        }
    }

    async function handleSetPrimary(e) {
        const btn = e.target.closest('button');
        const imageId = btn.dataset.imageId;

        // Confirmation for setting primary image
        if (!confirm('Set this image as your primary image? It will be the main image shown in search results and venue listings.')) {
            return;
        }

        const originalText = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        try {
            const response = await fetch(`/venues/provider/images/${imageId}/set-primary/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                },
            });

            const data = await response.json();

            if (data.success) {
                // Update all image cards to reflect new primary status
                document.querySelectorAll('.enhanced-image-card').forEach(card => {
                    const cardImageId = card.dataset.imageId;
                    const badge = card.querySelector('.image-card-badge');
                    const setPrimaryBtn = card.querySelector('.set-primary-btn');

                    if (cardImageId === imageId) {
                        // This is now the primary image
                        card.classList.add('primary');
                        if (badge) {
                            badge.className = 'image-card-badge primary';
                            badge.innerHTML = '<i class="fas fa-star me-1"></i>Primary';
                            badge.title = 'This is your primary image - it appears first in search results and listings';
                        }
                        if (setPrimaryBtn) {
                            setPrimaryBtn.className = 'image-card-btn btn-warning';
                            setPrimaryBtn.disabled = true;
                            setPrimaryBtn.innerHTML = '<i class="fas fa-star me-1"></i>Primary';
                            setPrimaryBtn.title = 'This is your primary image';
                        }
                    } else {
                        // This is now a gallery image
                        card.classList.remove('primary');
                        if (badge && badge.classList.contains('primary')) {
                            badge.className = 'image-card-badge gallery';
                            badge.innerHTML = '<i class="fas fa-images me-1"></i>Gallery';
                            badge.title = 'Gallery image - appears in your venue\'s photo gallery';
                        }
                        // Update any primary buttons to set primary buttons
                        const primaryBtn = card.querySelector('.btn-warning[disabled]');
                        if (primaryBtn) {
                            primaryBtn.className = 'image-card-btn btn-outline-warning set-primary-btn';
                            primaryBtn.disabled = false;
                            primaryBtn.innerHTML = '<i class="fas fa-star me-1"></i>Set Primary';
                            primaryBtn.title = 'Set as primary image - this will be the main image shown in search results';
                            primaryBtn.addEventListener('click', handleSetPrimary);
                        }
                    }
                });

                showNotification('Primary image updated successfully', 'success');
            } else {
                btn.disabled = false;
                btn.innerHTML = originalText;
                showNotification(data.error || 'Failed to set primary image', 'error');
            }
        } catch (error) {
            btn.disabled = false;
            btn.innerHTML = originalText;
            showNotification('Network error occurred while setting primary image', 'error');
        }
    }

    async function handleDeleteImage(e) {
        const btn = e.target.closest('button');
        const imageId = btn.dataset.imageId;
        const caption = btn.dataset.imageCaption;
        const isPrimary = btn.dataset.isPrimary === 'true';

        // Enhanced confirmation dialog with more information
        let confirmMessage = `Are you sure you want to delete "${caption}"?`;
        if (isPrimary) {
            confirmMessage += '\n\n⚠️ WARNING: This is your PRIMARY image. Deleting it will automatically set another image as primary, or leave your venue without a primary image if this is the last one.';
        }
        confirmMessage += '\n\nThis action cannot be undone.';

        if (!confirm(confirmMessage)) {
            return;
        }

        // Show loading state
        const originalText = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        try {
            const response = await fetch(`/venues/provider/images/${imageId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            });

            const data = await response.json();

            if (data.success) {
                // Remove the image card with animation
                const imageCard = btn.closest('.enhanced-image-card');
                imageCard.style.animation = 'slideOut 0.3s ease forwards';

                setTimeout(() => {
                    imageCard.remove();

                    // Update counts and UI
                    updateImageCounts(data.current_count, data.max_images);

                    // Show success message
                    showNotification('Image deleted successfully', 'success');

                    // If this was primary and there are other images, show info about new primary
                    if (data.was_primary && data.current_count > 0) {
                        showNotification('The next image has been automatically set as primary', 'info');
                    }

                    // Refresh the page if no images left to show empty state
                    if (data.current_count === 0) {
                        setTimeout(() => location.reload(), 1000);
                    }
                }, 300);

            } else {
                btn.disabled = false;
                btn.innerHTML = originalText;
                showNotification(data.error || 'Failed to delete image', 'error');
            }
        } catch (error) {
            btn.disabled = false;
            btn.innerHTML = originalText;
            showNotification('Network error occurred while deleting image', 'error');
        }
    }

    function updateImageCounts(currentCount, maxImages) {
        const countElement = document.getElementById('current-count');
        if (countElement) {
            countElement.textContent = currentCount;
        }

        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            const percentage = (currentCount / maxImages) * 100;
            progressBar.style.width = `${percentage}%`;
            progressBar.className = `progress-bar ${currentCount >= maxImages ? 'bg-warning' : currentCount >= 4 ? 'bg-info' : 'bg-success'}`;
        }

        const slotsText = document.querySelector('.progress-tracker small');
        if (slotsText) {
            if (currentCount < maxImages) {
                slotsText.textContent = `${maxImages - currentCount} slots left`;
            } else {
                slotsText.textContent = 'Limit reached';
            }
        }
    }

    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('notification-fade-out');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }

    // New function to show image details modal
    function showImageDetails(imageId) {
        const imageCard = document.querySelector(`[data-image-id="${imageId}"]`);
        if (!imageCard) return;

        const image = imageCard.querySelector('.image-card-img');
        const metadata = imageCard.querySelector('.image-metadata');
        const suggestions = imageCard.querySelector('.optimization-suggestions');

        // Create modal HTML
        const modalHTML = `
            <div class="image-details-modal" onclick="closeImageDetails(event)">
                <div class="image-details-content" onclick="event.stopPropagation()">
                    <div class="image-details-header">
                        <h5>Image Details</h5>
                        <button type="button" class="btn-close" onclick="closeImageDetails()"></button>
                    </div>
                    <div class="image-details-body">
                        <div class="row">
                            <div class="col-md-6">
                                <img src="${image.src}" alt="${image.alt}" class="img-fluid rounded">
                            </div>
                            <div class="col-md-6">
                                ${metadata ? metadata.outerHTML : '<p>No metadata available</p>'}
                                ${suggestions ? suggestions.outerHTML : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to body
        const modalDiv = document.createElement('div');
        modalDiv.innerHTML = modalHTML;
        document.body.appendChild(modalDiv.firstElementChild);
    }

    // Function to close image details modal
    function closeImageDetails(event) {
        if (event && event.target.closest('.image-details-content')) return;
        const modal = document.querySelector('.image-details-modal');
        if (modal) {
            modal.remove();
        }
    }

    // Download image function
    function downloadImage(imageUrl, filename) {
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = filename || 'venue-image';
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showNotification('Image download started', 'success');
    }

    // Make functions globally available
    window.showImageDetails = showImageDetails;
    window.closeImageDetails = closeImageDetails;
    window.downloadImage = downloadImage;
});

// Additional CSS for enhanced styling
const additionalStyle = document.createElement('style');
additionalStyle.textContent = `
    /* Enhanced hover effects */
    .enhanced-upload-zone:hover .upload-zone-icon {
        animation: bounce 0.6s ease-in-out;
    }

    @keyframes bounce {
        0%, 20%, 53%, 80%, 100% {
            animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
            transform: translate3d(0, 0, 0) scale(1.1);
        }
        40%, 43% {
            animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
            transform: translate3d(0, -10px, 0) scale(1.15);
        }
        70% {
            animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
            transform: translate3d(0, -5px, 0) scale(1.12);
        }
        90% {
            transform: translate3d(0, -2px, 0) scale(1.1);
        }
    }

    /* Enhanced image card animations */
    .enhanced-image-card {
        animation: slideIn 0.5s ease-out;
    }

    /* Better responsive behavior */
    @media (max-width: 576px) {
        .enhanced-image-card .image-card-actions {
            flex-direction: column;
        }

        .image-card-btn {
            min-width: auto;
            width: 100%;
        }

        .upload-zone-text {
            font-size: 1rem;
        }

        .upload-zone-subtext {
            font-size: 0.8rem;
        }

        .upload-zone-button {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
    }

    /* Loading states */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% {
            background-position: -200px 0;
        }
        100% {
            background-position: calc(200px + 100%) 0;
        }
    }

    /* Enhanced progress animations */
    .queue-item-progress-bar {
        animation: progressPulse 2s ease-in-out infinite;
    }

    @keyframes progressPulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.8;
        }
    }

    /* Success state animations */
    .queue-item.success {
        animation: successPulse 0.5s ease-out;
    }

    @keyframes successPulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.02);
        }
        100% {
            transform: scale(1);
        }
    }

    /* Error state styling */
    .queue-item.error {
        animation: errorShake 0.5s ease-out;
    }

    @keyframes errorShake {
        0%, 100% {
            transform: translateX(0);
        }
        25% {
            transform: translateX(-5px);
        }
        75% {
            transform: translateX(5px);
        }
    }
`;
document.head.appendChild(additionalStyle);
</script>
{% endblock %}
