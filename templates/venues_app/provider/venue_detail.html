{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ venue.venue_name }} - My Venue - CozyWish{% endblock %}

{% block dashboard_title %}My Venue{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="d-flex flex-wrap align-items-center gap-3">
    {% if venue.approval_status == 'draft' and can_submit_for_approval %}
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#approvalSubmissionModal" data-bs-toggle="tooltip" title="Submit for Approval">
        <i class="fas fa-paper-plane me-2"></i>Submit for Approval
    </button>
    {% endif %}
    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="Manage Services">
        <i class="fas fa-spa me-2"></i>Manage Services
    </a>
    <a href="{% url 'venues_app:venue_preview' %}" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="Preview Venue">
        <i class="fas fa-eye me-2"></i>Preview
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<!-- Venue Status Alert -->
{% if venue.approval_status == 'pending' %}
<div class="alert alert-warning mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-clock me-3"></i>
        <div>
            <h6 class="mb-1">Venue Under Review</h6>
            <p class="mb-0">Your venue is currently being reviewed by our team. We'll notify you once the review is complete.</p>
        </div>
    </div>
</div>
{% elif venue.approval_status == 'rejected' %}
<div class="alert alert-danger mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-times-circle me-3"></i>
        <div>
            <h6 class="mb-1">Venue Needs Updates</h6>
            <p class="mb-0">Your venue requires some updates before it can be approved. Please review the feedback and make necessary changes.</p>
            {% if venue.rejection_reason %}
            <small class="text-muted">Reason: {{ venue.rejection_reason }}</small>
            {% endif %}
        </div>
    </div>
</div>
{% elif venue.approval_status == 'approved' %}
<div class="alert alert-success mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-check-circle me-3"></i>
        <div>
            <h6 class="mb-1">Venue Approved</h6>
            <p class="mb-0">Your venue is live and visible to customers. Great job!</p>
        </div>
    </div>
</div>
{% endif %}

<!-- Venue Overview Cards -->
<div class="row mb-4">
    <div class="col-6 col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center py-3">
                <i class="fas fa-spa mb-2" style="font-size: 1.5rem; color: #42241A;"></i>
                <h6 class="card-title mb-1">{{ services|length }}</h6>
                <p class="card-text text-muted small">Services</p>
            </div>
        </div>
    </div>
    <div class="col-6 col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center py-3">
                <i class="fas fa-images mb-2" style="font-size: 1.5rem; color: #42241A;"></i>
                <h6 class="card-title mb-1">{{ images|length }}</h6>
                <p class="card-text text-muted small">Images</p>
            </div>
        </div>
    </div>
    <div class="col-6 col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center py-3">
                <i class="fas fa-star mb-2" style="font-size: 1.5rem; color: #42241A;"></i>
                <h6 class="card-title mb-1">{{ venue.average_rating|floatformat:1|default:"N/A" }}</h6>
                <p class="card-text text-muted small">Rating</p>
            </div>
        </div>
    </div>
    <div class="col-6 col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center py-3">
                <i class="fas fa-calendar-check mb-2" style="font-size: 1.5rem; color: #42241A;"></i>
                <h6 class="card-title mb-1">{{ venue.total_bookings|default:"0" }}</h6>
                <p class="card-text text-muted small">Bookings</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Venue Information -->
<div class="row">
    <div class="col-lg-8">
        <!-- Venue Details Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>Venue Information</h5>
                <a href="{% url 'venues_app:edit_venue_basic_information' venue_id=venue.id %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit me-1"></i>Edit
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Venue Name</h6>
                        <p class="mb-3">{{ venue.venue_name }}</p>

                        <h6 class="text-muted">Business Name</h6>
                        <p class="mb-3">{{ venue.service_provider.business_name }}</p>

                        <h6 class="text-muted">Categories</h6>
                        <div class="mb-3">
                            {% for category in venue.categories.all %}
                                <span class="badge bg-secondary me-1">{{ category.category_name }}</span>
                            {% empty %}
                                <span class="text-muted">No categories assigned</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Phone</h6>
                        <p class="mb-3">{{ venue.phone|default:"Not provided" }}</p>

                        <h6 class="text-muted">Email</h6>
                        <p class="mb-3">{{ venue.email|default:"Not provided" }}</p>

                        <h6 class="text-muted">Website</h6>
                        <p class="mb-3">
                            {% if venue.website_url %}
                                <a href="{{ venue.website_url }}" target="_blank" class="text-decoration-none">
                                    {{ venue.website_url }}
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            {% else %}
                                <span class="text-muted">Not provided</span>
                            {% endif %}
                        </p>
                    </div>
                </div>

                {% if venue.short_description %}
                <div class="mt-3">
                    <h6 class="text-muted">Description</h6>
                    <p>{{ venue.short_description }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Location Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Location</h5>
                <a href="{% url 'venues_app:edit_venue_location' venue_id=venue.id %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit me-1"></i>Edit
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="text-muted">Full Address</h6>
                        <p class="mb-3">{{ venue.get_full_address|default:"Address not provided" }}</p>

                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted">City</h6>
                                <p class="mb-2">{{ venue.city|default:"Not provided" }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">State</h6>
                                <p class="mb-2">{{ venue.state|default:"Not provided" }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        {% if venue.latitude and venue.longitude %}
                        <h6 class="text-muted">Coordinates</h6>
                        <p class="mb-2"><small>Lat: {{ venue.latitude|floatformat:6 }}</small></p>
                        <p class="mb-2"><small>Lng: {{ venue.longitude|floatformat:6 }}</small></p>
                        {% else %}
                        <p class="text-muted"><small>Coordinates not available</small></p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Services Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-spa me-2"></i>Services ({{ services|length }})</h5>
                <a href="{% url 'venues_app:manage_services' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit me-1"></i>Manage
                </a>
            </div>
            <div class="card-body">
                {% if services %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Service</th>
                                <th>Duration</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for service in services %}
                            <tr>
                                <td>
                                    <strong>{{ service.service_title }}</strong>
                                    {% if service.short_description %}
                                    <br><small class="text-muted">{{ service.short_description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ service.duration_minutes }} min</td>
                                <td>${{ service.price }}</td>
                                <td>
                                    {% if service.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'venues_app:service_edit' pk=service.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-spa text-muted mb-3" style="font-size: 3rem;"></i>
                    <h6 class="text-muted">No services added yet</h6>
                    <p class="text-muted mb-3">Add your first service to start accepting bookings</p>
                    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Your First Service
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Operating Hours Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Operating Hours</h5>
                <a href="{% url 'venues_app:manage_operating_hours' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit me-1"></i>Edit
                </a>
            </div>
            <div class="card-body">
                {% if opening_hours %}
                <div class="row">
                    {% for hours in opening_hours %}
                    <div class="col-md-6 mb-2">
                        <div class="d-flex justify-content-between">
                            <span class="fw-medium">{{ hours.get_day_display }}</span>
                            <span class="text-muted">
                                {% if hours.is_closed %}
                                    Closed
                                {% elif hours.is_24_hours %}
                                    24 Hours
                                {% else %}
                                    {{ hours.opening|time:"g:i A" }} - {{ hours.closing|time:"g:i A" }}
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-clock text-muted mb-2" style="font-size: 2rem;"></i>
                    <p class="text-muted mb-0">Operating hours not set</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Amenities Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-star me-2"></i>Amenities</h5>
                <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit me-1"></i>Edit
                </a>
            </div>
            <div class="card-body">
                {% if amenities %}
                <div class="row">
                    {% for amenity in amenities %}
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <span>{{ amenity.get_amenity_type_display }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-star text-muted mb-2" style="font-size: 2rem;"></i>
                    <p class="text-muted mb-0">No amenities added</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- FAQs Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>FAQs ({{ faqs|length }})</h5>
                <a href="{% url 'venues_app:manage_faqs' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit me-1"></i>Manage
                </a>
            </div>
            <div class="card-body">
                {% if faqs %}
                <div class="accordion" id="faqAccordion">
                    {% for faq in faqs|slice:":3" %}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq{{ faq.id }}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ faq.id }}" aria-expanded="false" aria-controls="collapse{{ faq.id }}">
                                {{ faq.question }}
                            </button>
                        </h2>
                        <div id="collapse{{ faq.id }}" class="accordion-collapse collapse" aria-labelledby="faq{{ faq.id }}" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {{ faq.answer }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if faqs|length > 3 %}
                <div class="text-center mt-3">
                    <small class="text-muted">+{{ faqs|length|add:"-3" }} more FAQs</small>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-question-circle text-muted mb-2" style="font-size: 2rem;"></i>
                    <p class="text-muted mb-0">No FAQs added yet</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Venue Image Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-images me-2"></i>Images</h5>
                <a href="{% url 'venues_app:manage_venue_images' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit me-1"></i>Manage
                </a>
            </div>
            <div class="card-body">
                {% if primary_image %}
                <div class="mb-3">
                    <img src="{{ primary_image.image.url }}" alt="{{ venue.venue_name }}" class="img-fluid rounded" style="width: 100%; height: 200px; object-fit: cover;">
                    <small class="text-muted d-block mt-1">Primary Image</small>
                </div>
                {% endif %}

                {% if gallery_images %}
                <div class="row g-2">
                    {% for image in gallery_images|slice:":4" %}
                    <div class="col-6">
                        <img src="{{ image.image.url }}" alt="{{ image.caption|default:venue.venue_name }}" class="img-fluid rounded" style="width: 100%; height: 80px; object-fit: cover;">
                    </div>
                    {% endfor %}
                </div>
                {% if gallery_images|length > 4 %}
                <small class="text-muted">+{{ gallery_images|length|add:"-4" }} more images</small>
                {% endif %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-images text-muted mb-2" style="font-size: 2rem;"></i>
                    <p class="text-muted mb-0">No images uploaded</p>
                </div>
                {% endif %}
            </div>
        </div>



                <!-- Venue Completeness Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>Venue Completeness</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold text-primary">Venue Completion</span>
                        <span class="badge {% if venue_completeness_score >= 80 %}bg-success{% elif venue_completeness_score >= 60 %}bg-warning{% else %}bg-danger{% endif %}">
                            {{ venue_completeness_score }}%
                        </span>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar {% if venue_completeness_score >= 80 %}bg-success{% elif venue_completeness_score >= 60 %}bg-warning{% else %}bg-danger{% endif %}"
                             style="width: {{ venue_completeness_score }}%"></div>
                    </div>

                    {% if venue_missing_info %}
                    <div class="missing-items">
                        <h6 class="text-muted mb-2">Venue Areas for Improvement:</h6>
                        {% for item in venue_missing_info|slice:":3" %}
                        <div class="missing-item mb-2 p-2 rounded {% if item.priority == 'high' %}bg-danger bg-opacity-10 border border-danger{% elif item.priority == 'medium' %}bg-warning bg-opacity-10 border border-warning{% else %}bg-info bg-opacity-10 border border-info{% endif %}">
                            <div class="d-flex align-items-center">
                                <i class="fas {% if item.priority == 'high' %}fa-exclamation-triangle text-danger{% elif item.priority == 'medium' %}fa-exclamation-circle text-warning{% else %}fa-info-circle text-info{% endif %} me-2"></i>
                                <div>
                                    <strong class="small">{{ item.title }}</strong>
                                    <br><small class="text-muted">{{ item.message }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% if venue_missing_info|length > 3 %}
                        <small class="text-muted">+{{ venue_missing_info|length|add:"-3" }} more venue items to complete</small>
                        {% endif %}
                    </div>
                    {% else %}
                    <div class="text-center py-2">
                        <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                        <p class="text-success mb-0">Great job! Your venue is complete.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>



        <!-- Venue Status Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Venue Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-muted">Approval Status</h6>
                    {% if venue.approval_status == 'draft' %}
                        <span class="badge bg-secondary">Draft</span>
                    {% elif venue.approval_status == 'pending' %}
                        <span class="badge bg-warning">Under Review</span>
                    {% elif venue.approval_status == 'approved' %}
                        <span class="badge bg-success">Approved</span>
                    {% elif venue.approval_status == 'rejected' %}
                        <span class="badge bg-danger">Needs Updates</span>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <h6 class="text-muted">Visibility</h6>
                    {% if venue.visibility == 'active' %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                </div>

                {% if venue.approval_status == 'draft' %}
                <div class="alert alert-info">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        Complete your venue setup and submit for approval to go live.
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Approval Submission Modal -->
{% if venue.approval_status == 'draft' and can_submit_for_approval %}
<div class="modal fade" id="approvalSubmissionModal" tabindex="-1" aria-labelledby="approvalSubmissionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalSubmissionModalLabel">Submit Venue for Approval</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you ready to submit your venue for approval? Please ensure:</p>
                <ul>
                    <li>All venue information is complete and accurate</li>
                    <li>You have added at least 2 services with pricing</li>
                    <li>You have uploaded venue images</li>
                    <li>Contact information is up to date</li>
                </ul>
                <p class="text-muted">Once submitted, our team will review your venue within 24-48 hours.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="{% url 'dashboard_app:provider_venue_approval_submission' %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-primary">Submit for Approval</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block dashboard_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
