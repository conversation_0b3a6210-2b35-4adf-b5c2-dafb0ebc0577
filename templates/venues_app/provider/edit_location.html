{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Edit Location - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap');

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: #2F160F;
        font-size: 2.5rem;
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(250, 225, 215, 0.2);
        border-radius: 0.75rem;
        flex-shrink: 0;
    }

    /* Form section description text */
    .form-section p,
    .form-section .text-muted,
    .form-section small {
        color: #525252;
        font-family: 'Inter', sans-serif;
        font-size: 1.125rem;
        line-height: 1.6;
    }

    .form-section small {
        font-size: 0.875rem;
    }

    /* Form labels */
    .form-label {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .form-label.fw-bold {
        font-weight: 600;
    }

    /* Required asterisk */
    .text-danger {
        color: #dc3545 !important;
    }

    /* Form text/help text */
    .form-text {
        color: #525252;
        font-family: 'Inter', sans-serif;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Button styling */
    .btn-cw-primary {
        background: white;
        color: #2F160F;
        border: 2px solid #2F160F;
        border-radius: 0.5rem;
        padding: 0.75rem 2rem;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
        font-size: 1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        background: #2F160F;
        color: white;
        border-color: #2F160F;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
    }

    .btn-cw-secondary {
        background: white;
        color: #525252;
        border: 2px solid #e5e5e5;
        border-radius: 0.5rem;
        padding: 0.75rem 2rem;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
        font-size: 1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: #f8f9fa;
        color: #2F160F;
        border-color: #2F160F;
    }

    /* Form inputs */
    .form-control {
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: 'Inter', sans-serif;
        color: #2F160F;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
    }

    .form-select {
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: 'Inter', sans-serif;
        color: #2F160F;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
    }

    .form-select:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
    }

    /* Error messages */
    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        margin-top: 0.25rem;
    }

    /* Address preview styling */
    .address-preview {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .address-preview h6 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .address-preview h6 i {
        color: #2F160F;
        font-size: 1rem;
    }

    .address-preview p {
        color: #525252;
        font-family: 'Inter', sans-serif;
        margin-bottom: 0;
        line-height: 1.5;
    }

    /* Coordinates info styling */
    .coordinates-info {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .coordinates-info p {
        color: #525252;
        font-family: 'Inter', sans-serif;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .coordinates-info p i {
        color: #2F160F;
        font-size: 1rem;
    }

    .coordinates-info small {
        color: #525252;
        font-family: 'Inter', sans-serif;
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

<!-- Edit Form -->
<form method="post" novalidate>
    {% csrf_token %}

    <!-- Address Details Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-map-marker-alt me-2"></i>Address Details
        </h4>

        <div class="row">
            <div class="col-md-3 mb-3">
                <label for="{{ form.street_number.id_for_label }}" class="form-label fw-bold">
                    Street Number <span class="text-danger">*</span>
                </label>
                {{ form.street_number }}
                {% if form.street_number.errors %}
                    {% for error in form.street_number.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Building or house number
                </small>
            </div>

            <div class="col-md-9 mb-3">
                <label for="{{ form.street_name.id_for_label }}" class="form-label fw-bold">
                    Street Name <span class="text-danger">*</span>
                </label>
                {{ form.street_name }}
                {% if form.street_name.errors %}
                    {% for error in form.street_name.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Street name or address line
                </small>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="{{ form.city.id_for_label }}" class="form-label fw-bold">
                    City <span class="text-danger">*</span>
                </label>
                {{ form.city }}
                {% if form.city.errors %}
                    {% for error in form.city.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    City where your venue is located
                </small>
            </div>

            <div class="col-md-4 mb-3">
                <label for="{{ form.county.id_for_label }}" class="form-label fw-bold">
                    County <span class="text-danger">*</span>
                </label>
                {{ form.county }}
                {% if form.county.errors %}
                    {% for error in form.county.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    County or administrative area
                </small>
            </div>

            <div class="col-md-4 mb-3">
                <label for="{{ form.state.id_for_label }}" class="form-label fw-bold">
                    State <span class="text-danger">*</span>
                </label>
                {{ form.state }}
                {% if form.state.errors %}
                    {% for error in form.state.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    State or province
                </small>
            </div>
        </div>

        <!-- Address Preview -->
        <div class="address-preview" id="address-preview">
            <h6><i class="fas fa-eye me-2"></i>Address Preview</h6>
            <p class="mb-0" id="preview-text">
                {% if venue.get_full_address %}
                    {{ venue.get_full_address }}
                {% else %}
                    Complete the address fields above to see preview
                {% endif %}
            </p>
        </div>
    </div>

    <!-- Coordinates Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-crosshairs me-2"></i>Coordinates
        </h4>

        <div class="coordinates-info">
            <p class="mb-2">
                <i class="fas fa-info-circle me-2"></i>
                Coordinates are automatically determined from your address and help customers find your venue on maps.
            </p>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.latitude.id_for_label }}" class="form-label fw-bold">
                        Latitude
                    </label>
                    {{ form.latitude }}
                    {% if form.latitude.errors %}
                        {% for error in form.latitude.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <small class="form-text text-muted">
                        Auto-filled from address
                    </small>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="{{ form.longitude.id_for_label }}" class="form-label fw-bold">
                        Longitude
                    </label>
                    {{ form.longitude }}
                    {% if form.longitude.errors %}
                        {% for error in form.longitude.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <small class="form-text text-muted">
                        Auto-filled from address
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="text-center">
        <button type="submit" class="btn btn-cw-primary">
            <i class="fas fa-save me-2"></i>Update
        </button>
    </div>
</form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Address preview update
    function updateAddressPreview() {
        const streetNumber = document.getElementById('{{ form.street_number.id_for_label }}').value.trim();
        const streetName = document.getElementById('{{ form.street_name.id_for_label }}').value.trim();
        const city = document.getElementById('{{ form.city.id_for_label }}').value.trim();
        const county = document.getElementById('{{ form.county.id_for_label }}').value.trim();
        const state = document.getElementById('{{ form.state.id_for_label }}').value.trim();

        const previewText = document.getElementById('preview-text');

        if (streetNumber || streetName || city || county || state) {
            const addressParts = [];
            if (streetNumber && streetName) {
                addressParts.push(`${streetNumber} ${streetName}`);
            } else if (streetName) {
                addressParts.push(streetName);
            }
            if (city) addressParts.push(city);
            if (county) addressParts.push(county);
            if (state) addressParts.push(state);

            previewText.textContent = addressParts.join(', ') || 'Complete the address fields above to see preview';
        } else {
            previewText.textContent = 'Complete the address fields above to see preview';
        }
    }

    // Add event listeners to address fields
    const addressFields = [
        '{{ form.street_number.id_for_label }}',
        '{{ form.street_name.id_for_label }}',
        '{{ form.city.id_for_label }}',
        '{{ form.county.id_for_label }}',
        '{{ form.state.id_for_label }}'
    ];

    addressFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', updateAddressPreview);
            field.addEventListener('change', updateAddressPreview);
        }
    });

    // Initial preview update
    updateAddressPreview();
});
</script>
{% endblock %}
