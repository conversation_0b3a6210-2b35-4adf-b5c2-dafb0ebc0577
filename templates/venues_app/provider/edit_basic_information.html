{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Edit Basic Information - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap');

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: #2F160F;
        font-size: 2.5rem;
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(250, 225, 215, 0.2);
        border-radius: 0.75rem;
        flex-shrink: 0;
    }

    /* Form section description text */
    .form-section p,
    .form-section .text-muted,
    .form-section small {
        color: #525252;
        font-family: 'Inter', sans-serif;
        font-size: 1.125rem;
        line-height: 1.6;
    }

    .form-section small {
        font-size: 0.875rem;
    }

    /* Form labels */
    .form-label {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    /* Category list layout */
    .category-list {
        margin-top: 1rem;
    }

    .form-check {
        margin-bottom: 0.75rem;
    }

    .form-check-input {
        margin-right: 0.5rem;
    }

    .form-check-label {
        font-weight: 500;
        color: #525252;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
    }

    /* Button styling */
    .btn-cw-primary {
        background: white;
        border: 2px solid #2F160F;
        color: #2F160F;
        padding: 0.75rem 3rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: block;
        margin: 2rem auto 0;
        width: fit-content;
    }

    .btn-cw-primary:hover {
        background: #2F160F;
        border-color: #2F160F;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
    }

    /* Form inputs */
    .form-control {
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: 'Inter', sans-serif;
        color: #2F160F;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
    }

    /* Character count */
    .character-count {
        font-size: 0.875rem;
        color: #525252;
        font-family: 'Inter', sans-serif;
        text-align: right;
        margin-top: 0.25rem;
    }

    /* Error messages */
    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

<!-- Edit Form -->
<form method="post" novalidate>
    {% csrf_token %}

    <!-- Basic Details Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-building me-2"></i>Basic Details
        </h4>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="{{ form.venue_name.id_for_label }}" class="form-label fw-bold">
                    Venue Name <span class="text-danger">*</span>
                </label>
                {{ form.venue_name }}
                {% if form.venue_name.errors %}
                    {% for error in form.venue_name.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="character-count">
                    <span id="venue-name-count">0</span>/255 characters
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label for="{{ form.short_description.id_for_label }}" class="form-label fw-bold">
                Description <span class="text-danger">*</span>
            </label>
            {{ form.short_description }}
            {% if form.short_description.errors %}
                {% for error in form.short_description.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="character-count">
                <span id="description-count">0</span>/500 characters
            </div>
            <small class="form-text text-muted">
                Describe your venue and what makes it special. This will be displayed to customers.
            </small>
        </div>
    </div>

    <!-- Categories Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-tags me-2"></i>Categories
        </h4>

        <p class="text-muted mb-3">
            Select up to 3 categories that best describe your venue. This helps customers find you.
        </p>

        <!-- Simple checkbox list like venue creation -->
        <div class="category-list">
            {% for choice in form.categories %}
                <div class="form-check mb-2">
                    {{ choice.tag }}
                    <label class="form-check-label" for="{{ choice.id_for_label }}">
                        {{ choice.choice_label }}
                    </label>
                </div>
            {% endfor %}
        </div>

        {% if form.categories.errors %}
            {% for error in form.categories.errors %}
                <div class="invalid-feedback mt-2">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Contact Information Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-phone me-2"></i>Contact Information
        </h4>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="{{ form.phone.id_for_label }}" class="form-label fw-bold">
                    Phone Number
                </label>
                {{ form.phone }}
                {% if form.phone.errors %}
                    {% for error in form.phone.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Primary contact number for your venue
                </small>
            </div>

            <div class="col-md-6 mb-3">
                <label for="{{ form.email.id_for_label }}" class="form-label fw-bold">
                    Email Address
                </label>
                {{ form.email }}
                {% if form.email.errors %}
                    {% for error in form.email.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Primary contact email for your venue
                </small>
            </div>
        </div>

        <div class="mb-3">
            <label for="{{ form.website_url.id_for_label }}" class="form-label fw-bold">
                Website URL
            </label>
            {{ form.website_url }}
            {% if form.website_url.errors %}
                {% for error in form.website_url.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <small class="form-text text-muted">
                Your venue's website (optional)
            </small>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="text-center">
        <button type="submit" class="btn btn-cw-primary">
            <i class="fas fa-save me-2"></i>Update
        </button>
    </div>
</form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counting
    function updateCharacterCount(input, countElement, maxLength) {
        const count = input.value.length;
        countElement.textContent = count;

        if (count > maxLength * 0.9) {
            countElement.style.color = '#dc3545';
        } else if (count > maxLength * 0.7) {
            countElement.style.color = '#ffc107';
        } else {
            countElement.style.color = '#6c757d';
        }
    }

    // Venue name character count
    const venueNameInput = document.getElementById('{{ form.venue_name.id_for_label }}');
    const venueNameCount = document.getElementById('venue-name-count');
    if (venueNameInput && venueNameCount) {
        updateCharacterCount(venueNameInput, venueNameCount, 255);
        venueNameInput.addEventListener('input', function() {
            updateCharacterCount(this, venueNameCount, 255);
        });
    }

    // Description character count
    const descriptionInput = document.getElementById('{{ form.short_description.id_for_label }}');
    const descriptionCount = document.getElementById('description-count');
    if (descriptionInput && descriptionCount) {
        updateCharacterCount(descriptionInput, descriptionCount, 500);
        descriptionInput.addEventListener('input', function() {
            updateCharacterCount(this, descriptionCount, 500);
        });
    }

    // Simple category selection with limit enforcement (like venue creation)
    function setupCategorySelection() {
        document.querySelectorAll('.category-list input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selectedCount = document.querySelectorAll('.category-list input[type="checkbox"]:checked').length;

                // Check selection limit (3 categories max)
                if (selectedCount > 3) {
                    this.checked = false;
                    alert('You can select up to 3 categories only.');
                    return;
                }
            });
        });
    }

    // Initialize category selection
    setupCategorySelection();
});
</script>
{% endblock %}
