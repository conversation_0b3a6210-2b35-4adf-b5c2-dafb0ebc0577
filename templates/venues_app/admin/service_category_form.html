{% extends 'admin_app/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ action }} Service Category - Admin{% endblock title %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .form-card {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .form-section-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    .color-preview {
        width: 40px;
        height: 40px;
        border-radius: 0.5rem;
        border: 2px solid var(--cw-neutral-300);
        display: inline-block;
        margin-left: 0.5rem;
        vertical-align: middle;
    }

    .icon-preview {
        width: 40px;
        height: 40px;
        border-radius: 0.5rem;
        background: var(--cw-brand-primary);
        color: white;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: 0.5rem;
        vertical-align: middle;
        font-size: 1.25rem;
    }

    .preview-section {
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .category-preview {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 2px solid var(--cw-brand-accent);
    }

    .preview-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        box-shadow: var(--cw-shadow-sm);
    }

    .preview-content h4 {
        margin: 0 0 0.5rem 0;
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .preview-content p {
        margin: 0;
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
    }

    .icon-suggestions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .icon-suggestion {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: var(--cw-accent-light);
        border-radius: 0.375rem;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all 0.2s ease;
    }

    .icon-suggestion:hover {
        background: var(--cw-brand-primary);
        color: white;
    }

    .color-suggestions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .color-suggestion {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        border-radius: 0.375rem;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all 0.2s ease;
        border: 2px solid transparent;
    }

    .color-suggestion:hover {
        border-color: var(--cw-brand-primary);
    }

    .color-dot {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
    }
</style>
{% endblock extra_css %}

{% block content %}
<div class="form-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 text-primary mb-1">{{ title }}</h1>
            <p class="text-muted mb-0">Configure service category details and appearance</p>
        </div>
        <a href="{% url 'venues_app:admin_service_category_list' %}" class="btn btn-cw-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Categories
        </a>
    </div>

    <div class="form-card">
        <form method="post" id="categoryForm">
            {% csrf_token %}

            <!-- Basic Information Section -->
            <div class="form-section">
                <h3 class="form-section-title">
                    <i class="fas fa-info-circle me-2"></i>Basic Information
                </h3>

                <div class="row">
                    <div class="col-md-8 mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label fw-semibold">
                            Category Name <span class="text-danger">*</span>
                        </label>
                        {{ form.name|add_class:"form-control form-control-cw" }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback d-block">{{ form.name.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.name.help_text }}</div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="{{ form.sort_order.id_for_label }}" class="form-label fw-semibold">
                            Sort Order
                        </label>
                        {{ form.sort_order|add_class:"form-control form-control-cw" }}
                        {% if form.sort_order.errors %}
                            <div class="invalid-feedback d-block">{{ form.sort_order.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.sort_order.help_text }}</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label fw-semibold">
                        Description
                    </label>
                    {{ form.description|add_class:"form-control form-control-cw" }}
                    {% if form.description.errors %}
                        <div class="invalid-feedback d-block">{{ form.description.errors.0 }}</div>
                    {% endif %}
                    <div class="form-text">{{ form.description.help_text }}</div>
                </div>
            </div>

            <!-- Appearance Section -->
            <div class="form-section">
                <h3 class="form-section-title">
                    <i class="fas fa-palette me-2"></i>Appearance
                </h3>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.icon_class.id_for_label }}" class="form-label fw-semibold">
                            Icon Class
                        </label>
                        <div class="input-group">
                            {{ form.icon_class|add_class:"form-control form-control-cw" }}
                            <span class="input-group-text">
                                <span class="icon-preview" id="iconPreview">
                                    <i class="fas fa-folder"></i>
                                </span>
                            </span>
                        </div>
                        {% if form.icon_class.errors %}
                            <div class="invalid-feedback d-block">{{ form.icon_class.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.icon_class.help_text }}</div>

                        <!-- Icon Suggestions -->
                        <div class="icon-suggestions">
                            <div class="icon-suggestion" onclick="setIcon('fas fa-spa')">
                                <i class="fas fa-spa"></i> Spa
                            </div>
                            <div class="icon-suggestion" onclick="setIcon('fas fa-cut')">
                                <i class="fas fa-cut"></i> Hair
                            </div>
                            <div class="icon-suggestion" onclick="setIcon('fas fa-hand-paper')">
                                <i class="fas fa-hand-paper"></i> Massage
                            </div>
                            <div class="icon-suggestion" onclick="setIcon('fas fa-smile')">
                                <i class="fas fa-smile"></i> Facial
                            </div>
                            <div class="icon-suggestion" onclick="setIcon('fas fa-hand-sparkles')">
                                <i class="fas fa-hand-sparkles"></i> Nails
                            </div>
                            <div class="icon-suggestion" onclick="setIcon('fas fa-leaf')">
                                <i class="fas fa-leaf"></i> Wellness
                            </div>
                            <div class="icon-suggestion" onclick="setIcon('fas fa-dumbbell')">
                                <i class="fas fa-dumbbell"></i> Fitness
                            </div>
                            <div class="icon-suggestion" onclick="setIcon('fas fa-magic')">
                                <i class="fas fa-magic"></i> Beauty
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="{{ form.color_code.id_for_label }}" class="form-label fw-semibold">
                            Color
                        </label>
                        <div class="input-group">
                            {{ form.color_code|add_class:"form-control form-control-cw" }}
                            <span class="input-group-text">
                                <span class="color-preview" id="colorPreview" style="background-color: #6b7280;"></span>
                            </span>
                        </div>
                        {% if form.color_code.errors %}
                            <div class="invalid-feedback d-block">{{ form.color_code.errors.0 }}</div>
                        {% endif %}
                        <div class="form-text">{{ form.color_code.help_text }}</div>

                        <!-- Color Suggestions -->
                        <div class="color-suggestions">
                            <div class="color-suggestion" onclick="setColor('#4CAF50')">
                                <span class="color-dot" style="background-color: #4CAF50;"></span> Green
                            </div>
                            <div class="color-suggestion" onclick="setColor('#FF9800')">
                                <span class="color-dot" style="background-color: #FF9800;"></span> Orange
                            </div>
                            <div class="color-suggestion" onclick="setColor('#9C27B0')">
                                <span class="color-dot" style="background-color: #9C27B0;"></span> Purple
                            </div>
                            <div class="color-suggestion" onclick="setColor('#E91E63')">
                                <span class="color-dot" style="background-color: #E91E63;"></span> Pink
                            </div>
                            <div class="color-suggestion" onclick="setColor('#00BCD4')">
                                <span class="color-dot" style="background-color: #00BCD4;"></span> Cyan
                            </div>
                            <div class="color-suggestion" onclick="setColor('#FF5722')">
                                <span class="color-dot" style="background-color: #FF5722;"></span> Red
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Section -->
            <div class="form-section">
                <h3 class="form-section-title">
                    <i class="fas fa-toggle-on me-2"></i>Status
                </h3>

                <div class="form-check form-switch">
                    {{ form.is_active|add_class:"form-check-input" }}
                    <label class="form-check-label fw-semibold" for="{{ form.is_active.id_for_label }}">
                        {{ form.is_active.label }}
                    </label>
                    {% if form.is_active.errors %}
                        <div class="invalid-feedback d-block">{{ form.is_active.errors.0 }}</div>
                    {% endif %}
                    <div class="form-text">{{ form.is_active.help_text }}</div>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="form-section">
                <h3 class="form-section-title">
                    <i class="fas fa-eye me-2"></i>Preview
                </h3>

                <div class="preview-section">
                    <h5 class="mb-3">How this category will appear:</h5>
                    <div class="category-preview" id="categoryPreview">
                        <div class="preview-icon" id="previewIconContainer" style="background-color: #6b7280;">
                            <i class="fas fa-folder" id="previewIcon"></i>
                        </div>
                        <div class="preview-content">
                            <h4 id="previewName">Category Name</h4>
                            <p id="previewDescription">Category description will appear here...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex gap-3 pt-4 border-top">
                <button type="submit" class="btn btn-cw-primary">
                    <i class="fas fa-save me-2"></i>Save Category
                </button>
                <a href="{% url 'venues_app:admin_service_category_list' %}" class="btn btn-cw-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock content %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    const descriptionField = document.getElementById('{{ form.description.id_for_label }}');
    const iconField = document.getElementById('{{ form.icon_class.id_for_label }}');
    const colorField = document.getElementById('{{ form.color_code.id_for_label }}');

    // Get preview elements
    const previewName = document.getElementById('previewName');
    const previewDescription = document.getElementById('previewDescription');
    const previewIcon = document.getElementById('previewIcon');
    const previewIconContainer = document.getElementById('previewIconContainer');
    const iconPreview = document.getElementById('iconPreview');
    const colorPreview = document.getElementById('colorPreview');

    // Update preview function
    function updatePreview() {
        const name = nameField.value || 'Category Name';
        const description = descriptionField.value || 'Category description will appear here...';
        const iconClass = iconField.value || 'fas fa-folder';
        const colorCode = colorField.value || '#6b7280';

        previewName.textContent = name;
        previewDescription.textContent = description;
        previewIcon.className = iconClass;
        previewIconContainer.style.backgroundColor = colorCode;
        iconPreview.querySelector('i').className = iconClass;
        colorPreview.style.backgroundColor = colorCode;
    }

    // Add event listeners
    nameField.addEventListener('input', updatePreview);
    descriptionField.addEventListener('input', updatePreview);
    iconField.addEventListener('input', updatePreview);
    colorField.addEventListener('input', updatePreview);

    // Initialize preview
    updatePreview();
});

function setIcon(iconClass) {
    document.getElementById('{{ form.icon_class.id_for_label }}').value = iconClass;
    document.getElementById('{{ form.icon_class.id_for_label }}').dispatchEvent(new Event('input'));
}

function setColor(colorCode) {
    document.getElementById('{{ form.color_code.id_for_label }}').value = colorCode;
    document.getElementById('{{ form.color_code.id_for_label }}').dispatchEvent(new Event('input'));
}
</script>
{% endblock extra_js %}
