{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Delete Service Category - Admin{% endblock title %}

{% block extra_css %}
<style>
    .delete-container {
        max-width: 600px;
        margin: 0 auto;
    }

    .delete-card {
        background: white;
        border: 2px solid var(--cw-danger);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: var(--cw-shadow-md);
    }

    .delete-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .delete-icon {
        width: 4rem;
        height: 4rem;
        background: var(--cw-danger-light);
        color: var(--cw-danger);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
    }

    .category-info {
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .category-preview {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 2px solid var(--cw-brand-accent);
        margin-bottom: 1rem;
    }

    .preview-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        box-shadow: var(--cw-shadow-sm);
    }

    .preview-content h4 {
        margin: 0 0 0.5rem 0;
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .preview-content p {
        margin: 0;
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
    }

    .warning-box {
        background: var(--cw-warning-light);
        border: 2px solid var(--cw-warning);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .warning-box .warning-title {
        font-weight: 600;
        color: var(--cw-warning-dark);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .warning-box .warning-text {
        color: var(--cw-warning-dark);
        margin: 0;
        font-size: 0.875rem;
    }

    .error-box {
        background: var(--cw-danger-light);
        border: 2px solid var(--cw-danger);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .error-box .error-title {
        font-weight: 600;
        color: var(--cw-danger-dark);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .error-box .error-text {
        color: var(--cw-danger-dark);
        margin: 0;
        font-size: 0.875rem;
    }

    .category-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .stat-item {
        text-align: center;
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 2px solid var(--cw-brand-accent);
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        display: block;
    }

    .stat-label {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
</style>
{% endblock extra_css %}

{% block content %}
<div class="delete-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 text-danger mb-1">{{ title }}</h1>
            <p class="text-muted mb-0">Confirm service category deletion</p>
        </div>
        <a href="{% url 'venues_app:admin_service_category_list' %}" class="btn btn-cw-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Categories
        </a>
    </div>

    <div class="delete-card">
        <div class="delete-header">
            <div class="delete-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h2 class="h4 text-danger mb-2">Delete Service Category</h2>
            <p class="text-muted">This action cannot be undone. Please review the information below.</p>
        </div>

        <!-- Category Information -->
        <div class="category-info">
            <h5 class="mb-3">Category to Delete:</h5>

            <div class="category-preview">
                <div class="preview-icon" style="background-color: {{ service_category.color_code|default:'#6b7280' }}">
                    <i class="{{ service_category.icon_class|default:'fas fa-folder' }}"></i>
                </div>
                <div class="preview-content">
                    <h4>{{ service_category.name }}</h4>
                    {% if service_category.description %}
                        <p>{{ service_category.description|truncatewords:15 }}</p>
                    {% else %}
                        <p class="text-muted">No description provided</p>
                    {% endif %}
                </div>
            </div>

            <div class="category-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ service_count }}</span>
                    <div class="stat-label">Services Using This Category</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ service_category.venue_count }}</span>
                    <div class="stat-label">Venues Affected</div>
                </div>
            </div>
        </div>

        <!-- Warning or Error -->
        {% if service_count > 0 %}
            <div class="error-box">
                <div class="error-title">
                    <i class="fas fa-ban"></i>
                    Cannot Delete Category
                </div>
                <p class="error-text">
                    This service category cannot be deleted because it has <strong>{{ service_count }}</strong>
                    service{% if service_count != 1 %}s{% endif %} assigned to it.
                    You must first reassign or remove all services from this category before it can be deleted.
                </p>
            </div>

            <div class="d-flex gap-3">
                <a href="{% url 'venues_app:admin_service_category_list' %}" class="btn btn-cw-secondary flex-fill">
                    <i class="fas fa-arrow-left me-2"></i>Back to Categories
                </a>
                <a href="{% url 'venues_app:admin_service_category_edit' service_category.id %}" class="btn btn-cw-primary flex-fill">
                    <i class="fas fa-edit me-2"></i>Edit Category Instead
                </a>
            </div>
        {% else %}
            <div class="warning-box">
                <div class="warning-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Permanent Deletion
                </div>
                <p class="warning-text">
                    This category will be permanently deleted and cannot be recovered.
                    Since no services are currently using this category, it's safe to delete.
                </p>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="d-flex gap-3">
                    <a href="{% url 'venues_app:admin_service_category_list' %}" class="btn btn-cw-secondary flex-fill">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-danger flex-fill">
                        <i class="fas fa-trash me-2"></i>Delete Category
                    </button>
                </div>
            </form>
        {% endif %}
    </div>
</div>
{% endblock content %}
