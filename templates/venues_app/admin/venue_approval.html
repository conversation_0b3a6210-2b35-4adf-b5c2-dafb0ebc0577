{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Venue Approval - {{ venue.venue_name }}{% endblock %}

{% block extra_css %}
<style>
    .change-alert {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
    }

    .change-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }

    .change-stat {
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        padding: 10px;
        border-radius: 6px;
        backdrop-filter: blur(10px);
    }

    .change-number {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .change-label {
        font-size: 0.85rem;
        opacity: 0.9;
    }

    .approval-actions {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .btn-change-details {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .btn-change-details:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
    }

    .recommendation-badge {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        margin-left: 10px;
    }

    .recommendation-required {
        background: #ffeaa7;
        color: #d63031;
    }

    .recommendation-recommended {
        background: #fdcb6e;
        color: #e17055;
    }

    .recommendation-optional {
        background: #a7d8f0;
        color: #0984e3;
    }

    .approval-form-section {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 25px;
        margin-bottom: 20px;
    }

    .enhanced-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }

    .action-button {
        padding: 12px 20px;
        border: none;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        text-decoration: none;
        display: inline-block;
    }

    .btn-approve-enhanced {
        background: #00b894;
        color: white;
    }

    .btn-approve-enhanced:hover {
        background: #00a085;
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }

    .btn-reject-enhanced {
        background: #e17055;
        color: white;
    }

    .btn-reject-enhanced:hover {
        background: #d63031;
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }

    .btn-info-request {
        background: #f39c12;
        color: white;
    }

    .btn-info-request:hover {
        background: #e67e22;
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block admin_content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1>Venue Approval Review</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'venues_app:admin_venue_approval_dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'venues_app:admin_pending_venues' %}">Pending Venues</a></li>
                    <li class="breadcrumb-item active">{{ venue.venue_name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Change Detection Alert -->
    {% if has_change_details and change_analysis %}
    <div class="change-alert">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <h4><i class="fas fa-exclamation-triangle"></i> Changes Detected</h4>
                <p class="mb-0">This venue was previously approved but has been modified. Review the changes below before making your decision.</p>

                <span class="recommendation-badge recommendation-{{ change_analysis.approval_recommendation }}">
                    {% if change_analysis.approval_recommendation == 'required' %}
                        Approval Required
                    {% elif change_analysis.approval_recommendation == 'recommended' %}
                        Approval Recommended
                    {% elif change_analysis.approval_recommendation == 'optional' %}
                        Optional Review
                    {% else %}
                        Standard Process
                    {% endif %}
                </span>
            </div>
        </div>

        <div class="change-summary">
            <div class="change-stat">
                <div class="change-number">{{ change_analysis.total_changes|default:0 }}</div>
                <div class="change-label">Total Changes</div>
            </div>
            <div class="change-stat">
                <div class="change-number">{{ change_analysis.severity_breakdown.critical|default:0 }}</div>
                <div class="change-label">Critical</div>
            </div>
            <div class="change-stat">
                <div class="change-number">{{ change_analysis.severity_breakdown.major|default:0 }}</div>
                <div class="change-label">Major</div>
            </div>
            <div class="change-stat">
                <div class="change-number">{{ change_analysis.severity_breakdown.moderate|default:0 }}</div>
                <div class="change-label">Moderate</div>
            </div>
            <div class="change-stat">
                <div class="change-number">{{ change_analysis.severity_breakdown.minor|default:0 }}</div>
                <div class="change-label">Minor</div>
            </div>
        </div>

        <div class="approval-actions">
            <a href="{% url 'venues_app:admin_venue_changes_comparison' venue.id %}" class="btn-change-details">
                <i class="fas fa-code-branch"></i> View Detailed Changes
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Status Alert -->
    <div class="row mb-4">
        <div class="col-12">
            {% if venue.approval_status == 'pending' %}
                <div class="alert alert-warning">
                    <i class="fas fa-clock"></i>
                    <strong>Pending Approval:</strong> This venue is awaiting your review and approval decision.
                </div>
            {% elif venue.approval_status == 'approved' %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Already Approved:</strong> This venue was approved on {{ venue.approved_at|date:"M d, Y H:i" }}.
                </div>
            {% elif venue.approval_status == 'rejected' %}
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    <strong>Already Rejected:</strong> This venue was rejected.
                    {% if venue.admin_notes %}
                        <br><strong>Reason:</strong> {{ venue.admin_notes }}
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <!-- Venue Information Panel -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building"></i> Venue Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Venue Name:</strong></td>
                                    <td>{{ venue.venue_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Service Provider:</strong></td>
                                    <td>
                                        {{ venue.service_provider.business_name }}<br>
                                        <small class="text-muted">{{ venue.service_provider.user.email }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Venue Type:</strong></td>
                                    <td>{{ venue.get_venue_type_display|default:"Not specified" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ venue.phone_number|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ venue.email|default:"Not provided" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ venue.created_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ venue.approval_status }}">
                                            {{ venue.get_approval_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Categories:</strong></td>
                                    <td>
                                        {% if venue.categories.exists %}
                                            {% for category in venue.categories.all %}
                                                <span class="badge bg-info me-1">{{ category.category_name }}</span>
                                            {% endfor %}
                                        {% else %}
                                            <span class="text-muted">Not set</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Website:</strong></td>
                                    <td>
                                        {% if venue.website_url %}
                                            <a href="{{ venue.website_url }}" target="_blank">{{ venue.website_url|truncatechars:30 }}</a>
                                        {% else %}
                                            <span class="text-muted">Not provided</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Visibility:</strong></td>
                                    <td>
                                        {% if venue.visibility == 'active' %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ venue.get_visibility_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Description -->
                    {% if venue.short_description %}
                    <div class="mt-3">
                        <h6><strong>Description:</strong></h6>
                        <p class="text-muted">{{ venue.short_description|linebreaks }}</p>
                    </div>
                    {% endif %}

                    <!-- Location Information -->
                    <div class="mt-3">
                        <h6><strong>Location:</strong></h6>
                        <address class="text-muted">
                            {% if venue.street_number and venue.street_name %}
                                {{ venue.street_number }} {{ venue.street_name }}<br>
                            {% endif %}
                            {{ venue.city }}, {{ venue.county }}, {{ venue.state }}
                            {% if venue.zip_code %} {{ venue.zip_code }}{% endif %}
                        </address>
                    </div>

                    <!-- Categories -->
                    {% if venue.categories.exists %}
                    <div class="mt-3">
                        <h6><strong>Categories:</strong></h6>
                        <div class="d-flex flex-wrap">
                            {% for category in venue.categories.all %}
                                <span class="badge bg-primary me-2 mb-2">{{ category.category_name }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Services -->
                    {% if venue.services.exists %}
                    <div class="mt-3">
                        <h6><strong>Services ({{ venue.services.count }}):</strong></h6>
                        <ul class="list-unstyled">
                            {% for service in venue.services.all|slice:":5" %}
                                <li class="mb-1">
                                    <i class="fas fa-spa text-primary me-2"></i>
                                    {{ service.service_title }}
                                    {% if service.price_min %}
                                        <span class="text-muted ms-2">${{ service.price_min }}{% if service.price_max and service.price_max != service.price_min %} - ${{ service.price_max }}{% endif %}</span>
                                    {% endif %}
                                </li>
                            {% endfor %}
                            {% if venue.services.count > 5 %}
                                <li class="text-muted">... and {{ venue.services.count|add:"-5" }} more services</li>
                            {% endif %}
                        </ul>
                    </div>
                    {% endif %}

                    <!-- Images -->
                    {% if venue.images.exists %}
                    <div class="mt-3">
                        <h6><strong>Images ({{ venue.images.count }}):</strong></h6>
                        <div class="row">
                            {% for image in venue.images.all|slice:":4" %}
                                <div class="col-3 mb-2">
                                    <img src="{{ image.image.url }}" class="img-fluid rounded" alt="Venue Image">
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Approval Actions Panel -->
        <div class="col-lg-4">
            <!-- Approval Form -->
            {% if venue.approval_status == 'pending' %}
            <div class="approval-form-section">
                <h5><i class="fas fa-gavel"></i> Approval Decision</h5>
                <form method="post">
                    {% csrf_token %}

                    <div class="form-group mb-3">
                        <label for="admin_notes" class="form-label">
                            <strong>Admin Notes:</strong>
                            <small class="text-muted">(Required for rejection, optional for approval)</small>
                        </label>
                        <textarea name="admin_notes" id="admin_notes" class="form-control" rows="4"
                                  placeholder="Enter your notes or feedback for the service provider..."></textarea>
                    </div>

                    <div class="enhanced-actions">
                        <button type="submit" name="action" value="approve" class="action-button btn-approve-enhanced">
                            <i class="fas fa-check"></i> Approve Venue
                        </button>

                        <button type="submit" name="action" value="reject" class="action-button btn-reject-enhanced">
                            <i class="fas fa-times"></i> Reject Venue
                        </button>

                        <button type="submit" name="action" value="request_more_info" class="action-button btn-info-request">
                            <i class="fas fa-question-circle"></i> Request Info
                        </button>
                    </div>
                </form>
            </div>
            {% endif %}

            <!-- Enhanced Approval Timeline -->
            {% include 'venues_app/components/approval_timeline.html' %}

            <!-- Notification Preview -->
            {% if notification_preview %}
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-bell"></i> Notification Preview</h6>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Preview of notifications that will be sent to the service provider:</p>

                    <div class="notification-previews">
                        {% if venue.approval_status == 'pending' %}
                        <!-- Approval Notification Preview -->
                        <div class="notification-preview-card approval mb-3 p-3 border rounded">
                            <div class="preview-header d-flex align-items-center mb-2">
                                <i class="{{ notification_preview.approval.icon }} text-success me-2"></i>
                                <span class="preview-type fw-bold text-success">If Approved</span>
                            </div>
                            <div class="preview-content">
                                <h6 class="mb-1">{{ notification_preview.approval.title }}</h6>
                                <p class="text-muted mb-1">{{ notification_preview.approval.message }}</p>
                                <small class="preview-action text-primary">→ {{ notification_preview.approval.action_text }}</small>
                            </div>
                        </div>

                        <!-- Rejection Notification Preview -->
                        <div class="notification-preview-card rejection mb-3 p-3 border rounded">
                            <div class="preview-header d-flex align-items-center mb-2">
                                <i class="{{ notification_preview.rejection.icon }} text-warning me-2"></i>
                                <span class="preview-type fw-bold text-warning">If Rejected</span>
                            </div>
                            <div class="preview-content">
                                <h6 class="mb-1">{{ notification_preview.rejection.title }}</h6>
                                <p class="text-muted mb-1">{{ notification_preview.rejection.message }}</p>
                                <small class="preview-action text-primary">→ {{ notification_preview.rejection.action_text }}</small>
                            </div>
                        </div>

                        <!-- Info Request Notification Preview -->
                        <div class="notification-preview-card info-request p-3 border rounded">
                            <div class="preview-header d-flex align-items-center mb-2">
                                <i class="{{ notification_preview.info_request.icon }} text-info me-2"></i>
                                <span class="preview-type fw-bold text-info">If Info Requested</span>
                            </div>
                            <div class="preview-content">
                                <h6 class="mb-1">{{ notification_preview.info_request.title }}</h6>
                                <p class="text-muted mb-1">{{ notification_preview.info_request.message }}</p>
                                <small class="preview-action text-primary">→ {{ notification_preview.info_request.action_text }}</small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Additional Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-tools"></i> Additional Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'venues_app:admin_venue_detail' venue.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> View Full Details
                        </a>

                        {% if has_change_details %}
                        <a href="{% url 'venues_app:admin_venue_changes_comparison' venue.id %}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-code-branch"></i> Compare Changes
                        </a>
                        {% endif %}

                        <a href="{% url 'venues_app:admin_pending_venues' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Pending
                        </a>
                    </div>
                </div>
            </div>

            <!-- Venue Statistics -->
            {% if venue.approval_status == 'approved' %}
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Venue Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number h4 mb-0">{{ venue.services.count }}</div>
                                <small class="text-muted">Services</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number h4 mb-0">{{ venue.images.count }}</div>
                                <small class="text-muted">Images</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
