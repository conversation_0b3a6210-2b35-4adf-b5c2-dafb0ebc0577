{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}All Venues - CozyWish Admin{% endblock %}

{% block venues_content %}
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-font text-brand-cw mb-2">All Venues</h1>
            <p class="lead text-neutral-cw mb-0">Manage all venue submissions across all statuses</p>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-cw">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <input type="text" name="search" class="form-control"
                                   placeholder="Search venues..." value="{{ search_query }}">
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                                <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>Approved</option>
                                <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejected</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-cw-primary">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                            <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-cw-secondary">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Venues Table -->
    <div class="row">
        <div class="col-12">
            <div class="card-cw">
                {% if venues %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Venue Name</th>
                                <th>Provider</th>
                                <th>Location</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for venue in venues %}
                            <tr>
                                <td>{{ venue.venue_name }}</td>
                                <td>{{ venue.service_provider.user.email }}</td>
                                <td>{{ venue.city }}, {{ venue.state }}</td>
                                <td>
                                    {% if venue.approval_status == 'pending' %}
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    {% elif venue.approval_status == 'approved' %}
                                        <span class="badge bg-success">Approved</span>
                                    {% elif venue.approval_status == 'rejected' %}
                                        <span class="badge bg-danger">Rejected</span>
                                    {% endif %}
                                </td>
                                <td>{{ venue.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'venues_app:admin_venue_detail' venue_id=venue.id %}" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if venue.approval_status == 'pending' %}
                                        <a href="{% url 'venues_app:admin_venue_approval' venue_id=venue.id %}" class="btn btn-outline-secondary">
                                            <i class="fas fa-check-circle"></i>
                                        </a>
                                        {% endif %}
                                        {% if venue.approval_status == 'approved' %}
                                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-outline-secondary" target="_blank">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="alert alert-info">
                    <p class="mb-0">No venues found matching your criteria.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-store"></i>
                    <div class="stat-number">{{ total_venues|default:page_obj.paginator.count }}</div>
                    <div class="stat-label">Total Venues</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-clock"></i>
                    <div class="stat-number">{{ pending_venues|default:"--" }}</div>
                    <div class="stat-label">Pending Approval</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-check-circle"></i>
                    <div class="stat-number">{{ approved_venues|default:"--" }}</div>
                    <div class="stat-label">Approved Venues</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-times-circle"></i>
                    <div class="stat-number">{{ rejected_venues|default:"--" }}</div>
                    <div class="stat-label">Rejected Venues</div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
