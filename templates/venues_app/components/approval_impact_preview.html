{% load venue_filters %}

<!-- Approval Impact Preview Component -->
<div class="impact-preview-container">
    <div class="impact-header">
        <h3 class="impact-title">
            <i class="fas fa-chart-line"></i> Approval Impact Preview
        </h3>
        <div class="current-status">
            Current Status:
            <span class="status-tag status-{{ venue.approval_status }}">{{ venue.get_approval_status_display }}</span>
        </div>
    </div>

    <!-- Current Impact Section -->
    <div class="current-impact">
        <h4 class="section-title">Current Status Impact</h4>
        <div class="impact-grid">
            {% for key, value in impact_preview.current_impact.items %}
            <div class="impact-card">
                <div class="impact-icon">
                    {% if key == 'visibility' %}
                        <i class="fas fa-eye"></i>
                    {% elif key == 'booking' %}
                        <i class="fas fa-calendar-check"></i>
                    {% elif key == 'seo' %}
                        <i class="fas fa-search"></i>
                    {% elif key == 'provider_access' %}
                        <i class="fas fa-user-cog"></i>
                    {% endif %}
                </div>
                <div class="impact-content">
                    <h5 class="impact-label">{{ key|title|underscore_to_spaces }}</h5>
                    <p class="impact-description">{{ value }}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Metrics Overview -->
    <div class="metrics-overview">
        <h4 class="section-title">Key Metrics</h4>
        <div class="metrics-grid">
            <!-- Visibility Metrics -->
            <div class="metric-card">
                <div class="metric-icon visibility">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="metric-info">
                    <h5>Visibility</h5>
                    <div class="metric-value">
                        {{ impact_preview.metrics.estimated_visibility_change.current }}
                    </div>
                    <small class="metric-subtitle">Current Visibility</small>
                </div>
            </div>

            <!-- Booking Metrics -->
            <div class="metric-card">
                <div class="metric-icon booking">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="metric-info">
                    <h5>Booking Access</h5>
                    <div class="metric-value">
                        {{ impact_preview.metrics.booking_impact.current }}
                    </div>
                    <small class="metric-subtitle">Booking Status</small>
                </div>
            </div>

            <!-- SEO Metrics -->
            <div class="metric-card">
                <div class="metric-icon seo">
                    <i class="fas fa-search"></i>
                </div>
                <div class="metric-info">
                    <h5>SEO Score</h5>
                    <div class="metric-value">
                        {{ impact_preview.metrics.seo_score_change.current_score|default:0 }}/100
                    </div>
                    <small class="metric-subtitle">Search Ranking</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Potential Status Changes -->
    {% if impact_preview.potential_impacts %}
    <div class="potential-changes">
        <h4 class="section-title">What Would Happen If Status Changes</h4>
        <div class="status-scenarios">
            {% for status, impacts in impact_preview.potential_impacts.items %}
            {% if status != impact_preview.current_status %}
            <div class="scenario-card scenario-{{ status }}">
                <div class="scenario-header">
                    <h5 class="scenario-title">
                        If status becomes: <span class="status-tag status-{{ status }}">{{ status|title }}</span>
                    </h5>
                </div>
                <div class="scenario-content">
                    {% for impact_type, impact_desc in impacts.items %}
                    <div class="scenario-impact">
                        <strong>{{ impact_type|title|underscore_to_spaces }}:</strong>
                        <span>{{ impact_desc }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Action Recommendations -->
    <div class="action-recommendations">
        <h4 class="section-title">Recommended Actions</h4>
        <div class="recommendations-list">
            {% if venue.approval_status == 'draft' %}
                <div class="recommendation primary">
                    <i class="fas fa-rocket"></i>
                    <div class="rec-content">
                        <strong>Submit for Approval</strong>
                        <p>Complete your venue setup and submit for admin review to go live.</p>
                    </div>
                </div>
            {% elif venue.approval_status == 'pending' %}
                <div class="recommendation info">
                    <i class="fas fa-clock"></i>
                    <div class="rec-content">
                        <strong>Under Review</strong>
                        <p>Your venue is being reviewed. You'll be notified once approved.</p>
                    </div>
                </div>
            {% elif venue.approval_status == 'rejected' %}
                <div class="recommendation warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="rec-content">
                        <strong>Address Feedback</strong>
                        <p>Review admin feedback and make necessary updates to resubmit.</p>
                    </div>
                </div>
            {% elif venue.approval_status == 'approved' %}
                <div class="recommendation success">
                    <i class="fas fa-check-circle"></i>
                    <div class="rec-content">
                        <strong>Maintain Quality</strong>
                        <p>Your venue is live! Keep information updated and respond to customer inquiries.</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.impact-preview-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.impact-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f3f4f6;
}

.impact-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.current-status {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.status-tag {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-tag.status-draft {
    background: #f1f5f9;
    color: #64748b;
}

.status-tag.status-pending {
    background: #fef3c7;
    color: #d97706;
}

.status-tag.status-approved {
    background: #dcfce7;
    color: #16a34a;
}

.status-tag.status-rejected {
    background: #fee2e2;
    color: #dc2626;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
}

.current-impact, .metrics-overview, .potential-changes, .action-recommendations {
    margin-bottom: 2rem;
}

.impact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.impact-card {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: all 0.2s ease;
}

.impact-card:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.impact-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.impact-content {
    flex: 1;
}

.impact-label {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.impact-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.metric-card {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 1rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
}

.metric-card:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.15);
}

.metric-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.metric-icon.visibility {
    background: linear-gradient(135deg, #10b981, #059669);
}

.metric-icon.booking {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.metric-icon.seo {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.metric-info {
    flex: 1;
}

.metric-info h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
}

.metric-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.metric-subtitle {
    font-size: 0.75rem;
    color: #6b7280;
}

.status-scenarios {
    display: grid;
    gap: 1rem;
}

.scenario-card {
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.scenario-card.scenario-approved {
    border-color: #bbf7d0;
    background: #f0fdf4;
}

.scenario-card.scenario-rejected {
    border-color: #fecaca;
    background: #fef2f2;
}

.scenario-card.scenario-pending {
    border-color: #fed7aa;
    background: #fffbeb;
}

.scenario-header {
    margin-bottom: 1rem;
}

.scenario-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.scenario-content {
    display: grid;
    gap: 0.75rem;
}

.scenario-impact {
    font-size: 0.875rem;
    color: #4b5563;
}

.scenario-impact strong {
    color: #1f2937;
}

.recommendations-list {
    display: grid;
    gap: 1rem;
}

.recommendation {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 2px solid;
}

.recommendation.primary {
    background: #eff6ff;
    border-color: #3b82f6;
    color: #1e40af;
}

.recommendation.success {
    background: #f0fdf4;
    border-color: #22c55e;
    color: #15803d;
}

.recommendation.warning {
    background: #fffbeb;
    border-color: #f59e0b;
    color: #d97706;
}

.recommendation.info {
    background: #f8fafc;
    border-color: #64748b;
    color: #475569;
}

.recommendation i {
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.rec-content {
    flex: 1;
}

.rec-content strong {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.rec-content p {
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .impact-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .impact-grid, .metrics-grid {
        grid-template-columns: 1fr;
    }

    .impact-card, .metric-card {
        flex-direction: column;
        text-align: center;
    }

    .impact-icon, .metric-icon {
        margin: 0 auto;
    }
}
</style>
