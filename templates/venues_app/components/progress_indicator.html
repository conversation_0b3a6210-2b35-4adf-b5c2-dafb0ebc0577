<!-- Venue Setup Progress Indicator -->
<div class="venue-progress-indicator mb-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0">
                <i class="fas fa-chart-line me-2"></i>
                Venue Setup Progress
                <span class="badge bg-light text-primary ms-2">{{ progress.completion_percentage }}%</span>
            </h6>
        </div>
        <div class="card-body">
            <!-- Progress Bar -->
            <div class="progress mb-3" style="height: 20px;">
                <div class="progress-bar {% if progress.completion_percentage >= 80 %}bg-success{% elif progress.completion_percentage >= 50 %}bg-warning{% else %}bg-danger{% endif %}"
                     role="progressbar"
                     style="width: {{ progress.completion_percentage }}%"
                     aria-valuenow="{{ progress.completion_percentage }}"
                     aria-valuemin="0"
                     aria-valuemax="100">
                    {{ progress.completion_percentage }}%
                </div>
            </div>

            <!-- Progress Status -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <small class="text-muted">
                        <strong>{{ progress.completed_weight }}/{{ progress.total_weight }}</strong> points completed
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    {% if progress.is_ready_for_approval %}
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-1"></i>
                            Ready for Approval
                        </span>
                    {% elif progress.completion_percentage >= 50 %}
                        <span class="badge bg-warning">
                            <i class="fas fa-clock me-1"></i>
                            In Progress
                        </span>
                    {% else %}
                        <span class="badge bg-secondary">
                            <i class="fas fa-hourglass-start me-1"></i>
                            Getting Started
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- Progress Items -->
            <div class="progress-items">
                {% for item_key, item_data in progress.items.items %}
                <div class="progress-item d-flex align-items-center mb-2">
                    <div class="me-3">
                        {% if item_data.completed %}
                            <i class="fas fa-check-circle text-success"></i>
                        {% else %}
                            <i class="far fa-circle text-muted"></i>
                        {% endif %}
                    </div>
                    <div class="flex-grow-1">
                        <span class="{% if item_data.completed %}text-success{% else %}text-muted{% endif %}">
                            {{ item_data.name }}
                        </span>
                        <small class="text-muted ms-2">({{ item_data.weight }} pts)</small>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Action Buttons -->
            <div class="mt-3 text-center">
                {% if progress.completion_percentage < 100 %}
                    <a href="{% url 'venues_app:venue_progress' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-tasks me-1"></i>
                        View Detailed Progress
                    </a>
                {% endif %}

                {% if venue.approval_status == 'pending' and progress.is_ready_for_approval %}
                    <a href="{% url 'venues_app:trigger_auto_approval_check' venue.id %}"
                       class="btn btn-success btn-sm ms-2">
                        <i class="fas fa-rocket me-1"></i>
                        Check Auto-Approval
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Include CSS for custom styling -->
<style>
.venue-progress-indicator .progress-item {
    font-size: 0.9rem;
}

.venue-progress-indicator .progress-bar {
    transition: width 0.3s ease;
}

.venue-progress-indicator .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.venue-progress-indicator .badge {
    font-size: 0.75rem;
}

@media (max-width: 768px) {
    .venue-progress-indicator .progress-items {
        font-size: 0.85rem;
    }

    .venue-progress-indicator .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}
</style>
