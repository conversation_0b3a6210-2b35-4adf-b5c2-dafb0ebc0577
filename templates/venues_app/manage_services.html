{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Manage Services - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap');

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: #2F160F;
        font-size: 2.5rem;
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(250, 225, 215, 0.2);
        border-radius: 0.75rem;
        flex-shrink: 0;
    }

    /* Form section description text */
    .form-section p,
    .form-section .text-muted,
    .form-section small {
        color: #525252;
        font-family: 'Inter', sans-serif;
        font-size: 1.125rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .form-section small {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    /* Quick Stats */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border: 1px solid #fae1d7;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 600;
        color: #2F160F;
        margin-bottom: 0.5rem;
        font-family: 'Poppins', sans-serif;
        line-height: 1;
    }

    .stat-label {
        color: #525252;
        font-weight: 500;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Services List - Line by Line Layout */
    .services-list {
        background: white;
        border: 1px solid #fae1d7;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .service-item {
        display: flex;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid #fae1d7;
        transition: background-color 0.3s ease;
        gap: 1rem;
    }

    .service-item:last-child {
        border-bottom: none;
    }

    .service-item:hover {
        background: rgba(250, 225, 215, 0.1);
    }

    .service-image {
        width: 60px;
        height: 60px;
        border-radius: 0.5rem;
        object-fit: cover;
        border: 1px solid #fae1d7;
        flex-shrink: 0;
    }

    .service-placeholder {
        width: 60px;
        height: 60px;
        border-radius: 0.5rem;
        background: linear-gradient(135deg, #fef7f0 0%, #fae1d7 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        border: 1px solid #fae1d7;
    }

    .service-placeholder i {
        font-size: 1.5rem;
        color: #2F160F;
        opacity: 0.6;
    }

    .service-info {
        flex: 1;
        min-width: 0;
    }

    .service-title-row {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .service-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        margin: 0;
    }

    .service-duration {
        color: #525252;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        background: rgba(250, 225, 215, 0.3);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        white-space: nowrap;
    }

    .service-status {
        margin-left: auto;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        font-family: 'Inter', sans-serif;
    }

    .status-badge.active {
        background: #10b981;
        color: white;
    }

    .status-badge.inactive {
        background: #6b7280;
        color: white;
    }

    .service-description {
        color: #525252;
        font-size: 0.875rem;
        margin-bottom: 0.75rem;
        line-height: 1.4;
        font-family: 'Inter', sans-serif;
    }

    .service-price {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        text-align: right;
        min-width: 80px;
        flex-shrink: 0;
    }

    .service-actions {
        display: flex;
        gap: 0.5rem;
        margin-left: 1rem;
        flex-shrink: 0;
    }

    .service-actions .btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        border: 1px solid;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-outline-primary {
        background: white;
        border-color: #2F160F;
        color: #2F160F;
    }

    .btn-outline-primary:hover {
        background: #2F160F;
        border-color: #2F160F;
        color: white;
    }

    .btn-outline-danger {
        background: white;
        border-color: #dc2626;
        color: #dc2626;
    }

    .btn-outline-danger:hover {
        background: #dc2626;
        border-color: #dc2626;
        color: white;
    }

    /* Primary action button */
    .btn-cw-primary {
        background: white;
        border: 2px solid #2F160F;
        color: #2F160F;
        padding: 0.75rem 3rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        background: #2F160F;
        border-color: #2F160F;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
    }

    /* No Services State */
    .no-services {
        text-align: center;
        padding: 3rem 2rem;
        color: #525252;
        font-family: 'Inter', sans-serif;
    }

    .no-services i {
        font-size: 4rem;
        color: #9ca3af;
        margin-bottom: 1.5rem;
    }

    .no-services h3 {
        color: #2F160F;
        margin-bottom: 1rem;
        font-family: 'Poppins', sans-serif;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .service-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .service-title-row {
            width: 100%;
        }

        .service-status {
            margin-left: 0;
        }

        .service-actions {
            margin-left: 0;
            width: 100%;
        }

        .service-price {
            text-align: left;
            min-width: auto;
        }
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

    <!-- Overview Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-spa me-2"></i>Service Overview
        </h4>

        <p class="text-muted">
            Manage all services for {{ venue.venue_name }}. Add new services, edit existing ones, or remove services that are no longer offered.
        </p>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ services_active_count }}</div>
                <div class="stat-label">Active Services</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ services_featured_count }}</div>
                <div class="stat-label">Featured Services</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ services_categories_count }}</div>
                <div class="stat-label">Categories</div>
            </div>
        </div>

        <div class="d-flex justify-content-center">
            <a href="{% url 'venues_app:service_create' %}" class="btn-cw-primary">
                <i class="fas fa-plus"></i>Add New Service
            </a>
        </div>
    </div>

    <!-- Services Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-list me-2"></i>Your Services
        </h4>

        <p class="text-muted">
            These are all the services currently available at your venue. Each service can be edited or removed as needed.
        </p>

        {% if services %}
            <div class="services-list">
                {% for service in services %}
                <div class="service-item">
                    <!-- Service Image or Placeholder -->
                    {% if service.service_image %}
                        <img src="{{ service.service_image.url }}" alt="{{ service.service_title }}" class="service-image">
                    {% else %}
                        <div class="service-placeholder">
                            <i class="fas fa-spa"></i>
                        </div>
                    {% endif %}

                    <!-- Service Information -->
                    <div class="service-info">
                        <div class="service-title-row">
                            <h5 class="service-title">{{ service.service_title }}</h5>
                            <span class="service-duration">{{ service.duration_minutes }} min</span>
                            <div class="service-status">
                                {% if service.is_active %}
                                    <span class="status-badge active">Active</span>
                                {% else %}
                                    <span class="status-badge inactive">Inactive</span>
                                {% endif %}
                            </div>
                        </div>
                        <p class="service-description">{{ service.short_description|truncatechars:120 }}</p>
                    </div>

                    <!-- Service Price -->
                    <div class="service-price">
                        ${{ service.price_min }}
                        {% if service.price_max and service.price_max != service.price_min %}
                            - ${{ service.price_max }}
                        {% endif %}
                    </div>

                    <!-- Service Actions -->
                    <div class="service-actions">
                        <a href="{% url 'venues_app:service_edit' service.id %}" class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{% url 'venues_app:service_delete' service.id %}" class="btn btn-outline-danger">
                            <i class="fas fa-trash"></i> Delete
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-services">
                <i class="fas fa-spa"></i>
                <h3>No Services Yet</h3>
                <p>Start by adding your first service to attract customers and showcase what you offer.</p>
                <a href="{% url 'venues_app:service_create' %}" class="btn-cw-primary">
                    <i class="fas fa-plus"></i>Add Your First Service
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Service Guidelines Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-lightbulb me-2"></i>Service Guidelines
        </h4>

        <p class="text-muted">
            Follow these best practices to create compelling service listings that attract customers.
        </p>

        <div class="row">
            <div class="col-md-6">
                <h6 class="mb-3" style="color: #2F160F; font-family: 'Poppins', sans-serif; font-weight: 500;">Service Information</h6>
                <ul class="list-unstyled mb-4" style="color: #525252; font-family: 'Inter', sans-serif;">
                    <li class="mb-2">
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Provide a clear and descriptive service name
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Set accurate pricing and duration
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Write a detailed description of what the service includes
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Add service images to make your listings more attractive
                    </li>
                    <li>
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Keep services active and up-to-date
                    </li>
                </ul>
            </div>

            <div class="col-md-6">
                <h6 class="mb-3" style="color: #2F160F; font-family: 'Poppins', sans-serif; font-weight: 500;">Pricing Tips</h6>
                <ul class="list-unstyled mb-4" style="color: #525252; font-family: 'Inter', sans-serif;">
                    <li class="mb-2">
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Research competitor pricing in your area
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Consider offering discounts for new customers
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Ensure pricing reflects service quality and duration
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Add service images to justify premium pricing
                    </li>
                    <li>
                        <i class="fas fa-check-circle me-2" style="color: #10b981;"></i>Update prices seasonally if applicable
                    </li>
                </ul>
            </div>
        </div>

        <div class="alert" style="background: rgba(250, 225, 215, 0.3); border: 1px solid #fae1d7; border-radius: 0.5rem; padding: 1rem; color: #525252; font-family: 'Inter', sans-serif;">
            <i class="fas fa-info-circle me-2" style="color: #2F160F;"></i>
            Services marked as "Active" will be visible to customers on your venue page. Inactive services are hidden but can be reactivated at any time. Consider adding service images to make your listings more attractive to potential customers.
        </div>
    </div>

</div>
{% endblock %}
