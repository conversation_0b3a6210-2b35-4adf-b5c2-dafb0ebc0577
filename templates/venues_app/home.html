{% extends 'venues_app/base_venues.html' %}

{% block title %}CozyWish - Book Beauty & Wellness Services{% endblock %}

{% block content %}
<div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="hero-title">Find & Book Beauty & Wellness Services</h1>
                    <p class="hero-subtitle">Discover top-rated spas, salons, and wellness centers near you</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Top Picks Section -->
    <section class="venues-section">
        <div class="container">
            <h2 class="section-title">Top Picks</h2>
            <div class="row">
                {% for venue in top_venues %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card venue-card">
                        {% if venue.primary_image %}
                        <img src="{{ venue.primary_image.image.url }}" class="card-img-top" alt="{{ venue.venue_name }}">
                        {% else %}
                        <img src="https://via.placeholder.com/300x200" class="card-img-top" alt="{{ venue.venue_name }}">
                        {% endif %}
                        <div class="card-body">
                            <h5 class="card-title">{{ venue.venue_name }}</h5>
                            <p class="card-text">{{ venue.description|truncatewords:20 }}</p>
                            <a href="{% url 'venues_app:venue_detail' venue.slug %}" class="btn btn-primary">View Details</a>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <p class="text-center">No venues available at the moment.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Trending Section -->
    <section class="venues-section bg-light">
        <div class="container">
            <h2 class="section-title">Trending</h2>
            <div class="row">
                {% for category in popular_categories %}
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card category-card">
                        <div class="card-body text-center">
                            <i class="fas fa-spa fa-3x mb-3 text-primary"></i>
                            <h5 class="card-title">{{ category.name }}</h5>
                            <p class="card-text">{{ category.description|truncatewords:15 }}</p>
                            <a href="{% url 'venues_app:category_venues' category.slug %}" class="btn btn-outline-primary">Explore</a>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <p class="text-center">No categories available.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Hot Deals Section -->
    <section class="venues-section">
        <div class="container">
            <h2 class="section-title">Hot Deals</h2>
            <div class="row">
                {% for service in featured_services %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card service-card">
                        <div class="card-body">
                            <h5 class="card-title">{{ service.name }}</h5>
                            <p class="card-text">{{ service.description|truncatewords:15 }}</p>
                            <div class="price-section">
                                <span class="original-price">${{ service.price }}</span>
                                {% if service.discounted_price %}
                                <span class="discounted-price">${{ service.discounted_price }}</span>
                                {% endif %}
                            </div>
                            <a href="{% url 'venues_app:service_detail' service.id %}" class="btn btn-success">Book Now</a>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <p class="text-center">No featured services available.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Business Section (for non-customers) -->
    {% if not user.is_authenticated or user.role != 'customer' %}
    <section class="business-section bg-primary text-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2>For Business</h2>
                    <p class="lead">Supercharge your business with CozyWish</p>
                    <p>Join our marketplace and reach thousands of potential customers looking for wellness services.</p>
                    <a href="{% url 'accounts_app:for_business' %}" class="btn btn-light btn-lg">Find out more</a>
                </div>
            </div>
        </div>
    </section>
    {% endif %}
</div>

<style>
.hero-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 4rem 0;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
}

.venues-section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    font-weight: 600;
}

.venue-card, .category-card, .service-card {
    height: 100%;
    transition: transform 0.3s ease;
}

.venue-card:hover, .category-card:hover, .service-card:hover {
    transform: translateY(-5px);
}

.business-section {
    padding: 4rem 0;
}

.price-section {
    margin: 1rem 0;
}

.original-price {
    text-decoration: line-through;
    color: #6c757d;
    margin-right: 0.5rem;
}

.discounted-price {
    font-weight: bold;
    color: #28a745;
    font-size: 1.25rem;
}
</style>
{% endblock %}
