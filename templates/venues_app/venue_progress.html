{% extends 'base.html' %}
{% load static %}

{% block title %}Venue Setup Progress - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<style>
    :root {
        --cw-primary: #2F160F;
        --cw-accent: #fae1d7;
        --cw-success: #10b981;
        --cw-warning: #f59e0b;
        --cw-error: #ef4444;
        --cw-info: #3b82f6;
    }

    .dashboard-container {
        background: linear-gradient(135deg, #fef7f0 0%, #fae1d7 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .progress-dashboard {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .dashboard-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .dashboard-title {
        color: var(--cw-primary);
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .dashboard-subtitle {
        color: #6b7280;
        font-size: 1.25rem;
        margin-bottom: 2rem;
    }

    /* Enhanced Progress Circle */
    .progress-circle-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 3rem;
    }

    .progress-circle {
        position: relative;
        width: 200px;
        height: 200px;
    }

    .progress-circle svg {
        transform: rotate(-90deg);
        width: 100%;
        height: 100%;
    }

    .progress-circle-bg {
        fill: none;
        stroke: #e5e7eb;
        stroke-width: 8;
    }

    .progress-circle-fill {
        fill: none;
        stroke: var(--cw-success);
        stroke-width: 8;
        stroke-linecap: round;
        stroke-dasharray: 0 628;
        transition: stroke-dasharray 1s ease-in-out;
    }

    .progress-percentage {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-primary);
    }

    /* Status Cards Grid */
    .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .status-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 2px solid transparent;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .status-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--cw-primary);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .status-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .status-card:hover::before {
        opacity: 1;
    }

    .status-card.completed::before {
        background: var(--cw-success);
        opacity: 1;
    }

    .status-card.incomplete::before {
        background: var(--cw-warning);
        opacity: 1;
    }

    .status-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .status-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-right: 1rem;
    }

    .status-icon.completed {
        background: var(--cw-success);
        animation: checkmarkBounce 0.6s ease-in-out;
    }

    .status-icon.incomplete {
        background: var(--cw-warning);
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .status-badge.completed {
        background: rgba(16, 185, 129, 0.1);
        color: var(--cw-success);
    }

    .status-badge.incomplete {
        background: rgba(245, 158, 11, 0.1);
        color: var(--cw-warning);
    }

    .status-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-primary);
        margin-bottom: 0.5rem;
    }

    .status-description {
        color: #6b7280;
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }

    .status-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .btn-action {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: var(--cw-primary);
        color: white;
    }

    .btn-primary:hover {
        background: #1f0e0a;
        transform: translateY(-1px);
    }

    .btn-outline {
        background: transparent;
        color: var(--cw-primary);
        border: 2px solid var(--cw-primary);
    }

    .btn-outline:hover {
        background: var(--cw-primary);
        color: white;
    }

    /* Preview Section */
    .preview-section {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 3rem;
    }

    .preview-header {
        display: flex;
        align-items: center;
        justify-content: between;
        margin-bottom: 2rem;
    }

    .preview-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-primary);
        margin-right: auto;
    }

    .preview-toggle {
        background: var(--cw-primary);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .preview-toggle:hover {
        background: #1f0e0a;
    }

    .venue-preview {
        border: 2px dashed #e5e7eb;
        border-radius: 0.75rem;
        padding: 2rem;
        text-align: center;
        color: #6b7280;
        display: none;
    }

    .venue-preview.active {
        display: block;
        animation: fadeIn 0.3s ease-in-out;
    }

    /* Help System */
    .contextual-help {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 1000;
    }

    .help-button {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--cw-primary);
        color: white;
        border: none;
        cursor: pointer;
        font-size: 1.5rem;
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        transition: all 0.3s ease;
    }

    .help-button:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(47, 22, 15, 0.4);
    }

    .help-panel {
        position: absolute;
        bottom: 80px;
        right: 0;
        width: 300px;
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        display: none;
    }

    .help-panel.active {
        display: block;
        animation: slideUp 0.3s ease-out;
    }

    /* Animations */
    @keyframes checkmarkBounce {
        0% { transform: scale(0); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes slideUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Success Animations */
    .success-animation {
        animation: celebration 1s ease-in-out;
    }

    @keyframes celebration {
        0%, 100% { transform: scale(1); }
        25% { transform: scale(1.05) rotate(-5deg); }
        75% { transform: scale(1.05) rotate(5deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-title {
            font-size: 2rem;
        }

        .progress-circle {
            width: 150px;
            height: 150px;
        }

        .progress-percentage {
            font-size: 2rem;
        }

        .status-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .contextual-help {
            bottom: 1rem;
            right: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="progress-dashboard">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">Venue Setup Progress</h1>
            <p class="dashboard-subtitle">{{ venue.venue_name }}</p>

            <!-- Enhanced Progress Circle -->
            <div class="progress-circle-container">
                <div class="progress-circle">
                    <svg viewBox="0 0 200 200">
                        <circle class="progress-circle-bg" cx="100" cy="100" r="90"></circle>
                        <circle class="progress-circle-fill" cx="100" cy="100" r="90"
                                data-percentage="{{ progress.completion_percentage }}"></circle>
                    </svg>
                    <div class="progress-percentage">{{ progress.completion_percentage }}%</div>
                </div>
            </div>
        </div>

        <!-- Status Cards Grid -->
        <div class="status-grid">
            <!-- Basic Information -->
            <div class="status-card {% if progress.items.basic_info.completed %}completed{% else %}incomplete{% endif %}">
                <div class="status-header">
                    <div class="status-icon {% if progress.items.basic_info.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.basic_info.completed %}
                            <i class="fas fa-check"></i>
                        {% else %}
                            <i class="fas fa-info-circle"></i>
                        {% endif %}
                    </div>
                    <span class="status-badge {% if progress.items.basic_info.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.basic_info.completed %}Complete{% else %}Pending{% endif %}
                    </span>
                </div>
                <h3 class="status-title">Basic Information</h3>
                <p class="status-description">
                    Essential venue details including name, description, and location information.
                </p>
                <div class="status-actions">
                    <a href="{% url 'venues_app:venue_edit' %}" class="btn-action btn-primary">
                        <i class="fas fa-edit"></i>
                        {% if progress.items.basic_info.completed %}Update Details{% else %}Complete Setup{% endif %}
                    </a>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="status-card {% if progress.items.contact.completed %}completed{% else %}incomplete{% endif %}">
                <div class="status-header">
                    <div class="status-icon {% if progress.items.contact.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.contact.completed %}
                            <i class="fas fa-check"></i>
                        {% else %}
                            <i class="fas fa-phone"></i>
                        {% endif %}
                    </div>
                    <span class="status-badge {% if progress.items.contact.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.contact.completed %}Complete{% else %}Optional{% endif %}
                    </span>
                </div>
                <h3 class="status-title">Contact Information</h3>
                <p class="status-description">
                    Phone and email so customers can easily reach you.
                </p>
                <div class="status-actions">
                    <a href="{% url 'venues_app:venue_edit' %}" class="btn-action btn-primary">
                        <i class="fas fa-phone"></i>
                        Add Contact Info
                    </a>
                </div>
            </div>

            <!-- Main Image -->
            <div class="status-card {% if progress.items.main_image.completed %}completed{% else %}incomplete{% endif %}">
                <div class="status-header">
                    <div class="status-icon {% if progress.items.main_image.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.main_image.completed %}
                            <i class="fas fa-check"></i>
                        {% else %}
                            <i class="fas fa-image"></i>
                        {% endif %}
                    </div>
                    <span class="status-badge {% if progress.items.main_image.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.main_image.completed %}Complete{% else %}Required{% endif %}
                    </span>
                </div>
                <h3 class="status-title">Main Image</h3>
                <p class="status-description">
                    An attractive main image that showcases your venue's best features.
                </p>
                <div class="status-actions">
                    <a href="{% url 'venues_app:manage_venue_images' %}" class="btn-action btn-primary">
                        <i class="fas fa-upload"></i>
                        {% if progress.items.main_image.completed %}Update Image{% else %}Upload Image{% endif %}
                    </a>
                </div>
            </div>

            <!-- Gallery Images -->
            <div class="status-card {% if progress.items.gallery_images.completed %}completed{% else %}incomplete{% endif %}">
                <div class="status-header">
                    <div class="status-icon {% if progress.items.gallery_images.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.gallery_images.completed %}
                            <i class="fas fa-check"></i>
                        {% else %}
                            <i class="fas fa-images"></i>
                        {% endif %}
                    </div>
                    <span class="status-badge {% if progress.items.gallery_images.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.gallery_images.completed %}Complete{% else %}Recommended{% endif %}
                    </span>
                </div>
                <h3 class="status-title">Gallery Images</h3>
                <p class="status-description">
                    Additional images showing different aspects and areas of your venue.
                </p>
                <div class="status-actions">
                    <a href="{% url 'venues_app:manage_venue_images' %}" class="btn-action btn-primary">
                        <i class="fas fa-plus"></i>
                        Add More Images
                    </a>
                </div>
            </div>

            <!-- Services -->
            <div class="status-card {% if progress.items.services.completed %}completed{% else %}incomplete{% endif %}">
                <div class="status-header">
                    <div class="status-icon {% if progress.items.services.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.services.completed %}
                            <i class="fas fa-check"></i>
                        {% else %}
                            <i class="fas fa-concierge-bell"></i>
                        {% endif %}
                    </div>
                    <span class="status-badge {% if progress.items.services.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.services.completed %}Complete{% else %}Required{% endif %}
                    </span>
                </div>
                <h3 class="status-title">Services</h3>
                <p class="status-description">
                    List the services you offer with pricing and detailed descriptions.
                </p>
                <div class="status-actions">
                    <a href="{% url 'venues_app:manage_services' %}" class="btn-action btn-primary">
                        <i class="fas fa-plus"></i>
                        {% if progress.items.services.completed %}Manage Services{% else %}Add Services{% endif %}
                    </a>
                </div>
            </div>

            <!-- Operating Hours -->
            <div class="status-card {% if progress.items.operating_hours.completed %}completed{% else %}incomplete{% endif %}">
                <div class="status-header">
                    <div class="status-icon {% if progress.items.operating_hours.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.operating_hours.completed %}
                            <i class="fas fa-check"></i>
                        {% else %}
                            <i class="fas fa-clock"></i>
                        {% endif %}
                    </div>
                    <span class="status-badge {% if progress.items.operating_hours.completed %}completed{% else %}incomplete{% endif %}">
                        {% if progress.items.operating_hours.completed %}Complete{% else %}Required{% endif %}
                    </span>
                </div>
                <h3 class="status-title">Operating Hours</h3>
                <p class="status-description">
                    Let customers know when you're open for business.
                </p>
                <div class="status-actions">
                    <a href="{% url 'venues_app:manage_operating_hours' %}" class="btn-action btn-primary">
                        <i class="fas fa-calendar-alt"></i>
                        Set Hours
                    </a>
                </div>
            </div>
        </div>

        <!-- Preview Section -->
        <div class="preview-section">
            <div class="preview-header">
                <h2 class="preview-title">Venue Preview</h2>
                <button class="preview-toggle" onclick="togglePreview()">
                    <i class="fas fa-eye"></i>
                    Toggle Preview
                </button>
            </div>
            <div class="venue-preview" id="venuePreview">
                <i class="fas fa-eye fa-3x" style="color: #e5e7eb; margin-bottom: 1rem;"></i>
                <p>This is how your venue will appear to customers once it's approved.</p>
                <p>Complete all required sections to see a full preview.</p>
                {% if progress.completion_percentage >= 80 %}
                    <a href="{% url 'venues_app:venue_detail' venue.slug %}" target="_blank" class="btn-action btn-outline">
                        <i class="fas fa-external-link-alt"></i>
                        View Full Preview
                    </a>
                {% endif %}
            </div>
        </div>

        <!-- Auto-Approval Status -->
        {% if venue.approval_status == 'pending' %}
        <div class="preview-section">
            <div class="preview-header">
                <h2 class="preview-title">Auto-Approval Status</h2>
                {% if meets_auto_approval %}
                    <span class="status-badge completed">
                        <i class="fas fa-rocket"></i>
                        Eligible
                    </span>
                {% else %}
                    <span class="status-badge incomplete">
                        <i class="fas fa-hourglass-half"></i>
                        Not Ready
                    </span>
                {% endif %}
            </div>
            {% if meets_auto_approval %}
                <p class="status-description">Great news! Your venue meets all criteria for automatic approval.</p>
                <a href="{% url 'venues_app:trigger_auto_approval_check' venue.id %}" class="btn-action btn-primary">
                    <i class="fas fa-rocket"></i>
                    Apply for Auto-Approval
                </a>
            {% else %}
                <p class="status-description">Your venue doesn't yet meet auto-approval criteria:</p>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
                    {% for reason in auto_approval_reasons %}
                    <li>{{ reason }}</li>
                    {% endfor %}
                </ul>
                <p><small>Complete the missing items above to qualify for automatic approval!</small></p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Contextual Help System -->
<div class="contextual-help">
    <button class="help-button" onclick="toggleHelp()">
        <i class="fas fa-question"></i>
    </button>
    <div class="help-panel" id="helpPanel">
        <h4 style="color: var(--cw-primary); margin-bottom: 1rem;">Need Help?</h4>
        <p style="margin-bottom: 1rem; font-size: 0.9rem; line-height: 1.5;">
            Focus on completing the required sections first. The more complete your venue profile,
            the better it will appear to customers.
        </p>
        <div style="display: flex; gap: 0.5rem;">
            <a href="#" class="btn-action btn-outline" style="font-size: 0.8rem; padding: 0.5rem 1rem;">
                <i class="fas fa-phone"></i>
                Contact Support
            </a>
        </div>
    </div>
</div>

<script>
// Progress Circle Animation
document.addEventListener('DOMContentLoaded', function() {
    const progressCircle = document.querySelector('.progress-circle-fill');
    const percentage = progressCircle.getAttribute('data-percentage');
    const circumference = 2 * Math.PI * 90; // radius = 90
    const offset = circumference - (percentage / 100) * circumference;

    setTimeout(() => {
        progressCircle.style.strokeDasharray = `${circumference - offset} ${circumference}`;
    }, 500);

    // Add success animation if completion is high
    if (percentage >= 90) {
        document.querySelector('.progress-circle-container').classList.add('success-animation');
    }
});

// Preview Toggle
function togglePreview() {
    const preview = document.getElementById('venuePreview');
    preview.classList.toggle('active');
}

// Help Panel Toggle
function toggleHelp() {
    const helpPanel = document.getElementById('helpPanel');
    helpPanel.classList.toggle('active');
}

// Auto-save progress (if editing inline)
function saveProgress() {
    // Placeholder for future auto-save functionality
    console.log('Progress saved');
}

// Retry mechanism for failed operations
function retryOperation(operation, maxRetries = 3) {
    let retries = 0;

    function attempt() {
        return operation().catch(error => {
            if (retries < maxRetries) {
                retries++;
                console.log(`Retrying operation (attempt ${retries}/${maxRetries})`);
                return new Promise(resolve => setTimeout(resolve, 1000 * retries))
                    .then(attempt);
            }
            throw error;
        });
    }

    return attempt();
}
</script>
{% endblock %}
