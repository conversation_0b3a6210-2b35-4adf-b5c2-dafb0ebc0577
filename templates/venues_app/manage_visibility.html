{% extends 'base.html' %}
{% load i18n %}

{% block title %}Manage Visibility Settings - {{ venue.venue_name }}{% endblock %}

{% block extra_css %}
<style>
    /* CozyWish Visibility Management Styles */
    .visibility-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    .visibility-header {
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .visibility-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .visibility-subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .visibility-settings-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .settings-panel {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: 1px solid #fae1d7;
    }

    .panel-title {
        color: #2F160F;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .visibility-option {
        border: 1px solid #e5e7eb;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
        background: white;
    }

    .visibility-option:hover {
        border-color: #2F160F;
        box-shadow: 0 2px 8px rgba(47, 22, 15, 0.1);
    }

    .visibility-option.active {
        border-color: #2F160F;
        background: #fef7f0;
    }

    .option-header {
        display: flex;
        align-items: center;
        justify-content: between;
        margin-bottom: 0.5rem;
    }

    .option-toggle {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .option-label {
        font-weight: 600;
        color: #2F160F;
        font-size: 1rem;
    }

    .option-description {
        color: #6b7280;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .visibility-status {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-visible {
        background: #d1fae5;
        color: #065f46;
    }

    .status-hidden {
        background: #fee2e2;
        color: #991b1b;
    }

    .completeness-panel {
        background: linear-gradient(135deg, #fef7f0 0%, #fae1d7 100%);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .completeness-score {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
    }

    .score-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 700;
        color: white;
        background: #2F160F;
    }

    .score-details h3 {
        color: #2F160F;
        margin-bottom: 0.5rem;
    }

    .missing-info-list {
        list-style: none;
        padding: 0;
    }

    .missing-info-item {
        background: white;
        border-left: 4px solid #f59e0b;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-radius: 0 0.5rem 0.5rem 0;
    }

    .missing-info-item.priority-high {
        border-left-color: #dc2626;
    }

    .freshness-indicators {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .freshness-item {
        background: white;
        border-radius: 0.5rem;
        padding: 1rem;
        border: 1px solid #e5e7eb;
        text-align: center;
    }

    .freshness-recent {
        border-color: #22c55e;
        background: #f0fdf4;
    }

    .freshness-stale {
        border-color: #ef4444;
        background: #fef2f2;
    }

    .preview-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn-preview {
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
    }

    .btn-preview:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-save {
        background: #22c55e;
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .btn-save:hover {
        background: #16a34a;
        transform: translateY(-1px);
    }

    @media (max-width: 768px) {
        .visibility-settings-grid {
            grid-template-columns: 1fr;
        }

        .freshness-indicators {
            grid-template-columns: 1fr;
        }

        .preview-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="visibility-container">
    <!-- Page Header -->
    <div class="visibility-header">
        <h1 class="visibility-title">Visibility Settings</h1>
        <p class="visibility-subtitle">Control what information customers can see on your venue profile</p>
    </div>

    <!-- Completeness Panel -->
    <div class="completeness-panel">
        <div class="completeness-score">
            <div>
                <h3>Profile Completeness</h3>
                <p class="text-muted">Complete your profile to improve customer trust and booking rates</p>
            </div>
            <div class="score-circle">{{ completeness_score }}%</div>
        </div>

        {% if missing_info %}
        <div class="missing-info">
            <h4>Improve Your Profile</h4>
            <ul class="missing-info-list">
                {% for item in missing_info %}
                <li class="missing-info-item priority-{{ item.priority }}">
                    <strong>{{ item.title }}</strong><br>
                    <small>{{ item.message }}</small>
                </li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <!-- Information Freshness -->
        <div class="freshness-indicators">
            {% for section, data in freshness_info.items %}
            <div class="freshness-item {% if data.is_recent %}freshness-recent{% elif data.is_stale %}freshness-stale{% endif %}">
                <strong>{{ section|title }}</strong><br>
                {% if data.days_ago %}
                    <small>Updated {{ data.days_ago }} day{{ data.days_ago|pluralize }} ago</small>
                {% else %}
                    <small>Never updated</small>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>

    <form method="post" id="visibilityForm">
        {% csrf_token %}

        <div class="visibility-settings-grid">
            <!-- Visibility Controls -->
            <div class="settings-panel">
                <h2 class="panel-title">
                    <i class="fas fa-eye"></i>
                    Information Sections
                </h2>

                {% for field in form %}
                <div class="visibility-option {% if field.value %}active{% endif %}" data-section="{{ field.html_name }}">
                    <div class="option-header">
                        <div class="option-toggle">
                            {{ field }}
                            <label for="{{ field.id_for_label }}" class="option-label">{{ field.label }}</label>
                        </div>
                        <span class="visibility-status {% if field.value %}status-visible{% else %}status-hidden{% endif %}">
                            {% if field.value %}
                                <i class="fas fa-eye"></i> Visible
                            {% else %}
                                <i class="fas fa-eye-slash"></i> Hidden
                            {% endif %}
                        </span>
                    </div>
                    <div class="option-description">{{ field.help_text }}</div>
                </div>
                {% endfor %}
            </div>

            <!-- Live Preview Summary -->
            <div class="settings-panel">
                <h2 class="panel-title">
                    <i class="fas fa-preview"></i>
                    Preview Summary
                </h2>

                <div id="previewSummary">
                    <div class="mb-3">
                        <strong>Visible Sections:</strong>
                        <ul id="visibleSections" class="mt-2">
                            {% for field in form %}
                                {% if field.value %}
                                <li data-section="{{ field.html_name }}">{{ field.label }}</li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>

                    <div class="mb-3">
                        <strong>Hidden Sections:</strong>
                        <ul id="hiddenSections" class="mt-2 text-muted">
                            {% for field in form %}
                                {% if not field.value %}
                                <li data-section="{{ field.html_name }}">{{ field.label }}</li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>Tips:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Show contact info to make it easy for customers to reach you</li>
                        <li>Display operating hours to set clear expectations</li>
                        <li>Highlight amenities to differentiate your venue</li>
                        <li>Use FAQs to address common customer questions</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="preview-actions">
            <button type="submit" class="btn-save">
                <i class="fas fa-save"></i>
                Save Visibility Settings
            </button>
            <a href="{% url 'venues_app:venue_preview' %}" class="btn-preview" target="_blank">
                <i class="fas fa-external-link-alt"></i>
                Preview Your Venue
            </a>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('visibilityForm');
    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
    const visibleList = document.getElementById('visibleSections');
    const hiddenList = document.getElementById('hiddenSections');

    // Update preview when checkboxes change
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updatePreview();
            updateOptionAppearance(this);
        });
    });

    function updatePreview() {
        const visible = [];
        const hidden = [];

        checkboxes.forEach(checkbox => {
            const label = checkbox.closest('.visibility-option').querySelector('.option-label').textContent;
            const section = checkbox.name;

            if (checkbox.checked) {
                visible.push({section, label});
            } else {
                hidden.push({section, label});
            }
        });

        // Update visible sections list
        visibleList.innerHTML = visible.map(item =>
            `<li data-section="${item.section}">${item.label}</li>`
        ).join('');

        // Update hidden sections list
        hiddenList.innerHTML = hidden.map(item =>
            `<li data-section="${item.section}">${item.label}</li>`
        ).join('');
    }

    function updateOptionAppearance(checkbox) {
        const option = checkbox.closest('.visibility-option');
        const status = option.querySelector('.visibility-status');

        if (checkbox.checked) {
            option.classList.add('active');
            status.classList.remove('status-hidden');
            status.classList.add('status-visible');
            status.innerHTML = '<i class="fas fa-eye"></i> Visible';
        } else {
            option.classList.remove('active');
            status.classList.remove('status-visible');
            status.classList.add('status-hidden');
            status.innerHTML = '<i class="fas fa-eye-slash"></i> Hidden';
        }
    }
});
</script>
{% endblock %}
