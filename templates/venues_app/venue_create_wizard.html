{% extends 'base.html' %}

{% block title %}Create Your Venue - Step {{ step_number }} of {{ total_steps }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Load external CSS files -->
<link rel="stylesheet" href="{% static 'css/venues_app/venue_enhanced.css' %}">



<!-- Enhanced Venue Creation Wizard Styles -->
<style>
    :root {
        --cw-primary: #2F160F;
        --cw-accent: #fae1d7;
        --cw-success: #10b981;
        --cw-warning: #f59e0b;
        --cw-error: #ef4444;
        --cw-info: #3b82f6;
        --cw-light-gray: #f8fafc;
        --cw-border: #e5e7eb;
    }

    /* Enhanced Progress Bar */
    .wizard-progress {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .progress-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-primary);
    }

    .progress-percentage {
        background: var(--cw-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        position: relative;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        height: 3px;
        background: #e5e7eb;
        z-index: 1;
        border-radius: 2px;
    }

    .progress-line {
        position: absolute;
        top: 20px;
        left: 20px;
        height: 3px;
        background: linear-gradient(90deg, var(--cw-success) 0%, var(--cw-primary) 100%);
        z-index: 2;
        border-radius: 2px;
        transition: width 0.6s ease;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 3;
        flex: 1;
        text-align: center;
    }

    .step-circle {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        background: #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #6b7280;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
        border: 3px solid #e5e7eb;
        position: relative;
    }

    .step-circle.completed {
        background: var(--cw-success);
        color: white;
        border-color: var(--cw-success);
    }

    .step-circle.active {
        background: var(--cw-brand-primary) !important;
        background-color: #2F160F !important;
        color: white !important;
        border-color: var(--cw-brand-primary) !important;
        border-color: #2F160F !important;
        box-shadow: 0 0 0 4px rgba(47, 22, 15, 0.1);
        font-weight: 700 !important;
    }

    .step-circle.active,
    .step-circle.active * {
        color: white !important;
    }

    .step-circle.in-progress {
        background: var(--cw-warning);
        color: white;
        border-color: var(--cw-warning);
    }

    .step-circle.blocked {
        background: #fca5a5;
        color: white;
        border-color: #ef4444;
    }

    /* Step completion indicator */
    .step-completion-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        font-size: 0.625rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .step-completion-badge.complete {
        background: var(--cw-success);
        color: white;
    }

    .step-completion-badge.partial {
        background: var(--cw-warning);
        color: white;
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
        transition: all 0.3s ease;
    }

    .step-title.active {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .step-title.completed {
        color: var(--cw-success);
    }

    /* Step requirements tooltip */
    .step-requirements {
        position: absolute;
        bottom: -120px;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        border: 1px solid var(--cw-border);
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        width: 250px;
        font-size: 0.875rem;
        z-index: 10;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .progress-step:hover .step-requirements {
        opacity: 1;
        visibility: visible;
    }

    .step-requirements h6 {
        margin: 0 0 0.5rem 0;
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .step-requirements ul {
        margin: 0;
        padding-left: 1rem;
        color: #6b7280;
    }

    .step-requirements li {
        margin-bottom: 0.25rem;
    }

    /* Enhanced Progress Bar */
    .progress-bar-container {
        background: #e5e7eb;
        border-radius: 1rem;
        height: 8px;
        overflow: hidden;
        margin-top: 1rem;
    }

    .progress-bar-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--cw-success) 0%, var(--cw-brand-primary) 100%);
        border-radius: 1rem;
        transition: width 0.6s ease;
        position: relative;
    }

    .progress-bar-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* Enhanced Auto-save indicator */
    .auto-save-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--cw-brand-primary);
        color: white;
        padding: 12px 20px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        transform: translateX(120%);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1050;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border: 2px solid transparent;
    }

    .auto-save-indicator.show {
        transform: translateX(0);
        opacity: 1;
    }

    .auto-save-indicator.saving {
        background: linear-gradient(45deg, #ffc107, #ffeb3b);
        color: #212529;
        border-color: #ffc107;
    }

    .auto-save-indicator.saved {
        background: linear-gradient(45deg, var(--cw-success), #34d399);
        color: white;
        border-color: var(--cw-success);
    }

    .auto-save-indicator.failed {
        background: linear-gradient(45deg, var(--cw-error), #f87171);
        color: white;
        border-color: var(--cw-error);
    }

    .auto-save-indicator i {
        font-size: 16px;
    }

    .auto-save-indicator .pulse {
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* Enhanced Wizard Content */
    .wizard-content {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .step-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--cw-border);
    }

    .step-header h2 {
        color: var(--cw-brand-primary);
        font-size: 1.875rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: #6b7280;
        font-size: 1.125rem;
        max-width: 600px;
        margin: 0 auto;
    }

    /* Enhanced Form Fields */
    .form-group {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .form-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .form-label .required {
        color: var(--cw-error);
    }

    .form-control {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 0.75rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
        transform: translateY(-1px);
    }

    .form-control.is-valid {
        border-color: var(--cw-success);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2310b981' d='m2.3 6.73.94-.94 1.88 1.88 3.75-3.75.94.94L4.2 9.5z'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .form-control.is-invalid {
        border-color: var(--cw-error);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ef4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m6 6-3-3 3-3m0 6l3-3-3-3'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    /* Enhanced Field Feedback */
    .field-feedback {
        margin-top: 0.75rem;
        font-size: 0.875rem;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
        padding: 0.5rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .field-feedback.success {
        color: var(--cw-success);
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .field-feedback.error {
        color: var(--cw-error);
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .field-feedback.info {
        color: var(--cw-info);
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .field-suggestions {
        margin-top: 0.5rem;
        font-size: 0.8125rem;
        color: #6b7280;
    }

    .field-suggestions ul {
        margin: 0.25rem 0 0 0;
        padding-left: 1rem;
    }

    .field-suggestions li {
        margin-bottom: 0.25rem;
    }

    /* Enhanced Character Counter */
    .character-counter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .character-count {
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        background: var(--cw-light-gray);
    }

    .character-count.warning {
        color: var(--cw-warning);
        background: rgba(245, 158, 11, 0.1);
    }

    .character-count.error {
        color: var(--cw-error);
        background: rgba(239, 68, 68, 0.1);
    }

    /* Mobile Responsive Design */
    @media (max-width: 768px) {
        .wizard-progress {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .progress-header {
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .progress-steps {
            flex-wrap: wrap;
            gap: 1rem;
        }

        .progress-step {
            flex: 1 1 calc(50% - 0.5rem);
            min-width: 120px;
        }

        .step-circle {
            width: 36px;
            height: 36px;
            font-size: 0.875rem;
        }

        .step-title {
            font-size: 0.8125rem;
        }

        .step-requirements {
            width: 200px;
            font-size: 0.8125rem;
            padding: 0.75rem;
        }

        .wizard-content {
            padding: 1.25rem;
            margin-bottom: 1rem;
        }

        .step-header h2 {
            font-size: 1.5rem;
        }

        .step-description {
            font-size: 1rem;
        }

        .auto-save-indicator {
            top: 10px;
            right: 10px;
            left: 10px;
            transform: translateY(-120%);
            font-size: 13px;
            padding: 10px 16px;
            border-radius: 10px;
        }

        .auto-save-indicator.show {
            transform: translateY(0);
        }

        .wizard-navigation {
            flex-direction: column;
            gap: 1rem;
        }

        .wizard-navigation > div {
            width: 100%;
        }

        .btn-wizard {
            width: 100%;
            justify-content: center;
            padding: 1rem 2rem;
            font-size: 1rem;
        }

        .form-control {
            font-size: 16px; /* Prevent zoom on iOS */
        }
    }

    /* Touch-friendly improvements */
    @media (max-width: 768px) {
        .status-card,
        .amenity-option {
            min-height: 48px; /* Touch target size */
            padding: 1rem;
        }

        .step-circle {
            min-width: 44px; /* Touch target size */
            min-height: 44px;
        }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        .wizard-progress,
        .wizard-content {
            background: #1f2937;
            color: #f9fafb;
        }

        .step-requirements {
            background: #374151;
            border-color: #4b5563;
        }

        .form-control {
            background: #374151;
            border-color: #4b5563;
            color: #f9fafb;
        }

        .form-control:focus {
            border-color: var(--cw-accent);
        }
    }

    /* Category Selection */
    .category-list {
        max-height: 300px;
        overflow-y: auto;
        padding: 0.5rem 0;
        border: 1px solid var(--cw-border);
        border-radius: 0.5rem;
        background: white;
    }

    .category-list .form-check {
        margin-bottom: 0.75rem;
        padding: 0.75rem 1rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .category-list .form-check:hover {
        background: var(--cw-accent);
    }

    .category-list .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.25rem;
        background-color: white;
        margin: 0;
        flex-shrink: 0;
        cursor: pointer;
    }

    .category-list .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .category-list .form-check-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .category-list .form-check-label {
        font-weight: 500;
        color: var(--cw-neutral-600);
        cursor: pointer;
        margin: 0;
        flex: 1;
        line-height: 1.4;
    }

    .category-list::-webkit-scrollbar {
        width: 6px;
    }

    .category-list::-webkit-scrollbar-track {
        background: var(--cw-light-gray);
        border-radius: 3px;
    }

    .category-list::-webkit-scrollbar-thumb {
        background: var(--cw-brand-primary);
        border-radius: 3px;
    }

    /* Navigation Buttons */
    .wizard-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--cw-border);
    }

    .btn-wizard {
        padding: 0.875rem 2rem;
        border-radius: 0.75rem;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        font-size: 1rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--cw-primary) 0%, #1f0a08 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.25);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #1f0a08 0%, var(--cw-primary) 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(47, 22, 15, 0.35);
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
        box-shadow: 0 4px 12px rgba(107, 114, 128, 0.25);
    }

    .btn-secondary:hover {
        background: #4b5563;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(107, 114, 128, 0.35);
    }

    /* Status Selection Cards */
    .status-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
    }

    .status-card {
        border: 2px solid var(--cw-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        background: white;
    }

    .status-card:hover {
        border-color: var(--cw-primary);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.1);
        transform: translateY(-2px);
    }

    .status-card.selected {
        border-color: var(--cw-primary);
        background: var(--cw-accent);
    }

    .status-card input[type="radio"] {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 1.25rem;
        height: 1.25rem;
    }

    .status-content h5 {
        color: var(--cw-primary);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .status-content p {
        color: #6b7280;
        margin: 0;
        font-size: 0.875rem;
    }

    /* Amenity Options */
    .amenities-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .amenity-option {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        border: 1px solid var(--cw-border);
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .amenity-option:hover {
        border-color: var(--cw-primary);
        background: var(--cw-light-gray);
    }

    .amenity-option input[type="checkbox"]:checked + .amenity-label {
        color: var(--cw-primary);
        font-weight: 600;
    }

    .amenity-label {
        cursor: pointer;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Alert Enhancements */
    .alert {
        border-radius: 0.75rem;
        border: none;
        padding: 1rem 1.25rem;
    }

    .alert-danger {
        background: rgba(239, 68, 68, 0.1);
        color: var(--cw-error);
        border-left: 4px solid var(--cw-error);
    }

    .alert-success {
        background: rgba(16, 185, 129, 0.1);
        color: var(--cw-success);
        border-left: 4px solid var(--cw-success);
    }

    .alert-warning {
        background: rgba(245, 158, 11, 0.1);
        color: var(--cw-warning);
        border-left: 4px solid var(--cw-warning);
    }

    .alert-info {
        background: rgba(59, 130, 246, 0.1);
        color: var(--cw-info);
        border-left: 4px solid var(--cw-info);
    }
</style>
{% endblock %}

{% block content %}
<section class="venue-creation-wizard">
    <div class="container">
        <!-- Progress Bar -->
        <div class="wizard-progress">
            <div class="progress-header">
                <div class="progress-title">
                    Venue Creation Progress
                </div>
                <div class="progress-percentage">
                    {{ progress_percentage }}%
                </div>
            </div>
            <div class="progress-steps">
                <div class="progress-line" style="width: {{ progress_percentage }}%"></div>
                {% for step_key, step_name in step_choices %}
                    <div class="progress-step" data-step="{{ step_key }}">
                        <div class="step-circle {% if step_key == current_step %}active{% elif step_key in completed_steps %}completed{% else %}{% if forloop.counter == 1 and progress_percentage >= 20 %}in-progress{% elif forloop.counter == 2 and progress_percentage >= 40 %}in-progress{% elif forloop.counter == 3 and progress_percentage >= 60 %}in-progress{% elif forloop.counter == 4 and progress_percentage >= 80 %}in-progress{% elif forloop.counter == 5 and progress_percentage >= 100 %}in-progress{% else %}blocked{% endif %}{% endif %}">
                            {% if step_key == current_step %}
                                {{ step_number }}
                            {% elif step_key in completed_steps %}
                                ✓
                            {% else %}
                                {{ forloop.counter }}
                            {% endif %}

                            <!-- Step completion badge -->
                            <div class="step-completion-badge" style="display: none;">
                                <span class="completion-text"></span>
                            </div>
                        </div>
                        <div class="step-title {% if step_key == current_step %}active{% elif step_key in completed_steps %}completed{% endif %}">
                            {{ step_name }}
                        </div>

                        <!-- Step requirements tooltip -->
                        <div class="step-requirements">
                            <h6>{{ step_name }} Requirements</h6>
                            <ul class="requirements-list">
                                {% if step_key == 'basic' %}
                                    <li>Enter venue name (3-100 chars)</li>
                                    <li>Write description (50-500 chars)</li>
                                    <li>Select 1-3 categories</li>
                                {% elif step_key == 'location' %}
                                    <li>Complete street address</li>
                                    <li>City, state, and ZIP code</li>
                                    <li>Business phone number</li>
                                    <li>Email and website (optional)</li>
                                {% elif step_key == 'services' %}
                                    <li>Add at least 1 service</li>
                                    <li>Clear service descriptions</li>
                                    <li>Set appropriate pricing</li>
                                {% elif step_key == 'gallery' %}
                                    <li>Upload 1 main image</li>
                                    <li>Add 2-5 gallery images</li>
                                    <li>Use high-quality photos</li>
                                {% elif step_key == 'details' %}
                                    <li>Set operating hours</li>
                                    <li>Add venue amenities</li>
                                    <li>FAQs and policies (optional)</li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                {% endfor %}
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: {{ progress_percentage }}%"></div>
            </div>
        </div>

        <!-- Wizard Content -->
        <div class="wizard-content">
            <div class="step-header">
                <h2>{{ current_step_title }}</h2>
                <p class="step-description">
                    {% if current_step == 'basic' %}
                        Let's start with the essential information about your venue including name, description, and categories.
                    {% elif current_step == 'location' %}
                        Help customers find you with accurate location, address, and contact details.
                    {% elif current_step == 'services' %}
                        Add your services with pricing information and any special discounts.
                    {% elif current_step == 'gallery' %}
                        Upload images to showcase your venue and attract customers.
                    {% elif current_step == 'details' %}
                        Set operating hours, amenities, FAQs, policies, and review all information before submitting.
                    {% endif %}
                </p>
            </div>

            <form method="post" id="wizardForm" data-step="{{ current_step }}" class="venue-create-wizard">
                {% csrf_token %}

                <!-- Display form errors -->
                {% if form.errors %}
                    <div class="alert alert-danger" role="alert">
                        <h6><i class="fas fa-exclamation-triangle"></i> Please correct the following errors:</h6>
                        {% for field, errors in form.errors.items %}
                            <div class="mb-2">
                                <strong>{{ field|title }}:</strong>
                                {% for error in errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                {% if current_step == 'basic' %}
                    <div class="form-group">
                        <label class="form-label" for="{{ form.venue_name.id_for_label }}">
                            <i class="fas fa-store"></i>
                            Venue Name
                            <span class="required">*</span>
                        </label>
                        {{ form.venue_name|add_class:"form-control" }}
                        {% if form.venue_name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.venue_name.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="field-feedback" id="venue-name-feedback"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="{{ form.short_description.id_for_label }}">
                            <i class="fas fa-align-left"></i>
                            Venue Description
                            <span class="required">*</span>
                        </label>
                        {{ form.short_description|add_class:"form-control" }}
                        <div class="character-counter">
                            <span class="character-help">Describe what makes your venue special</span>
                            <span class="character-count" id="description-count">0 / 500</span>
                        </div>
                        {% if form.short_description.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.short_description.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="field-feedback" id="short-description-feedback"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tags"></i>
                            Venue Categories
                            <span class="required">*</span>
                        </label>
                        <p class="text-muted mb-3">Select up to 3 categories that best describe your venue</p>

                        <!-- Simple checkbox list -->
                        <div class="category-list">
                            {% for choice in form.categories %}
                                <div class="form-check mb-2">
                                    {{ choice.tag }}
                                    <label class="form-check-label" for="{{ choice.id_for_label }}">
                                        {{ choice.choice_label }}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>

                        {% if form.categories.errors %}
                            <div class="invalid-feedback d-block mt-2">
                                {% for error in form.categories.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="field-feedback" id="categories-feedback"></div>
                    </div>

                {% elif current_step == 'location' %}
                    <!-- Location Section -->
                    <div class="location-section">
                        <h5>Address Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.state.id_for_label }}">
                                        State <span class="text-danger">*</span>
                                    </label>
                                    {{ form.state|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.county.id_for_label }}">
                                        County <span class="text-danger">*</span>
                                    </label>
                                    {{ form.county|add_class:"form-control" }}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.city.id_for_label }}">
                                City <span class="text-danger">*</span>
                            </label>
                            {{ form.city|add_class:"form-control" }}
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.street_number.id_for_label }}">
                                        Street Number
                                    </label>
                                    {{ form.street_number|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.street_name.id_for_label }}">
                                        Street Name <span class="text-danger">*</span>
                                    </label>
                                    {{ form.street_name|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.zip_code.id_for_label }}">
                                        ZIP Code <span class="text-danger">*</span>
                                    </label>
                                    {{ form.zip_code|add_class:"form-control" }}
                                </div>
                            </div>
                        </div>

                        <!-- Hidden fields for coordinates -->
                        {{ form.latitude }}
                        {{ form.longitude }}
                    </div>

                    <!-- Contact Section -->
                    <div class="contact-section">
                        <h5>Contact Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.phone.id_for_label }}">
                                        Phone Number <span class="text-danger">*</span>
                                    </label>
                                    {{ form.phone|add_class:"form-control" }}
                                    <div class="field-feedback" id="phone-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.email.id_for_label }}">
                                        Email Address <span class="text-danger">*</span>
                                    </label>
                                    {{ form.email|add_class:"form-control" }}
                                    <div class="field-feedback" id="email-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.website_url.id_for_label }}">
                                Website URL
                            </label>
                            {{ form.website_url|add_class:"form-control" }}
                        </div>

                        <div class="social-media-section">
                            <h6>Social Media Links (Optional)</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="{{ form.instagram_url.id_for_label }}">
                                            <i class="fab fa-instagram"></i> Instagram
                                        </label>
                                        {{ form.instagram_url|add_class:"form-control" }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="{{ form.facebook_url.id_for_label }}">
                                            <i class="fab fa-facebook"></i> Facebook
                                        </label>
                                        {{ form.facebook_url|add_class:"form-control" }}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="{{ form.twitter_url.id_for_label }}">
                                            <i class="fab fa-twitter"></i> Twitter
                                        </label>
                                        {{ form.twitter_url|add_class:"form-control" }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="{{ form.linkedin_url.id_for_label }}">
                                            <i class="fab fa-linkedin"></i> LinkedIn
                                        </label>
                                        {{ form.linkedin_url|add_class:"form-control" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                {% elif current_step == 'services' %}

                    <div class="services-section">
                        <h5>Your Services</h5>
                        <p class="text-muted">Add the services you offer with pricing information</p>

                        <div id="services-container">
                            <!-- Dynamic services will be generated by JavaScript -->
                        </div>

                        <button type="button" class="btn btn-cw-primary" id="add-service-btn">
                            <i class="fas fa-plus"></i> Add Service
                        </button>

                        {{ form.services }}
                    </div>

                {% elif current_step == 'gallery' %}

                    <!-- Image Gallery Section -->
                    <div class="gallery-section">
                        <h5>Venue Gallery</h5>
                        <p class="text-muted">Upload images to showcase your venue (minimum 3 recommended, up to 5 total)</p>

                        <!-- Image Upload Cards Container -->
                        <div class="image-cards-container" id="imageCardsContainer">
                            <!-- Initial 3 image cards -->
                            <div class="image-upload-card" data-card-index="1">
                                <div class="upload-area" data-upload-area="1">
                                    <div class="upload-placeholder">
                                        <i class="fas fa-camera"></i>
                                        <h6>Image 1</h6>
                                        <p>Click to upload or drag & drop</p>
                                        <span class="upload-hint">Recommended: Main venue photo</span>
                                    </div>
                                    <input type="file" class="file-input" id="image-upload-1" accept="image/*" data-slot="1" style="display: none;">
                                </div>
                                <div class="image-preview-wrapper" style="display: none;">
                                    <img class="preview-image" src="" alt="Preview">
                                    <div class="image-overlay">
                                        <button type="button" class="btn btn-sm btn-primary set-main-btn" data-slot="1">
                                            <i class="fas fa-star"></i> Set as Main
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger remove-btn" data-slot="1">
                                            <i class="fas fa-trash"></i> Remove
                                        </button>
                                    </div>
                                    <div class="main-badge" style="display: none;">
                                        <i class="fas fa-star"></i> Main Image
                                    </div>
                                </div>
                                <div class="upload-progress" style="display: none;">
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="upload-status">Uploading...</small>
                                </div>
                            </div>

                            <div class="image-upload-card" data-card-index="2">
                                <div class="upload-area" data-upload-area="2">
                                    <div class="upload-placeholder">
                                        <i class="fas fa-camera"></i>
                                        <h6>Image 2</h6>
                                        <p>Click to upload or drag & drop</p>
                                        <span class="upload-hint">Show interior/features</span>
                                    </div>
                                    <input type="file" class="file-input" id="image-upload-2" accept="image/*" data-slot="2" style="display: none;">
                                </div>
                                <div class="image-preview-wrapper" style="display: none;">
                                    <img class="preview-image" src="" alt="Preview">
                                    <div class="image-overlay">
                                        <button type="button" class="btn btn-sm btn-primary set-main-btn" data-slot="2">
                                            <i class="fas fa-star"></i> Set as Main
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger remove-btn" data-slot="2">
                                            <i class="fas fa-trash"></i> Remove
                                        </button>
                                    </div>
                                    <div class="main-badge" style="display: none;">
                                        <i class="fas fa-star"></i> Main Image
                                    </div>
                                </div>
                                <div class="upload-progress" style="display: none;">
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="upload-status">Uploading...</small>
                                </div>
                            </div>

                            <div class="image-upload-card" data-card-index="3">
                                <div class="upload-area" data-upload-area="3">
                                    <div class="upload-placeholder">
                                        <i class="fas fa-camera"></i>
                                        <h6>Image 3</h6>
                                        <p>Click to upload or drag & drop</p>
                                        <span class="upload-hint">Additional angle/detail</span>
                                    </div>
                                    <input type="file" class="file-input" id="image-upload-3" accept="image/*" data-slot="3" style="display: none;">
                                </div>
                                <div class="image-preview-wrapper" style="display: none;">
                                    <img class="preview-image" src="" alt="Preview">
                                    <div class="image-overlay">
                                        <button type="button" class="btn btn-sm btn-primary set-main-btn" data-slot="3">
                                            <i class="fas fa-star"></i> Set as Main
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger remove-btn" data-slot="3">
                                            <i class="fas fa-trash"></i> Remove
                                        </button>
                                    </div>
                                    <div class="main-badge" style="display: none;">
                                        <i class="fas fa-star"></i> Main Image
                                    </div>
                                </div>
                                <div class="upload-progress" style="display: none;">
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="upload-status">Uploading...</small>
                                </div>
                            </div>
                        </div>

                        <!-- Add More Images Button -->
                        <div class="add-more-container" id="addMoreContainer">
                            <button type="button" class="btn btn-outline-primary add-image-btn" id="addImageBtn">
                                <i class="fas fa-plus"></i> Add Another Image
                            </button>
                            <small class="text-muted">You can add up to 2 more images (5 total)</small>
                        </div>

                        <!-- Gallery Tips -->
                        <div class="gallery-tips">
                            <h6><i class="fas fa-lightbulb"></i> Photo Tips</h6>
                            <ul class="tips-list">
                                <li>Use high-quality, well-lit photos</li>
                                <li>Show different angles and features of your venue</li>
                                <li>Include both exterior and interior shots</li>
                                <li>Make sure images are at least 800x600 pixels</li>
                                <li>First image will be used as your main venue photo</li>
                            </ul>
                        </div>

                        {{ form.images }}
                        {{ form.main_image }}
                    </div>

                {% elif current_step == 'details' %}
                    <!-- Operating Hours Section -->
                    <div class="hours-section">
                        <h5>Operating Hours</h5>
                        <div id="operating-hours-container">
                            <!-- Dynamic operating hours will be generated by JavaScript -->
                        </div>
                        {{ form.operating_hours }}
                    </div>

                    <!-- Amenities Section -->
                    <div class="amenities-section">
                        <h5>Venue Amenities</h5>
                        <p class="text-muted">Select all amenities available at your venue</p>
                        <div class="amenities-grid">
                            {% for choice in form.amenities %}
                                <div class="amenity-option">
                                    {{ choice.tag }}
                                    <label for="{{ choice.id_for_label }}" class="amenity-label">
                                        <i class="fas fa-check"></i>
                                        {{ choice.choice_label }}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- FAQs Section -->
                    <div class="faqs-section">
                        <h5>Frequently Asked Questions (Optional)</h5>
                        <p class="text-muted">Add up to 5 FAQs to help customers</p>

                        <div id="faqs-container">
                            <!-- Dynamic FAQs will be generated by JavaScript -->
                        </div>

                        <button type="button" class="btn btn-outline-primary" id="add-faq-btn">
                            <i class="fas fa-plus"></i> Add FAQ
                        </button>

                        {{ form.faqs }}
                    </div>

                    <!-- Policies Section -->
                    <div class="policies-section">
                        <h5>Policies (Optional)</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.cancellation_policy.id_for_label }}">
                                        Cancellation Policy
                                    </label>
                                    {{ form.cancellation_policy|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.booking_policy.id_for_label }}">
                                        Booking Policy
                                    </label>
                                    {{ form.booking_policy|add_class:"form-control" }}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.special_instructions.id_for_label }}">
                                Special Instructions
                            </label>
                            {{ form.special_instructions|add_class:"form-control" }}
                        </div>
                    </div>

                    <!-- Final Review and Status Selection -->
                    <div class="review-section">
                        <h5>Review & Submit</h5>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> Almost Done!</h6>
                            <p>Review your venue information and choose how to save it.</p>
                        </div>

                        <!-- Status Selection -->
                        <div class="form-group">
                            <label class="form-label">Choose Action</label>

                            <div class="status-options">
                                {% for choice in form.venue_status %}
                                    <div class="status-card" data-status="{{ choice.data.value }}">
                                        {{ choice.tag }}
                                        <div class="status-content">
                                            <h5>{{ choice.choice_label }}</h5>
                                            <p>
                                                {% if choice.data.value == 'draft' %}
                                                    Save privately for later editing and submit when ready.
                                                {% else %}
                                                    Submit for admin review to make visible to customers.
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Approval Requirements Warning -->
                            <div class="approval-requirements-warning" id="approvalWarning" style="display: none;">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Approval Requirements</h6>
                                    <p>To submit for approval, your venue must meet certain quality standards:</p>
                                    <ul class="requirements-list">
                                        <li>Complete venue profile (80% or higher)</li>
                                        <li>At least 2 services with pricing</li>
                                        <li>At least 2 contact methods (phone, email, website)</li>
                                        <li>Main image + 2 gallery images</li>
                                        <li>Operating hours configured</li>
                                        <li>At least 3 amenities listed</li>
                                    </ul>
                                    <p class="mb-0"><small>If requirements aren't met, your venue will be saved as a draft instead.</small></p>
                                </div>
                            </div>
                        </div>
                    </div>

                {% endif %}

                <!-- Navigation -->
                <div class="wizard-navigation">
                    <div>
                        {% if not is_first_step and previous_step %}
                            <a href="{% url 'venues_app:venue_create_wizard' step=previous_step %}" class="btn-wizard btn-secondary">
                                <i class="fas fa-arrow-left"></i> Previous
                            </a>
                        {% endif %}
                    </div>

                    <div>
                        {% if is_final_step %}
                            <button type="submit" class="btn-wizard btn-primary">
                                <i class="fas fa-check"></i> Create Venue
                            </button>
                        {% else %}
                            <button type="submit" class="btn-wizard btn-primary">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Auto-save Indicator -->
    <div class="auto-save-indicator" id="autoSaveIndicator">
        <i class="fas fa-save"></i> <span id="autoSaveText">Progress saved automatically</span>
    </div>

    <!-- Draft Restoration Banner -->
    {% if has_draft and draft_updated_at %}
    <div class="draft-restoration-banner alert alert-info alert-dismissible fade show" role="alert">
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle me-2"></i>
            <div>
                <strong>Draft restored!</strong> Your progress was saved {{ draft_updated_at|timesince }} ago.
                <small class="d-block text-muted">Changes are automatically saved as you type.</small>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </div>
    {% endif %}
</section>

<script>
// Enhanced venue creation wizard functionality with comprehensive features
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('wizardForm');
    const currentStep = form.dataset.step;

    // Enhanced auto-save functionality
    let autoSaveTimeout;
    let lastSaveData = {};
    let stepCompletionStatus = {};
    const autoSaveDelay = 2000; // 2 seconds
    const autoSaveIndicator = document.getElementById('autoSaveIndicator');
    const autoSaveText = document.getElementById('autoSaveText');

    // Initialize wizard
    initializeWizard();

    function initializeWizard() {
        setupAutoSave();
        setupRealTimeValidation();
        setupStepCompletion();
        setupCategorySelection();
        setupStatusSelection();
        setupProgressTracking();
        setupMobileOptimizations();
        updateCharacterCounters();
        loadProgressStatus();
    }

    // Enhanced auto-save with better feedback
    function autoSave() {
        const formData = new FormData(form);
        formData.append('action', 'save_progress');

        // Check if data has changed
        const currentData = Object.fromEntries(formData.entries());
        if (JSON.stringify(currentData) === JSON.stringify(lastSaveData)) {
            return; // No changes, skip save
        }

        // Show saving indicator with pulse animation
        showAutoSaveIndicator('Saving...', 'saving', true);

        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                lastSaveData = currentData;
                showAutoSaveIndicator('Progress saved', 'saved');

                // Update progress indicators
                updateProgressBar(data.progress_percentage);
                if (data.step_completion) {
                    updateStepCompletionIndicators(data.step_completion);
                }

                // Trigger success animation
                animateSuccessfulSave();
            } else {
                showAutoSaveIndicator('Save failed - ' + (data.message || 'Unknown error'), 'failed');
                console.error('Auto-save failed:', data);
            }
        })
        .catch(error => {
            showAutoSaveIndicator('Connection error', 'failed');
            console.error('Auto-save error:', error);
        });
    }

    function showAutoSaveIndicator(text, type = 'saved', pulse = false) {
        autoSaveText.textContent = text;
        autoSaveIndicator.className = 'auto-save-indicator show ' + type;

        // Add pulse animation for saving state
        if (pulse) {
            autoSaveIndicator.querySelector('i').classList.add('pulse');
        } else {
            autoSaveIndicator.querySelector('i').classList.remove('pulse');
        }

        // Auto-hide after delay (except for saving state)
        if (type !== 'saving') {
            setTimeout(() => {
                autoSaveIndicator.classList.remove('show');
                setTimeout(() => {
                    autoSaveIndicator.className = 'auto-save-indicator';
                }, 300);
            }, type === 'failed' ? 5000 : 3000);
        }
    }

    function animateSuccessfulSave() {
        // Add subtle animation to form to indicate successful save
        form.style.transform = 'scale(1.002)';
        setTimeout(() => {
            form.style.transform = 'scale(1)';
        }, 200);
    }

    // Enhanced real-time validation with suggestions
    function validateField(fieldName, fieldValue, showSuccessMessage = true) {
        const formData = new FormData();
        formData.append('action', 'validate_field');
        formData.append('field_name', fieldName);
        formData.append('field_value', fieldValue);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            const feedback = document.getElementById(fieldName.replace('_', '-') + '-feedback');
            const field = document.getElementById('id_' + fieldName);

            if (feedback) {
                if (data.is_valid && fieldValue.trim()) {
                    if (showSuccessMessage) {
                        feedback.innerHTML = '<i class="fas fa-check"></i> Looks good!';
                        feedback.className = 'field-feedback success';
                    } else {
                        feedback.style.display = 'none';
                    }

                    // Add success styling to field
                    if (field) {
                        field.classList.remove('is-invalid');
                        field.classList.add('is-valid');
                    }
                } else if (!data.is_valid) {
                    let feedbackHtml = '<div><i class="fas fa-times"></i> ' + data.errors.join(', ') + '</div>';

                    // Add suggestions if available
                    if (data.suggestions && data.suggestions.length > 0) {
                        feedbackHtml += '<div class="field-suggestions">';
                        feedbackHtml += '<strong>Suggestions:</strong>';
                        feedbackHtml += '<ul>';
                        data.suggestions.forEach(suggestion => {
                            feedbackHtml += '<li>' + suggestion + '</li>';
                        });
                        feedbackHtml += '</ul>';
                        feedbackHtml += '</div>';
                    }

                    feedback.innerHTML = feedbackHtml;
                    feedback.className = 'field-feedback error';

                    // Add error styling to field
                    if (field) {
                        field.classList.remove('is-valid');
                        field.classList.add('is-invalid');
                    }
                } else {
                    // Clear feedback for empty fields
                    feedback.style.display = 'none';
                    if (field) {
                        field.classList.remove('is-valid', 'is-invalid');
                    }
                }
            }
        })
        .catch(error => {
            console.error('Validation error:', error);
        });
    }

    // Setup auto-save listeners
    function setupAutoSave() {
        if (form) {
            // Debounced auto-save on input changes
            form.addEventListener('input', scheduleAutoSave);
            form.addEventListener('change', scheduleAutoSave);

            // Save before page unload using modern API
            window.addEventListener('beforeunload', function(e) {
                const formData = new FormData(form);
                const currentData = Object.fromEntries(formData.entries());

                if (JSON.stringify(currentData) !== JSON.stringify(lastSaveData)) {
                    const beaconData = new FormData(form);
                    beaconData.append('action', 'save_progress');

                    // Use sendBeacon for reliable data sending on page unload
                    if (navigator.sendBeacon) {
                        navigator.sendBeacon(window.location.href, beaconData);
                    }
                }
            });

            // Initialize baseline data
            setTimeout(() => {
                const formData = new FormData(form);
                lastSaveData = Object.fromEntries(formData.entries());
            }, 500);
        }
    }

    function scheduleAutoSave() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(autoSave, autoSaveDelay);
    }

    // Setup real-time validation for key fields
    function setupRealTimeValidation() {
        const watchedFields = ['venue_name', 'short_description', 'phone', 'email', 'website_url'];

        watchedFields.forEach(fieldName => {
            const field = document.getElementById('id_' + fieldName);
            if (field) {
                let validationTimeout;

                field.addEventListener('input', function() {
                    clearTimeout(validationTimeout);
                    clearTimeout(autoSaveTimeout);

                    // Real-time validation with debounce
                    if (this.value.trim()) {
                        validationTimeout = setTimeout(() => {
                            validateField(fieldName, this.value);
                        }, 800);
                    } else {
                        // Clear validation for empty fields
                        const feedback = document.getElementById(fieldName.replace('_', '-') + '-feedback');
                        if (feedback) feedback.style.display = 'none';
                        this.classList.remove('is-valid', 'is-invalid');
                    }

                    // Update character counter
                    if (fieldName === 'short_description') {
                        updateCharacterCounter(this);
                    }

                    // Schedule auto-save
                    scheduleAutoSave();
                });

                // Validate on blur for immediate feedback
                field.addEventListener('blur', function() {
                    if (this.value.trim()) {
                        validateField(fieldName, this.value, false);
                    }
                });
            }
        });
    }

    // Enhanced character counter with visual feedback
    function updateCharacterCounter(field) {
        const counter = document.getElementById('description-count');
        const maxLength = 500;
        const minLength = 50;
        const currentLength = field.value.length;

        if (counter) {
            counter.textContent = currentLength + ' / ' + maxLength;

            // Dynamic styling based on length
            counter.className = 'character-count';

            if (currentLength < minLength) {
                counter.classList.add('info');
                counter.style.background = 'rgba(59, 130, 246, 0.1)';
                counter.style.color = 'var(--cw-info)';
            } else if (currentLength > maxLength * 0.9) {
                counter.classList.add('warning');
            } else if (currentLength > maxLength) {
                counter.classList.add('error');
            } else {
                counter.style.background = 'rgba(16, 185, 129, 0.1)';
                counter.style.color = 'var(--cw-success)';
            }
        }
    }

    function updateCharacterCounters() {
        const descField = document.getElementById('id_short_description');
        if (descField) {
            updateCharacterCounter(descField);
        }
    }

    // Simple category selection with limit enforcement
    function setupCategorySelection() {
        document.querySelectorAll('.category-list input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selectedCount = document.querySelectorAll('.category-list input[type="checkbox"]:checked').length;

                // Check selection limit (3 categories max)
                if (selectedCount > 3) {
                    this.checked = false;
                    showTemporaryMessage('You can select up to 3 categories only.', 'warning');
                    return;
                }

                // Update feedback
                updateCategoryFeedback();

                // Auto-save
                scheduleAutoSave();
            });
        });
    }

    function updateCategoryFeedback() {
        const selectedCount = document.querySelectorAll('.category-list input[type="checkbox"]:checked').length;
        const feedback = document.getElementById('categories-feedback');

        if (feedback) {
            if (selectedCount === 0) {
                feedback.innerHTML = '<i class="fas fa-info-circle"></i> Please select at least 1 category';
                feedback.className = 'field-feedback info';
                feedback.style.display = 'flex';
            } else if (selectedCount > 0 && selectedCount <= 3) {
                feedback.innerHTML = '<i class="fas fa-check"></i> ' + selectedCount + ' categor' + (selectedCount === 1 ? 'y' : 'ies') + ' selected';
                feedback.className = 'field-feedback success';
                feedback.style.display = 'flex';
            }
        }
    }

    // Status selection with requirement checking
    function setupStatusSelection() {
        document.querySelectorAll('.status-card').forEach(card => {
            card.addEventListener('click', function() {
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    // Update visual selection
                    document.querySelectorAll('.status-card').forEach(c => c.classList.remove('selected'));
                    radio.checked = true;
                    this.classList.add('selected');

                    // Show/hide approval requirements warning
                    const approvalWarning = document.getElementById('approvalWarning');
                    if (approvalWarning) {
                        if (radio.value === 'pending') {
                            approvalWarning.style.display = 'block';
                            approvalWarning.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                        } else {
                            approvalWarning.style.display = 'none';
                        }
                    }

                    // Auto-save
                    scheduleAutoSave();
                }
            });
        });
    }

    // Step completion tracking and progress updates
    function setupStepCompletion() {
        // Check step requirements periodically
        setInterval(checkCurrentStepCompletion, 5000); // Every 5 seconds
    }

    function checkCurrentStepCompletion() {
        const formData = new FormData();
        formData.append('action', 'check_step_requirements');
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            updateCurrentStepCompletion(data.completion_status);
        })
        .catch(error => {
            console.error('Step completion check error:', error);
        });
    }

    function updateCurrentStepCompletion(completionStatus) {
        if (!completionStatus) return;

        const currentStepElement = document.querySelector('.progress-step[data-step="' + currentStep + '"]');
        if (currentStepElement) {
            const circle = currentStepElement.querySelector('.step-circle');
            const badge = currentStepElement.querySelector('.step-completion-badge');

            if (completionStatus.complete) {
                circle.classList.add('completed');
                if (badge) {
                    badge.style.display = 'flex';
                    badge.className = 'step-completion-badge complete';
                    badge.querySelector('.completion-text').textContent = '✓';
                }
            } else if (completionStatus.percentage > 0) {
                circle.classList.add('in-progress');
                if (badge) {
                    badge.style.display = 'flex';
                    badge.className = 'step-completion-badge partial';
                    badge.querySelector('.completion-text').textContent = completionStatus.percentage + '%';
                }
            }
        }
    }

    function updateStepCompletionIndicators(stepCompletion) {
        Object.keys(stepCompletion).forEach(stepKey => {
            const stepElement = document.querySelector('.progress-step[data-step="' + stepKey + '"]');
            const status = stepCompletion[stepKey];

            if (stepElement && status) {
                const circle = stepElement.querySelector('.step-circle');
                const badge = stepElement.querySelector('.step-completion-badge');
                const title = stepElement.querySelector('.step-title');

                // Update circle status
                circle.classList.remove('completed', 'in-progress', 'blocked');

                if (status.complete) {
                    circle.classList.add('completed');
                    title.classList.add('completed');
                    if (badge) {
                        badge.style.display = 'flex';
                        badge.className = 'step-completion-badge complete';
                        badge.querySelector('.completion-text').textContent = '✓';
                    }
                } else if (status.percentage > 0) {
                    circle.classList.add('in-progress');
                    if (badge) {
                        badge.style.display = 'flex';
                        badge.className = 'step-completion-badge partial';
                        badge.querySelector('.completion-text').textContent = status.percentage + '%';
                    }
                } else {
                    circle.classList.add('blocked');
                }
            }
        });
    }

    function updateProgressBar(percentage) {
        const progressBar = document.querySelector('.progress-bar-fill');
        const progressLine = document.querySelector('.progress-line');
        const progressPercentage = document.querySelector('.progress-percentage');

        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }

        if (progressLine) {
            progressLine.style.width = percentage + '%';
        }

        if (progressPercentage) {
            progressPercentage.textContent = percentage + '%';
        }
    }

    function setupProgressTracking() {
        // Load initial progress status
        loadProgressStatus();

        // Update progress every 30 seconds
        setInterval(loadProgressStatus, 30000);
    }

    function loadProgressStatus() {
        const formData = new FormData();
        formData.append('action', 'get_progress_status');
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateProgressBar(data.progress_percentage);
                if (data.step_completion) {
                    updateStepCompletionIndicators(data.step_completion);
                }
            }
        })
        .catch(error => {
            console.error('Progress status load error:', error);
        });
    }

    // Mobile optimizations
    function setupMobileOptimizations() {
        // Improve touch interactions on mobile
        if (window.innerWidth <= 768) {
            // Add touch-friendly improvements
            document.querySelectorAll('.status-card, .amenity-option').forEach(element => {
                element.style.minHeight = '48px';
            });

            // Improve auto-save indicator positioning
            autoSaveIndicator.style.position = 'fixed';
            autoSaveIndicator.style.top = '10px';
            autoSaveIndicator.style.left = '10px';
            autoSaveIndicator.style.right = '10px';
        }

        // Handle orientation changes
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                // Recalculate layouts after orientation change
                updateProgressBar(document.querySelector('.progress-percentage').textContent.replace('%', ''));
            }, 500);
        });
    }

    // Utility function for temporary messages
    function showTemporaryMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'alert alert-' + type + ' temporary-message';
        messageDiv.innerHTML = '<i class="fas fa-info-circle"></i> ' + message;
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.left = '50%';
        messageDiv.style.transform = 'translateX(-50%)';
        messageDiv.style.zIndex = '1060';
        messageDiv.style.maxWidth = '400px';

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.remove();
        }, 4000);
    }

    // Initialize category feedback on page load
    setTimeout(updateCategoryFeedback, 500);

    // Initialize services management for services step
    if (currentStep === 'services') {
        initializeServicesManagement();
    }

    // Services management functionality
    function initializeServicesManagement() {
        setupServiceEventListeners();
        loadExistingServices();
    }

    function setupServiceEventListeners() {
        const addServiceBtn = document.getElementById('add-service-btn');
        if (addServiceBtn) {
            addServiceBtn.addEventListener('click', function() {
                addNewService();
            });
        }

        // Event delegation for dynamically created service forms
        const servicesContainer = document.getElementById('services-container');
        if (servicesContainer) {
            servicesContainer.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-service-btn')) {
                    removeService(e.target.closest('.service-form'));
                }
            });

            servicesContainer.addEventListener('input', function(e) {
                if (e.target.closest('.service-form')) {
                    scheduleAutoSave();
                    updateServicesData();
                }
            });
        }
    }

    function addNewService() {
        const servicesContainer = document.getElementById('services-container');
        if (!servicesContainer) return;

        const serviceIndex = servicesContainer.children.length;
        const serviceHtml = createServiceFormHtml(serviceIndex);

        const serviceDiv = document.createElement('div');
        serviceDiv.innerHTML = serviceHtml;
        servicesContainer.appendChild(serviceDiv.firstElementChild);

        // Update services data
        updateServicesData();

        // Auto-save
        scheduleAutoSave();

        // Focus on the service title field
        const titleInput = serviceDiv.querySelector('.service-title-input');
        if (titleInput) {
            titleInput.focus();
        }
    }

    function createServiceFormHtml(index) {
        return `
        <div class="service-form" data-service-index="${index}">
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Service ${index + 1}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-service-btn">
                        <i class="fas fa-trash"></i> Remove
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Service Title *</label>
                                <input type="text" class="form-control service-title-input" name="service_title_${index}"
                                       placeholder="e.g., Deep Tissue Massage" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Category</label>
                                <select class="form-control service-category-select" name="service_category_${index}">
                                    <option value="">Select category...</option>
                                    <option value="massage">Massage Therapy</option>
                                    <option value="facial">Facial Treatments</option>
                                    <option value="body">Body Treatments</option>
                                    <option value="nail">Nail Services</option>
                                    <option value="hair">Hair Services</option>
                                    <option value="medical">Medical Services</option>
                                    <option value="fitness">Fitness & Wellness</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control service-description-input" name="service_description_${index}"
                                  rows="3" placeholder="Describe your service..."></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="form-label">Duration (minutes)</label>
                                <input type="number" class="form-control service-duration-input" name="service_duration_${index}"
                                       placeholder="60" min="15" max="480">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="form-label">Price (USD) *</label>
                                <input type="number" class="form-control service-price-input" name="service_price_${index}"
                                       placeholder="0.00" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="form-label">Max Price (USD)</label>
                                <input type="number" class="form-control service-max-price-input" name="service_max_price_${index}"
                                       placeholder="0.00" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `;
    }

    function removeService(serviceForm) {
        if (confirm('Are you sure you want to remove this service?')) {
            serviceForm.remove();
            updateServicesData();
            scheduleAutoSave();

            // Renumber remaining services
            const servicesContainer = document.getElementById('services-container');
            const serviceForms = servicesContainer.querySelectorAll('.service-form');
            serviceForms.forEach((form, index) => {
                form.querySelector('.card-header h6').textContent = `Service ${index + 1}`;
                form.setAttribute('data-service-index', index);
            });
        }
    }

    function updateServicesData() {
        const servicesContainer = document.getElementById('services-container');
        const servicesField = document.getElementById('id_services');

        if (!servicesContainer || !servicesField) return;

        const services = [];
        const serviceForms = servicesContainer.querySelectorAll('.service-form');

        serviceForms.forEach((form, index) => {
            const serviceData = {
                title: form.querySelector('.service-title-input')?.value || '',
                category: form.querySelector('.service-category-select')?.value || '',
                description: form.querySelector('.service-description-input')?.value || '',
                duration: form.querySelector('.service-duration-input')?.value || '',
                price_min: form.querySelector('.service-price-input')?.value || '',
                price_max: form.querySelector('.service-max-price-input')?.value || '',
                order: index + 1
            };

            if (serviceData.title.trim() || serviceData.price_min) {
                services.push(serviceData);
            }
        });

        servicesField.value = JSON.stringify(services);
    }

    function loadExistingServices() {
        const servicesField = document.getElementById('id_services');
        const servicesContainer = document.getElementById('services-container');

        if (!servicesField || !servicesContainer) return;

        try {
            const servicesData = servicesField.value ? JSON.parse(servicesField.value) : [];

            if (servicesData.length > 0) {
                // Load existing services
                servicesData.forEach((service, index) => {
                    const serviceHtml = createServiceFormHtml(index);
                    const serviceDiv = document.createElement('div');
                    serviceDiv.innerHTML = serviceHtml;
                    const serviceForm = serviceDiv.firstElementChild;

                    // Populate the form with existing data
                    const titleInput = serviceForm.querySelector('.service-title-input');
                    const categorySelect = serviceForm.querySelector('.service-category-select');
                    const descriptionInput = serviceForm.querySelector('.service-description-input');
                    const durationInput = serviceForm.querySelector('.service-duration-input');
                    const priceInput = serviceForm.querySelector('.service-price-input');
                    const maxPriceInput = serviceForm.querySelector('.service-max-price-input');

                    if (titleInput) titleInput.value = service.title || '';
                    if (categorySelect) categorySelect.value = service.category || '';
                    if (descriptionInput) descriptionInput.value = service.description || '';
                    if (durationInput) durationInput.value = service.duration || '';
                    if (priceInput) priceInput.value = service.price_min || '';
                    if (maxPriceInput) maxPriceInput.value = service.price_max || '';

                    servicesContainer.appendChild(serviceForm);
                });
            } else {
                // No existing services, add two empty service forms by default
                for (let i = 0; i < 2; i++) {
                    const serviceHtml = createServiceFormHtml(i);
                    const serviceDiv = document.createElement('div');
                    serviceDiv.innerHTML = serviceHtml;
                    servicesContainer.appendChild(serviceDiv.firstElementChild);
                }
            }
        } catch (error) {
            console.error('Error loading existing services:', error);
            // Fallback: add two empty service forms
            for (let i = 0; i < 2; i++) {
                const serviceHtml = createServiceFormHtml(i);
                const serviceDiv = document.createElement('div');
                serviceDiv.innerHTML = serviceHtml;
                servicesContainer.appendChild(serviceDiv.firstElementChild);
            }
        }
    }

    // Initialize gallery functionality for the new image cards
    if (currentStep === 'gallery') {
        initializeImageGallery();
    }

    // Initialize FAQ management for details step
    if (currentStep === 'details') {
        initializeFaqManagement();
    }

    function initializeImageGallery() {
        let uploadedImages = [];
        let imageCount = 3; // Start with 3 cards
        const maxImages = 5;

        // Handle click events on upload areas
        document.querySelectorAll('.upload-area').forEach(uploadArea => {
            uploadArea.addEventListener('click', function() {
                const slot = this.dataset.uploadArea;
                const fileInput = document.getElementById(`image-upload-${slot}`);
                fileInput.click();
            });

            // Handle drag and drop
            setupDragAndDrop(uploadArea);
        });

        // Handle file input changes
        document.querySelectorAll('.file-input').forEach(fileInput => {
            fileInput.addEventListener('change', function() {
                const slot = this.dataset.slot;
                const file = this.files[0];
                if (file) {
                    handleImageUpload(slot, file);
                }
            });
        });

        // Handle set main buttons
        document.addEventListener('click', function(e) {
            if (e.target.matches('.set-main-btn') || e.target.closest('.set-main-btn')) {
                const btn = e.target.closest('.set-main-btn');
                const slot = btn.dataset.slot;
                setMainImage(slot);
            }
        });

        // Handle remove buttons
        document.addEventListener('click', function(e) {
            if (e.target.matches('.remove-btn') || e.target.closest('.remove-btn')) {
                const btn = e.target.closest('.remove-btn');
                const slot = btn.dataset.slot;
                removeImage(slot);
            }
        });

        // Handle add more images button
        const addImageBtn = document.getElementById('addImageBtn');
        if (addImageBtn) {
            addImageBtn.addEventListener('click', function() {
                if (imageCount < maxImages) {
                    addNewImageCard();
                }
            });
        }

        function setupDragAndDrop(uploadArea) {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, () => {
                    uploadArea.classList.add('drag-over');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, () => {
                    uploadArea.classList.remove('drag-over');
                }, false);
            });

            uploadArea.addEventListener('drop', function(e) {
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const slot = this.dataset.uploadArea;
                    handleImageUpload(slot, files[0]);
                }
            });
        }

        async function handleImageUpload(slot, file) {
            if (!file.type.startsWith('image/')) {
                showNotification('Please select an image file.', 'error');
                return;
            }

            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                showNotification('Image file too large. Maximum size is 5MB.', 'error');
                return;
            }

            const card = document.querySelector(`[data-card-index="${slot}"]`);
            const uploadArea = card.querySelector('.upload-area');
            const previewWrapper = card.querySelector('.image-preview-wrapper');
            const progressDiv = card.querySelector('.upload-progress');
            const previewImg = previewWrapper.querySelector('.preview-image');

            // Show immediate preview using FileReader (like customer profile picture)
            const reader = new FileReader();
            reader.onload = function(e) {
                // Show the image preview immediately in full card format
                previewImg.src = e.target.result;
                uploadArea.style.display = 'none';
                previewWrapper.style.display = 'block';
                card.classList.add('has-image');

                // Ensure full card display
                previewWrapper.style.height = '100%';
                previewWrapper.style.minHeight = '300px';
                previewImg.style.minHeight = '300px';
                previewImg.style.height = '100%';

                // Show uploading indicator overlay
                progressDiv.style.display = 'block';
                progressDiv.style.position = 'absolute';
                progressDiv.style.bottom = '0';
                progressDiv.style.left = '0';
                progressDiv.style.right = '0';
                progressDiv.style.background = 'rgba(255, 255, 255, 0.95)';
                progressDiv.style.zIndex = '15';
                progressDiv.querySelector('.upload-status').textContent = 'Uploading...';

                // Animate progress bar
                const progressBar = progressDiv.querySelector('.progress-bar');
                if (progressBar) {
                    progressBar.style.width = '0%';
                    // Simulate progress animation
                    let progress = 0;
                    const progressInterval = setInterval(() => {
                        progress += Math.random() * 15;
                        if (progress > 90) progress = 90; // Don't go to 100% until upload completes
                        progressBar.style.width = progress + '%';
                    }, 200);

                    // Store interval to clear it later
                    progressDiv.dataset.progressInterval = progressInterval;
                }
            };
            reader.readAsDataURL(file);

            try {
                const formData = new FormData();
                formData.append('image', file);
                formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

                const response = await fetch('/venues/provider/create/wizard/upload-image/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Update the uploaded images array
                    const existingIndex = uploadedImages.findIndex(img => img.slot === slot);
                    const imageData = {
                        slot: slot,
                        url: data.image_url,
                        path: data.image_path,
                        name: data.filename,
                        isMain: uploadedImages.length === 0 // First image is main by default
                    };

                    if (existingIndex >= 0) {
                        uploadedImages[existingIndex] = imageData;
                    } else {
                        uploadedImages.push(imageData);
                    }

                    // Update the preview with server URL (in case it's different from local preview)
                    previewImg.src = data.image_url;

                    // Ensure full card display is maintained
                    previewWrapper.style.height = '100%';
                    previewWrapper.style.minHeight = '300px';
                    previewImg.style.minHeight = '300px';
                    previewImg.style.height = '100%';

                    // Complete progress animation
                    const progressBar = progressDiv.querySelector('.progress-bar');
                    if (progressBar && progressDiv.dataset.progressInterval) {
                        clearInterval(progressDiv.dataset.progressInterval);
                        progressBar.style.width = '100%';
                        // Hide progress after a brief moment
                        setTimeout(() => {
                            progressDiv.style.display = 'none';
                        }, 800);
                    } else {
                        progressDiv.style.display = 'none';
                    }

                    // Add visual indicators
                    if (imageData.isMain) {
                        card.classList.add('main-image');
                        card.querySelector('.main-badge').style.display = 'block';
                    }

                    // Update hidden form field
                    updateImagesField();

                    showNotification('Image uploaded successfully!', 'success');
                } else {
                    throw new Error(data.error || 'Upload failed');
                }
            } catch (error) {
                console.error('Upload error:', error);
                showNotification('Upload failed: ' + error.message, 'error');

                // Clear progress interval if it exists
                if (progressDiv.dataset.progressInterval) {
                    clearInterval(progressDiv.dataset.progressInterval);
                }

                // Reset to upload area on error
                progressDiv.style.display = 'none';
                previewWrapper.style.display = 'none';
                uploadArea.style.display = 'flex';
                card.classList.remove('has-image');
            }
        }

        function setMainImage(slot) {
            // Remove main status from all images
            uploadedImages.forEach(img => img.isMain = false);
            document.querySelectorAll('.image-upload-card').forEach(card => {
                card.classList.remove('main-image');
                card.querySelector('.main-badge').style.display = 'none';
            });

            // Set new main image
            const imageData = uploadedImages.find(img => img.slot === slot);
            if (imageData) {
                imageData.isMain = true;
                const card = document.querySelector(`[data-card-index="${slot}"]`);
                card.classList.add('main-image');
                card.querySelector('.main-badge').style.display = 'block';

                updateImagesField();
                showNotification('Main image updated!', 'success');
            }
        }

        function removeImage(slot) {
            const card = document.querySelector(`[data-card-index="${slot}"]`);
            const uploadArea = card.querySelector('.upload-area');
            const previewWrapper = card.querySelector('.image-preview-wrapper');

            // Remove from uploaded images array
            uploadedImages = uploadedImages.filter(img => img.slot !== slot);

            // Reset the card UI completely
            card.classList.remove('has-image', 'main-image');
            previewWrapper.style.display = 'none';
            previewWrapper.style.height = '';
            previewWrapper.style.minHeight = '';
            uploadArea.style.display = 'flex';
            card.querySelector('.main-badge').style.display = 'none';

            // Reset preview image styles
            const previewImg = previewWrapper.querySelector('.preview-image');
            if (previewImg) {
                previewImg.style.height = '';
                previewImg.style.minHeight = '';
                previewImg.src = '';
            }

            // Clear file input
            const fileInput = document.getElementById(`image-upload-${slot}`);
            fileInput.value = '';

            // If this was the main image, set the first remaining image as main
            if (uploadedImages.length > 0 && !uploadedImages.some(img => img.isMain)) {
                setMainImage(uploadedImages[0].slot);
            }

            updateImagesField();
            showNotification('Image removed!', 'success');
        }

        function addNewImageCard() {
            if (imageCount >= maxImages) return;

            imageCount++;
            const container = document.getElementById('imageCardsContainer');

            const newCard = document.createElement('div');
            newCard.className = 'image-upload-card';
            newCard.dataset.cardIndex = imageCount;

            newCard.innerHTML = `
                <div class="upload-area" data-upload-area="${imageCount}">
                    <div class="upload-placeholder">
                        <i class="fas fa-camera"></i>
                        <h6>Image ${imageCount}</h6>
                        <p>Click to upload or drag & drop</p>
                        <span class="upload-hint">Additional venue photo</span>
                    </div>
                    <input type="file" class="file-input" id="image-upload-${imageCount}" accept="image/*" data-slot="${imageCount}" style="display: none;">
                </div>
                <div class="image-preview-wrapper" style="display: none;">
                    <img class="preview-image" src="" alt="Preview">
                    <div class="image-overlay">
                        <button type="button" class="btn btn-sm btn-primary set-main-btn" data-slot="${imageCount}">
                            <i class="fas fa-star"></i> Set as Main
                        </button>
                        <button type="button" class="btn btn-sm btn-danger remove-btn" data-slot="${imageCount}">
                            <i class="fas fa-trash"></i> Remove
                        </button>
                    </div>
                    <div class="main-badge" style="display: none;">
                        <i class="fas fa-star"></i> Main Image
                    </div>
                </div>
                <div class="upload-progress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="upload-status">Uploading...</small>
                </div>
            `;

            container.appendChild(newCard);

            // Set up event listeners for the new card
            const uploadArea = newCard.querySelector('.upload-area');
            uploadArea.addEventListener('click', function() {
                const fileInput = document.getElementById(`image-upload-${imageCount}`);
                fileInput.click();
            });

            setupDragAndDrop(uploadArea);

            const fileInput = newCard.querySelector('.file-input');
            fileInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    handleImageUpload(imageCount, file);
                }
            });

            // Update add more button visibility
            const addMoreContainer = document.getElementById('addMoreContainer');
            if (imageCount >= maxImages) {
                addMoreContainer.style.display = 'none';
            }

            showNotification('New image slot added!', 'success');
        }

        function updateImagesField() {
            const imagesField = document.querySelector('input[name="images"]');
            if (imagesField) {
                const imageData = uploadedImages.map((img, index) => ({
                    url: img.url,
                    path: img.path,
                    name: img.name,
                    order: index + 1,
                    is_main: img.isMain
                }));
                imagesField.value = JSON.stringify(imageData);
            }

            // Update main image field
            const mainImageField = document.querySelector('input[name="main_image"]');
            if (mainImageField) {
                const mainImage = uploadedImages.find(img => img.isMain);
                mainImageField.value = mainImage ? mainImage.url : '';
            }
        }

        function showNotification(message, type = 'info') {
            // Create or update notification
            let notification = document.querySelector('.gallery-notification');
            if (!notification) {
                notification = document.createElement('div');
                notification.className = 'gallery-notification alert';
                document.querySelector('.gallery-section').prepend(notification);
            }

            notification.className = `gallery-notification alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                ${message}
            `;

            // Auto hide after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }
    }

    function initializeFaqManagement() {
        const addFaqBtn = document.getElementById('add-faq-btn');
        const faqsContainer = document.getElementById('faqs-container');

        if (addFaqBtn) {
            addFaqBtn.addEventListener('click', addNewFaq);
        }

        if (faqsContainer) {
            faqsContainer.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-faq-btn')) {
                    removeFaq(e.target.closest('.faq-form'));
                }
            });

            // Add three initial FAQ forms
            for (let i = 0; i < 3; i++) {
                addNewFaq();
            }
        }
    }

    function addNewFaq() {
        const faqsContainer = document.getElementById('faqs-container');
        if (!faqsContainer) return;

        const faqIndex = faqsContainer.children.length;
        const faqHtml = `
            <div class="faq-form" data-faq-index="${faqIndex}">
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">FAQ ${faqIndex + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-faq-btn">
                            <i class="fas fa-trash"></i> Remove
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label class="form-label">Question</label>
                            <input type="text" class="form-control" name="faq_question_${faqIndex}" placeholder="e.g., What is your cancellation policy?">
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label">Answer</label>
                            <textarea class="form-control" name="faq_answer_${faqIndex}" rows="3" placeholder="Provide a clear and concise answer."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const faqDiv = document.createElement('div');
        faqDiv.innerHTML = faqHtml;
        faqsContainer.appendChild(faqDiv.firstElementChild);
    }

    function removeFaq(faqForm) {
        if (confirm('Are you sure you want to remove this FAQ?')) {
            faqForm.remove();
            // Renumber remaining FAQs
            const faqsContainer = document.getElementById('faqs-container');
            const faqForms = faqsContainer.querySelectorAll('.faq-form');
            faqForms.forEach((form, index) => {
                form.querySelector('.card-header h6').textContent = `FAQ ${index + 1}`;
                form.setAttribute('data-faq-index', index);
            });
        }
    }
});
</script>

{% if show_guided_tour %}
<!-- Guided Tour Integration -->
<script src="{% static 'js/intro.min.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const tourSteps = {{ tour_steps|safe }};

    if (tourSteps && tourSteps.length > 0) {
        const intro = introJs();

        intro.setOptions({
            steps: tourSteps.map(step => ({
                element: step.target,
                intro: `<h4>${step.title}</h4><p>${step.content}</p>`,
                position: step.placement || 'bottom'
            })),
            showProgress: true,
            showBullets: false,
            exitOnOverlayClick: false,
            disableInteraction: false
        });

        // Show tour after a brief delay
        setTimeout(() => {
            intro.start();
        }, 1000);
    }
});
</script>
{% endif %}
{% endblock %}
