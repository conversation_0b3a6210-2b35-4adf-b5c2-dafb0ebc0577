{% extends 'utility_app/base.html' %}
{% load static %}

{% block title %}{{ service_category.name }} Services - CozyWish{% endblock title %}

{% block meta_description %}Find the best {{ service_category.name|lower }} services near you. Browse {{ total_services }} services from {{ venues_with_services }} verified venues.{% endblock meta_description %}

{% block extra_css %}
<style>
    .category-hero {
        background: linear-gradient(135deg, var(--cw-brand-primary), var(--cw-brand-secondary));
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
    }

    .category-header {
        display: flex;
        align-items: center;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .category-icon-large {
        width: 5rem;
        height: 5rem;
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        box-shadow: var(--cw-shadow-lg);
    }

    .category-stats {
        display: flex;
        gap: 2rem;
        margin-top: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        backdrop-filter: blur(10px);
    }

    .filters-section {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .filter-row {
        display: grid;
        grid-template-columns: 1fr auto auto auto;
        gap: 1rem;
        align-items: end;
    }

    .location-search {
        position: relative;
    }

    .location-search input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        font-size: 0.875rem;
    }

    .location-search i {
        position: absolute;
        top: 50%;
        left: 0.75rem;
        transform: translateY(-50%);
        color: var(--cw-neutral-500);
    }

    .price-filters {
        display: flex;
        gap: 0.5rem;
    }

    .price-filters input {
        width: 100px;
        padding: 0.75rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        font-size: 0.875rem;
    }

    .sort-select select {
        padding: 0.75rem 1rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        font-size: 0.875rem;
        background: white;
        min-width: 150px;
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .service-card {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .service-card:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
    }

    .service-image {
        height: 200px;
        background: var(--cw-gradient-card);
        position: relative;
        overflow: hidden;
    }

    .service-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .service-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--cw-brand-primary);
        color: white;
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .service-content {
        padding: 1.5rem;
    }

    .service-header {
        margin-bottom: 1rem;
    }

    .service-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .venue-name {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .service-description {
        color: var(--cw-neutral-700);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .service-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .service-detail {
        text-align: center;
        padding: 0.75rem;
        background: var(--cw-accent-light);
        border-radius: 0.5rem;
    }

    .service-detail-value {
        font-weight: 600;
        color: var(--cw-brand-primary);
        display: block;
    }

    .service-detail-label {
        font-size: 0.75rem;
        color: var(--cw-neutral-600);
    }

    .service-actions {
        display: flex;
        gap: 0.75rem;
    }

    .other-categories {
        background: var(--cw-accent-light);
        border-radius: 1rem;
        padding: 2rem;
        margin-top: 3rem;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .category-card {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1rem;
        text-align: center;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
    }

    .category-card:hover {
        border-color: var(--cw-brand-primary);
        transform: translateY(-3px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
        color: inherit;
    }

    .category-card-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        color: white;
        margin: 0 auto 0.75rem;
    }

    .category-card-name {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 0.875rem;
    }

    @media (max-width: 768px) {
        .category-header {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .category-stats {
            justify-content: center;
        }

        .filter-row {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .services-grid {
            grid-template-columns: 1fr;
        }

        .categories-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }
    }
</style>
{% endblock extra_css %}

{% block content %}
<!-- Category Hero Section -->
<section class="category-hero">
    <div class="container">
        <div class="category-header">
            <div class="category-icon-large" style="background-color: {{ service_category.color_code|default:'#6b7280' }}">
                <i class="{{ service_category.icon_class|default:'fas fa-folder' }}"></i>
            </div>
            <div>
                <h1>{{ service_category.name }} Services</h1>
                {% if service_category.description %}
                    <p class="lead mb-0">{{ service_category.description }}</p>
                {% endif %}
                <div class="category-stats">
                    <div class="stat-item">
                        <i class="fas fa-list"></i>
                        <span>{{ total_services }} Services</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{{ venues_with_services }} Venues</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container">
    <!-- Filters Section -->
    <div class="filters-section">
        <form method="GET" class="filter-form">
            <div class="filter-row">
                <div class="location-search">
                    <label class="form-label fw-semibold mb-2">Location</label>
                    <div class="position-relative">
                        <i class="fas fa-map-marker-alt"></i>
                        <input type="text" name="location" value="{{ location }}"
                               placeholder="Enter city, state, or zip..."
                               class="form-control">
                    </div>
                </div>

                <div class="price-filters">
                    <div>
                        <label class="form-label fw-semibold mb-2">Price Range</label>
                        <div class="d-flex gap-2">
                            <input type="number" name="min_price" value="{{ min_price }}"
                                   placeholder="Min $" min="0" step="5">
                            <input type="number" name="max_price" value="{{ max_price }}"
                                   placeholder="Max $" min="0" step="5">
                        </div>
                    </div>
                </div>

                <div class="sort-select">
                    <label class="form-label fw-semibold mb-2">Sort By</label>
                    <select name="sort" onchange="this.form.submit()">
                        <option value="venue_name" {% if sort_by == 'venue_name' %}selected{% endif %}>Venue Name</option>
                        <option value="service_name" {% if sort_by == 'service_name' %}selected{% endif %}>Service Name</option>
                        <option value="price_low" {% if sort_by == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                        <option value="price_high" {% if sort_by == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                    </select>
                </div>

                <div>
                    <label class="form-label fw-semibold mb-2">&nbsp;</label>
                    <button type="submit" class="btn btn-cw-primary d-block">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Services Grid -->
    {% if services %}
        <div class="services-grid">
            {% for service in services %}
                <div class="service-card">
                    <div class="service-image">
                        {% if service.image_url %}
                            <img src="{{ service.image_url }}" alt="{{ service.service_title }}">
                        {% else %}
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <i class="{{ service_category.icon_class|default:'fas fa-spa' }} fa-3x text-white-50"></i>
                            </div>
                        {% endif %}
                        <div class="service-badge">
                            ${{ service.price_min }}{% if service.price_max and service.price_max != service.price_min %} - ${{ service.price_max }}{% endif %}
                        </div>
                    </div>

                    <div class="service-content">
                        <div class="service-header">
                            <h3 class="service-title">{{ service.service_title }}</h3>
                            <div class="venue-name">
                                <i class="fas fa-map-marker-alt"></i>
                                <a href="{% url 'venues_app:venue_detail' service.venue.slug %}" class="text-decoration-none">
                                    {{ service.venue.venue_name }}
                                </a>
                            </div>
                        </div>

                        {% if service.short_description %}
                            <p class="service-description">{{ service.short_description|truncatewords:15 }}</p>
                        {% endif %}

                        <div class="service-details">
                            {% if service.duration_minutes %}
                                <div class="service-detail">
                                    <span class="service-detail-value">{{ service.duration_minutes }} min</span>
                                    <div class="service-detail-label">Duration</div>
                                </div>
                            {% endif %}
                            <div class="service-detail">
                                <span class="service-detail-value">{{ service.venue.city }}, {{ service.venue.state }}</span>
                                <div class="service-detail-label">Location</div>
                            </div>
                        </div>

                        <div class="service-actions">
                            <a href="{% url 'venues_app:service_detail' service.venue.slug service.slug %}"
                               class="btn btn-cw-primary flex-fill">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                            <a href="{% url 'venues_app:venue_detail' service.venue.slug %}"
                               class="btn btn-cw-secondary">
                                <i class="fas fa-store"></i>
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <nav aria-label="Services pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if location %}&location={{ location }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if location %}&location={{ location }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if location %}&location={{ location }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="{{ service_category.icon_class|default:'fas fa-spa' }} fa-4x text-muted"></i>
            </div>
            <h3 class="text-muted">No {{ service_category.name }} Services Found</h3>
            <p class="text-muted mb-4">
                {% if location or min_price or max_price %}
                    Try adjusting your search filters or browse all services.
                {% else %}
                    No services are currently available in this category.
                {% endif %}
            </p>
            {% if location or min_price or max_price %}
                <a href="{% url 'venues_app:services_by_category' service_category.slug %}" class="btn btn-cw-secondary">
                    <i class="fas fa-refresh me-2"></i>Clear Filters
                </a>
            {% endif %}
            <a href="{% url 'venues_app:venue_search' %}" class="btn btn-cw-primary ms-2">
                <i class="fas fa-search me-2"></i>Browse All Services
            </a>
        </div>
    {% endif %}

    <!-- Other Categories -->
    {% if other_categories %}
        <div class="other-categories">
            <div class="text-center">
                <h2 class="h4 text-primary mb-2">Explore Other Categories</h2>
                <p class="text-muted">Discover more wellness and beauty services</p>
            </div>

            <div class="categories-grid">
                {% for category in other_categories %}
                    <a href="{% url 'venues_app:services_by_category' category.slug %}" class="category-card">
                        <div class="category-card-icon" style="background-color: {{ category.color_code|default:'#6b7280' }}">
                            <i class="{{ category.icon_class|default:'fas fa-folder' }}"></i>
                        </div>
                        <div class="category-card-name">{{ category.name }}</div>
                    </a>
                {% endfor %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock content %}
