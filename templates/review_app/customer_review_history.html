{% extends 'review_app/base_review.html' %}

{% block title %}My Reviews{% endblock %}

{% block extra_css %}
{% load static %}
<style>
/* Customer Review History - Professional Black & White Design */
.review-history-card {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.review-history-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.venue-image {
    border-radius: 0.75rem;
    border: 2px solid black;
    object-fit: cover;
    height: 100px;
    width: 100%;
}

.venue-placeholder {
    background: #f8f9fa;
    border: 2px solid black;
    border-radius: 0.75rem;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.review-content h5 a {
    color: black;
    text-decoration: none;
    font-weight: 600;
}

.review-content h5 a:hover {
    color: black;
    opacity: 0.8;
}

.rating-display {
    font-size: 1.1rem;
}

.review-text {
    color: black;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.review-meta {
    color: black;
    opacity: 0.7;
    font-size: 0.9rem;
}

.status-badges .badge {
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

.provider-response-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.75rem;
    padding: 1rem;
}

.customer-response-card {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-top: 0.5rem;
}

.btn-group-vertical .btn {
    margin-bottom: 0.5rem;
    border: 2px solid black;
    font-weight: 500;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

.empty-state {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
}

.empty-state i {
    color: black;
    opacity: 0.3;
}

.empty-state h4 {
    color: black;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    color: black;
    opacity: 0.7;
    margin-bottom: 2rem;
}

/* Filters Panel */
.filters-panel {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-section {
    margin-bottom: 1rem;
}

.filter-section:last-child {
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    color: black;
    margin-bottom: 0.5rem;
    display: block;
}

.filter-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: center;
}

.filter-controls .form-control,
.filter-controls .form-select {
    border: 2px solid black;
    border-radius: 0.5rem;
}

.filter-controls .btn {
    border: 2px solid black;
    font-weight: 500;
}

/* Drafts Sidebar */
.drafts-sidebar {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.draft-item {
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 0;
}

.draft-item:last-child {
    border-bottom: none;
}

.draft-venue {
    font-weight: 600;
    color: black;
    font-size: 0.9rem;
}

.draft-preview {
    color: #6c757d;
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

.draft-actions {
    margin-top: 0.5rem;
}

.draft-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border: 1px solid black;
}

/* Stats Cards */
.stats-card {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: black;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-controls .form-control,
    .filter-controls .form-select,
    .filter-controls .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Page Header with Stats -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>
                <i class="fas fa-star me-2"></i>My Reviews & Drafts
            </h2>
            <p class="text-muted">Manage your venue reviews and saved drafts</p>
        </div>
        <div class="col-md-4">
            <div class="row">
                <div class="col-6">
                    <div class="stats-card">
                        <div class="stats-number">{{ total_reviews }}</div>
                        <div class="stats-label">Published Reviews</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stats-card">
                        <div class="stats-number">{{ total_drafts }}</div>
                        <div class="stats-label">Saved Drafts</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Enhanced Filters Panel -->
            <div class="filters-panel">
                <h5 class="mb-3">
                    <i class="fas fa-filter me-2"></i>Filter Reviews
                </h5>
                <form method="get" id="filterForm">
                    <div class="row">
                        <!-- Search -->
                        <div class="col-md-6 filter-section">
                            <label class="filter-label">Search Venues</label>
                            <div class="filter-controls">
                                <input type="text" name="venue_search" class="form-control"
                                       placeholder="Search by venue or business name..."
                                       value="{{ current_venue_search }}">
                            </div>
                        </div>

                        <!-- Rating Filter -->
                        <div class="col-md-6 filter-section">
                            <label class="filter-label">Rating</label>
                            <div class="filter-controls">
                                <select name="rating" class="form-select">
                                    <option value="">All Ratings</option>
                                    {% for value, label in rating_choices %}
                                        <option value="{{ value }}" {% if current_rating == value|stringformat:'s' %}selected{% endif %}>
                                            {{ label }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- Date Range -->
                        <div class="col-md-6 filter-section">
                            <label class="filter-label">Date Range</label>
                            <div class="filter-controls">
                                <input type="date" name="date_from" class="form-control"
                                       value="{{ current_date_from }}" placeholder="From">
                                <input type="date" name="date_to" class="form-control"
                                       value="{{ current_date_to }}" placeholder="To">
                            </div>
                        </div>

                        <!-- Status Filter -->
                        <div class="col-md-6 filter-section">
                            <label class="filter-label">Status</label>
                            <div class="filter-controls">
                                <select name="status" class="form-select">
                                    {% for value, label in status_choices %}
                                        <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>
                                            {{ label }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- Sort and Controls -->
                        <div class="col-12 filter-section">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="filter-controls">
                                    <label class="filter-label">Sort by</label>
                                    <select name="sort" class="form-select">
                                        {% for value, label in sort_choices %}
                                            <option value="{{ value }}" {% if current_sort == value %}selected{% endif %}>
                                                {{ label }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                    <label class="filter-label">Per page</label>
                                    <select name="page_size" class="form-select">
                                        {% for size in page_size_choices %}
                                            <option value="{{ size }}" {% if current_page_size == size %}selected{% endif %}>
                                                {{ size }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="filter-controls">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Apply Filters
                                    </button>
                                    <a href="{% url 'review_app:customer_review_history' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Results Summary -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h5 class="mb-0">Reviews ({{ reviews.paginator.count }} found)</h5>
                </div>
                {% if reviews.has_other_pages %}
                <div class="text-muted small">
                    Showing {{ reviews.start_index }} - {{ reviews.end_index }} of {{ reviews.paginator.count }}
                </div>
                {% endif %}
            </div>

            {% if reviews.object_list %}
                <!-- Reviews List -->
                {% for review in reviews %}
                <div class="review-history-card">
                    <div class="card-body p-4">
                        <div class="row">
                            <!-- Venue Image -->
                            <div class="col-md-2">
                                {% if review.venue.main_image %}
                                    <img src="{{ review.venue.main_image.url }}" alt="{{ review.venue.venue_name }}"
                                         class="venue-image">
                                {% else %}
                                    <div class="venue-placeholder">
                                        <i class="fas fa-image fa-2x text-muted"></i>
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Review Content -->
                            <div class="col-md-7 review-content">
                                <!-- Review Card Content -->
                                <div class="review-card-content">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="review-header-info">
                                            <h6 class="venue-name mb-1">
                                                <a href="{% url 'venues_app:venue_detail' review.venue.slug %}" class="text-decoration-none">
                                                    {{ review.venue.venue_name }}
                                                </a>
                                            </h6>
                                            <p class="provider-name text-muted mb-2">{{ review.venue.service_provider.business_name }}</p>

                                            <!-- Enhanced Status Badges -->
                                            {% include 'review_app/components/review_status_badges.html' with review=review show_verified=False %}
                                        </div>

                                        <div class="review-actions">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{% url 'review_app:edit_review' review.slug %}">
                                                            <i class="fas fa-edit me-2"></i>Edit Review
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{% url 'venues_app:venue_detail' review.venue.slug %}">
                                                            <i class="fas fa-eye me-2"></i>View Venue
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <a class="dropdown-item text-danger" href="{% url 'review_app:delete_review' review.slug %}">
                                                            <i class="fas fa-trash me-2"></i>Delete Review
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Rating Display -->
                                    <div class="rating-display mb-3">
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="text-warning stars">
                                                {% for i in "12345" %}
                                                    {% if forloop.counter <= review.rating %}
                                                        <i class="fas fa-star"></i>
                                                    {% else %}
                                                        <i class="far fa-star"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                            <span class="rating-text fw-bold">{{ review.rating }}/5</span>

                                            <!-- Helpful votes summary -->
                                            {% with stats=review.get_helpfulness_stats %}
                                                {% if stats.total_votes > 0 %}
                                                    <span class="helpful-summary text-muted small ms-2">
                                                        <i class="fas fa-thumbs-up me-1"></i>
                                                        {{ stats.helpful_votes }}/{{ stats.total_votes }} helpful
                                                    </span>
                                                {% endif %}
                                            {% endwith %}
                                        </div>
                                    </div>

                                    <!-- Review Text -->
                                    {% if review.written_review %}
                                        <div class="review-text mb-3">
                                            <p class="mb-0">{{ review.written_review|truncatewords:30 }}</p>
                                            {% if review.written_review|wordcount > 30 %}
                                                <a href="{% url 'review_app:edit_review' review.slug %}" class="small text-primary">
                                                    Read full review →
                                                </a>
                                            {% endif %}
                                        </div>
                                    {% endif %}

                                    <!-- Enhanced Review Metadata -->
                                    <div class="review-metadata">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-muted d-block">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    Posted {{ review.created_at|date:"M d, Y" }}
                                                    <span class="time-ago">({{ review.created_at|timesince }} ago)</span>
                                                </small>

                                                {% if review.updated_at != review.created_at %}
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-history me-1"></i>
                                                        Last edited {{ review.updated_at|date:"M d, Y" }}
                                                    </small>
                                                {% endif %}
                                            </div>

                                            <div class="col-md-6 text-end">
                                                {% if review.response %}
                                                    <small class="text-success d-block">
                                                        <i class="fas fa-reply me-1"></i>
                                                        Provider responded
                                                    </small>
                                                {% endif %}

                                                <small class="text-muted d-block">
                                                    <i class="fas fa-eye me-1"></i>
                                                    <a href="{% url 'review_app:venue_reviews' review.venue.id %}" class="text-muted">
                                                        View all reviews
                                                    </a>
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Provider Response Preview -->
                                    {% if review.response %}
                                        <div class="provider-response-preview mt-3">
                                            <div class="alert alert-light">
                                                <h6 class="alert-heading small mb-2">
                                                    <i class="fas fa-reply me-1"></i>
                                                    Response from {{ review.venue.service_provider.business_name }}
                                                </h6>
                                                <p class="mb-1 small">{{ review.response.response_text|truncatewords:20 }}</p>
                                                <small class="text-muted">{{ review.response.created_at|date:"M d, Y" }}</small>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="col-md-3">
                                <div class="btn-group-vertical w-100">
                                    <a href="{% url 'review_app:venue_reviews' review.venue.id %}"
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View on Venue Page
                                    </a>
                                    {% if review.can_be_edited %}
                                        <a href="{% url 'review_app:edit_review' review.slug %}"
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-edit me-1"></i>Edit Review
                                        </a>
                                        <a href="{% url 'review_app:delete_review' review.slug %}"
                                           class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash me-1"></i>Delete Review
                                        </a>
                                    {% else %}
                                        <small class="text-muted text-center p-2">
                                            <i class="fas fa-clock me-1"></i>
                                            Edit time expired
                                        </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- Pagination -->
                {% if reviews.has_other_pages %}
                <nav aria-label="Reviews pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if reviews.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ reviews.previous_page_number }}">
                                    <i class="fas fa-chevron-left me-1"></i>Previous
                                </a>
                            </li>
                        {% endif %}

                        {% for num in reviews.paginator.page_range %}
                            {% if reviews.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > reviews.number|add:'-3' and num < reviews.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if reviews.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ reviews.next_page_number }}">
                                    Next<i class="fas fa-chevron-right ms-1"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- Empty State -->
                <div class="empty-state">
                    <i class="fas fa-star fa-4x mb-3"></i>
                    <h4>No Reviews Found</h4>
                    <p>You haven't written any reviews yet, or no reviews match your current filters.</p>
                    {% if drafts %}
                        <p>You have {{ drafts|length }} saved draft{{ drafts|length|pluralize }} waiting to be completed.</p>
                    {% endif %}
                    <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Find Venues to Review
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            {% if drafts %}
            <!-- Drafts Management -->
            <div class="drafts-sidebar">
                <h5 class="mb-3">
                    <i class="fas fa-edit me-2"></i>Saved Drafts ({{ total_drafts }})
                </h5>
                {% for draft in drafts %}
                    <div class="draft-item">
                        <div class="draft-venue">{{ draft.venue.venue_name }}</div>
                        <div class="draft-preview">
                            {% if draft.rating %}
                                {{ draft.rating }}/5 stars •
                            {% else %}
                                No rating yet •
                            {% endif %}
                            {% if draft.written_review %}
                                {{ draft.written_review|truncatewords:10 }}
                            {% else %}
                                No content yet
                            {% endif %}
                        </div>
                        <div class="draft-actions">
                            <a href="{% url 'review_app:edit_review_draft' draft.id %}"
                               class="btn btn-primary btn-sm">
                                <i class="fas fa-edit me-1"></i>Continue
                            </a>
                            {% if draft.can_be_converted_to_review %}
                                <a href="{% url 'review_app:publish_review_draft' draft.id %}"
                                   class="btn btn-success btn-sm">
                                    <i class="fas fa-paper-plane me-1"></i>Publish
                                </a>
                            {% endif %}
                            <a href="{% url 'review_app:delete_review_draft' draft.id %}"
                               class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-trash me-1"></i>Delete
                            </a>
                        </div>
                    </div>
                {% endfor %}
                {% if total_drafts > 5 %}
                    <div class="text-center mt-3">
                        <small class="text-muted">Showing 5 of {{ total_drafts }} drafts</small>
                    </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Quick Actions -->
            <div class="drafts-sidebar">
                <h5 class="mb-3">
                    <i class="fas fa-lightning-bolt me-2"></i>Quick Actions
                </h5>
                <div class="d-grid gap-2">
                    <a href="{% url 'venues_app:venue_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>Find New Venues
                    </a>
                    <a href="{% url 'booking_cart_app:customer_booking_history' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-calendar-alt me-2"></i>My Bookings
                    </a>
                    <a href="{% url 'dashboard_app:customer_dashboard' %}" class="btn btn-outline-info">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Response Modal -->
<div class="modal fade" id="customerResponseModal" tabindex="-1" aria-labelledby="customerResponseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerResponseModalLabel">
                    <i class="fas fa-reply me-2"></i>Respond to Provider
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="customerResponseForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="responseText" class="form-label">Your Response</label>
                        <textarea class="form-control" id="responseText" name="response_text" rows="3"
                                  placeholder="Keep your response professional and constructive..."
                                  maxlength="300" required></textarea>
                        <div class="form-text">
                            <span id="responseCharCount">0</span>/300 characters
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitResponseBtn">
                    <i class="fas fa-paper-plane me-1"></i>Submit Response
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit filter form on select changes
    const filterForm = document.getElementById('filterForm');
    const selectInputs = filterForm.querySelectorAll('select:not([name="sort"]):not([name="page_size"])');

    selectInputs.forEach(select => {
        select.addEventListener('change', function() {
            filterForm.submit();
        });
    });

    // Customer response modal functionality
    const responseModal = new bootstrap.Modal(document.getElementById('customerResponseModal'));
    const responseForm = document.getElementById('customerResponseForm');
    const responseTextarea = document.getElementById('responseText');
    const charCount = document.getElementById('responseCharCount');
    const submitBtn = document.getElementById('submitResponseBtn');
    let currentResponseId = null;

    // Character counter
    responseTextarea.addEventListener('input', function() {
        const count = this.value.length;
        charCount.textContent = count;

        if (count > 300) {
            charCount.style.color = '#dc3545';
        } else if (count > 250) {
            charCount.style.color = '#fd7e14';
        } else {
            charCount.style.color = '#6c757d';
        }
    });

    // Handle respond to provider buttons
    document.querySelectorAll('.respond-to-provider-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            currentResponseId = this.dataset.responseId;
            responseTextarea.value = '';
            charCount.textContent = '0';
            responseModal.show();
        });
    });

    // Submit customer response
    submitBtn.addEventListener('click', function() {
        if (!currentResponseId || !responseTextarea.value.trim()) {
            alert('Please enter a response.');
            return;
        }

        const formData = new FormData();
        formData.append('response_text', responseTextarea.value.trim());
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch(`{% url 'review_app:submit_customer_response' 0 %}`.replace('0', currentResponseId), {
            method: 'POST',
            body: formData,
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                responseModal.hide();
                location.reload(); // Refresh to show the new response
            } else {
                alert('Error submitting response: ' + (data.errors?.response_text?.[0] || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error submitting response. Please try again.');
        });
    });

    // Reset modal when closed
    document.getElementById('customerResponseModal').addEventListener('hidden.bs.modal', function() {
        currentResponseId = null;
        responseTextarea.value = '';
        charCount.textContent = '0';
        charCount.style.color = '#6c757d';
    });
});
</script>
{% endblock %}
