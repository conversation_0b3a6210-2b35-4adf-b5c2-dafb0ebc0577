{% extends 'review_app/base_review.html' %}

{% block title %}
    Resolve Flag - Admin Panel
{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'review_app:admin_review_moderation' %}">Review Moderation</a></li>
            <li class="breadcrumb-item active" aria-current="page">Resolve Flag</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-warning bg-opacity-10">
                    <h3 class="mb-0">
                        <i class="fas fa-flag me-2"></i>
                        Resolve Review Flag
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Flag Information -->
                    <div class="card bg-light mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Flag Details</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Flagged by:</strong> {{ flag.flagged_by.email }}</p>
                                    <p><strong>Reason:</strong> {{ flag.reason }}</p>
                                    <p><strong>Status:</strong>
                                        <span class="badge bg-warning">{{ flag.get_status_display }}</span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Flagged on:</strong> {{ flag.created_at|date:"F d, Y g:i A" }}</p>
                                    {% if flag.reviewed_by %}
                                        <p><strong>Reviewed by:</strong> {{ flag.reviewed_by.email }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Flagged Review -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Flagged Review</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-start mb-3">
                                <div class="me-3">
                                    <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="mb-0">{{ flag.review.customer.email }}</h6>
                                        <div class="text-warning">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= flag.review.rating %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <p class="mb-2">{{ flag.review.written_review }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ flag.review.created_at|date:"F d, Y g:i A" }}
                                    </small>
                                </div>
                            </div>

                            <!-- Venue Info -->
                            <div class="border-top pt-3">
                                <div class="d-flex align-items-center">
                                    <div>
                                        <h6 class="mb-0">{{ flag.review.venue.venue_name }}</h6>
                                        <small class="text-muted">{{ flag.review.venue.service_provider.business_name }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resolution Form -->
                    <form method="post">
                        {% csrf_token %}

                        <!-- Admin Notes -->
                        <div class="mb-4">
                            <label class="form-label fw-bold" for="admin_notes">
                                Admin Notes
                            </label>
                            <textarea name="admin_notes" id="admin_notes" class="form-control" rows="4"
                                      placeholder="Add notes about your decision..."></textarea>
                            <small class="form-text text-muted">
                                These notes will be recorded for audit purposes.
                            </small>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'review_app:admin_review_moderation' %}"
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Moderation
                            </a>
                            <div>
                                <button type="submit" name="action" value="dismiss" class="btn btn-outline-warning me-2">
                                    <i class="fas fa-times me-2"></i>Dismiss Flag
                                </button>
                                <button type="submit" name="action" value="resolve" class="btn btn-success">
                                    <i class="fas fa-check me-2"></i>Resolve Flag
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Resolution Guidelines -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-info-circle me-2"></i>Resolution Guidelines
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="small fw-bold text-success">Resolve Flag:</h6>
                            <ul class="small text-muted">
                                <li>Content violates community guidelines</li>
                                <li>Review contains inappropriate language</li>
                                <li>Review is spam or fake</li>
                                <li>Review contains personal information</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="small fw-bold text-warning">Dismiss Flag:</h6>
                            <ul class="small text-muted">
                                <li>Content is appropriate and follows guidelines</li>
                                <li>Flag was submitted in error</li>
                                <li>Review expresses legitimate concerns</li>
                                <li>No policy violations found</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
