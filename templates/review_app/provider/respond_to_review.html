{% extends 'review_app/base_review.html' %}

{% block title %}
    {% if review.response %}Edit Response{% else %}Respond to Review{% endif %} - {{ review.venue.venue_name }}
{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'review_app:provider_venue_reviews' %}">Venue Reviews</a></li>
            <li class="breadcrumb-item active" aria-current="page">
                {% if review.response %}Edit Response{% else %}Respond to Review{% endif %}
            </li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h3 class="mb-0">
                        <i class="fas fa-reply me-2"></i>
                        {% if review.response %}Edit Your Response{% else %}Respond to Review{% endif %}
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Original Review -->
                    <div class="card bg-light mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Customer Review</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-start mb-3">
                                <div class="me-3">
                                    {% if review.customer.customerprofile.profile_picture %}
                                        <img src="{{ review.customer.customerprofile.profile_picture.url }}"
                                             alt="Customer" class="rounded-circle" style="width: 50px; height: 50px;">
                                    {% else %}
                                        <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="mb-0">{{ review.customer.customerprofile.get_full_name|default:"Anonymous Customer" }}</h6>
                                        <div class="text-warning">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= review.rating %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <p class="mb-2">{{ review.written_review }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ review.created_at|date:"F d, Y g:i A" }}
                                    </small>
                                </div>
                            </div>

                            <!-- Venue Info -->
                            <div class="border-top pt-3">
                                <div class="d-flex align-items-center">
                                    {% if review.venue.main_image %}
                                        <img src="{{ review.venue.main_image.url }}" alt="{{ review.venue.venue_name }}"
                                             class="rounded me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                    {% endif %}
                                    <div>
                                        <h6 class="mb-0">{{ review.venue.venue_name }}</h6>
                                        <small class="text-muted">{{ review.venue.service_provider.business_name }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Response Form -->
                    <form method="post">
                        {% csrf_token %}

                        <!-- Response Text -->
                        <div class="mb-4">
                            <label class="form-label fw-bold" for="{{ form.response_text.id_for_label }}">
                                {{ form.response_text.label }}
                            </label>
                            {{ form.response_text|add_class:"form-control" }}
                            {% if form.response_text.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.response_text.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.response_text.help_text %}
                                <small class="form-text text-muted">{{ form.response_text.help_text }}</small>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'review_app:provider_venue_reviews' %}"
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>
                                {% if review.response %}Update Response{% else %}Submit Response{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Response (if editing) -->
            {% if review.response %}
            <div class="card mt-4">
                <div class="card-header bg-info bg-opacity-10">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Current Response
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">{{ review.response.response_text }}</p>
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        Originally responded on {{ review.response.created_at|date:"F d, Y g:i A" }}
                        {% if review.response.updated_at != review.response.created_at %}
                            <span class="ms-2">
                                <i class="fas fa-edit me-1"></i>
                                Last updated {{ review.response.updated_at|date:"F d, Y g:i A" }}
                            </span>
                        {% endif %}
                    </small>
                </div>
            </div>
            {% endif %}

            <!-- Response Guidelines -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lightbulb me-2"></i>Response Guidelines
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="small fw-bold text-success">Best Practices:</h6>
                            <ul class="small text-muted">
                                <li>Thank the customer for their feedback</li>
                                <li>Address specific concerns mentioned</li>
                                <li>Keep responses professional and courteous</li>
                                <li>Offer solutions or improvements</li>
                                <li>Invite them to contact you directly if needed</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="small fw-bold text-warning">Avoid:</h6>
                            <ul class="small text-muted">
                                <li>Defensive or argumentative language</li>
                                <li>Sharing private customer information</li>
                                <li>Making excuses without solutions</li>
                                <li>Generic copy-paste responses</li>
                                <li>Ignoring the customer's concerns</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sample Responses -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-comments me-2"></i>Sample Response Templates
                    </h6>
                    <div class="accordion" id="responseTemplates">
                        <!-- Positive Review Response -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="positiveHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#positiveCollapse" aria-expanded="false" aria-controls="positiveCollapse">
                                    For Positive Reviews (4-5 stars)
                                </button>
                            </h2>
                            <div id="positiveCollapse" class="accordion-collapse collapse" aria-labelledby="positiveHeading"
                                 data-bs-parent="#responseTemplates">
                                <div class="accordion-body small">
                                    "Thank you so much for taking the time to share your wonderful experience! We're thrilled that you enjoyed [specific service/aspect]. Your feedback means the world to us and motivates our team to continue providing excellent service. We look forward to welcoming you back soon!"
                                </div>
                            </div>
                        </div>

                        <!-- Negative Review Response -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="negativeHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#negativeCollapse" aria-expanded="false" aria-controls="negativeCollapse">
                                    For Negative Reviews (1-2 stars)
                                </button>
                            </h2>
                            <div id="negativeCollapse" class="accordion-collapse collapse" aria-labelledby="negativeHeading"
                                 data-bs-parent="#responseTemplates">
                                <div class="accordion-body small">
                                    "Thank you for bringing this to our attention. We sincerely apologize that your experience didn't meet your expectations. We take all feedback seriously and are committed to improving. Please contact us directly at [contact info] so we can discuss this further and make things right."
                                </div>
                            </div>
                        </div>

                        <!-- Mixed Review Response -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="mixedHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#mixedCollapse" aria-expanded="false" aria-controls="mixedCollapse">
                                    For Mixed Reviews (3 stars)
                                </button>
                            </h2>
                            <div id="mixedCollapse" class="accordion-collapse collapse" aria-labelledby="mixedHeading"
                                 data-bs-parent="#responseTemplates">
                                <div class="accordion-body small">
                                    "Thank you for your honest feedback. We're glad you enjoyed [positive aspects] and appreciate you pointing out areas where we can improve. We've taken note of your concerns about [specific issue] and are working to address this. We'd love the opportunity to provide you with a better experience in the future."
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
