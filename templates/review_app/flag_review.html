{% extends 'review_app/base_review.html' %}

{% block title %}Flag Review - {{ review.venue.venue_name }}{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_list' %}">Venues</a></li>
            <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_detail' review.venue.slug %}">{{ review.venue.venue_name }}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'review_app:venue_reviews' review.venue.id %}">Reviews</a></li>
            <li class="breadcrumb-item active" aria-current="page">Flag Review</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning bg-opacity-10">
                    <h3 class="mb-0">
                        <i class="fas fa-flag me-2 text-warning"></i>Flag Inappropriate Review
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Review Being Flagged -->
                    <div class="card bg-light mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Review Being Flagged</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-start mb-3">
                                <div class="me-3">
                                    {% if review.customer.customerprofile.profile_picture %}
                                        <img src="{{ review.customer.customerprofile.profile_picture.url }}"
                                             alt="Profile" class="rounded-circle" style="width: 50px; height: 50px;">
                                    {% else %}
                                        <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <h6 class="mb-0 me-3">{{ review.customer.customerprofile.get_full_name|default:"Anonymous Customer" }}</h6>
                                        <div class="text-warning">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= review.rating %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <p class="mb-2">{{ review.written_review }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ review.created_at|date:"F d, Y" }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Flag Form -->
                    <form method="post">
                        {% csrf_token %}

                        <!-- Reason for Flagging -->
                        <div class="mb-4">
                            <label class="form-label fw-bold" for="{{ form.reason.id_for_label }}">
                                {{ form.reason.label }}
                            </label>
                            {{ form.reason|add_class:"form-select" }}
                            {% if form.reason.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.reason.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.reason.help_text %}
                                <small class="form-text text-muted">{{ form.reason.help_text }}</small>
                            {% endif %}
                        </div>

                        <!-- Additional Details -->
                        <div class="mb-4">
                            <label class="form-label fw-bold" for="{{ form.reason_text.id_for_label }}">
                                {{ form.reason_text.label }}
                            </label>
                            {{ form.reason_text|add_class:"form-control" }}
                            {% if form.reason_text.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.reason_text.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.reason_text.help_text %}
                                <small class="form-text text-muted">{{ form.reason_text.help_text }}</small>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'review_app:venue_reviews' review.venue.id %}"
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-flag me-2"></i>Submit Flag
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Flagging Information -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-info-circle me-2"></i>About Flagging Reviews
                    </h6>
                    <ul class="mb-0 small text-muted">
                        <li>Flagged reviews are reviewed by our moderation team</li>
                        <li>Reviews remain visible while under review</li>
                        <li>False flagging may result in account restrictions</li>
                        <li>Only flag reviews that violate our community guidelines</li>
                        <li>We typically review flagged content within 24-48 hours</li>
                    </ul>
                </div>
            </div>

            <!-- Community Guidelines -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-shield-alt me-2"></i>Community Guidelines
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="small fw-bold text-success">Appropriate Reviews:</h6>
                            <ul class="small text-muted">
                                <li>Honest personal experiences</li>
                                <li>Constructive feedback</li>
                                <li>Professional language</li>
                                <li>Factual information</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="small fw-bold text-danger">Inappropriate Reviews:</h6>
                            <ul class="small text-muted">
                                <li>Fake or spam content</li>
                                <li>Offensive language</li>
                                <li>Personal attacks</li>
                                <li>Irrelevant content</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
