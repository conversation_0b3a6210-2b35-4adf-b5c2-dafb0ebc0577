{% extends 'review_app/base_review.html' %}
{% load i18n %}

{% block page_title %}{% trans "Delete Review" %}{% endblock %}

{% block extra_css %}
<style>
    .delete-confirmation-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.95));
        border-radius: 15px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        margin-bottom: 2rem;
    }

    .warning-icon {
        font-size: 3rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }

    .review-preview {
        background-color: rgba(248, 249, 250, 0.8);
        border-radius: 10px;
        padding: 1.5rem;
        border: 1px solid rgba(0, 0, 0, 0.1);
        margin: 1.5rem 0;
    }

    .venue-info {
        background-color: white;
        border-radius: 10px;
        padding: 1rem;
        border: 1px solid rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
    }
</style>
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="delete-confirmation-card">
                <div class="card-body p-5 text-center">
                    <i class="fas fa-exclamation-triangle warning-icon"></i>
                    <h2 class="mb-4">{% trans "Delete Review" %}</h2>
                    <p class="lead mb-4">{% trans "Are you sure you want to delete this review? This action cannot be undone." %}</p>

                    <!-- Time Remaining Alert -->
                    {% if time_remaining %}
                    <div class="alert alert-warning mb-4">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="fas fa-clock me-2"></i>
                            <div class="text-center">
                                <strong>{% trans "Time Remaining to Delete:" %}</strong>
                                {% if time_remaining.hours > 0 %}
                                    {{ time_remaining.hours }} hour{{ time_remaining.hours|pluralize }} and
                                {% endif %}
                                {{ time_remaining.minutes }} minute{{ time_remaining.minutes|pluralize }}
                                <br>
                                <small class="text-muted">{% trans "Deadline:" %} {{ edit_deadline|date:"F d, Y g:i A" }}</small>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Venue Information -->
                    <div class="venue-info text-start">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                {% if review.venue.main_image %}
                                    <img src="{{ review.venue.main_image.url }}"
                                         alt="{{ review.venue.venue_name }}"
                                         class="img-fluid rounded" style="max-height: 80px;">
                                {% else %}
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                         style="height: 80px;">
                                        <i class="fas fa-image fa-2x text-muted"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-9">
                                <h5 class="mb-1">{{ review.venue.venue_name }}</h5>
                                <p class="text-muted mb-0">{{ review.venue.service_provider.business_name }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Review Preview -->
                    <div class="review-preview text-start">
                        <h6 class="mb-3">{% trans "Your Review:" %}</h6>

                        <!-- Rating -->
                        <div class="d-flex align-items-center mb-3">
                            <div class="text-warning me-2">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span>{{ review.rating }} out of 5 stars</span>
                        </div>

                        <!-- Written Review -->
                        {% if review.written_review %}
                            <div class="mb-3">
                                <p class="mb-0">"{{ review.written_review }}"</p>
                            </div>
                        {% else %}
                            <div class="mb-3">
                                <p class="text-muted mb-0"><em>{% trans "No written review provided" %}</em></p>
                            </div>
                        {% endif %}

                        <!-- Review Date -->
                        <div class="text-muted small">
                            <i class="fas fa-calendar me-1"></i>
                            {% trans "Reviewed on" %} {{ review.created_at|date:"F d, Y" }}
                            {% if review.updated_at != review.created_at %}
                                <span class="ms-2">
                                    <i class="fas fa-edit me-1"></i>
                                    {% trans "Updated" %} {{ review.updated_at|date:"F d, Y" }}
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Confirmation Form -->
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-flex justify-content-center gap-3">
                            <a href="{% url 'review_app:customer_review_history' %}"
                               class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>{% trans "Delete Review" %}
                            </button>
                        </div>
                    </form>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            {% trans "Once deleted, you will be able to write a new review for this venue." %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
