{% extends 'review_app/base_review.html' %}
{% load static %}
{% load i18n %}

{% block title %}Review Published Successfully - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .success-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    .success-header {
        text-align: center;
        margin-bottom: 3rem;
        padding: 3rem 2rem;
        background: linear-gradient(135deg, #d4edda 0%, #f8f9fa 100%);
        border-radius: 1rem;
        border: 2px solid #28a745;
    }

    .success-icon {
        width: 100px;
        height: 100px;
        background: #28a745;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
        animation: successPulse 2s ease-in-out infinite;
    }

    .success-icon i {
        font-size: 3rem;
        color: white;
    }

    @keyframes successPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .success-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 1rem;
        font-family: var(--font-heading);
    }

    .success-subtitle {
        font-size: 1.2rem;
        color: #6c757d;
        margin-bottom: 0;
    }

    .review-summary-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .review-summary-header {
        border-bottom: 2px solid #f8f9fa;
        padding-bottom: 1rem;
        margin-bottom: 1.5rem;
    }

    .review-rating {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .review-text {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #495057;
        font-style: italic;
        margin-bottom: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.5rem;
        border-left: 4px solid #28a745;
    }

    .venue-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.5rem;
    }

    .venue-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 0.5rem;
        border: 2px solid black;
    }

    .venue-placeholder {
        width: 80px;
        height: 80px;
        background: #e9ecef;
        border-radius: 0.5rem;
        border: 2px solid black;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .next-steps {
        display: grid;
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .step-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .step-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .step-icon {
        width: 60px;
        height: 60px;
        background: #007bff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
    }

    .step-icon i {
        font-size: 1.5rem;
        color: white;
    }

    .step-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: black;
    }

    .step-description {
        color: #6c757d;
        font-size: 0.95rem;
    }

    .sharing-section {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        margin: 2rem 0;
    }

    .share-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .share-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.5rem;
        color: white;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
    }

    .share-btn.facebook { background: #3b5998; }
    .share-btn.twitter { background: #1da1f2; }
    .share-btn.linkedin { background: #0077b5; }
    .share-btn.copy { background: #6c757d; }

    .share-btn:hover {
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }

    .primary-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 2rem;
    }

    .btn-success-primary {
        background: #28a745;
        border: 2px solid #28a745;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .btn-success-primary:hover {
        background: #218838;
        border-color: #218838;
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }

    .btn-outline-primary {
        background: white;
        border: 2px solid #007bff;
        color: #007bff;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .btn-outline-primary:hover {
        background: #007bff;
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
    }

    @media (min-width: 768px) {
        .next-steps {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (min-width: 992px) {
        .next-steps {
            grid-template-columns: repeat(3, 1fr);
        }
    }
</style>
{% endblock %}

{% block review_content %}
<div class="success-container">
    <!-- Success Header -->
    <div class="success-header">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <h1 class="success-title">Review Published!</h1>
        <p class="success-subtitle">
            Thank you for sharing your experience. Your review helps other customers make informed decisions.
        </p>
    </div>

    <!-- Review Summary -->
    <div class="review-summary-card">
        <div class="review-summary-header">
            <h4 class="mb-0">
                <i class="fas fa-star me-2"></i>Your Review Summary
            </h4>
        </div>

        <!-- Venue Info -->
        <div class="venue-info">
            {% if review.venue.main_image %}
                <img src="{{ review.venue.main_image.url }}"
                     alt="{{ review.venue.venue_name }}"
                     class="venue-image">
            {% else %}
                <div class="venue-placeholder">
                    <i class="fas fa-image fa-2x text-muted"></i>
                </div>
            {% endif %}
            <div>
                <h5 class="mb-1">{{ review.venue.venue_name }}</h5>
                <p class="text-muted mb-0">{{ review.venue.service_provider.business_name }}</p>
            </div>
        </div>

        <!-- Rating -->
        <div class="review-rating">
            <div class="d-flex align-items-center gap-2">
                <div class="text-warning">
                    {% for i in "12345" %}
                        {% if forloop.counter <= review.rating %}
                            <i class="fas fa-star"></i>
                        {% else %}
                            <i class="far fa-star"></i>
                        {% endif %}
                    {% endfor %}
                </div>
                <span class="fw-bold">{{ review.rating }} out of 5 stars</span>
            </div>
        </div>

        <!-- Review Text -->
        {% if review.written_review %}
        <div class="review-text">
            "{{ review.written_review }}"
        </div>
        {% endif %}

        <!-- Review Meta -->
        <div class="text-muted small">
            <i class="fas fa-calendar me-1"></i>
            Published on {{ review.created_at|date:"F d, Y g:i A" }}
        </div>
    </div>

    <!-- Sharing Section -->
    <div class="sharing-section">
        <h5 class="mb-3">
            <i class="fas fa-share-alt me-2"></i>Share Your Experience
        </h5>
        <p class="text-muted mb-0">Let your friends know about this great venue!</p>

        <div class="share-buttons">
            <a href="#" class="share-btn facebook" onclick="shareOnFacebook()">
                <i class="fab fa-facebook-f"></i>Facebook
            </a>
            <a href="#" class="share-btn twitter" onclick="shareOnTwitter()">
                <i class="fab fa-twitter"></i>Twitter
            </a>
            <a href="#" class="share-btn linkedin" onclick="shareOnLinkedIn()">
                <i class="fab fa-linkedin-in"></i>LinkedIn
            </a>
            <a href="#" class="share-btn copy" onclick="copyReviewLink()">
                <i class="fas fa-link"></i>Copy Link
            </a>
        </div>
    </div>

    <!-- Next Steps -->
    <div class="next-steps">
        <a href="{% url 'review_app:venue_reviews' review.venue.id %}" class="step-card text-decoration-none">
            <div class="step-icon">
                <i class="fas fa-comments"></i>
            </div>
            <h6 class="step-title">View All Reviews</h6>
            <p class="step-description">See how other customers rated this venue</p>
        </a>

        <a href="{% url 'review_app:customer_review_history' %}" class="step-card text-decoration-none">
            <div class="step-icon">
                <i class="fas fa-history"></i>
            </div>
            <h6 class="step-title">Your Review History</h6>
            <p class="step-description">Manage all your reviews in one place</p>
        </a>

        <a href="{% url 'venues_app:venue_list' %}" class="step-card text-decoration-none">
            <div class="step-icon">
                <i class="fas fa-search"></i>
            </div>
            <h6 class="step-title">Discover More Venues</h6>
            <p class="step-description">Find your next amazing experience</p>
        </a>
    </div>

    <!-- Primary Actions -->
    <div class="primary-actions">
        <a href="{% url 'venues_app:venue_detail' review.venue.slug %}" class="btn-success-primary">
            <i class="fas fa-arrow-left me-2"></i>Back to Venue
        </a>
        <a href="{% url 'review_app:edit_review' review.slug %}" class="btn-outline-primary">
            <i class="fas fa-edit me-2"></i>Edit Review
        </a>
    </div>
</div>

<script>
// Sharing functionality
function shareOnFacebook() {
    const url = encodeURIComponent(window.location.origin + "{% url 'venues_app:venue_detail' review.venue.slug %}");
    const text = encodeURIComponent("I just reviewed {{ review.venue.venue_name }} on CozyWish!");
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank', 'width=600,height=400');
}

function shareOnTwitter() {
    const url = encodeURIComponent(window.location.origin + "{% url 'venues_app:venue_detail' review.venue.slug %}");
    const text = encodeURIComponent("I just gave {{ review.venue.venue_name }} {{ review.rating }} stars on @CozyWish! #spa #wellness");
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank', 'width=600,height=400');
}

function shareOnLinkedIn() {
    const url = encodeURIComponent(window.location.origin + "{% url 'venues_app:venue_detail' review.venue.slug %}");
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank', 'width=600,height=400');
}

function copyReviewLink() {
    const reviewUrl = window.location.origin + "{% url 'venues_app:venue_detail' review.venue.slug %}";
    navigator.clipboard.writeText(reviewUrl).then(function() {
        // Show success feedback
        const copyBtn = document.querySelector('.share-btn.copy');
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i>Copied!';
        copyBtn.style.background = '#28a745';

        setTimeout(function() {
            copyBtn.innerHTML = originalText;
            copyBtn.style.background = '#6c757d';
        }, 2000);
    }).catch(function() {
        alert('Could not copy link. Please copy manually: ' + reviewUrl);
    });
}

// Auto-scroll to top on page load
window.addEventListener('load', function() {
    window.scrollTo(0, 0);
});
</script>
{% endblock %}
