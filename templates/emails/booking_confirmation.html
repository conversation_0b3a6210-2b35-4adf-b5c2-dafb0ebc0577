<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmed - CozyWish</title>
    <style>
        /* CozyWish Email Design System */
        :root {
            --cw-brand-primary: #2F160F;
            --cw-brand-light: #4a2a1f;
            --cw-brand-accent: #fae1d7;
            --cw-accent-light: #fef7f0;
            --cw-accent-dark: #f1d4c4;
        }

        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #ffffff;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #262626;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .email-header {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }

        .email-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .email-title {
            font-family: 'Playfair Display', Georgia, serif;
            font-size: 28px;
            font-weight: 700;
            color: white;
            margin: 0 0 10px 0;
        }

        .email-body {
            padding: 40px 30px;
        }

        .booking-card {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
        }

        .booking-details {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #10b981;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            color: white !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            margin: 20px 0;
        }

        .email-footer {
            background: #fef7f0;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #fae1d7;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-header, .email-body, .email-footer {
                padding: 20px !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="email-logo">
                ✅
            </div>
            <h1 class="email-title">Booking Confirmed!</h1>
            <p>Your wellness experience awaits</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <p><strong>Hello {{ booking.customer.get_full_name|default:booking.customer.first_name }},</strong></p>

            <p>Great news! Your booking has been confirmed. We can't wait for you to enjoy your wellness experience!</p>

            <div class="booking-card">
                <h3 style="color: #166534; margin-top: 0;">📋 Booking Details</h3>

                <div class="booking-details">
                    <p><strong>Booking Reference:</strong> {{ booking.friendly_id }}</p>
                    <p><strong>Venue:</strong> {{ booking.venue.venue_name }}</p>
                    <p><strong>Date:</strong> {{ booking.booking_date|date:"F j, Y" }}</p>
                    <p><strong>Total Amount:</strong> ${{ booking.total_price }}</p>

                    {% if booking.special_requests %}
                    <p><strong>Special Requests:</strong> {{ booking.special_requests }}</p>
                    {% endif %}
                </div>

                <h4 style="color: #166534;">🔹 Services Booked:</h4>
                {% for item in booking.items.all %}
                <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 6px;">
                    <p><strong>{{ item.service_title }}</strong></p>
                    <p>Date: {{ item.scheduled_date|date:"F j, Y" }} at {{ item.scheduled_time|time:"g:i A" }}</p>
                    <p>Price: ${{ item.service_price }}</p>
                </div>
                {% endfor %}
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ site_url }}/bookings/{{ booking.booking_id }}/" class="cta-button">
                    View Booking Details
                </a>
            </div>

            <div style="background: #fffbeb; border: 1px solid #fed7aa; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h4 style="color: #92400e; margin-top: 0;">📍 Important Information:</h4>
                <ul style="color: #92400e;">
                    <li>Please arrive 15 minutes before your first appointment</li>
                    <li>Bring a valid ID and any relevant health information</li>
                    <li>Cancellations must be made at least 24 hours in advance</li>
                    <li>Contact the venue directly for any special accommodations</li>
                </ul>
            </div>

            <p>If you need to make any changes or have questions, please contact us as soon as possible.</p>

            <p>Thank you for choosing CozyWish. Enjoy your wellness journey! 🌸</p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p><strong>CozyWish</strong> - Your trusted spa and wellness marketplace</p>
            <p style="font-size: 12px; color: #525252;">
                This email was sent to {{ booking.customer.email }} regarding booking {{ booking.friendly_id }}
            </p>
        </div>
    </div>
</body>
</html>
