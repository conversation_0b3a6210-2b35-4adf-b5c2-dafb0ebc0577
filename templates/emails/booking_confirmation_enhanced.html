{% extends "emails/base_email.html" %}

{% block email_title %}Booking Confirmed - {{ booking.friendly_id }}{% endblock %}

{% block header_gradient %}linear-gradient(135deg, #16a34a 0%, #22c55e 100%){% endblock %}
{% block button_gradient %}linear-gradient(135deg, #16a34a 0%, #22c55e 100%){% endblock %}

{% block header_icon %}✅{% endblock %}
{% block header_title %}Booking Confirmed!{% endblock %}
{% block header_subtitle %}Your wellness journey is scheduled{% endblock %}

{% block custom_styles %}
.booking-card {
    background: #f0fdf4;
    border: 2px solid #bbf7d0;
    border-radius: 12px;
    padding: 25px;
    margin: 25px 0;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.1);
}

.booking-id {
    background: #dcfce7;
    color: #166534;
    font-weight: 700;
    font-size: 18px;
    padding: 10px 15px;
    border-radius: 6px;
    text-align: center;
    margin-bottom: 20px;
    border: 1px solid #bbf7d0;
}

.status-badge {
    display: inline-block;
    background: #22c55e;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.date-highlight {
    background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    margin: 15px 0;
}

.date-highlight .day {
    font-size: 24px;
    font-weight: 700;
    color: #92400e;
    display: block;
}

.date-highlight .month-year {
    font-size: 16px;
    color: #d97706;
    font-weight: 600;
}

.service-highlight {
    background: #f8fafc;
    border-left: 4px solid #3b82f6;
    padding: 15px 20px;
    margin: 15px 0;
    border-radius: 0 8px 8px 0;
}

.venue-info {
    background: #fafafa;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.venue-name {
    font-size: 20px;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 10px;
}

.quick-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 20px 0;
}

.quick-action-btn {
    background: #f3f4f6;
    color: #374151;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    background: #e5e7eb;
    transform: translateY(-1px);
}

@media only screen and (max-width: 600px) {
    .quick-actions {
        flex-direction: column;
        align-items: center;
    }

    .quick-action-btn {
        width: 200px;
        text-align: center;
    }
}
{% endblock %}

{% block email_content %}
<p><strong>Hello {{ booking.customer.get_full_name|default:booking.customer.first_name|default:booking.customer.email }},</strong></p>

<p>Great news! Your booking has been confirmed and we can't wait to help you relax and rejuvenate. Here are all the details you need:</p>

<div class="booking-card">
    <div class="booking-id">
        📋 Booking ID: {{ booking.friendly_id }}
    </div>

    <div style="text-align: center;">
        <span class="status-badge">✓ Confirmed</span>
    </div>
</div>
{% endblock %}

{% block action_section %}
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ site_url }}{% url 'booking_cart_app:booking_detail' booking.id %}" class="action-button">
        📅 View Booking Details
    </a>
</div>

<div class="quick-actions">
    <a href="{{ site_url }}{% url 'venues_app:venue_detail' booking.venue.slug %}" class="quick-action-btn">
        🏢 View Venue
    </a>
    <a href="{{ site_url }}{% url 'dashboard_app:customer_dashboard' %}" class="quick-action-btn">
        📊 My Dashboard
    </a>
    <a href="tel:{{ booking.venue.phone|default:'******-0123' }}" class="quick-action-btn">
        📞 Contact Venue
    </a>
</div>
{% endblock %}

{% block details_section %}
<div class="venue-info">
    <div class="venue-name">{{ booking.venue.venue_name }}</div>
    <p><strong>📍 Address:</strong> {{ booking.venue.address }}</p>
    <p><strong>📞 Phone:</strong> {{ booking.venue.phone|default:"Contact via platform" }}</p>
    <p><strong>🌐 Provider:</strong> {{ booking.venue.service_provider.business_name }}</p>
</div>

<div class="details-section">
    <div class="details-title">
        <span>📋</span> Booking Details
    </div>
    <ul class="details-list">
        <li>
            <span class="details-label">Service:</span>
            <span class="details-value">{{ booking.cart_item.venue.venue_name }}</span>
        </li>
        <li>
            <span class="details-label">Date:</span>
            <span class="details-value">{{ booking.booking_date|date:"F j, Y" }}</span>
        </li>
        <li>
            <span class="details-label">Time:</span>
            <span class="details-value">{{ booking.booking_time|time:"g:i A" }}</span>
        </li>
        <li>
            <span class="details-label">Duration:</span>
            <span class="details-value">{{ booking.duration|default:"Standard session" }}</span>
        </li>
        <li>
            <span class="details-label">Total Amount:</span>
            <span class="details-value">${{ booking.total_amount|default:"0.00" }}</span>
        </li>
        <li>
            <span class="details-label">Status:</span>
            <span class="details-value">{{ booking.get_status_display|default:"Confirmed" }}</span>
        </li>
    </ul>
</div>

<div class="date-highlight">
    <span class="day">{{ booking.booking_date|date:"d" }}</span>
    <span class="month-year">{{ booking.booking_date|date:"F Y" }} at {{ booking.booking_time|time:"g:i A" }}</span>
</div>
{% endblock %}

{% block additional_content %}
<div class="info-box success">
    <p><strong>✅ What's Next:</strong></p>
    <ul style="margin: 10px 0 0 20px; padding: 0;">
        <li>You'll receive a reminder 24 hours before your appointment</li>
        <li>Please arrive 10-15 minutes early for check-in</li>
        <li>Bring a valid ID and any specific health information</li>
        <li>Feel free to contact the venue with any special requests</li>
    </ul>
</div>

<div class="info-box info">
    <p><strong>📞 Need to Make Changes?</strong></p>
    <p>If you need to reschedule or cancel your appointment, please do so at least 24 hours in advance to avoid any cancellation fees. You can manage your booking through your dashboard or contact the venue directly.</p>
</div>

<div class="service-highlight">
    <p><strong>🌟 Prepare for Your Visit:</strong></p>
    <p>We recommend arriving relaxed and hydrated. Avoid heavy meals 2 hours before your appointment and let us know about any allergies or health conditions that might affect your treatment.</p>
</div>

<p>We're excited to provide you with an exceptional wellness experience. Thank you for choosing CozyWish!</p>
{% endblock %}
