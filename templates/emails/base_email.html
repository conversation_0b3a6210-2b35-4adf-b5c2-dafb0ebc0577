<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block email_title %}CozyWish Notification{% endblock %}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* CozyWish Email Design System - Base Template */
        :root {
            --cw-brand-primary: #2F160F;
            --cw-brand-light: #4a2a1f;
            --cw-brand-accent: #fae1d7;
            --cw-accent-light: #fef7f0;
            --cw-accent-dark: #f1d4c4;
            --cw-neutral-600: #525252;
            --cw-neutral-700: #404040;
            --cw-neutral-800: #262626;
            --cw-success: #16a34a;
            --cw-warning: #d97706;
            --cw-error: #dc2626;
            --cw-info: #0284c7;
        }

        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        /* Email base styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #ffffff;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--cw-neutral-800);
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        /* Header styles - customizable per email type */
        .email-header {
            padding: 40px 30px;
            text-align: center;
            color: white;
            background: {% block header_gradient %}linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%){% endblock %};
        }

        .email-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .email-title {
            font-family: 'Playfair Display', Georgia, 'Times New Roman', serif;
            font-size: 28px;
            font-weight: 700;
            color: white;
            margin: 0 0 10px 0;
            line-height: 1.2;
        }

        .email-subtitle {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            line-height: 1.5;
        }

        /* Body styles */
        .email-body {
            padding: 40px 30px;
            background: #ffffff;
        }

        .email-content {
            font-size: 16px;
            line-height: 1.6;
            color: var(--cw-neutral-700);
            margin-bottom: 30px;
        }

        .email-content p {
            margin: 0 0 20px 0;
        }

        .email-content strong {
            color: var(--cw-brand-primary);
            font-weight: 600;
        }

        /* Action button styles */
        .action-button {
            display: inline-block;
            background: {% block button_gradient %}linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%){% endblock %};
            color: white !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(47, 22, 15, 0.3);
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(47, 22, 15, 0.4);
        }

        /* Info boxes */
        .info-box {
            background: var(--cw-accent-light);
            border: 1px solid var(--cw-brand-accent);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .info-box.success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #166534;
        }

        .info-box.warning {
            background: #fffbeb;
            border-color: #fed7aa;
            color: #92400e;
        }

        .info-box.error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #991b1b;
        }

        .info-box.info {
            background: #f0f9ff;
            border-color: #bae6fd;
            color: #0c4a6e;
        }

        .info-box strong {
            font-weight: 600;
        }

        /* Details section */
        .details-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .details-title {
            font-weight: 600;
            color: var(--cw-brand-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .details-list {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .details-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .details-list li:last-child {
            border-bottom: none;
        }

        .details-label {
            font-weight: 500;
            color: var(--cw-neutral-600);
        }

        .details-value {
            font-weight: 600;
            color: var(--cw-brand-primary);
        }

        /* Footer styles */
        .email-footer {
            background: var(--cw-accent-light);
            padding: 30px;
            text-align: center;
            border-top: 1px solid var(--cw-brand-accent);
        }

        .email-footer p {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: var(--cw-neutral-600);
        }

        .email-footer .brand-name {
            font-weight: 700;
            color: var(--cw-brand-primary);
            font-size: 16px;
        }

        .social-links {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--cw-brand-primary);
            color: white;
            text-decoration: none;
            border-radius: 50%;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--cw-brand-light);
            transform: translateY(-2px);
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-header,
            .email-body,
            .email-footer {
                padding: 20px !important;
            }

            .email-title {
                font-size: 24px !important;
            }

            .email-logo {
                width: 60px !important;
                height: 60px !important;
                font-size: 1.5rem !important;
            }

            .action-button {
                padding: 14px 24px !important;
                font-size: 14px !important;
            }

            .details-list li {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }
        }

        /* Custom styles per email type */
        {% block custom_styles %}{% endblock %}
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="email-logo">
                {% block header_icon %}🏠{% endblock %}
            </div>
            <h1 class="email-title">{% block header_title %}CozyWish Notification{% endblock %}</h1>
            <p class="email-subtitle">{% block header_subtitle %}Your wellness journey continues{% endblock %}</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <div class="email-content">
                {% block email_content %}
                <p><strong>Hello {% if user %}{{ user.get_full_name|default:user.first_name|default:"there" }}{% else %}there{% endif %},</strong></p>
                <p>Thank you for being part of the CozyWish community!</p>
                {% endblock %}

                {% block action_section %}{% endblock %}

                {% block details_section %}{% endblock %}

                {% block additional_content %}{% endblock %}
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            {% block footer_content %}
            <p>Thanks for using <span class="brand-name">CozyWish</span>!</p>
            <p>Your trusted spa and wellness marketplace</p>
            <p style="margin-top: 20px; font-size: 12px; color: var(--cw-neutral-600);">
                {% if user %}This email was sent to {{ user.email }}.{% endif %}
                If you have any questions, please contact our support team.
            </p>
            {% endblock %}

            {% block social_links %}
            <div class="social-links">
                <a href="#" class="social-link" title="Facebook">📘</a>
                <a href="#" class="social-link" title="Instagram">📷</a>
                <a href="#" class="social-link" title="Twitter">🐦</a>
            </div>
            {% endblock %}
        </div>
    </div>

    {% block tracking_pixels %}
    <!-- Email tracking pixel -->
    {% if email_id %}
    <img src="{{ site_url }}/api/email-tracking/pixel/{{ email_id }}/" width="1" height="1" style="display:none;" alt="">
    {% endif %}
    {% endblock %}
</body>
</html>
