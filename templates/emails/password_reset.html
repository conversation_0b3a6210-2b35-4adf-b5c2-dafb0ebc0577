{% extends "emails/base_email.html" %}

{% block email_title %}Reset Your CozyWish Password{% endblock %}

{% block header_gradient %}linear-gradient(135deg, #dc2626 0%, #ef4444 100%){% endblock %}
{% block button_gradient %}linear-gradient(135deg, #dc2626 0%, #ef4444 100%){% endblock %}

{% block header_icon %}🔐{% endblock %}
{% block header_title %}Password Reset{% endblock %}
{% block header_subtitle %}Secure password reset for your account{% endblock %}

{% block custom_styles %}
.reset-link-box {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    word-break: break-all;
    font-family: monospace;
    font-size: 14px;
    color: #991b1b;
}

.security-warning {
    background: #fffbeb;
    border-left: 4px solid #f59e0b;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 0 8px 8px 0;
}

.account-details {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}
{% endblock %}

{% block email_content %}
<p><strong>Hello {% if user %}{{ user.get_full_name|default:user.first_name|default:"there" }}{% else %}there{% endif %},</strong></p>

<p>You're receiving this email because you requested a password reset for your CozyWish account.</p>

<p>If you did not request this password reset, please ignore this email and your password will remain unchanged.</p>
{% endblock %}

{% block action_section %}
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ reset_url }}" class="action-button">
        🔐 Reset My Password
    </a>
</div>

<p>Or copy and paste this link into your browser:</p>

<div class="reset-link-box">
    {{ reset_url }}
</div>
{% endblock %}

{% block details_section %}
{% if user %}
<div class="account-details">
    <div class="details-title">
        <span>🏠</span> Account Details
    </div>
    <ul class="details-list">
        <li>
            <span class="details-label">Email:</span>
            <span class="details-value">{{ user.email }}</span>
        </li>
        <li>
            <span class="details-label">Account Type:</span>
            <span class="details-value">{{ user.role|default:"Customer"|title }}</span>
        </li>
        <li>
            <span class="details-label">Reset Requested:</span>
            <span class="details-value">{{ timestamp|date:"F j, Y g:i A" }}</span>
        </li>
    </ul>
</div>
{% endif %}

<div class="security-warning">
    <p><strong>⚠️ Security Notice:</strong></p>
    <p>This password reset link will expire in 24 hours for your security. If you don't reset your password within this time, you'll need to request a new reset link.</p>
</div>
{% endblock %}

{% block additional_content %}
<div class="info-box info">
    <p><strong>💡 Password Tips:</strong></p>
    <ul style="margin: 10px 0 0 20px; padding: 0;">
        <li>Use at least 8 characters</li>
        <li>Include uppercase and lowercase letters</li>
        <li>Add numbers and special characters</li>
        <li>Avoid using personal information</li>
    </ul>
</div>

<p>If you're having trouble with the button above, you can also visit our website and use the "Forgot Password" option on the login page.</p>

<p>For additional security questions or support, please contact our customer service team.</p>
{% endblock %}
