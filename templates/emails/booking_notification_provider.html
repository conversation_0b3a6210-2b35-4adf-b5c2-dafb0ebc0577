<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Booking Received - CozyWish Business</title>
    <style>
        /* CozyWish Email Design System */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #ffffff;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #262626;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .email-header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }

        .email-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .email-title {
            font-family: 'Playfair Display', Georgia, serif;
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 10px 0;
        }

        .email-body {
            padding: 40px 30px;
        }

        .booking-card {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
        }

        .booking-details {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #1e40af;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            text-align: center;
            margin: 20px 0;
        }

        .customer-info {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .email-footer {
            background: #f8fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="email-logo">
                📅
            </div>
            <h1 class="email-title">New Booking Received!</h1>
            <p>You have a new customer booking</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <p><strong>Hello {{ booking.venue.service_provider.business_name }},</strong></p>

            <p>Great news! You have received a new booking. Here are the details:</p>

            <div class="booking-card">
                <h3 style="color: #1e40af; margin-top: 0;">📋 Booking Information</h3>

                <div class="booking-details">
                    <p><strong>Booking Reference:</strong> {{ booking.friendly_id }}</p>
                    <p><strong>Venue:</strong> {{ booking.venue.venue_name }}</p>
                    <p><strong>Booking Date:</strong> {{ booking.booking_date|date:"F j, Y" }}</p>
                    <p><strong>Total Amount:</strong> ${{ booking.total_price }}</p>
                    <p><strong>Status:</strong> {{ booking.get_status_display }}</p>

                    {% if booking.special_requests %}
                    <p><strong>Special Requests:</strong> {{ booking.special_requests }}</p>
                    {% endif %}
                </div>

                <h4 style="color: #1e40af;">🔹 Services Booked:</h4>
                {% for item in booking.items.all %}
                <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 6px;">
                    <p><strong>{{ item.service_title }}</strong></p>
                    <p>📅 {{ item.scheduled_date|date:"F j, Y" }} at {{ item.scheduled_time|time:"g:i A" }}</p>
                    <p>💰 ${{ item.service_price }}</p>
                </div>
                {% endfor %}
            </div>

            <div class="customer-info">
                <h4 style="color: #166534; margin-top: 0;">👤 Customer Information</h4>
                <p><strong>Name:</strong> {{ booking.customer.get_full_name|default:booking.customer.first_name }}</p>
                <p><strong>Email:</strong> {{ booking.customer.email }}</p>
                {% if booking.customer.phone %}
                <p><strong>Phone:</strong> {{ booking.customer.phone }}</p>
                {% endif %}
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ site_url }}/provider/bookings/{{ booking.booking_id }}/" class="cta-button">
                    Manage This Booking
                </a>
            </div>

            <div style="background: #fffbeb; border: 1px solid #fed7aa; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h4 style="color: #92400e; margin-top: 0;">⏰ Action Required:</h4>
                <ul style="color: #92400e; margin: 0;">
                    <li>Review the booking details and confirm availability</li>
                    <li>Prepare your space and services for the appointment</li>
                    <li>Contact the customer if you have any questions</li>
                    <li>Update the booking status as needed</li>
                </ul>
            </div>

            <p>This booking contributes to your business growth on CozyWish. Provide excellent service to encourage positive reviews and repeat customers!</p>

            <p>If you have any questions about this booking, please contact our business support team.</p>

            <p>Thank you for being a valued CozyWish partner! 🏢</p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p><strong>CozyWish Business</strong> - Your trusted partner in wellness business growth</p>
            <p style="font-size: 12px; color: #525252;">
                This email was sent to {{ booking.venue.service_provider.user.email }} regarding booking {{ booking.friendly_id }}
            </p>
        </div>
    </div>
</body>
</html>
