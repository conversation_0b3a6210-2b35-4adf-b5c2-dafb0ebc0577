{% load static %}

<div class="avatar-container {{ css_class }}">
    {% if has_avatar and avatar_url %}
        <!-- Django Avatar -->
        <img src="{{ avatar_url }}" 
             alt="{{ user.get_full_name|default:user.email }}" 
             class="avatar-image"
             style="width: {{ size }}px; height: {{ size }}px; border-radius: 50%; object-fit: cover;">
    {% elif fallback_url %}
        <!-- Fallback to profile picture -->
        <img src="{{ fallback_url }}" 
             alt="{{ user.get_full_name|default:user.email }}" 
             class="avatar-image"
             style="width: {{ size }}px; height: {{ size }}px; border-radius: 50%; object-fit: cover;">
    {% else %}
        <!-- Default placeholder -->
        <div class="avatar-placeholder" 
             style="width: {{ size }}px; height: {{ size }}px; border-radius: 50%; background: #fae1d7; display: flex; align-items: center; justify-content: center; color: #2F160F; font-weight: 600;">
            {% if user.first_name %}
                {{ user.first_name.0|upper }}{% if user.last_name %}{{ user.last_name.0|upper }}{% endif %}
            {% else %}
                {{ user.email.0|upper }}
            {% endif %}
        </div>
    {% endif %}
</div>
