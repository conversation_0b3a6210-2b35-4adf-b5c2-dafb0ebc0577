{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}Team Members - {{ profile.business_name }}{% endblock %}

{% block dashboard_title %}Team Members{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'accounts_app:team_member_add' %}" class="btn btn-sm btn-outline-success">
        <i class="fas fa-plus"></i> Add Member
    </a>
    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-user"></i> My Profile
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard_app:provider_dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{% url 'accounts_app:service_provider_profile' %}">My Profile</a></li>
        <li class="breadcrumb-item active" aria-current="page">Team Members</li>
    </ol>
</nav>

<!-- Team Management Container -->
<div class="team-management-container">
    <div class="container">
        <!-- Team Header -->
        <div class="team-header">
            <div>
                <h1 class="team-title">Team Members</h1>
                <p class="team-subtitle">Manage your business team and their roles</p>
            </div>
            <div class="ms-auto">
                <a href="{% url 'accounts_app:team_member_add' %}" class="btn btn-cw-primary">
                    <i class="fas fa-plus"></i>Add Team Member
                </a>
            </div>
        </div>

        <!-- Team Stats -->
        <div class="team-stats-card">
            <div class="team-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ team_members.count }}</span>
                    <span class="stat-label">Total Members</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ active_members_count }}</span>
                    <span class="stat-label">Active Members</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ max_team_members }}</span>
                    <span class="stat-label">Maximum Allowed</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ available_slots }}</span>
                    <span class="stat-label">Available Slots</span>
                </div>
            </div>
        </div>

        <!-- Team Members List -->
        {% if team_members %}
            <div class="row">
                {% for member in team_members %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="team-member-card">
                        <div class="team-member-content">
                            <div class="member-avatar">
                                {% if member.profile_picture %}
                                    <img src="{{ member.profile_picture.url }}" alt="{{ member.get_full_name }}" class="avatar-img">
                                {% else %}
                                    <div class="avatar-placeholder">
                                        {{ member.first_name.0|default:"T" }}{{ member.last_name.0|default:"" }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="member-info">
                                <h4 class="member-name">{{ member.get_full_name }}</h4>
                                <p class="member-role">{{ member.role|default:"Team Member" }}</p>
                                <p class="member-email">{{ member.email }}</p>
                                <div class="member-status">
                                    {% if member.is_active %}
                                        <span class="status-badge active">Active</span>
                                    {% else %}
                                        <span class="status-badge inactive">Inactive</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="team-member-actions">
                            <a href="{% url 'accounts_app:team_member_edit' member.id %}" class="btn-cw-secondary">
                                <i class="fas fa-edit"></i>Edit
                            </a>
                            <button class="btn-cw-danger" onclick="removeTeamMember({{ member.id }})">
                                <i class="fas fa-trash"></i>Remove
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h3>No Team Members Yet</h3>
                <p class="text-muted">Start building your team to help manage your business more effectively.</p>
                <a href="{% url 'accounts_app:team_member_add' %}" class="btn btn-cw-primary">
                    <i class="fas fa-plus me-2"></i>Add Your First Team Member
                </a>
            </div>
        {% endif %}

        <!-- Team Management Tips -->
        <div class="team-tips mt-5">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Team Management Tips
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Assign specific roles to team members for better organization
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Keep team member information up to date
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    Review team member access regularly
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    Maximum {{ max_team_members }} team members allowed
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    Only active members can access the system
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    You can edit member roles and permissions anytime
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Team Member Modal -->
<div class="modal fade" id="removeTeamMemberModal" tabindex="-1" aria-labelledby="removeTeamMemberModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="removeTeamMemberModalLabel">Remove Team Member</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove this team member? This action cannot be undone.</p>
                <p class="text-muted small">The team member will lose access to your business account.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRemove">Remove Member</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Professional sidebar styling with high specificity */
.dashboard-wrapper .dashboard-sidebar {
    width: 280px !important;
    min-width: 280px !important;
}

/* Remove icons and enhance text-only navigation */
.dashboard-wrapper .dashboard-sidebar .nav-link i {
    display: none !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link {
    font-weight: 500 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.5rem !important;
    margin: 0.125rem 1rem !important;
    border: 1px solid transparent !important;
    transition: all 0.2s ease !important;
    text-align: left !important;
}

/* Remove "Create Venue" special background styling */
.dashboard-wrapper .dashboard-sidebar .venue-creation-link {
    background: transparent !important;
    border-left: none !important;
    font-weight: 500 !important;
    margin: 0.125rem 1rem !important;
    border-radius: 0.5rem !important;
    border: 1px solid transparent !important;
}

.dashboard-wrapper .dashboard-sidebar .venue-creation-link:hover {
    background: #f1f5f9 !important;
    color: var(--cw-brand-primary) !important;
    border-color: var(--cw-brand-accent) !important;
    transform: none !important;
    transition: all 0.2s ease !important;
}

/* Remove dashboard button special background */
.dashboard-wrapper .dashboard-sidebar .nav-link.active {
    background: transparent !important;
    color: var(--cw-brand-primary) !important;
    border: 1px solid var(--cw-brand-accent) !important;
    font-weight: 600 !important;
    box-shadow: none !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link:hover {
    background: #f1f5f9 !important;
    color: var(--cw-brand-primary) !important;
    border-color: var(--cw-brand-accent) !important;
}

/* Keep badges and status icons visible */
.dashboard-wrapper .dashboard-sidebar .nav-link .badge {
    font-size: 0.7rem !important;
    display: inline-block !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-check,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-clock,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-times,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-edit {
    display: inline !important;
    opacity: 0.6 !important;
}

/* Adjust main content to accommodate wider sidebar */
.dashboard-wrapper .col-md-9.col-lg-10 {
    flex: 0 0 calc(100% - 280px) !important;
    max-width: calc(100% - 280px) !important;
}

/* CozyWish Design System - Team Management */
:root {
    /* Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Status Colors */
    --cw-success: #059669;
    --cw-warning: #d97706;
    --cw-error: #dc2626;

    /* Neutral Colors */
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;
    --cw-neutral-800: #262626;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Gradients */
    --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    --cw-gradient-hero: linear-gradient(135deg, var(--cw-accent-light) 0%, #ffffff 100%);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--cw-font-heading);
    font-weight: 600;
    color: var(--cw-brand-primary);
    line-height: 1.3;
}

/* Team Management Container */
.team-management-container {
    background: var(--cw-gradient-hero);
    min-height: 100vh;
    padding: 2rem 0;
}

.team-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;
}

.team-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin: 0;
}

.team-subtitle {
    color: var(--cw-neutral-600);
    margin: 0;
    font-size: 1.1rem;
}

/* Team Stats Card */
.team-stats-card {
    background: white;
    border: 1px solid var(--cw-brand-accent);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--cw-shadow-lg);
}

.team-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    display: block;
}

.stat-label {
    color: var(--cw-neutral-600);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Button Styling */
.btn-cw-primary {
    background: var(--cw-gradient-brand-button);
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    color: white;
    transition: all 0.2s ease;
    box-shadow: var(--cw-shadow-sm);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-cw-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
    color: white;
    text-decoration: none;
}

.btn-cw-secondary {
    background: var(--cw-brand-accent);
    border: 2px solid var(--cw-brand-primary);
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    color: var(--cw-brand-primary);
    transition: all 0.2s ease;
    text-decoration: none;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-cw-secondary:hover {
    background: var(--cw-brand-primary);
    color: white;
    text-decoration: none;
}

.btn-cw-danger {
    background: var(--cw-error);
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    color: white;
    transition: all 0.2s ease;
    text-decoration: none;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-cw-danger:hover {
    background: #b91c1c;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Team Member Cards */
.team-member-card {
    background: white;
    border: 1px solid var(--cw-brand-accent);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--cw-shadow-md);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.team-member-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--cw-shadow-lg);
    border-color: var(--cw-brand-primary);
}

.team-member-content {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.member-avatar {
    flex-shrink: 0;
}

.avatar-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--cw-brand-accent);
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--cw-brand-accent);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    border: 3px solid var(--cw-brand-accent);
}

.member-info {
    flex: 1;
}

.member-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 0.5rem;
}

.member-role {
    color: var(--cw-neutral-600);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.member-email {
    color: var(--cw-neutral-600);
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.member-status {
    margin-top: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: var(--cw-success);
    color: white;
}

.status-badge.inactive {
    background: var(--cw-neutral-600);
    color: white;
}

.team-member-actions {
    padding: 1rem 2rem;
    background: var(--cw-accent-light);
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

/* Team Tips */
.team-tips .card {
    border: 1px solid var(--cw-brand-accent);
    border-radius: 1rem;
    box-shadow: var(--cw-shadow-md);
}

.team-tips .card-header {
    background: var(--cw-accent-light);
    border-bottom: 1px solid var(--cw-brand-accent);
    color: var(--cw-brand-primary);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .team-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .team-title {
        font-size: 1.75rem;
    }

    .team-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.75rem;
    }

    .team-member-content {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .team-member-actions {
        flex-direction: column;
        padding: 1rem;
    }

    .btn-cw-secondary,
    .btn-cw-danger {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
let currentMemberId = null;

function removeTeamMember(memberId) {
    currentMemberId = memberId;
    const modal = new bootstrap.Modal(document.getElementById('removeTeamMemberModal'));
    modal.show();
}

document.getElementById('confirmRemove').addEventListener('click', function() {
    if (currentMemberId) {
        window.location.href = `/accounts/team-member/${currentMemberId}/remove/`;
    }
});
</script>
{% endblock %}
