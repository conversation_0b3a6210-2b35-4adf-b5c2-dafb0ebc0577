{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Edit Business Profile - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Service Provider Profile Edit */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Edit Profile Section */
    .edit-profile-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .edit-profile-container {
        max-width: 800px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Header */
    .profile-edit-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 2rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        border-radius: 1rem 1rem 0 0;
    }

    .profile-edit-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="edit-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23edit-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .profile-edit-header .content {
        position: relative;
        z-index: 2;
    }

    .edit-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .edit-title {
        font-family: var(--cw-font-display);
        font-size: 2.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .edit-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Form Cards */
    .form-card {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .form-card:hover {
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .form-card-header {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .form-card-title {
        font-family: var(--cw-font-heading);
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-card-title i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    .form-card-body {
        padding: 2rem;
    }

    /* Form Styling */
    .form-label {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control,
    .form-select {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--cw-font-primary);
        transition: all 0.2s ease;
        background: white;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control::placeholder {
        color: var(--cw-neutral-600);
        opacity: 0.7;
    }

    .form-text {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding: 0.5rem 0.75rem;
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 0.375rem;
        display: block !important; /* Ensure visible even without JS */
    }

    .invalid-feedback::before {
        content: "\f071"; /* FontAwesome exclamation-triangle */
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        margin-right: 0.5rem;
        color: #dc2626;
    }

    /* Ensure form controls with errors are highlighted */
    .form-control.is-invalid,
    .form-select.is-invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
    }

    /* Non-field errors styling */
    .alert-danger {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        font-family: var(--cw-font-primary);
        font-weight: 500;
    }

    .alert-danger::before {
        content: "\f071"; /* FontAwesome exclamation-triangle */
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        margin-right: 0.75rem;
    }

    /* Logo Preview */
    .logo-preview-container {
        text-align: center;
        margin-bottom: 1rem;
    }

    .logo-preview {
        max-width: 200px;
        max-height: 200px;
        border-radius: 1rem;
        border: 3px solid var(--cw-brand-accent);
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .logo-preview:hover {
        border-color: var(--cw-brand-primary);
        transform: scale(1.02);
    }

    /* Checkbox Styling */
    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.25rem;
        background-color: white;
        transition: all 0.2s ease;
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .form-check-label {
        font-family: var(--cw-font-heading);
        font-weight: 500;
        color: var(--cw-brand-primary);
        margin-left: 0.5rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Action Buttons */
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        padding: 2rem 0;
        border-top: 2px solid var(--cw-brand-accent);
        margin-top: 2rem;
    }

    /* Alert Styling */
    .alert {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        font-family: var(--cw-font-primary);
        font-weight: 500;
    }

    .alert-success {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .alert-info {
        background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .edit-profile-section {
            padding: 3rem 0;
        }

        .edit-profile-container {
            padding: 0 1.5rem;
        }

        .profile-edit-header {
            padding: 2rem 2rem 1.5rem;
        }

        .edit-title {
            font-size: 2.25rem;
        }

        .edit-subtitle {
            font-size: 1.125rem;
        }

        .edit-profile-body {
            padding: 2rem;
        }

        .form-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .edit-profile-container {
            padding: 0 1rem;
        }

        .profile-edit-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .edit-profile-body {
            padding: 1.5rem;
        }

        .form-card-body {
            padding: 1.5rem;
        }

        .edit-title {
            font-size: 1.875rem;
        }

        .edit-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        /* Mobile toast notifications */
        .toast-container {
            top: 10px;
            right: 10px;
            left: 10px;
            max-width: none;
        }

        .toast-notification {
            margin-bottom: 0.5rem;
        }

        .toast-header {
            padding: 0.75rem 1rem 0.25rem;
        }

        .toast-body {
            padding: 0.25rem 1rem 0.75rem;
            font-size: 0.875rem;
        }
    }

    /* Toast Notification Styling */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
    }

    .toast-notification {
        background: white;
        border-radius: 0.75rem;
        box-shadow: var(--cw-shadow-lg);
        border: 1px solid var(--cw-brand-accent);
        margin-bottom: 0.75rem;
        overflow: hidden;
        transform: translateX(100%);
        transition: all 0.3s ease;
        opacity: 0;
    }

    .toast-notification.show {
        transform: translateX(0);
        opacity: 1;
    }

    .toast-notification.error {
        border-left: 4px solid #dc2626;
    }

    .toast-notification.success {
        border-left: 4px solid #059669;
    }

    .toast-notification.warning {
        border-left: 4px solid #d97706;
    }

    .toast-header {
        padding: 1rem 1.5rem 0.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .toast-title {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .toast-close {
        background: none;
        border: none;
        color: var(--cw-neutral-600);
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .toast-close:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .toast-body {
        padding: 0.5rem 1.5rem 1rem;
        color: var(--cw-neutral-700);
        line-height: 1.5;
    }

    /* Inline Error Styling */
    .field-error {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding: 0.5rem 0.75rem;
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .field-error.show {
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
/**
 * Service Provider Profile Edit JavaScript
 * Handles business logo preview, validation, and form feedback
 */
document.addEventListener('DOMContentLoaded', function() {

    // Create toast container
    function createToastContainer() {
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
        return container;
    }

    // Show toast notification
    function showToast(message, type = 'error', title = null) {
        const container = createToastContainer();

        const toastId = 'toast-' + Date.now();
        const iconMap = {
            error: 'fas fa-exclamation-triangle',
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };

        const titleMap = {
            error: 'Error',
            success: 'Success',
            warning: 'Warning',
            info: 'Information'
        };

        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast-notification ${type}`;
        toast.innerHTML = `
            <div class="toast-header">
                <h6 class="toast-title">
                    <i class="${iconMap[type]}"></i>
                    ${title || titleMap[type]}
                </h6>
                <button type="button" class="toast-close" onclick="removeToast('${toastId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="toast-body">${message}</div>
        `;

        container.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove after 5 seconds
        setTimeout(() => removeToast(toastId), 5000);
    }

    // Remove toast notification
    window.removeToast = function(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    };

    // Show inline field error
    function showFieldError(field, message) {
        // Remove existing error
        clearFieldError(field);

        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error show';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i>${message}`;

        field.parentNode.insertBefore(errorDiv, field.nextSibling);
        field.classList.add('is-invalid');
    }

    // Clear field error
    function clearFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        field.classList.remove('is-invalid');
    }

    // Business logo validation and preview functionality
    const logoInput = document.getElementById('{{ form.logo.id_for_label }}');
    const logoPreview = document.getElementById('logo-preview');

    if (logoInput && logoPreview) {
        logoInput.addEventListener('change', function() {
            // Clear previous errors
            clearFieldError(this);

            if (this.files && this.files[0]) {
                const file = this.files[0];

                // Validate file type
                const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
                if (!validTypes.includes(file.type)) {
                    showFieldError(this, 'Please select a valid image file (JPG, PNG, WebP, or GIF).');
                    showToast('Invalid file type. Please upload a JPG, PNG, WebP, or GIF logo.', 'error', 'Logo Upload Error');
                    this.value = '';
                    return;
                }

                // Validate file size (5MB limit)
                const maxSize = 5 * 1024 * 1024; // 5MB
                if (file.size > maxSize) {
                    showFieldError(this, 'Logo file is too large. Maximum size is 5MB.');
                    showToast('Logo file is too large. Please select a file smaller than 5MB.', 'error', 'File Size Error');
                    this.value = '';
                    return;
                }

                // Validate image dimensions
                const img = new Image();
                img.onload = function() {
                    const width = this.width;
                    const height = this.height;
                    const aspectRatio = width / height;
                    const fileSize = file.size;

                    // Critical size validation (blocking errors)
                    if (width < 50 || height < 50) {
                        showFieldError(logoInput, 'Logo is too small. Minimum size is 50x50 pixels.');
                        showToast('Logo image is too small. Please use an image at least 50x50 pixels.', 'error', 'Image Quality');
                        logoInput.value = '';
                        return;
                    }

                    if (width > 2000 || height > 2000) {
                        showFieldError(logoInput, 'Logo is too large. Maximum size is 2000x2000 pixels.');
                        showToast('Logo image is too large. Please use an image smaller than 2000x2000 pixels.', 'error', 'Image Size');
                        logoInput.value = '';
                        return;
                    }

                    // Quality analysis and recommendations
                    let warnings = [];
                    let recommendations = [];
                    let qualityScore = 100;
                    let qualityIssues = [];

                    // Size quality analysis
                    if (width < 100 || height < 100) {
                        warnings.push('Image is very small and may appear pixelated when displayed.');
                        qualityScore -= 20;
                        qualityIssues.push('low_resolution');
                    } else if (width < 200 || height < 200) {
                        warnings.push('Image is small. For crisp display, consider using at least 200x200 pixels.');
                        qualityScore -= 10;
                    }

                    // Aspect ratio analysis for logo suitability
                    if (aspectRatio < 0.5 || aspectRatio > 2.0) {
                        warnings.push('Unusual aspect ratio for a logo. Square or nearly square images work best for business logos.');
                        qualityScore -= 15;
                        qualityIssues.push('poor_aspect_ratio');
                    } else if (aspectRatio >= 0.8 && aspectRatio <= 1.25) {
                        recommendations.push('Perfect aspect ratio for a business logo!');
                        qualityScore += 5;
                    }

                    // File size efficiency analysis
                    const pixelCount = width * height;
                    const bytesPerPixel = fileSize / pixelCount;

                    if (bytesPerPixel > 15) {
                        warnings.push('File size is quite large for the image dimensions. Consider optimizing to improve loading speed.');
                        qualityScore -= 10;
                        qualityIssues.push('inefficient_compression');
                    } else if (bytesPerPixel < 2 && file.type === 'image/jpeg') {
                        warnings.push('JPEG compression may be too aggressive, potentially affecting image quality.');
                        qualityScore -= 5;
                    }

                    // Large file warnings
                    if (fileSize < 1024 * 1024) { // Under 1MB
                        recommendations.push('Good file size - will load quickly!');
                    } else if (fileSize > 2 * 1024 * 1024) { // Over 2MB
                        warnings.push('Large file size may slow page loading. Consider compressing the image.');
                        qualityScore -= 10;
                    }

                    // Format-specific analysis
                    switch (file.type) {
                        case 'image/jpeg':
                        case 'image/jpg':
                            if (fileSize < 30000 && pixelCount > 40000) {
                                warnings.push('JPEG quality appears low. Consider PNG for better logo quality.');
                                qualityScore -= 15;
                                qualityIssues.push('low_jpeg_quality');
                            } else {
                                recommendations.push('JPEG format is good for photos and detailed logos.');
                            }
                            break;

                        case 'image/png':
                            if (fileSize > 500000) {
                                recommendations.push('PNG is excellent for logos but creates large files. Consider JPEG if it\'s photo-like.');
                            } else {
                                recommendations.push('PNG format is perfect for logos with solid colors and text!');
                                qualityScore += 5;
                            }
                            break;

                        case 'image/webp':
                            recommendations.push('WebP provides excellent compression while maintaining quality. Great choice!');
                            qualityScore += 10;
                            break;

                        case 'image/gif':
                            if (fileSize > 200000) {
                                warnings.push('GIF file is large. This format is better for simple graphics or animations.');
                            }
                            recommendations.push('GIF format works but PNG usually provides better quality for logos.');
                            break;
                    }

                    // Resolution quality assessment
                    const isHighRes = width >= 300 && height >= 300;
                    const isVeryHighRes = width >= 500 && height >= 500;

                    if (isVeryHighRes) {
                        recommendations.push('Excellent resolution! Your logo will look crisp on all devices including high-DPI screens.');
                        qualityScore += 10;
                    } else if (isHighRes) {
                        recommendations.push('Good resolution for most uses. Logo will display well on standard screens.');
                        qualityScore += 5;
                    }

                    // Overall quality assessment
                    let qualityLevel = 'Poor';
                    if (qualityScore >= 90) qualityLevel = 'Excellent';
                    else if (qualityScore >= 80) qualityLevel = 'Very Good';
                    else if (qualityScore >= 70) qualityLevel = 'Good';
                    else if (qualityScore >= 60) qualityLevel = 'Fair';

                    // Display quality summary
                    const qualityMessage = `Image Quality: ${qualityLevel} (${Math.max(0, qualityScore)}/100)`;
                    const dimensionsMessage = `Dimensions: ${width}×${height}px | Size: ${(fileSize/1024).toFixed(1)}KB`;

                    if (qualityScore >= 70) {
                        showToast(`${qualityMessage}\n${dimensionsMessage}`, 'success', 'Logo Quality Check');
                    } else if (qualityScore >= 50) {
                        showToast(`${qualityMessage}\n${dimensionsMessage}\nConsider optimizing for better results.`, 'warning', 'Logo Quality Check');
                    } else {
                        showToast(`${qualityMessage}\n${dimensionsMessage}\nImage quality may not be suitable for professional use.`, 'warning', 'Logo Quality Check');
                    }

                    // Display warnings
                    if (warnings.length > 0) {
                        warnings.forEach((warning, index) => {
                            setTimeout(() => {
                                showToast(warning, 'warning', 'Quality Notice');
                            }, 1000 + (index * 1500)); // Stagger warnings
                        });
                    }

                    // Display recommendations
                    if (recommendations.length > 0) {
                        recommendations.forEach((rec, index) => {
                            setTimeout(() => {
                                showToast(rec, 'info', 'Recommendation');
                            }, 2000 + (index * 1500)); // Stagger after warnings
                        });
                    }

                    // Log detailed info for debugging
                    console.log('Logo Analysis:', {
                        dimensions: `${width}×${height}`,
                        aspectRatio: aspectRatio.toFixed(2),
                        fileSize: `${(fileSize/1024).toFixed(1)}KB`,
                        bytesPerPixel: bytesPerPixel.toFixed(2),
                        format: file.type,
                        qualityScore: qualityScore,
                        qualityLevel: qualityLevel,
                        issues: qualityIssues
                    });
                };

                img.onerror = function() {
                    showFieldError(logoInput, 'Invalid image file. Please upload a valid logo.');
                    showToast('Unable to read image file. Please try a different logo file.', 'error', 'Image Error');
                    logoInput.value = '';
                    return;
                };

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    logoPreview.src = e.target.result;
                    logoPreview.style.display = 'block';

                    // Set image source for dimension validation
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        // Clear errors when field gets focus
        logoInput.addEventListener('focus', function() {
            clearFieldError(this);
        });
    }

    // Enhanced form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            let errorCount = 0;
            const requiredFields = form.querySelectorAll('[required]');

            // Clear all previous errors
            requiredFields.forEach(field => clearFieldError(field));

            // Validate required fields
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    errorCount++;
                    const fieldLabel = field.closest('.mb-3').querySelector('label')?.textContent || 'This field';
                    showFieldError(field, `${fieldLabel.replace(':', '')} is required.`);
                }
            });

            // Validate email format
            const emailFields = form.querySelectorAll('input[type="email"]');
            emailFields.forEach(field => {
                if (field.value && !isValidEmail(field.value)) {
                    isValid = false;
                    errorCount++;
                    showFieldError(field, 'Please enter a valid email address.');
                }
            });

            // Validate URL fields
            const urlFields = form.querySelectorAll('input[type="url"]');
            urlFields.forEach(field => {
                if (field.value && !isValidURL(field.value)) {
                    isValid = false;
                    errorCount++;
                    showFieldError(field, 'Please enter a valid URL (include http:// or https://).');
                }
            });

            // Validate phone numbers
            const phoneFields = form.querySelectorAll('input[name="phone"]');
            phoneFields.forEach(field => {
                if (field.value && !isValidPhone(field.value)) {
                    isValid = false;
                    errorCount++;
                    showFieldError(field, 'Please enter a valid phone number.');
                }
            });

            // Validate EIN
            const einFields = form.querySelectorAll('input[name="ein"]');
            einFields.forEach(field => {
                if (field.value && !isValidEIN(field.value)) {
                    isValid = false;
                    errorCount++;
                    showFieldError(field, 'Please enter a valid EIN (9 digits).');
                }
            });

            // Validate ZIP codes
            const zipFields = form.querySelectorAll('input[name="zip_code"]');
            zipFields.forEach(field => {
                if (field.value && !isValidZipCode(field.value)) {
                    isValid = false;
                    errorCount++;
                    showFieldError(field, 'Please enter a valid ZIP code.');
                }
            });

            // Validate Instagram URLs
            const instagramFields = form.querySelectorAll('input[name="instagram"]');
            instagramFields.forEach(field => {
                if (field.value && !isValidInstagram(field.value)) {
                    isValid = false;
                    errorCount++;
                    showFieldError(field, 'Please enter a valid Instagram URL or username.');
                }
            });

            // Validate Facebook URLs
            const facebookFields = form.querySelectorAll('input[name="facebook"]');
            facebookFields.forEach(field => {
                if (field.value && !isValidFacebook(field.value)) {
                    isValid = false;
                    errorCount++;
                    showFieldError(field, 'Please enter a valid Facebook page URL.');
                }
            });

            // Validate business name
            const businessNameFields = form.querySelectorAll('input[name="legal_name"]');
            businessNameFields.forEach(field => {
                if (field.value && !isValidBusinessName(field.value)) {
                    isValid = false;
                    errorCount++;
                    showFieldError(field, 'Please enter a valid business name.');
                }
            });

            if (!isValid) {
                e.preventDefault();
                showToast(`Please fix ${errorCount} error${errorCount > 1 ? 's' : ''} before submitting.`, 'error', 'Form Validation');

                // Scroll to first error
                const firstError = form.querySelector('.field-error');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
    }

    // Email validation helper
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Enhanced URL validation helper
    function isValidURL(url) {
        try {
            // Add protocol if missing
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }
            const urlObj = new URL(url);
            return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
        } catch {
            return false;
        }
    }

    // Auto-resize textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });

        // Initial resize
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
    });

    // Initialize real-time validation
    setupRealTimeValidation();

    // Phone validation helper (enhanced)
    function isValidPhone(phone) {
        // Remove all non-digit characters
        const digitsOnly = phone.replace(/\D/g, '');

        // US phone: 10 digits or 11 with leading 1
        if (digitsOnly.length === 10) return true;
        if (digitsOnly.length === 11 && digitsOnly.startsWith('1')) return true;

        // International: check if starts with + and has reasonable length
        if (phone.startsWith('+')) {
            return digitsOnly.length >= 10 && digitsOnly.length <= 15;
        }

        return false;
    }

    // Enhanced Django messages and error handling
    function showDjangoMessages() {
        // Handle alert messages
        const messageElements = document.querySelectorAll('.alert');
        messageElements.forEach(alert => {
            const classList = alert.classList;
            let type = 'info';

            if (classList.contains('alert-success')) type = 'success';
            else if (classList.contains('alert-danger') || classList.contains('alert-error')) type = 'error';
            else if (classList.contains('alert-warning')) type = 'warning';

            const messageText = alert.textContent.trim();
            // Remove the icon text and button text from the message
            const cleanMessage = messageText
                .replace(/^(Success|Error|Warning|Info)\s*/, '')
                .replace(/Close$/, '')
                .replace(/×$/, '')
                .trim();

            if (cleanMessage) {
                showToast(cleanMessage, type);
            }

            // Hide the original Django alert after showing toast
            alert.style.display = 'none';
        });

        // Handle form field errors and convert them to consistent format
        const fieldErrors = document.querySelectorAll('.invalid-feedback');
        fieldErrors.forEach(errorElement => {
            const field = errorElement.closest('.mb-3')?.querySelector('input, textarea, select');
            if (field && errorElement.textContent.trim()) {
                const fieldLabel = errorElement.closest('.mb-3')?.querySelector('label')?.textContent?.replace(':', '') || 'Field';
                const errorMessage = errorElement.textContent.trim();

                // Don't show immediate toast for field errors, but ensure consistent styling
                errorElement.style.display = 'none'; // Hide Django error, we'll show our own
                showFieldError(field, errorMessage);
            }
        });

        // Handle non-field errors
        const nonFieldErrors = document.querySelectorAll('.alert-danger, .alert-error');
        nonFieldErrors.forEach(errorAlert => {
            if (!errorAlert.style.display || errorAlert.style.display !== 'none') {
                const errorText = errorAlert.textContent.trim()
                    .replace(/^Error\s*/, '')
                    .replace(/Close$/, '')
                    .replace(/×$/, '')
                    .trim();

                if (errorText) {
                    showToast(errorText, 'error', 'Form Error');
                    errorAlert.style.display = 'none';
                }
            }
        });
    }

    // EIN validation helper
    function isValidEIN(ein) {
        if (!ein) return true; // Optional field
        const einDigits = ein.replace(/\D/g, '');
        return einDigits.length === 9;
    }

    // ZIP code validation helper
    function isValidZipCode(zip) {
        if (!zip) return true; // Optional in some contexts
        const zipDigits = zip.replace(/\D/g, '');
        return zipDigits.length === 5 || zipDigits.length === 9;
    }

    // Instagram URL validation helper
    function isValidInstagram(url) {
        if (!url) return true; // Optional field

        // Allow username only, @username, or full URL
        if (!url.includes('/')) {
            return /^@?[a-zA-Z0-9._]+$/.test(url);
        }

        const instagramPattern = /^https?:\/\/(www\.)?instagram\.com\/[a-zA-Z0-9._]+\/?$/i;
        return instagramPattern.test(url);
    }

    // Facebook URL validation helper
    function isValidFacebook(url) {
        if (!url) return true; // Optional field

        // Allow page name only or full URL
        if (!url.includes('/')) {
            return /^[a-zA-Z0-9.]+$/.test(url);
        }

        const facebookPattern = /^https?:\/\/(www\.)?facebook\.com\/[a-zA-Z0-9.]+\/?$/i;
        return facebookPattern.test(url);
    }

    // Business name validation helper
    function isValidBusinessName(name) {
        if (!name) return false;
        return name.trim().length >= 2 && /^[a-zA-Z0-9\s\-\.\,\&\'\"]/.test(name);
    }

    // Comprehensive real-time validation setup
    function setupRealTimeValidation() {
        const form = document.querySelector('form');
        if (!form) return;

        // Legal name validation
        const legalNameField = form.querySelector('input[name="legal_name"]');
        if (legalNameField) {
            legalNameField.addEventListener('blur', function() {
                if (this.value && !isValidBusinessName(this.value)) {
                    showFieldError(this, 'Business name must be at least 2 characters and contain only valid characters.');
                } else {
                    clearFieldError(this);
                }
            });
        }

        // Email validation (if any email fields exist)
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            field.addEventListener('input', function() {
                clearFieldError(this);
            });

            field.addEventListener('blur', function() {
                if (this.value && !isValidEmail(this.value)) {
                    showFieldError(this, 'Please enter a valid email address.');
                } else {
                    clearFieldError(this);
                }
            });
        });

        // Website URL validation
        const websiteField = form.querySelector('input[name="website"]');
        if (websiteField) {
            websiteField.addEventListener('input', function() {
                clearFieldError(this);
            });

            websiteField.addEventListener('blur', function() {
                if (this.value && !isValidURL(this.value)) {
                    showFieldError(this, 'Please enter a valid website URL (e.g., https://example.com).');
                } else {
                    clearFieldError(this);
                }
            });
        }

        // Instagram URL validation
        const instagramField = form.querySelector('input[name="instagram"]');
        if (instagramField) {
            instagramField.addEventListener('input', function() {
                clearFieldError(this);
            });

            instagramField.addEventListener('blur', function() {
                if (this.value && !isValidInstagram(this.value)) {
                    showFieldError(this, 'Please enter a valid Instagram URL or username.');
                } else {
                    clearFieldError(this);
                }
            });
        }

        // Facebook URL validation
        const facebookField = form.querySelector('input[name="facebook"]');
        if (facebookField) {
            facebookField.addEventListener('input', function() {
                clearFieldError(this);
            });

            facebookField.addEventListener('blur', function() {
                if (this.value && !isValidFacebook(this.value)) {
                    showFieldError(this, 'Please enter a valid Facebook page URL or page name.');
                } else {
                    clearFieldError(this);
                }
            });
        }

        // EIN validation
        const einField = form.querySelector('input[name="ein"]');
        if (einField) {
            einField.addEventListener('input', function() {
                clearFieldError(this);

                // Auto-format EIN as user types
                let value = this.value.replace(/\D/g, '');
                if (value.length >= 2) {
                    value = value.slice(0, 2) + '-' + value.slice(2, 9);
                }
                if (this.value !== value) {
                    this.value = value;
                }
            });

            einField.addEventListener('blur', function() {
                if (this.value && !isValidEIN(this.value)) {
                    showFieldError(this, 'EIN must be 9 digits (format: XX-XXXXXXX).');
                } else {
                    clearFieldError(this);
                }
            });
        }

        // ZIP code validation
        const zipField = form.querySelector('input[name="zip_code"]');
        if (zipField) {
            zipField.addEventListener('input', function() {
                clearFieldError(this);

                // Auto-format ZIP code as user types
                let value = this.value.replace(/\D/g, '');
                if (value.length > 5) {
                    value = value.slice(0, 5) + '-' + value.slice(5, 9);
                }
                if (this.value !== value) {
                    this.value = value;
                }
            });

            zipField.addEventListener('blur', function() {
                if (this.value && !isValidZipCode(this.value)) {
                    showFieldError(this, 'Please enter a valid ZIP code (5 digits or 5+4 format).');
                } else {
                    clearFieldError(this);
                }
            });
        }

        // Phone number formatting
        const phoneField = form.querySelector('input[name="phone"]');
        if (phoneField) {
            phoneField.addEventListener('input', function() {
                clearFieldError(this);

                // Auto-format phone number as user types (US format)
                let value = this.value.replace(/\D/g, '');
                if (value.length <= 3) {
                    value = value;
                } else if (value.length <= 6) {
                    value = `(${value.slice(0, 3)}) ${value.slice(3)}`;
                } else if (value.length <= 10) {
                    value = `(${value.slice(0, 3)}) ${value.slice(3, 6)}-${value.slice(6)}`;
                } else if (value.length === 11 && value.startsWith('1')) {
                    value = `+1 (${value.slice(1, 4)}) ${value.slice(4, 7)}-${value.slice(7, 11)}`;
                }

                if (this.value !== value) {
                    this.value = value;
                }
            });
        }

        // Clear errors when fields get focus
        const allInputs = form.querySelectorAll('input, textarea, select');
        allInputs.forEach(input => {
            input.addEventListener('focus', function() {
                clearFieldError(this);
            });
        });
    }

    // Initialize comprehensive validation system
    function initializeValidationSystem() {
        // Set up real-time validation
        setupRealTimeValidation();

        // Process Django messages and errors consistently
        showDjangoMessages();

        // Add accessibility enhancements
        const form = document.querySelector('form');
        if (form) {
            form.setAttribute('novalidate', ''); // Disable browser validation, use our own

            // Add aria-describedby for better accessibility
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                const helpText = input.parentNode.querySelector('.form-text');
                if (helpText && !helpText.id) {
                    helpText.id = input.id + '_help';
                    input.setAttribute('aria-describedby', helpText.id);
                }
            });
        }

        // Initialize logo quality checking if logo input exists
        const logoInput = document.getElementById('{{ form.logo.id_for_label }}');
        if (logoInput) {
            // Add quality assessment container
            const qualityContainer = document.createElement('div');
            qualityContainer.id = 'logo-quality-info';
            qualityContainer.className = 'mt-2 text-muted small';
            qualityContainer.style.display = 'none';
            logoInput.parentNode.appendChild(qualityContainer);
        }

        console.log('Validation system initialized successfully');
    }

    // Initialize everything when DOM is ready
    initializeValidationSystem();
});
</script>
{% endblock %}

{% block content %}
<section class="edit-profile-section">
    <div class="edit-profile-container">
        <!-- Header -->
        <div class="profile-edit-header">
            <div class="content">
                <div class="edit-icon">
                    <i class="fas fa-edit"></i>
                </div>
                <h1 class="edit-title">Edit Business Profile</h1>
                <p class="edit-subtitle">Update your business information and settings</p>
            </div>
        </div>

        <!-- Display messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <div class="edit-profile-card">
            <div class="edit-profile-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Basic Business Information -->
                    <div class="form-card">
                        <div class="form-card-header">
                            <h3 class="form-card-title">
                                <i class="fas fa-building"></i>
                                Basic Information
                            </h3>
                        </div>
                        <div class="form-card-body">
                            <div class="row">
                                <!-- Legal Name -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" for="{{ form.legal_name.id_for_label }}">{{ form.legal_name.label }}</label>
                                    {{ form.legal_name|add_class:"form-control" }}
                                    {% if form.legal_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.legal_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.legal_name.help_text %}
                                        <div class="form-text">{{ form.legal_name.help_text }}</div>
                                    {% endif %}
                                </div>

                                <!-- Display Name -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" for="{{ form.display_name.id_for_label }}">{{ form.display_name.label }}</label>
                                    {{ form.display_name|add_class:"form-control" }}
                                    {% if form.display_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.display_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.display_name.help_text %}
                                        <div class="form-text">{{ form.display_name.help_text }}</div>
                                    {% endif %}
                                </div>

                                <!-- Description -->
                                <div class="col-12 mb-3">
                                    <label class="form-label" for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                                    {{ form.description|add_class:"form-control" }}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.description.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.description.help_text %}
                                        <div class="form-text">{{ form.description.help_text }}</div>
                                    {% endif %}
                                </div>

                                <!-- Business Logo -->
                                <div class="col-12 mb-3">
                                    <label class="form-label" for="{{ form.logo.id_for_label }}">{{ form.logo.label }}</label>
                                    <div class="logo-preview-container">
                                        {% if form.instance.logo %}
                                            <img id="logo-preview" src="{{ form.instance.logo.url }}" alt="Current Logo" class="logo-preview">
                                            <p class="form-text">Current logo</p>
                                        {% else %}
                                            <img id="logo-preview" src="https://via.placeholder.com/200x200/fae1d7/2F160F?text=No+Logo" alt="Logo Preview" class="logo-preview" style="display: none;">
                                            <p class="form-text">No logo uploaded</p>
                                        {% endif %}
                                    </div>
                                    {{ form.logo|add_class:"form-control" }}
                                    {% if form.logo.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.logo.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.logo.help_text %}
                                        <div class="form-text">{{ form.logo.help_text }}</div>
                                    {% endif %}
                                </div>

                                <!-- Progressive Enhancement Notice -->
                                <div class="col-12 mb-3">
                                    <noscript>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>JavaScript Disabled:</strong> All form validation is still functional.
                                            Image preview and real-time validation are not available, but comprehensive
                                            server-side validation will check your data when you submit the form.
                                        </div>
                                    </noscript>
                                </div>

                                <!-- EIN -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" for="{{ form.ein.id_for_label }}">{{ form.ein.label }}</label>
                                    {{ form.ein|add_class:"form-control" }}
                                    {% if form.ein.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.ein.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.ein.help_text %}
                                        <div class="form-text">{{ form.ein.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="form-card">
                        <div class="form-card-header">
                            <h3 class="form-card-title">
                                <i class="fas fa-phone"></i>
                                Contact Information
                            </h3>
                        </div>
                        <div class="form-card-body">
                            <div class="row">
                                <!-- Phone -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" for="{{ form.phone.id_for_label }}">{{ form.phone.label }}</label>
                                    {{ form.phone|add_class:"form-control" }}
                                    {% if form.phone.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Contact Name -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" for="{{ form.contact_name.id_for_label }}">{{ form.contact_name.label }}</label>
                                    {{ form.contact_name|add_class:"form-control" }}
                                    {% if form.contact_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.contact_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.contact_name.help_text %}
                                        <div class="form-text">{{ form.contact_name.help_text }}</div>
                                    {% endif %}
                                </div>

                                <!-- Address Information -->
                                <div class="col-12 mb-3">
                                    <label class="form-label" for="{{ form.address.id_for_label }}">{{ form.address.label }}</label>
                                    {{ form.address|add_class:"form-control" }}
                                    {% if form.address.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.address.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- City -->
                                <div class="col-md-4 mb-3">
                                    <label class="form-label" for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
                                    {{ form.city|add_class:"form-control" }}
                                    {% if form.city.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.city.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- State -->
                                <div class="col-md-4 mb-3">
                                    <label class="form-label" for="{{ form.state.id_for_label }}">{{ form.state.label }}</label>
                                    {{ form.state|add_class:"form-select" }}
                                    {% if form.state.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.state.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- ZIP Code -->
                                <div class="col-md-4 mb-3">
                                    <label class="form-label" for="{{ form.zip_code.id_for_label }}">{{ form.zip_code.label }}</label>
                                    {{ form.zip_code|add_class:"form-control" }}
                                    {% if form.zip_code.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.zip_code.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- County -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" for="{{ form.county.id_for_label }}">{{ form.county.label }}</label>
                                    {{ form.county|add_class:"form-control" }}
                                    {% if form.county.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.county.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Online Presence -->
                    <div class="form-card">
                        <div class="form-card-header">
                            <h3 class="form-card-title">
                                <i class="fas fa-globe"></i>
                                Online Presence
                            </h3>
                        </div>
                        <div class="form-card-body">
                            <div class="row">
                                <!-- Website -->
                                <div class="col-12 mb-3">
                                    <label class="form-label" for="{{ form.website.id_for_label }}">{{ form.website.label }}</label>
                                    {{ form.website|add_class:"form-control" }}
                                    {% if form.website.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.website.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Instagram -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" for="{{ form.instagram.id_for_label }}">{{ form.instagram.label }}</label>
                                    {{ form.instagram|add_class:"form-control" }}
                                    {% if form.instagram.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.instagram.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Facebook -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" for="{{ form.facebook.id_for_label }}">{{ form.facebook.label }}</label>
                                    {{ form.facebook|add_class:"form-control" }}
                                    {% if form.facebook.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.facebook.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Visibility Settings -->
                    <div class="form-card">
                        <div class="form-card-header">
                            <h3 class="form-card-title">
                                <i class="fas fa-eye"></i>
                                Visibility Settings
                            </h3>
                        </div>
                        <div class="form-card-body">
                            <div class="form-check">
                                {{ form.is_public|add_class:"form-check-input" }}
                                <label class="form-check-label" for="{{ form.is_public.id_for_label }}">
                                    {{ form.is_public.label }}
                                </label>
                                {% if form.is_public.help_text %}
                                    <div class="form-text">{{ form.is_public.help_text }}</div>
                                {% endif %}
                                {% if form.is_public.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.is_public.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Action buttons -->
                    <div class="form-actions">
                        <button type="submit" class="btn-cw-primary">
                            <i class="fas fa-save"></i>
                            Save Changes
                        </button>
                        <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn-cw-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
{% endblock %}
