{% extends 'accounts_app/base_account.html' %}
{% load static %}
{% load custom_avatar_tags %}

{% block title %}Avatar Management{% endblock %}

{% block account_content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card-cw">
                <div class="card-header-cw">
                    <h2 class="card-title-cw">
                        <i class="fas fa-user-circle"></i>
                        Avatar Management
                    </h2>
                </div>
                
                <div class="card-body-cw">
                    <!-- Current Avatar Display -->
                    <div class="text-center mb-4">
                        <h5>Current Avatar</h5>
                        {% display_avatar user 120 "mb-3" %}
                        
                        {% if has_avatar %}
                            <button type="button" class="btn btn-outline-danger btn-sm" id="delete-avatar-btn">
                                <i class="fas fa-trash"></i> Delete Avatar
                            </button>
                        {% endif %}
                    </div>
                    
                    <!-- Upload New Avatar -->
                    <div class="upload-section">
                        <h5>Upload New Avatar</h5>
                        <form id="avatar-upload-form" enctype="multipart/form-data">
                            {% csrf_token %}
                            <div class="mb-3">
                                {{ upload_form.avatar.label_tag }}
                                {{ upload_form.avatar }}
                                <div class="form-text">
                                    Upload an image file (JPG, PNG, WebP, or GIF). 
                                    It will be automatically resized to multiple sizes.
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload"></i> Upload Avatar
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Messages -->
                    <div id="avatar-messages" class="mt-3"></div>
                </div>
                
                <div class="card-footer-cw text-center">
                    <a href="{% url 'accounts_app:customer_profile' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('avatar-upload-form');
    const deleteBtn = document.getElementById('delete-avatar-btn');
    const messagesDiv = document.getElementById('avatar-messages');
    
    // Handle avatar upload
    if (uploadForm) {
        uploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('{% url "accounts_app:avatar_upload" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('success', data.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showMessage('error', data.message);
                }
            })
            .catch(error => {
                showMessage('error', 'An error occurred while uploading your avatar.');
            });
        });
    }
    
    // Handle avatar deletion
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete your avatar?')) {
                fetch('{% url "accounts_app:avatar_delete" %}', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('success', data.message);
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showMessage('error', data.message);
                    }
                })
                .catch(error => {
                    showMessage('error', 'An error occurred while deleting your avatar.');
                });
            }
        });
    }
    
    function showMessage(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        messagesDiv.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
});
</script>
{% endblock %}
