{% extends 'accounts_app/base_account.html' %}
{% load widget_tweaks %}

{% block title %}Add Team Member - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Add Team Member */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Add Team Member Section */
    .add-member-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .add-member-container {
        max-width: 900px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .add-member-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .add-member-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .add-member-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="add-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23add-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .add-member-header .content {
        position: relative;
        z-index: 2;
    }

    .add-member-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .add-member-title {
        font-family: var(--cw-font-display);
        font-size: 2.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .add-member-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .add-member-body {
        padding: 3rem;
    }

    /* Section Headers */
    .section-header {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-header i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    /* Form Styling */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        color: var(--cw-neutral-700);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-label i {
        color: var(--cw-brand-primary);
        font-size: 1rem;
    }

    .form-control-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
        width: 100%;
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
    }

    .form-select-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
        width: 100%;
    }

    .form-select-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-text {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* Profile Picture Section */
    .profile-picture-section {
        text-align: center;
        margin-bottom: 2rem;
        padding: 2rem;
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .profile-picture-preview-container {
        position: relative;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .profile-picture-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid var(--cw-brand-accent);
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .profile-picture-preview:hover {
        border-color: var(--cw-brand-primary);
        transform: scale(1.02);
    }

    .profile-picture-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: var(--cw-accent-light);
        border: 4px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        transition: all 0.3s ease;
        color: var(--cw-neutral-600);
        font-size: 2rem;
    }

    .profile-picture-placeholder:hover {
        border-color: var(--cw-brand-primary);
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    /* Two Column Layout */
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
        font-size: 1.125rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
        font-size: 1.125rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Action Buttons */
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: space-between;
        padding: 2rem 0;
        border-top: 2px solid var(--cw-brand-accent);
        margin-top: 2rem;
    }

    /* Tips Card */
    .tips-card {
        background: var(--cw-gradient-card-subtle);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-top: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .tips-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .tips-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .tips-list li {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 1rem;
        color: var(--cw-neutral-700);
        line-height: 1.5;
    }

    .tips-list li:before {
        content: '•';
        color: var(--cw-brand-primary);
        font-weight: bold;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    /* Alert Styling */
    .alert {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        font-family: var(--cw-font-primary);
        font-weight: 500;
    }

    .alert-success {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .alert-danger {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
    }

    .alert-info {
        background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .add-member-section {
            padding: 3rem 0;
        }

        .add-member-container {
            padding: 0 1.5rem;
        }

        .add-member-title {
            font-size: 2rem;
        }

        .add-member-body {
            padding: 2rem;
        }

        .form-row {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .form-actions {
            flex-direction: column-reverse;
            align-items: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .add-member-container {
            padding: 0 1rem;
        }

        .add-member-body {
            padding: 1.5rem;
        }

        .add-member-title {
            font-size: 1.75rem;
        }

        .tips-card {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="add-member-section">
    <div class="add-member-container">
        <div class="add-member-card">
            <!-- Header Section -->
            <div class="add-member-header">
                <div class="content">
                    <div class="add-member-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h1 class="add-member-title">Add Team Member</h1>
                    <p class="add-member-subtitle">Welcome a new team member to your CozyWish family</p>
                </div>
            </div>

            <!-- Form Section -->
            <div class="add-member-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}

                    <!-- Profile Picture Section -->
                    <div class="section-header">
                        <i class="fas fa-camera"></i>
                        Profile Picture
                    </div>
                    <div class="profile-picture-section">
                        <div class="profile-picture-preview-container">
                            <div class="profile-picture-placeholder" id="photoPlaceholder">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="id_profile_picture" class="form-label">
                                <i class="fas fa-upload"></i>
                                Upload Photo
                            </label>
                            <input type="file" id="id_profile_picture" name="profile_picture" class="form-control-cw" accept="image/*">
                            <div class="form-text">Choose a professional photo (JPG or PNG, max 5MB)</div>
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <div class="section-header">
                        <i class="fas fa-user"></i>
                        Personal Information
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="id_first_name" class="form-label">
                                <i class="fas fa-user"></i>
                                First Name
                            </label>
                            <input type="text" id="id_first_name" name="first_name" class="form-control-cw" required>
                        </div>
                        <div class="form-group">
                            <label for="id_last_name" class="form-label">
                                <i class="fas fa-user"></i>
                                Last Name
                            </label>
                            <input type="text" id="id_last_name" name="last_name" class="form-control-cw" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="id_email" class="form-label">
                            <i class="fas fa-envelope"></i>
                            Email Address
                        </label>
                        <input type="email" id="id_email" name="email" class="form-control-cw" required>
                        <div class="form-text">Team member will receive login credentials at this email</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="id_phone" class="form-label">
                                <i class="fas fa-phone"></i>
                                Phone Number
                            </label>
                            <input type="tel" id="id_phone" name="phone" class="form-control-cw">
                        </div>
                        <div class="form-group">
                            <label for="id_position" class="form-label">
                                <i class="fas fa-briefcase"></i>
                                Position
                            </label>
                            <select id="id_position" name="position" class="form-select-cw" required>
                                <option value="">Select Position</option>
                                <option value="manager">Manager</option>
                                <option value="therapist">Therapist</option>
                                <option value="massage_therapist">Massage Therapist</option>
                                <option value="esthetician">Esthetician</option>
                                <option value="receptionist">Receptionist</option>
                                <option value="staff">General Staff</option>
                            </select>
                        </div>
                    </div>

                    <!-- Professional Details -->
                    <div class="section-header">
                        <i class="fas fa-certificate"></i>
                        Professional Details
                    </div>
                    <div class="form-group">
                        <label for="id_bio" class="form-label">
                            <i class="fas fa-user-circle"></i>
                            Bio/Description
                        </label>
                        <textarea id="id_bio" name="bio" class="form-control-cw" rows="4" placeholder="Brief description of experience and specialties..."></textarea>
                        <div class="form-text">This will be displayed to customers when booking services</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="id_experience" class="form-label">
                                <i class="fas fa-clock"></i>
                                Years of Experience
                            </label>
                            <input type="number" id="id_experience" name="experience" class="form-control-cw" min="0" max="50">
                        </div>
                        <div class="form-group">
                            <label for="id_specialties" class="form-label">
                                <i class="fas fa-star"></i>
                                Specialties
                            </label>
                            <input type="text" id="id_specialties" name="specialties" class="form-control-cw" placeholder="e.g., Deep Tissue, Swedish, Facials">
                            <div class="form-text">Separate multiple specialties with commas</div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="form-actions">
                        <a href="{% url 'accounts_app:team_member_list' %}" class="btn-cw-secondary">
                            <i class="fas fa-arrow-left"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn-cw-primary">
                            <i class="fas fa-save"></i>
                            Save Team Member
                        </button>
                    </div>
                </form>

                <!-- Tips Card -->
                <div class="tips-card">
                    <h3 class="tips-title">
                        <i class="fas fa-lightbulb"></i>
                        Tips for Adding Team Members
                    </h3>
                    <ul class="tips-list">
                        <li>Ensure the email address is correct - login credentials will be sent there</li>
                        <li>Upload a professional headshot for better customer trust</li>
                        <li>Include relevant certifications and specialties in the bio</li>
                        <li>Choose the appropriate position for proper access permissions</li>
                        <li>Keep bio concise but informative for customer bookings</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
/**
 * Add Team Member Form JavaScript
 * Handles photo preview functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Photo preview functionality
    const photoInput = document.getElementById('id_profile_picture');
    const photoPlaceholder = document.getElementById('photoPlaceholder');

    if (photoInput && photoPlaceholder) {
        photoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // Validate file type
                const file = this.files[0];
                const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];

                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG or PNG).');
                    this.value = '';
                    return;
                }

                // Validate file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB.');
                    this.value = '';
                    return;
                }

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Replace placeholder with actual image
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'profile-picture-preview';
                    img.alt = 'Profile Preview';

                    // Replace the placeholder
                    photoPlaceholder.parentNode.replaceChild(img, photoPlaceholder);
                };
                reader.readAsDataURL(file);
            }
        });
    }
});
</script>
{% endblock %}
