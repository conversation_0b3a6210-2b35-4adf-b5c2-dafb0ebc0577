{% extends 'base.html' %}

{% block title %}Set New Password - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Password Reset Confirm */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Password Reset Confirm Section */
    .password-reset-confirm-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .password-reset-confirm-container {
        max-width: 700px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .password-reset-confirm-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .password-reset-confirm-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .password-reset-confirm-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="confirm-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23confirm-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .password-reset-confirm-header .content {
        position: relative;
        z-index: 2;
    }

    .password-reset-confirm-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .password-reset-confirm-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .password-reset-confirm-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .password-reset-confirm-body {
        padding: 3rem;
    }

    /* Invalid Link Header */
    .invalid-link-header {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid #fecaca;
    }

    .invalid-link-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="invalid-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M15,5 L20,15 L10,15 Z" fill="%23fecaca" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23invalid-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .invalid-link-header .content {
        position: relative;
        z-index: 2;
    }

    .invalid-link-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .invalid-link-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: #dc2626;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .invalid-link-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Form Styling */
    .form-control-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
    }

    .form-label {
        color: var(--cw-neutral-700);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        margin-bottom: 0.5rem;
    }

    .form-label i {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        width: 100%;
        font-size: 1.125rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    /* Alert Styling */
    .alert-cw {
        border-radius: 0.5rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid;
        font-family: var(--cw-font-primary);
    }

    .alert-cw-error {
        background: #fef2f2;
        border-color: #fecaca;
        color: #991b1b;
    }

    /* Error Messages */
    .invalid-feedback {
        display: block !important;
        width: 100%;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #dc2626;
        font-weight: 500;
        font-family: var(--cw-font-primary);
    }

    .invalid-feedback i {
        margin-right: 0.25rem;
    }

    /* Form Text */
    .form-text {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        font-family: var(--cw-font-primary);
    }

    /* Password Toggle */
    .toggle-password {
        border: 2px solid var(--cw-brand-accent) !important;
        border-left: none !important;
        background: white !important;
        color: var(--cw-brand-primary) !important;
        padding: 0.875rem 1rem !important;
        border-radius: 0 0.5rem 0.5rem 0 !important;
        transition: all 0.2s ease;
    }

    .toggle-password:hover,
    .toggle-password:focus {
        background: var(--cw-accent-light) !important;
        border-color: var(--cw-brand-primary) !important;
        color: var(--cw-brand-primary) !important;
    }

    .input-group .form-control-cw:focus + .toggle-password {
        border-color: var(--cw-brand-primary) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .password-reset-confirm-section {
            padding: 3rem 0;
        }

        .password-reset-confirm-container {
            max-width: 600px;
            padding: 0 1.5rem;
        }

        .password-reset-confirm-header,
        .invalid-link-header {
            padding: 2rem 2rem 1.5rem;
        }

        .password-reset-confirm-title,
        .invalid-link-title {
            font-size: 2rem;
        }

        .password-reset-confirm-subtitle,
        .invalid-link-subtitle {
            font-size: 1rem;
        }

        .password-reset-confirm-body {
            padding: 2rem;
        }
    }

    @media (max-width: 576px) {
        .password-reset-confirm-container {
            padding: 0 1rem;
        }

        .password-reset-confirm-header,
        .invalid-link-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .password-reset-confirm-body {
            padding: 1.5rem;
        }

        .password-reset-confirm-title,
        .invalid-link-title {
            font-size: 1.75rem;
        }

        .password-reset-confirm-icon,
        .invalid-link-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="password-reset-confirm-section">
    <div class="password-reset-confirm-container">
        <div class="password-reset-confirm-card">
            {% if validlink %}
                <div class="password-reset-confirm-header">
                    <div class="content">
                        <div class="password-reset-confirm-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <h1 class="password-reset-confirm-title">Set New Password</h1>
                        <p class="password-reset-confirm-subtitle">Please enter your new password twice to confirm</p>
                    </div>
                </div>

                <div class="password-reset-confirm-body">
                    <form method="post" novalidate>
                      {% csrf_token %}

                      {% if form.non_field_errors %}
                      <div class="alert-cw alert-cw-error mb-4">
                        {% for error in form.non_field_errors %}
                        <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}

                      <!-- New Password field -->
                      <div class="mb-4">
                        <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                          <i class="fas fa-lock"></i>New Password
                        </label>
                        <div class="input-group">
                          {% if form.new_password1.errors %}
                            {{ form.new_password1|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your new password" }}
                          {% else %}
                            {{ form.new_password1|add_class:"form-control-cw"|attr:"placeholder:Enter your new password" }}
                          {% endif %}
                          <button type="button" class="toggle-password" data-target="#{{ form.new_password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                            <i class="fas fa-eye" aria-hidden="true"></i>
                          </button>
                        </div>
                        {% if form.new_password1.errors %}
                        <div class="invalid-feedback" role="alert" aria-live="polite">
                          {% for error in form.new_password1.errors %}
                          <i class="fas fa-exclamation-circle"></i>{{ error }}
                          {% endfor %}
                        </div>
                        {% endif %}
                        {% if form.new_password1.help_text %}
                        <div class="form-text">{{ form.new_password1.help_text }}</div>
                        {% endif %}
                      </div>

                      <!-- Confirm Password field -->
                      <div class="mb-4">
                        <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                          <i class="fas fa-lock"></i>Confirm Password
                        </label>
                        <div class="input-group">
                          {% if form.new_password2.errors %}
                            {{ form.new_password2|add_class:"form-control-cw is-invalid"|attr:"placeholder:Confirm your new password" }}
                          {% else %}
                            {{ form.new_password2|add_class:"form-control-cw"|attr:"placeholder:Confirm your new password" }}
                          {% endif %}
                          <button type="button" class="toggle-password" data-target="#{{ form.new_password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                            <i class="fas fa-eye" aria-hidden="true"></i>
                          </button>
                        </div>
                        {% if form.new_password2.errors %}
                        <div class="invalid-feedback" role="alert" aria-live="polite">
                          {% for error in form.new_password2.errors %}
                          <i class="fas fa-exclamation-circle"></i>{{ error }}
                          {% endfor %}
                        </div>
                        {% endif %}
                        {% if form.new_password2.help_text %}
                        <div class="form-text">{{ form.new_password2.help_text }}</div>
                        {% endif %}
                      </div>

                      <div class="d-grid mb-4">
                        <button type="submit" class="btn-cw-primary">
                          <i class="fas fa-check-circle"></i>Set New Password
                        </button>
                      </div>
                    </form>
                </div>

            {% else %}
                <div class="invalid-link-header">
                    <div class="content">
                        <div class="invalid-link-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h1 class="invalid-link-title">Invalid Link</h1>
                        <p class="invalid-link-subtitle">The password reset link was invalid, possibly because it has already been used. Please request a new password reset.</p>
                    </div>
                </div>

                <div class="password-reset-confirm-body">
                    <div class="d-grid mb-4">
                      <a href="{% url 'accounts_app:customer_password_reset' %}" class="btn-cw-primary">
                        <i class="fas fa-redo"></i>Request New Reset Link
                      </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Password Toggle JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Password toggle functionality
    function togglePassword(toggleBtn) {
        const targetSelector = toggleBtn.getAttribute('data-target');
        if (!targetSelector) {
            console.error('No data-target attribute found on toggle button');
            return;
        }

        const input = document.querySelector(targetSelector);
        if (!input) {
            console.error('Target input not found:', targetSelector);
            return;
        }

        // Toggle password visibility
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';

        // Update icon
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            if (isPassword) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Update accessibility attributes
        const newTitle = isPassword ? 'Hide password' : 'Show password';
        toggleBtn.title = newTitle;
        toggleBtn.setAttribute('aria-label', newTitle);
    }

    // Click event handler
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Keyboard event handler for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Spacebar')) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Initialize toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(function(btn) {
        // Ensure button has proper attributes
        if (!btn.hasAttribute('tabindex')) {
            btn.setAttribute('tabindex', '0');
        }

        // Add role for screen readers
        btn.setAttribute('role', 'button');

        // Ensure aria-label is set
        if (!btn.hasAttribute('aria-label')) {
            btn.setAttribute('aria-label', 'Toggle password visibility');
        }
    });
});
</script>
{% endblock %}
