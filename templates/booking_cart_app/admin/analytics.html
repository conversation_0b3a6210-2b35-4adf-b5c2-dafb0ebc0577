{% extends 'booking_cart_app/base.html' %}

{% block title %}Booking Analytics - Admin - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .analytics-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .analytics-card:hover {
        transform: translateY(-2px);
    }
    .metric-card {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-radius: 15px;
    }
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
    }
    .metric-label {
        font-size: 1rem;
        opacity: 0.9;
    }
    .chart-container {
        position: relative;
        height: 400px;
    }
    .date-filter {
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:admin_booking_dashboard' %}">Booking Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Analytics</li>
                    </ol>
                </nav>

                <h1 class="page-title mb-3">
                    <i class="fas fa-chart-line me-2"></i>Booking Analytics
                </h1>
                <p class="text-muted">Comprehensive booking performance and insights for {{ period_name }}</p>
            </div>
        </div>

        <!-- Date Range Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card date-filter">
                    <div class="card-body">
                        <form method="get" class="row g-3 align-items-end" aria-label="Analytics filter">
                            <div class="col-md-3">
                                <label class="form-label">Time Period</label>
                                <select name="date_range" class="form-control">
                                    <option value="7" {% if date_range == '7' %}selected{% endif %}>Last 7 Days</option>
                                    <option value="30" {% if date_range == '30' %}selected{% endif %}>Last 30 Days</option>
                                    <option value="90" {% if date_range == '90' %}selected{% endif %}>Last 90 Days</option>
                                    <option value="custom" {% if date_range == 'custom' %}selected{% endif %}>Custom Range</option>
                                </select>
                            </div>
                            <div class="col-md-3" id="custom-start" {% if date_range != 'custom' %}style="display: none;"{% endif %}>
                                <label class="form-label">Start Date</label>
                                <input type="date" name="start_date" class="form-control" value="{{ custom_start }}">
                            </div>
                            <div class="col-md-3" id="custom-end" {% if date_range != 'custom' %}style="display: none;"{% endif %}>
                                <label class="form-label">End Date</label>
                                <input type="date" name="end_date" class="form-control" value="{{ custom_end }}">
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Update Analytics
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <div class="metric-value">{{ analytics.total_bookings }}</div>
                        <div class="metric-label">Total Bookings</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <div class="metric-value">${{ analytics.total_revenue|floatformat:0 }}</div>
                        <div class="metric-label">Total Revenue</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <div class="metric-value">{{ analytics.confirmation_rate }}%</div>
                        <div class="metric-label">Confirmation Rate</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <div class="metric-value">${{ analytics.avg_booking_value|floatformat:0 }}</div>
                        <div class="metric-label">Avg Booking Value</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <!-- Daily Trends Chart -->
            <div class="col-lg-8">
                <div class="card analytics-card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>Daily Booking Trends
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dailyTrendsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Distribution -->
            <div class="col-lg-4">
                <div class="card analytics-card">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-chart-pie me-2"></i>Status Distribution
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Statistics -->
        <div class="row mb-4">
            <!-- Booking Statistics -->
            <div class="col-lg-6">
                <div class="card analytics-card">
                    <div class="card-header bg-success text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-list-alt me-2"></i>Booking Statistics
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <h4 class="text-warning">{{ analytics.pending_bookings }}</h4>
                                <small class="text-muted">Pending</small>
                            </div>
                            <div class="col-6 mb-3">
                                <h4 class="text-success">{{ analytics.confirmed_bookings }}</h4>
                                <small class="text-muted">Confirmed</small>
                            </div>
                            <div class="col-6 mb-3">
                                <h4 class="text-primary">{{ analytics.completed_bookings }}</h4>
                                <small class="text-muted">Completed</small>
                            </div>
                            <div class="col-6 mb-3">
                                <h4 class="text-secondary">{{ analytics.cancelled_bookings }}</h4>
                                <small class="text-muted">Cancelled</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-danger">{{ analytics.disputed_bookings }}</h4>
                                <small class="text-muted">Disputed</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning">{{ analytics.no_show_bookings }}</h4>
                                <small class="text-muted">No Show</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="col-lg-6">
                <div class="card analytics-card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>Performance Metrics
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Confirmation Rate</span>
                                <span class="fw-bold">{{ analytics.confirmation_rate }}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: {{ analytics.confirmation_rate }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Cancellation Rate</span>
                                <span class="fw-bold">{{ analytics.cancellation_rate }}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-secondary" style="width: {{ analytics.cancellation_rate }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Dispute Rate</span>
                                <span class="fw-bold">{{ analytics.dispute_rate }}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-danger" style="width: {{ analytics.dispute_rate }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>No Show Rate</span>
                                <span class="fw-bold">{{ analytics.no_show_rate }}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: {{ analytics.no_show_rate }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dispute Analytics -->
        {% if dispute_analytics.total_disputes > 0 %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card analytics-card">
                    <div class="card-header bg-danger text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Dispute Analytics
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <h4>{{ dispute_analytics.total_disputes }}</h4>
                                <small class="text-muted">Total Disputes</small>
                            </div>
                            <div class="col-md-2">
                                <h4 class="text-danger">{{ dispute_analytics.unresolved_disputes }}</h4>
                                <small class="text-muted">Unresolved</small>
                            </div>
                            <div class="col-md-2">
                                <h4 class="text-success">{{ dispute_analytics.resolved_disputes }}</h4>
                                <small class="text-muted">Resolved</small>
                            </div>
                            <div class="col-md-2">
                                <h4>{{ dispute_analytics.resolution_rate }}%</h4>
                                <small class="text-muted">Resolution Rate</small>
                            </div>
                            <div class="col-md-2">
                                <h4>{{ dispute_analytics.avg_resolution_time_hours|floatformat:1 }}h</h4>
                                <small class="text-muted">Avg Resolution Time</small>
                            </div>
                            <div class="col-md-2">
                                <a href="{% url 'booking_cart_app:admin_dispute_list' %}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-gavel me-1"></i>Manage Disputes
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Top Venues -->
        <div class="row">
            <div class="col-12">
                <div class="card analytics-card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-trophy me-2"></i>Top Performing Venues
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Venue</th>
                                        <th>Total Bookings</th>
                                        <th>Revenue</th>
                                        <th>Confirmed</th>
                                        <th>Disputed</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for venue_stat in top_venues %}
                                    <tr>
                                        <td>{{ forloop.counter }}</td>
                                        <td>
                                            <strong>{{ venue_stat.venue.venue_name|truncatechars:30 }}</strong><br>
                                            <small class="text-muted">{{ venue_stat.venue.service_provider.get_full_name|default:venue_stat.venue.service_provider.user.email }}</small>
                                        </td>
                                        <td>{{ venue_stat.total_bookings }}</td>
                                        <td>${{ venue_stat.total_revenue|floatformat:2 }}</td>
                                        <td>{{ venue_stat.confirmed_bookings }}</td>
                                        <td>
                                            {% if venue_stat.disputed_bookings > 0 %}
                                                <span class="text-danger">{{ venue_stat.disputed_bookings }}</span>
                                            {% else %}
                                                <span class="text-success">0</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'booking_cart_app:admin_booking_list' %}?venue={{ venue_stat.venue.id }}"
                                               class="btn btn-sm btn-outline-primary">
                                                View Bookings
                                            </a>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">No venue data available</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Date range selector
        const dateRangeSelect = document.querySelector('select[name="date_range"]');
        const customStart = document.getElementById('custom-start');
        const customEnd = document.getElementById('custom-end');

        dateRangeSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customStart.style.display = 'block';
                customEnd.style.display = 'block';
            } else {
                customStart.style.display = 'none';
                customEnd.style.display = 'none';
            }
        });

        // Daily trends chart
        const dailyCtx = document.getElementById('dailyTrendsChart').getContext('2d');
        const dailyTrends = {{ daily_trends|safe }};

        new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: dailyTrends.map(item => item.date),
                datasets: [{
                    label: 'Total Bookings',
                    data: dailyTrends.map(item => item.total),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Confirmed',
                    data: dailyTrends.map(item => item.confirmed),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Status distribution chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusData = {{ status_distribution|safe }};

        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: statusData.map(item => item.status),
                datasets: [{
                    data: statusData.map(item => item.count),
                    backgroundColor: [
                        '#28a745', // Confirmed
                        '#ffc107', // Pending
                        '#6c757d', // Cancelled
                        '#007bff', // Completed
                        '#dc3545', // Disputed
                        '#fd7e14'  // No Show
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
{% endblock %}
