{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Cancel Booking - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Cancel Booking
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Booking Information -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Booking Details</h6>
                        <p><strong>Booking ID:</strong> {{ booking.booking_id }}</p>
                        <p><strong>Venue:</strong> {{ booking.venue.venue_name }}</p>
                        <p><strong>Status:</strong> {{ booking.get_status_display }}</p>
                        <p><strong>Total Amount:</strong> ${{ booking.total_amount }}</p>
                    </div>

                    <!-- Services to be cancelled -->
                    <h6 class="mt-3 mb-3">Services to be cancelled:</h6>
                    {% for item in booking.items.all %}
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-md-8">
                                    <strong>{{ item.service_title }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        {{ item.scheduled_date|date:"F d, Y" }} at {{ item.scheduled_time|time:"g:i A" }}
                                    </small>
                                </div>
                                <div class="col-md-4 text-right">
                                    <strong>${{ item.service_price }}</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    <!-- Cancellation Policy -->
                    <div class="alert alert-warning mt-4">
                        <h6><i class="fas fa-clock"></i> Cancellation Policy</h6>
                        <ul class="mb-0">
                            <li>Free cancellation up to 24 hours before your appointment</li>
                            <li>Cancellations within 24 hours may incur a fee</li>
                            <li>No-shows will be charged the full amount</li>
                        </ul>
                    </div>

                    <!-- Cancellation Form -->
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="cancellation_reason">
                                <strong>Reason for cancellation:</strong>
                            </label>
                            <textarea
                                class="form-control"
                                id="cancellation_reason"
                                name="cancellation_reason"
                                rows="3"
                                placeholder="Please provide a reason for cancelling this booking..."
                                required>{{ form.cancellation_reason.value|default:'' }}</textarea>
                            <small class="form-text text-muted">
                                This helps us improve our services and may be shared with the service provider.
                            </small>
                        </div>

                        <div class="form-check mt-3">
                            <input
                                class="form-check-input"
                                type="checkbox"
                                id="confirm_cancellation"
                                required>
                            <label class="form-check-label" for="confirm_cancellation">
                                I understand the cancellation policy and confirm that I want to cancel this booking.
                            </label>
                        </div>

                        <div class="mt-4 text-center">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times"></i> Cancel Booking
                            </button>
                            <a href="{% url 'booking_cart_app:booking_detail' booking.slug %}" class="btn btn-secondary ml-2">
                                <i class="fas fa-arrow-left"></i> Go Back
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');

    form.addEventListener('submit', function(e) {
        if (!confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
            e.preventDefault();
            return false;
        }

        // Disable submit button to prevent double submission
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cancelling...';
    });
});
</script>
{% endblock %}
