{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Booking Details - {{ booking.booking_id }}{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Booking Details</h4>
                    <small class="text-muted">Booking ID: {{ booking.booking_id }}</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Venue Information</h6>
                            <p><strong>{{ booking.venue.venue_name }}</strong></p>
                            <p>{{ booking.venue.short_description }}</p>
                            <p>
                                {{ booking.venue.street_number }} {{ booking.venue.street_name }}<br>
                                {{ booking.venue.city }}, {{ booking.venue.state }}
                            </p>

                            <!-- Service Provider Contact Information -->
                            <div class="mt-3">
                                <h6>Service Provider Contact</h6>
                                {% if booking.venue.service_provider %}
                                    <p><strong>Business:</strong> {{ booking.venue.service_provider.business_name }}</p>
                                    <p><strong>Contact Person:</strong> {{ booking.venue.service_provider.contact_name|default:"Not provided" }}</p>
                                    <p><strong>Phone:</strong>
                                        {% if booking.venue.service_provider.phone %}
                                            <a href="tel:{{ booking.venue.service_provider.phone }}">{{ booking.venue.service_provider.phone }}</a>
                                        {% else %}
                                            Not provided
                                        {% endif %}
                                    </p>
                                    <p><strong>Email:</strong>
                                        <a href="mailto:{{ booking.venue.service_provider.user.email }}">{{ booking.venue.service_provider.user.email }}</a>
                                    </p>
                                    {% if booking.venue.service_provider.business_address %}
                                        <p><strong>Business Address:</strong><br>
                                        {{ booking.venue.service_provider.business_address }}<br>
                                        {{ booking.venue.service_provider.city }}, {{ booking.venue.service_provider.state }} {{ booking.venue.service_provider.zip_code }}
                                        </p>
                                    {% endif %}
                                {% else %}
                                    <p class="text-muted">Provider information not available</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Booking Status</h6>
                            <span class="badge badge-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %} p-2">
                                {{ booking.get_status_display }}
                            </span>

                            <h6 class="mt-3">Booking Date</h6>
                            <p>{{ booking.booking_date|date:"F d, Y g:i A" }}</p>

                            <h6>Total Price</h6>
                            <p class="h5 text-success">${{ booking.total_price }}</p>

                            <!-- Cancellation Information -->
                            <div class="mt-3">
                                <h6>Cancellation Policy</h6>
                                {% if booking.can_be_cancelled %}
                                    <div class="alert alert-info">
                                        <small>
                                            <i class="fas fa-info-circle"></i>
                                            {{ booking.cancellation_reason_display }}
                                            {% if booking.cancellation_deadline %}
                                                <br>Deadline: {{ booking.cancellation_deadline|date:"M d, Y g:i A" }}
                                            {% endif %}
                                        </small>
                                    </div>
                                {% else %}
                                    <div class="alert alert-warning">
                                        <small>
                                            <i class="fas fa-exclamation-triangle"></i>
                                            {{ booking.cancellation_reason_display }}
                                        </small>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if booking.notes %}
                    <div class="mt-3">
                        <h6>Special Notes</h6>
                        <div class="alert alert-light">
                            {{ booking.notes }}
                        </div>
                    </div>
                    {% endif %}

                    <div class="mt-4">
                        <h6>Services Booked</h6>
                        {% for item in booking.items.all %}
                        <div class="border p-3 mb-2 rounded">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>{{ item.service_title }}</h6>
                                    <p class="text-muted">{{ item.service.short_description }}</p>
                                    <p><strong>Date:</strong> {{ item.scheduled_date|date:"F d, Y" }}</p>
                                    <p><strong>Time:</strong> {{ item.scheduled_time|time:"g:i A" }}</p>
                                    <p><strong>Duration:</strong> {{ item.duration_minutes }} minutes</p>
                                </div>
                                <div class="col-md-4 text-right">
                                    <p><strong>Quantity:</strong> {{ item.quantity }}</p>
                                    <p><strong>Price:</strong> ${{ item.service_price }} each</p>
                                    <p class="h6"><strong>Total:</strong> ${{ item.total_price|floatformat:2 }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="card-footer">
                    {% if booking.can_be_cancelled %}
                    <a href="{% url 'booking_cart_app:cancel_booking' booking.slug %}"
                       class="btn btn-danger"
                       onclick="return confirm('Are you sure you want to cancel this booking? This action cannot be undone.')">
                        <i class="fas fa-times"></i> Cancel Booking
                    </a>
                    {% endif %}
                    <a href="{% url 'booking_cart_app:booking_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to My Bookings
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-history"></i> Booking Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <!-- Booking Created -->
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6>Booking Created</h6>
                                <p class="text-muted">{{ booking.booking_date|date:"M d, Y g:i A" }}</p>
                                <small class="text-muted">Your booking request was submitted</small>
                            </div>
                        </div>

                        <!-- Status History from Database -->
                        {% for history in booking.status_history.all %}
                        <div class="timeline-item">
                            <div class="timeline-marker
                                {% if history.new_status == 'confirmed' %}bg-success
                                {% elif history.new_status == 'cancelled' %}bg-danger
                                {% elif history.new_status == 'declined' %}bg-warning
                                {% elif history.new_status == 'completed' %}bg-info
                                {% elif history.new_status == 'disputed' %}bg-dark
                                {% else %}bg-secondary
                                {% endif %}"></div>
                            <div class="timeline-content">
                                <h6>
                                    {% if history.new_status == 'confirmed' %}
                                        <i class="fas fa-check-circle text-success"></i> Booking Confirmed
                                    {% elif history.new_status == 'cancelled' %}
                                        <i class="fas fa-times-circle text-danger"></i> Booking Cancelled
                                    {% elif history.new_status == 'declined' %}
                                        <i class="fas fa-minus-circle text-warning"></i> Booking Declined
                                    {% elif history.new_status == 'completed' %}
                                        <i class="fas fa-flag-checkered text-info"></i> Service Completed
                                    {% elif history.new_status == 'disputed' %}
                                        <i class="fas fa-exclamation-triangle text-dark"></i> Dispute Filed
                                    {% else %}
                                        <i class="fas fa-info-circle text-secondary"></i> Status Updated
                                    {% endif %}
                                </h6>
                                <p class="text-muted">{{ history.changed_at|date:"M d, Y g:i A" }}</p>
                                <small class="text-muted">
                                    Status changed from "{{ history.old_status|title }}" to "{{ history.new_status|title }}"
                                </small>
                            </div>
                        </div>
                        {% endfor %}

                        <!-- Show cancellation reason if applicable -->
                        {% if booking.status == 'cancelled' and booking.cancellation_reason %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-secondary"></div>
                            <div class="timeline-content">
                                <h6><i class="fas fa-comment"></i> Cancellation Reason</h6>
                                <p class="text-muted">{{ booking.cancellation_reason }}</p>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Future service dates -->
                        {% for item in booking.items.all %}
                            {% if item.scheduled_date >= today %}
                            <div class="timeline-item future-event">
                                <div class="timeline-marker bg-light border-primary"></div>
                                <div class="timeline-content">
                                    <h6><i class="fas fa-calendar text-primary"></i> Scheduled Service</h6>
                                    <p class="text-muted">{{ item.scheduled_date|date:"M d, Y" }} at {{ item.scheduled_time|time:"g:i A" }}</p>
                                    <small class="text-muted">{{ item.service_title }}</small>
                                </div>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Emergency Contact Card -->
            {% if booking.venue.service_provider.business_phone_number %}
            <div class="card mt-3">
                <div class="card-body text-center">
                    <h6><i class="fas fa-phone"></i> Need Help?</h6>
                    <p class="mb-2">Contact the service provider directly:</p>
                    <a href="tel:{{ booking.venue.service_provider.business_phone_number }}"
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-phone"></i> Call {{ booking.venue.service_provider.business_phone_number }}
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 25px;
}

.timeline-item.future-event {
    opacity: 0.7;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-content p {
    margin-bottom: 5px;
    font-size: 0.85rem;
}

.timeline-content small {
    font-size: 0.8rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #28a745, #ffc107);
}

.badge {
    font-size: 0.9rem;
}

.alert {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
}

.alert small {
    margin: 0;
}
</style>
{% endblock %}
