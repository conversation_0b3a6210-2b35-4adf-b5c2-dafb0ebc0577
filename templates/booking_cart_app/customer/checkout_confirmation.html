{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Confirm Your Booking - CozyWish{% endblock %}

{% block booking_extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Checkout Confirmation */
    :root {
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    .booking-wrapper {
        background: var(--cw-accent-light);
        font-family: var(--cw-font-primary);
        min-height: 100vh;
        padding: 2rem 0;
    }

    /* Checkout Progress Indicator */
    .checkout-progress {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
    }

    .progress-header {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .progress-header h3 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
    }

    .progress-header .subtitle {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin: 0;
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        margin-bottom: 1rem;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        height: 2px;
        background: var(--cw-brand-accent);
        z-index: 1;
    }

    .progress-steps::after {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        width: 66.66%;
        height: 2px;
        background: var(--cw-brand-primary);
        z-index: 2;
        transition: width 0.3s ease;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        position: relative;
        z-index: 3;
        flex: 1;
        max-width: 120px;
    }

    .step-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: white;
        border: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: var(--cw-neutral-600);
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .step-circle.completed {
        background: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
        color: white;
    }

    .step-circle.active {
        background: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 0 0 4px rgba(47, 22, 15, 0.1);
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--cw-neutral-600);
        line-height: 1.3;
    }

    .step-title.active {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .step-title.completed {
        color: var(--cw-brand-primary);
    }

    /* Breadcrumb Navigation */
    .checkout-breadcrumb {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .breadcrumb-nav {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }

    .breadcrumb-path {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .breadcrumb-link {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .breadcrumb-link:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    .breadcrumb-separator {
        color: var(--cw-neutral-600);
    }

    .breadcrumb-current {
        color: var(--cw-neutral-700);
        font-weight: 600;
    }

    .breadcrumb-actions {
        display: flex;
        gap: 0.75rem;
    }

    .btn-breadcrumb {
        background: white;
        border: 1px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-breadcrumb:hover {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        text-decoration: none;
        transform: translateY(-1px);
    }

    /* Page Header */
    .confirmation-header {
        text-align: center;
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
    }

    .confirmation-header .icon {
        background: var(--cw-gradient-brand-button);
        width: 4rem;
        height: 4rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: white;
        font-size: 1.75rem;
    }

    .confirmation-header h1 {
        font-family: var(--cw-font-heading);
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 2.5rem;
    }

    .confirmation-header .subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin: 0;
        line-height: 1.5;
    }

    /* Booking Summary Stats */
    .booking-summary-stats {
        background: var(--cw-gradient-card);
        border: 1px solid var(--cw-brand-primary);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-md);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 1rem;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        background: white;
        border-radius: 0.75rem;
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-sm);
    }

    .stat-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.25rem;
        font-family: var(--cw-font-display);
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    /* Service Cards */
    .service-confirmation-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .service-confirmation-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .venue-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .venue-info h3 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        font-size: 1.5rem;
    }

    .venue-location {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin: 0;
    }

    .service-card-body {
        padding: 1.5rem;
    }

    .service-item-preview {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .service-item-preview:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-sm);
    }

    .service-preview-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .service-preview-info h4 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
    }

    .service-preview-info .service-description {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin: 0;
        line-height: 1.5;
    }

    .service-preview-price {
        text-align: right;
    }

    .service-preview-price .price {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin: 0;
    }

    .service-preview-price .quantity {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin: 0;
    }

    .service-preview-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem;
    }

    .detail-item {
        text-align: center;
    }

    .detail-item .label {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .detail-item .value {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1rem;
    }

    /* Total Summary */
    .total-summary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .total-summary h3 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }

    .total-amount {
        font-family: var(--cw-font-display);
        font-size: 3rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .total-label {
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin: 0;
    }

    /* Booking Notes */
    .booking-notes-section {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .booking-notes-section h4 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-size: 1.25rem;
    }

    .form-control {
        font-family: var(--cw-font-primary);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        background-color: white;
        color: var(--cw-neutral-800);
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
        resize: vertical;
    }

    .form-control:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    /* Action Buttons */
    .confirmation-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-confirm {
        background: var(--cw-gradient-brand-button);
        color: white;
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 3rem;
        font-size: 1.125rem;
        font-weight: 600;
        font-family: var(--cw-font-heading);
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        text-decoration: none;
        display: inline-block;
    }

    .btn-confirm:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        color: white;
        text-decoration: none;
    }

    .btn-back {
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.75rem;
        padding: 1rem 2rem;
        font-size: 1rem;
        font-weight: 600;
        font-family: var(--cw-font-primary);
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-back:hover {
        background: var(--cw-brand-primary);
        color: white;
        text-decoration: none;
    }

    /* Important Notice */
    .important-notice {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeeba 100%);
        border: 1px solid #ffeaa7;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .important-notice h5 {
        color: #856404;
        margin-bottom: 0.75rem;
        font-weight: 600;
    }

    .important-notice ul {
        margin: 0;
        color: #856404;
    }

    .important-notice li {
        margin-bottom: 0.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .booking-wrapper {
            padding: 1rem 0;
        }

        .confirmation-header h1 {
            font-size: 2rem;
        }

        .total-amount {
            font-size: 2.5rem;
        }

        .confirmation-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-confirm, .btn-back {
            width: 100%;
            max-width: 300px;
        }
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="booking-wrapper">
    <div class="container">
        <!-- Checkout Progress Indicator -->
        <div class="checkout-progress">
            <div class="progress-header">
                <h3>Checkout Progress</h3>
                <p class="subtitle">Step-by-step guide to completing your booking</p>
            </div>
            <div class="progress-steps">
                <div class="progress-step">
                    <div class="step-circle">1</div>
                    <span class="step-title">Select Services</span>
                </div>
                <div class="progress-step">
                    <div class="step-circle">2</div>
                    <span class="step-title">Confirm Details</span>
                </div>
                <div class="progress-step">
                    <div class="step-circle">3</div>
                    <span class="step-title">Payment</span>
                </div>
            </div>
        </div>

        <!-- Breadcrumb Navigation -->
        <div class="checkout-breadcrumb">
            <div class="breadcrumb-nav">
                <div class="breadcrumb-path">
                    <a href="{% url 'booking_cart_app:cart' %}" class="breadcrumb-link">Cart</a>
                    <span class="breadcrumb-separator">/</span>
                    <a href="{% url 'booking_cart_app:checkout' %}" class="breadcrumb-link">Checkout</a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current">Confirmation</span>
                </div>
                <div class="breadcrumb-actions">
                    <a href="{% url 'booking_cart_app:cart' %}" class="btn-breadcrumb">
                        <i class="fas fa-arrow-left me-2"></i>Back to Cart
                    </a>
                </div>
            </div>
        </div>

        <!-- Confirmation Header -->
        <div class="confirmation-header">
            <div class="icon">
                <i class="fas fa-clipboard-check"></i>
            </div>
            <h1>Confirm Your Booking</h1>
            <p class="subtitle">Please review your booking details carefully before confirming</p>
        </div>

        <!-- Booking Summary Stats -->
        <div class="booking-summary-stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">{{ summary_stats.total_services }}</div>
                    <div class="stat-label">Service{{ summary_stats.total_services|pluralize }}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ summary_stats.total_duration }}</div>
                    <div class="stat-label">Total Minutes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ summary_stats.earliest_appointment|date:"M d" }}</div>
                    <div class="stat-label">First Appointment</div>
                </div>
                {% if summary_stats.earliest_appointment != summary_stats.latest_appointment %}
                <div class="stat-item">
                    <div class="stat-value">{{ summary_stats.latest_appointment|date:"M d" }}</div>
                    <div class="stat-label">Last Appointment</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Service Details by Venue -->
        {% for venue_data in venues_data %}
        <div class="service-confirmation-card">
            <div class="service-card-header">
                <div class="venue-info">
                    <div>
                        <h3>{{ venue_data.venue.venue_name }}</h3>
                        <p class="venue-location">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            {{ venue_data.venue.city }}, {{ venue_data.venue.state }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="service-card-body">
                {% for item in venue_data.items %}
                <div class="service-item-preview">
                    <div class="service-preview-header">
                        <div class="service-preview-info">
                            <h4>{{ item.service.service_title }}</h4>
                            <p class="service-description">{{ item.service.short_description|truncatewords:15 }}</p>
                        </div>
                        <div class="service-preview-price">
                            <p class="price">${{ item.total_price }}</p>
                            <p class="quantity">Qty: {{ item.quantity }}</p>
                        </div>
                    </div>

                    <div class="service-preview-details">
                        <div class="detail-item">
                            <div class="label">Date</div>
                            <div class="value">{{ item.selected_date|date:"M d, Y" }}</div>
                        </div>
                        <div class="detail-item">
                            <div class="label">Time</div>
                            <div class="value">{{ item.selected_time_slot }}</div>
                        </div>
                        <div class="detail-item">
                            <div class="label">Duration</div>
                            <div class="value">{{ item.service.duration_minutes }} min</div>
                        </div>
                        <div class="detail-item">
                            <div class="label">Price per item</div>
                            <div class="value">${{ item.price_per_item }}</div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}

        <!-- Total Summary -->
        <div class="total-summary">
            <h3>Total Amount</h3>
            <div class="total-amount">${{ total_price }}</div>
            <p class="total-label">for {{ summary_stats.total_services }} service{{ summary_stats.total_services|pluralize }}</p>
        </div>

        <!-- Important Notice -->
        <div class="important-notice">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>Before You Confirm</h5>
            <ul>
                <li>By confirming this booking, you agree to the selected dates and times</li>
                <li>A payment will be required to complete your booking</li>
                <li>Changes to bookings may be subject to venue policies</li>
                <li>You will receive a confirmation email with all booking details</li>
                <li>The service provider may contact you to confirm specific details</li>
            </ul>
        </div>

        <!-- Booking Notes -->
        <div class="booking-notes-section">
            <h4><i class="fas fa-sticky-note me-2"></i>Special Requests or Notes</h4>
            <p class="mb-3 text-muted">Add any special requests, preferences, or notes for the service provider (optional):</p>

            <form method="post" id="confirmationForm">
                {% csrf_token %}
                <textarea
                    name="booking_notes"
                    class="form-control"
                    rows="4"
                    placeholder="Example: First time customer, prefer quiet treatment room, have allergies to certain oils, etc."
                    maxlength="500"></textarea>
                <small class="text-muted mt-2 d-block">Maximum 500 characters</small>

                <!-- Action Buttons -->
                <div class="confirmation-actions mt-4">
                    <a href="{% url 'booking_cart_app:cart' %}" class="btn-back">
                        <i class="fas fa-arrow-left me-2"></i>Back to Cart
                    </a>
                    <button type="submit" class="btn-confirm" onclick="return confirmBooking()">
                        <i class="fas fa-calendar-check me-2"></i>Confirm Booking
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block booking_extra_js %}
<script>
// Initialize checkout progress
document.addEventListener('DOMContentLoaded', function() {
    initializeCheckoutProgress();
    initializeNotesCharacterCounter();
    initializeRealTimeValidation();
});

function initializeCheckoutProgress() {
    // Mark cart and checkout as completed, confirmation as active
    const steps = document.querySelectorAll('.progress-step');
    const circles = document.querySelectorAll('.step-circle');
    const titles = document.querySelectorAll('.step-title');

    // Step 1: Cart (completed)
    if (circles[0]) {
        circles[0].classList.add('completed');
        circles[0].innerHTML = '<i class="fas fa-check"></i>';
        titles[0].classList.add('completed');
    }

    // Step 2: Confirmation (active)
    if (circles[1]) {
        circles[1].classList.add('active');
        titles[1].classList.add('active');
    }

    // Update progress bar to show 66% completion
    const progressBar = document.querySelector('.progress-steps::after');
    if (progressBar) {
        document.styleSheets[0].insertRule(
            '.progress-steps::after { width: 66.66% !important; }',
            0
        );
    }
}

function initializeNotesCharacterCounter() {
    const notesTextarea = document.querySelector('textarea[name="booking_notes"]');
    if (!notesTextarea) return;

    const maxLength = parseInt(notesTextarea.getAttribute('maxlength')) || 500;

    // Create enhanced character counter
    const counterContainer = document.createElement('div');
    counterContainer.className = 'character-counter-container mt-2';
    counterContainer.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <div class="character-status">
                <span class="character-counter text-muted">
                    <span class="current-count">0</span>/<span class="max-count">${maxLength}</span> characters
                </span>
            </div>
            <div class="character-indicator">
                <div class="progress" style="width: 100px; height: 4px;">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>
        <div class="character-feedback mt-1" style="display: none;">
            <small class="text-muted">
                <i class="fas fa-lightbulb me-1"></i>
                <span class="feedback-text">Add details like preferences, allergies, or special requests</span>
            </small>
        </div>
    `;

    notesTextarea.parentNode.appendChild(counterContainer);

    const currentCount = counterContainer.querySelector('.current-count');
    const progressBar = counterContainer.querySelector('.progress-bar');
    const characterStatus = counterContainer.querySelector('.character-status');
    const feedback = counterContainer.querySelector('.character-feedback');

    function updateCounter() {
        const length = notesTextarea.value.length;
        const percentage = (length / maxLength) * 100;

        // Update count
        currentCount.textContent = length;

        // Update progress bar
        progressBar.style.width = `${Math.min(percentage, 100)}%`;

        // Update colors based on usage
        progressBar.className = 'progress-bar';
        characterStatus.className = 'character-status';

        if (percentage < 50) {
            progressBar.classList.add('bg-success');
        } else if (percentage < 80) {
            progressBar.classList.add('bg-warning');
        } else if (percentage < 95) {
            progressBar.classList.add('bg-danger');
        } else {
            progressBar.classList.add('bg-danger');
            characterStatus.classList.add('text-danger');
        }

        // Show/hide helpful feedback
        if (length === 0) {
            feedback.style.display = 'block';
            feedback.querySelector('.feedback-text').textContent =
                'Add details like preferences, allergies, or special requests';
        } else if (length < 20) {
            feedback.style.display = 'block';
            feedback.querySelector('.feedback-text').textContent =
                'Consider adding more details to help your service provider';
        } else if (length > maxLength - 50) {
            feedback.style.display = 'block';
            feedback.querySelector('.feedback-text').textContent =
                `You have ${maxLength - length} characters remaining`;
        } else {
            feedback.style.display = 'none';
        }
    }

    // Update counter on input with debouncing
    let timeout;
    notesTextarea.addEventListener('input', function() {
        clearTimeout(timeout);
        timeout = setTimeout(updateCounter, 100);
    });

    // Initial update
    updateCounter();
}

function initializeRealTimeValidation() {
    const form = document.getElementById('confirmationForm');
    if (!form) return;

    // Add real-time availability checking
    const submitButton = form.querySelector('.btn-confirm');
    if (submitButton) {
        submitButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Show loading state
            const originalText = this.innerHTML;
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Validating availability...';

            // Perform final availability check
            checkFinalAvailability()
                .then(isAvailable => {
                    if (isAvailable) {
                        return confirmBookingWithUser();
                    } else {
                        throw new Error('Some services are no longer available');
                    }
                })
                .then(confirmed => {
                    if (confirmed) {
                        this.innerHTML = '<i class="fas fa-check me-2"></i>Confirming...';
                        form.submit();
                    } else {
                        // Reset button
                        this.disabled = false;
                        this.innerHTML = originalText;
                    }
                })
                .catch(error => {
                    // Show error and reset button
                    showAvailabilityError(error.message);
                    this.disabled = false;
                    this.innerHTML = originalText;
                });
        });
    }
}

function checkFinalAvailability() {
    // Check availability for all cart items one final time
    return fetch('/bookings/api/validate-cart-availability/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            action: 'final_validation'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.available) {
            return true;
        } else {
            if (data.unavailable_items && data.unavailable_items.length > 0) {
                const itemsList = data.unavailable_items
                    .map(item => `• ${item.service_title} on ${item.date} at ${item.time}`)
                    .join('\n');
                throw new Error(`The following services are no longer available:\n\n${itemsList}`);
            }
            throw new Error('Some services are no longer available');
        }
    });
}

function confirmBookingWithUser() {
    return new Promise((resolve) => {
        // Create enhanced confirmation modal
        const modal = createConfirmationModal();
        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Handle confirmation
        modal.querySelector('.btn-confirm-final').addEventListener('click', () => {
            bootstrapModal.hide();
            resolve(true);
        });

        modal.querySelector('.btn-cancel-final').addEventListener('click', () => {
            bootstrapModal.hide();
            resolve(false);
        });

        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    });
}

function createConfirmationModal() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        Final Confirmation Required
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Before you proceed:</h6>
                        <ul class="mb-0">
                            <li>Your booking will be created and cannot be easily cancelled</li>
                            <li>Payment will be required to complete the process</li>
                            <li>You will receive confirmation details via email</li>
                            <li>The service provider will be notified of your booking</li>
                        </ul>
                    </div>
                    <p class="mb-0"><strong>Are you sure you want to confirm this booking?</strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-cancel-final">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-success btn-confirm-final">
                        <i class="fas fa-check me-2"></i>Yes, Create Booking
                    </button>
                </div>
            </div>
        </div>
    `;
    return modal;
}

function showAvailabilityError(message) {
    // Create error alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    alert.innerHTML = `
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Availability Issue</h6>
        <p class="mb-2">${message}</p>
        <div class="d-flex gap-2">
            <a href="{% url 'booking_cart_app:cart' %}" class="btn btn-sm btn-outline-danger">
                <i class="fas fa-arrow-left me-1"></i>Return to Cart
            </a>
            <button type="button" class="btn btn-sm btn-danger" onclick="location.reload()">
                <i class="fas fa-refresh me-1"></i>Refresh Page
            </button>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 10000);
}

// Legacy confirmation function (kept for compatibility)
function confirmBooking() {
    // This function is now handled by the enhanced validation above
    return false;
}
</script>
{% endblock %}
