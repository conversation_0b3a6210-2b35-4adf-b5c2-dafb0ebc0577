{% extends 'discount_app/base_discount_admin.html' %}
{% load i18n %}

{% block title %}{% trans "Approve Discount" %} - {{ discount.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        {% trans "Approve Discount" %}: {{ discount.name }}
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Discount Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>{% trans "Discount Information" %}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{% trans "Name" %}:</strong></td>
                                    <td>{{ discount.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Type" %}:</strong></td>
                                    <td>
                                        <span class="badge bg-primary">{{ discount_type|title }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Discount Value" %}:</strong></td>
                                    <td>
                                        {% if discount.discount_type == 'percentage' %}
                                            {{ discount.discount_value }}%
                                        {% else %}
                                            ${{ discount.discount_value }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Start Date" %}:</strong></td>
                                    <td>{{ discount.start_date|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "End Date" %}:</strong></td>
                                    <td>{{ discount.end_date|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Current Status" %}:</strong></td>
                                    <td>
                                        {% if discount.is_approved %}
                                            <span class="badge bg-success">{% trans "Approved" %}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{% trans "Pending Approval" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Additional Details" %}</h5>
                            <table class="table table-borderless">
                                {% if discount_type == 'service' %}
                                    <tr>
                                        <td><strong>{% trans "Service" %}:</strong></td>
                                        <td>{{ discount.service.service_title }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{% trans "Venue" %}:</strong></td>
                                        <td>{{ discount.service.venue.venue_name }}</td>
                                    </tr>
                                {% elif discount_type == 'venue' %}
                                    <tr>
                                        <td><strong>{% trans "Venue" %}:</strong></td>
                                        <td>{{ discount.venue.venue_name }}</td>
                                    </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>{% trans "Created By" %}:</strong></td>
                                    <td>{{ discount.created_by.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% trans "Created Date" %}:</strong></td>
                                    <td>{{ discount.created_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                {% if discount.max_uses %}
                                    <tr>
                                        <td><strong>{% trans "Max Uses" %}:</strong></td>
                                        <td>{{ discount.max_uses }}</td>
                                    </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    {% if discount.description %}
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>{% trans "Description" %}</h5>
                                <p class="text-muted">{{ discount.description }}</p>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Approval Form -->
                    <div class="row">
                        <div class="col-12">
                            <h5>{% trans "Approval Decision" %}</h5>
                            <form method="post">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label class="form-label">{% trans "Decision" %}</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="is_approved" id="approve" value="True"
                                               {% if discount.is_approved %}checked{% endif %}>
                                        <label class="form-check-label text-success" for="approve">
                                            <i class="fas fa-check me-1"></i>{% trans "Approve" %}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="is_approved" id="reject" value="False"
                                               {% if not discount.is_approved %}checked{% endif %}>
                                        <label class="form-check-label text-danger" for="reject">
                                            <i class="fas fa-times me-1"></i>{% trans "Reject" %}
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="rejection_reason" class="form-label">{% trans "Rejection Reason" %} <small class="text-muted">({% trans "optional" %})</small></label>
                                    <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3"
                                              placeholder="{% trans 'Provide reason for rejection...' %}">{{ form.rejection_reason.value|default:'' }}</textarea>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>{% trans "Save Decision" %}
                                    </button>
                                    <a href="{% url 'discount_app:admin_discount_list' discount_type %}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to List" %}
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
