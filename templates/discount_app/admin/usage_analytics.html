{% extends 'discount_app/base_discount_admin.html' %}

{% block title %}Usage Analytics - Discount Management - Admin - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'css/admin_app/admin_app.css' %}">
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .analytics-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .metric-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .table-responsive {
        max-height: 400px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'discount_app:admin_discount_dashboard' %}">Discount Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Usage Analytics</li>
                    </ol>
                </nav>

                <h1 class="page-title mb-3">
                    <i class="fas fa-chart-line me-2"></i>Discount Usage Analytics
                </h1>
                <p class="text-muted">Comprehensive analytics and insights on discount usage patterns</p>
            </div>
        </div>

        <!-- Date Range Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card analytics-card">
                    <div class="card-body">
                        <form method="get" class="row align-items-end" aria-label="Usage analytics filter">
                            <div class="col-md-3">
                                <label for="range" class="form-label">Date Range</label>
                                <select name="range" id="range" class="form-select" aria-label="Date range">
                                    <option value="7" {% if date_range == '7' %}selected{% endif %}>Last 7 days</option>
                                    <option value="30" {% if date_range == '30' %}selected{% endif %}>Last 30 days</option>
                                    <option value="90" {% if date_range == '90' %}selected{% endif %}>Last 90 days</option>
                                    <option value="180" {% if date_range == '180' %}selected{% endif %}>Last 6 months</option>
                                    <option value="365" {% if date_range == '365' %}selected{% endif %}>Last year</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i> Apply Filter
                                </button>
                            </div>
                            <div class="col-md-7 text-end">
                                <small class="text-muted">
                                    Showing data from {{ start_date }} to {{ end_date }}
                                </small>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overall Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value text-primary">{{ overall_stats.total_usage|default:0 }}</div>
                        <div class="metric-label">Total Discount Uses</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value text-success">${{ overall_stats.total_savings|default:0|floatformat:2 }}</div>
                        <div class="metric-label">Total Customer Savings</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value text-warning">${{ overall_stats.avg_discount|default:0|floatformat:2 }}</div>
                        <div class="metric-label">Average Discount Amount</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value text-info">{{ overall_savings_percentage }}%</div>
                        <div class="metric-label">Average Savings Percentage</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage by Discount Type -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card analytics-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Usage by Discount Type</h5>
                    </div>
                    <div class="card-body">
                        {% if usage_by_type %}
                            {% for type_data in usage_by_type %}
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <strong>{{ type_data.discount_type|title }}</strong>
                                    <div class="progress mt-1" style="height: 8px;">
                                        <div class="progress-bar" role="progressbar"
                                             style="width: {% widthratio type_data.usage_count overall_stats.total_usage 100 %}%">
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div><strong>{{ type_data.usage_count }}</strong> uses</div>
                                    <small class="text-muted">${{ type_data.total_savings|floatformat:2 }} saved</small>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No usage data available for the selected period.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card analytics-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">Revenue Impact</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="metric-value text-secondary">${{ overall_stats.total_original_value|default:0|floatformat:2 }}</div>
                                    <div class="metric-label">Original Value</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="metric-value text-success">${{ overall_stats.total_final_value|default:0|floatformat:2 }}</div>
                                    <div class="metric-label">Final Value</div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <div class="metric-value text-danger">${{ overall_stats.total_savings|default:0|floatformat:2 }}</div>
                            <div class="metric-label">Total Discount Given</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Discounts -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card analytics-card">
                    <div class="card-header bg-success text-white">
                        <h6 class="card-title mb-0">Top Venue Discounts</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Discount</th>
                                        <th>Uses</th>
                                        <th>Savings</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for discount in top_venue_discounts %}
                                    <tr>
                                        <td>
                                            <strong>{{ discount.name|truncatechars:15 }}</strong><br>
                                            <small class="text-muted">{{ discount.venue.venue_name|truncatechars:20 }}</small>
                                        </td>
                                        <td>{{ discount.usage_count }}</td>
                                        <td>${{ discount.total_savings|default:0|floatformat:2 }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="3" class="text-muted">No data available</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card analytics-card">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">Top Service Discounts</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Discount</th>
                                        <th>Uses</th>
                                        <th>Savings</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for discount in top_service_discounts %}
                                    <tr>
                                        <td>
                                            <strong>{{ discount.name|truncatechars:15 }}</strong><br>
                                            <small class="text-muted">{{ discount.service.service_title|truncatechars:20 }}</small>
                                        </td>
                                        <td>{{ discount.usage_count }}</td>
                                        <td>${{ discount.total_savings|default:0|floatformat:2 }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="3" class="text-muted">No data available</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card analytics-card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="card-title mb-0">Top Platform Discounts</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Discount</th>
                                        <th>Uses</th>
                                        <th>Savings</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for discount in top_platform_discounts %}
                                    <tr>
                                        <td>
                                            <strong>{{ discount.name|truncatechars:15 }}</strong><br>
                                            <small class="text-muted">
                                                {% if discount.category %}{{ discount.category.name }}{% else %}All Categories{% endif %}
                                            </small>
                                        </td>
                                        <td>{{ discount.usage_count }}</td>
                                        <td>${{ discount.total_savings|default:0|floatformat:2 }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="3" class="text-muted">No data available</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="row">
            <div class="col-12">
                <div class="card analytics-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">Top Customers by Discount Usage</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Email</th>
                                        <th>Total Uses</th>
                                        <th>Total Savings</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in top_customers %}
                                    <tr>
                                        <td>{{ customer.user__first_name }} {{ customer.user__last_name }}</td>
                                        <td>{{ customer.user__email }}</td>
                                        <td>{{ customer.usage_count }}</td>
                                        <td>${{ customer.total_savings|floatformat:2 }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="4" class="text-muted">No customer usage data available for the selected period.</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when date range changes
        document.getElementById('range').addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
{% endblock %}
