{% extends 'dashboard_app/base_dashboard.html' %}
{% load static i18n %}
{% load discount_tags %}

{% block title %}Manage Discounts - Provider Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Manage Discounts{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'discount_app:provider_discount_analytics' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-chart-line me-1"></i>Analytics
    </a>
    <a href="{% url 'discount_app:provider_discount_list_detailed' %}" class="btn btn-sm btn-outline-secondary">
        <i class="fas fa-list me-1"></i>Detailed View
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard_app:provider_dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item active" aria-current="page">Manage Discounts</li>
    </ol>
</nav>



<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="h3 mb-1 text-primary">{{ total_active_discounts }}</div>
                <div class="text-muted small">Active Discounts</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="h3 mb-1 text-warning">{{ total_pending_discounts }}</div>
                <div class="text-muted small">Pending Approval</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="h3 mb-1 text-success">{{ recent_usage.total_uses|default:0 }}</div>
                <div class="text-muted small">Uses (30 days)</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="h3 mb-1 text-info">${{ recent_usage.total_savings|default:0|floatformat:0 }}</div>
                <div class="text-muted small">Customer Savings</div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <a href="{% url 'discount_app:create_venue_discount' %}" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-building me-2"></i>Create Venue-Wide Discount
                            <small class="d-block text-white-50">Apply discount to all services</small>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-primary w-100 mb-2" data-bs-toggle="modal" data-bs-target="#quickServiceDiscountModal">
                            <i class="fas fa-spa me-2"></i>Quick Service Discount
                            <small class="d-block text-muted">Fast discount for specific service</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Services with Discount Management -->
<div class="row">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-spa me-2"></i>Your Services</h5>
                <small class="text-muted">{{ services.count }} service{{ services.count|pluralize }}</small>
            </div>
            <div class="card-body">
                {% if services_with_discounts %}
                    <div class="row">
                        {% for service_data in services_with_discounts %}
                        {% with service=service_data.service discount=service_data.discount %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card service-discount-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">
                                            {% if service_data.has_discount and discount %}
                                                {{ discount.name|truncatechars:25 }}
                                            {% else %}
                                                {{ service.service_title|truncatechars:25 }}
                                            {% endif %}
                                        </h6>
                                        {% if service_data.has_discount %}
                                            <span class="badge bg-success">Active Discount</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">No Discount</span>
                                        {% endif %}
                                    </div>

                                    <div class="mb-2">
                                        <small class="text-muted">Price: ${{ service.price_min }}{% if service.price_max and service.price_max != service.price_min %} - ${{ service.price_max }}{% endif %}</small>
                                    </div>

                                    {% if service_data.has_discount and discount %}
                                        <div class="discount-info mb-2">
                                            <div class="text-success small">
                                                <i class="fas fa-percentage me-1"></i>
                                                {% if discount.discount_type == 'percentage' %}
                                                    {{ discount.discount_value }}% OFF
                                                {% else %}
                                                    ${{ discount.discount_value }} OFF
                                                {% endif %}
                                            </div>
                                            <div class="text-muted small">
                                                Expires: {{ discount.end_date|date:"M d, Y" }}
                                            </div>
                                        </div>
                                        <div class="btn-group w-100">
                                            <a href="{% url 'discount_app:edit_service_discount' discount_slug=discount.slug %}"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="{% url 'discount_app:service_discount_detail' discount_slug=discount.slug %}"
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </div>
                                    {% else %}
                                        <a href="{% url 'discount_app:create_service_discount' %}?service={{ service.id }}"
                                           class="btn btn-sm btn-primary w-100">
                                            <i class="fas fa-plus me-1"></i>Add Discount
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endwith %}
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Services Yet</h5>
                        <p class="text-muted">Add services to your venue to create discounts.</p>
                        <a href="{% url 'venues_app:service_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Your First Service
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Venue-Wide Discounts -->
{% if active_venue_discounts or pending_venue_discounts %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>Venue-Wide Discounts</h5>
            </div>
            <div class="card-body">
                {% for discount in active_venue_discounts %}
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <div>
                        <h6 class="mb-1">{{ discount.name }}</h6>
                        <small class="text-muted">
                            {% if discount.discount_type == 'percentage' %}
                                {{ discount.discount_value }}% OFF all services
                            {% else %}
                                ${{ discount.discount_value }} OFF all services
                            {% endif %}
                            • Expires: {{ discount.end_date|date:"M d, Y" }}
                        </small>
                    </div>
                    <div class="btn-group">
                        <a href="{% url 'discount_app:edit_venue_discount' discount_slug=discount.slug %}"
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="{% url 'discount_app:venue_discount_detail' discount_slug=discount.slug %}"
                           class="btn btn-sm btn-outline-info">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
                {% endfor %}

                {% for discount in pending_venue_discounts %}
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <div>
                        <h6 class="mb-1">{{ discount.name }} <span class="badge bg-warning">Pending</span></h6>
                        <small class="text-muted">
                            {% if discount.discount_type == 'percentage' %}
                                {{ discount.discount_value }}% OFF all services
                            {% else %}
                                ${{ discount.discount_value }} OFF all services
                            {% endif %}
                        </small>
                    </div>
                    <div class="btn-group">
                        <a href="{% url 'discount_app:edit_venue_discount' discount_slug=discount.slug %}"
                           class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-edit"></i>
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Service Discount Modal -->
<div class="modal fade" id="quickServiceDiscountModal" tabindex="-1" aria-labelledby="quickServiceDiscountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickServiceDiscountModalLabel">Quick Service Discount</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="quickDiscountForm" method="post" action="{% url 'discount_app:create_service_discount' %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="service_select" class="form-label">Select Service</label>
                        <select class="form-select" id="service_select" name="service" required>
                            <option value="">Choose a service...</option>
                            {% for service in services %}
                            <option value="{{ service.id }}">{{ service.service_title }} (${{ service.price_min }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="discount_type" class="form-label">Discount Type</label>
                        <select class="form-select" id="discount_type" name="discount_type" required>
                            <option value="percentage">Percentage Off</option>
                            <option value="fixed_amount">Fixed Amount Off</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="discount_value" class="form-label">Discount Value</label>
                        <input type="number" class="form-control" id="discount_value" name="discount_value"
                               min="1" max="80" step="1" required>
                        <div class="form-text">For percentage: 1-80%. For fixed amount: dollar value.</div>
                    </div>
                    <div class="mb-3">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="quickDiscountForm" class="btn btn-primary">Create Discount</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block dashboard_js %}
<style>
.service-discount-card {
    border: 2px solid #f8f9fa;
    transition: all 0.2s ease;
}

.service-discount-card:hover {
    border-color: #007bff;
    transform: translateY(-2px);
}

.dashboard-card {
    border: 2px solid black;
    border-radius: 0.5rem;
}

.discount-info {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date for end date to tomorrow
    const endDateInput = document.getElementById('end_date');
    if (endDateInput) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        endDateInput.min = tomorrow.toISOString().split('T')[0];
    }

    // Update discount value validation based on type
    const discountTypeSelect = document.getElementById('discount_type');
    const discountValueInput = document.getElementById('discount_value');

    if (discountTypeSelect && discountValueInput) {
        discountTypeSelect.addEventListener('change', function() {
            if (this.value === 'percentage') {
                discountValueInput.max = '80';
                discountValueInput.placeholder = 'e.g., 20 (for 20% off)';
            } else {
                discountValueInput.max = '1000';
                discountValueInput.placeholder = 'e.g., 50 (for $50 off)';
            }
        });
    }
});
</script>
{% endblock %}
