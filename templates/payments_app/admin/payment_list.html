{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Payment Management" %} - CozyWish Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Payment Management" %}</h3>
                    <div class="card-tools">
                        <a href="{% url 'payments_app:admin_payment_analytics' %}" class="btn btn-info">
                            <i class="fas fa-chart-bar"></i> {% trans "Analytics" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Payment ID" %}</th>
                                    <th>{% trans "Customer" %}</th>
                                    <th>{% trans "Provider" %}</th>
                                    <th>{% trans "Amount" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ payment.payment_id|truncatechars:8 }}...</td>
                                    <td>{{ payment.customer.get_full_name|default:payment.customer.email }}</td>
                                    <td>{{ payment.provider.get_full_name|default:payment.provider.email }}</td>
                                    <td>${{ payment.amount_paid }}</td>
                                    <td>
                                        <span class="badge badge-{% if payment.payment_status == 'succeeded' %}success{% elif payment.payment_status == 'failed' %}danger{% else %}warning{% endif %}">
                                            {{ payment.get_payment_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <a href="{% url 'payments_app:admin_payment_detail' payment_id=payment.payment_id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> {% trans "View" %}
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    {% if is_paginated %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <p>{% trans "No payments found." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
