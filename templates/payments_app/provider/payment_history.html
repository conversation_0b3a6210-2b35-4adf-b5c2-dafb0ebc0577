{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Payment History" %} - Service Provider - CozyWish{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'accounts_app:service_provider_profile' %}">Provider Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'payments_app:provider_earnings' %}">Earnings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Payment History</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1>Payment History</h1>
                    {% if user.service_provider_profile %}
                        <p class="text-muted">{{ user.service_provider_profile.business_name }}</p>
                    {% endif %}
                </div>
                <div class="btn-group" role="group">
                    <a href="{% url 'payments_app:provider_earnings' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-2"></i>Earnings Overview
                    </a>
                    <a href="{% url 'payments_app:provider_payout_history' %}" class="btn btn-outline-success">
                        <i class="fas fa-money-bill-wave me-2"></i>Payout History
                    </a>
                </div>
            </div>

            <!-- Payment Statistics Summary -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title text-primary">Total Payments</h5>
                            <h3 class="text-primary">{{ payment_stats.total_payments }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title text-success">Successful</h5>
                            <h3 class="text-success">{{ payment_stats.successful_payments }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-danger">
                        <div class="card-body text-center">
                            <h5 class="card-title text-danger">Failed</h5>
                            <h3 class="text-danger">{{ payment_stats.failed_payments }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h5 class="card-title text-warning">Total Earnings</h5>
                            <h3 class="text-warning">${{ payment_stats.total_earnings|floatformat:2 }}</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter Form -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>Search &amp; Filter Payments
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#providerPaymentSearchCollapse" aria-expanded="true" aria-controls="providerPaymentSearchCollapse">
                        <i class="fas fa-filter me-1"></i>Filters
                    </button>
                </div>
                <div id="providerPaymentSearchCollapse" class="collapse show">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            {{ search_form.search_query.label_tag }}
                            {{ search_form.search_query }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.status.label_tag }}
                            {{ search_form.status }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.payment_method.label_tag }}
                            {{ search_form.payment_method }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.date_from.label_tag }}
                            {{ search_form.date_from }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.date_to.label_tag }}
                            {{ search_form.date_to }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.amount_min.label_tag }}
                            {{ search_form.amount_min }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.amount_max.label_tag }}
                            {{ search_form.amount_max }}
                        </div>
                        <div class="col-md-8 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                            <a href="{% url 'payments_app:provider_payment_history' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
                </div>
            </div>

            {% if active_filters %}
            <div class="mb-3">
                {% for chip in active_filters %}
                <span class="badge bg-secondary me-2">{{ chip }}</span>
                {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3 text-end">
                <a href="?{{ request.GET.urlencode }}&format=csv" class="btn btn-sm btn-outline-primary me-2">
                    <i class="fas fa-file-csv me-1"></i>CSV
                </a>
                <a href="?{{ request.GET.urlencode }}&format=pdf" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-file-pdf me-1"></i>PDF
                </a>
            </div>

            {% include 'payments_app/includes/payment_skeleton.html' %}
            <div id="payment-results" style="display:none;">
            <!-- Payment List -->
            {% if payments %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Payment Records" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><a href="?sort=payment_date&dir={% if request.GET.sort == 'payment_date' and request.GET.dir != 'asc' %}asc{% else %}desc{% endif %}">{% trans "Date" %}</a></th>
                                    <th><a href="?sort=payment_id&dir={% if request.GET.sort == 'payment_id' and request.GET.dir != 'asc' %}asc{% else %}desc{% endif %}">{% trans "Payment ID" %}</a></th>
                                    <th>{% trans "Customer" %}</th>
                                    <th>{% trans "Booking" %}</th>
                                    <th><a href="?sort=amount_paid&dir={% if request.GET.sort == 'amount_paid' and request.GET.dir != 'asc' %}asc{% else %}desc{% endif %}">{% trans "Amount" %}</a></th>
                                    <th>{% trans "Method" %}</th>
                                    <th><a href="?sort=payment_status&dir={% if request.GET.sort == 'payment_status' and request.GET.dir != 'asc' %}asc{% else %}desc{% endif %}">{% trans "Status" %}</a></th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ payment.payment_date|date:"M d, Y H:i" }}</td>
                                    <td>
                                        <small id="ppid{{ forloop.counter }}" class="text-muted font-monospace">{{ payment.payment_id|truncatechars:8 }}</small>
                                        <button type="button" class="btn btn-link btn-sm p-0 ms-1 btn-copy" data-copy-target="ppid{{ forloop.counter }}" data-bs-toggle="tooltip" title="{% trans 'Copy ID' %}">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </td>
                                    <td>
                                        {% if payment.customer.first_name %}
                                            {{ payment.customer.first_name }} {{ payment.customer.last_name }}
                                        {% else %}
                                            {{ payment.customer.email }}
                                        {% endif %}
                                        <br>
                                        <small class="text-muted">{{ payment.customer.email }}</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ payment.booking.booking_id|truncatechars:8 }}</small>
                                        <br>
                                        <small class="text-muted">{{ payment.booking.venue.name|truncatechars:20 }}</small>
                                    </td>
                                    <td>
                                        <strong>${{ payment.amount_paid|floatformat:2 }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            {{ payment.get_payment_method_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if payment.payment_status == 'succeeded' %}
                                            <span class="badge" style="background-color: black; color: white;">Succeeded</span>
                                        {% elif payment.payment_status == 'pending' %}
                                            <span class="badge" style="background-color: white; color: black; border: 2px solid black;">Pending</span>
                                        {% elif payment.payment_status == 'processing' %}
                                            <span class="badge" style="background-color: white; color: black; border: 2px solid black;">Processing</span>
                                        {% elif payment.payment_status == 'failed' %}
                                            <span class="badge" style="background-color: black; color: white;">Failed</span>
                                        {% elif payment.payment_status == 'cancelled' %}
                                            <span class="badge" style="background-color: white; color: black; border: 2px solid black;">Cancelled</span>
                                        {% elif payment.payment_status == 'refunded' %}
                                            <span class="badge" style="background-color: black; color: white;">Refunded</span>
                                        {% elif payment.payment_status == 'partially_refunded' %}
                                            <span class="badge" style="background-color: white; color: black; border: 2px solid black;">Partially Refunded</span>
                                        {% else %}
                                            <span class="badge" style="background-color: white; color: black; border: 2px solid black;">{{ payment.get_payment_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'payments_app:provider_payment_detail' payment_id=payment.payment_id %}"
                                           class="btn btn-sm btn-outline-primary" title="View Details" data-bs-toggle="tooltip">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="Payment history pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">First</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
            {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-receipt fa-3x mb-3" style="color: black; opacity: 0.6;"></i>
                    <h5 style="color: black; opacity: 0.7;">{% trans "No payments found" %}</h5>
                    <p style="color: black; opacity: 0.7;">
                        {% if request.GET %}
                            {% trans "No payments match your search criteria. Try adjusting your filters." %}
                        {% else %}
                            {% trans "You haven't received any payments yet. Payments from customers will appear here." %}
                        {% endif %}
                    </p>
                    {% if not request.GET %}
                        <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>{% trans "Create Your First Venue" %}
                        </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            </div>
            <!-- Navigation Links -->
            <div class="d-flex justify-content-between mt-4">
                <a href="{% url 'payments_app:provider_earnings' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Earnings" %}
                </a>
                <div>
                    <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-calendar-check me-2"></i>{% trans "View Bookings" %}
                    </a>
                    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>{% trans "Dashboard" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
