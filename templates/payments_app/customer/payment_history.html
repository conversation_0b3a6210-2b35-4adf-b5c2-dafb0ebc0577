{% extends 'payments_app/base_payments.html' %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block title %}{% trans "Payment History" %} - CozyWish{% endblock %}

{% block payments_extra_css %}
<style>
    /* Payment history specific styles - black & white theme */
    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .section-subtitle {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.7);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Statistics cards */
    .stats-card {
        text-align: center;
        padding: 1.5rem;
    }

    .stats-card h5 {
        font-family: var(--font-heading);
        font-weight: 700;
        font-size: 1.5rem;
        color: black;
        margin-bottom: 0.5rem;
    }

    .stats-card p {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 0;
    }

    /* Copy button styling */
    .btn-copy {
        color: black !important;
        font-size: 0.8rem;
    }

    .btn-copy:hover {
        color: rgba(0, 0, 0, 0.7) !important;
    }
</style>
{% endblock %}

{% block payments_content %}
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="section-title">{% trans "Payment History" %}</h1>
        <p class="section-subtitle">{% trans "View your payment transaction history" %} - {{ user.email }}</p>
    </div>
    <div>
        <a href="{% url 'payments_app:refund_history' %}" class="btn btn-outline-primary">
            <i class="fas fa-undo me-2"></i>{% trans "Refund History" %}
        </a>
    </div>
</div>
<!-- Payment Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <h5>{{ payment_stats.total_payments }}</h5>
            <p>Total Payments</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <h5>${{ payment_stats.total_amount|floatformat:2 }}</h5>
            <p>Total Amount</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <h5>{{ payment_stats.successful_payments }}</h5>
            <p>Successful</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <h5>{{ payment_stats.pending_payments }}</h5>
            <p>Pending</p>
        </div>
    </div>
</div>
<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>Search &amp; Filter
        </h5>
        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#paymentSearchCollapse" aria-expanded="true" aria-controls="paymentSearchCollapse">
            <i class="fas fa-filter me-1"></i>Filters
        </button>
    </div>
    <div id="paymentSearchCollapse" class="collapse show">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                {{ search_form.search_query|as_crispy_field }}
            </div>
            <div class="col-md-2">
                {{ search_form.status|as_crispy_field }}
            </div>
            <div class="col-md-2">
                {{ search_form.payment_method|as_crispy_field }}
            </div>
            <div class="col-md-2">
                {{ search_form.date_from|as_crispy_field }}
            </div>
            <div class="col-md-2">
                {{ search_form.date_to|as_crispy_field }}
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Search
                </button>
                <a href="{% url 'payments_app:payment_history' %}" class="btn btn-outline-primary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
    </div>
</div>

{% if active_filters %}
<div class="mb-3">
    {% for chip in active_filters %}
    <span class="badge bg-primary me-2">{{ chip }}</span>
    {% endfor %}
</div>
{% endif %}

<div class="mb-3 text-end">
    <a href="?{{ request.GET.urlencode }}&format=csv" class="btn btn-sm btn-outline-primary me-2">
        <i class="fas fa-file-csv me-1"></i>CSV
    </a>
    <a href="?{{ request.GET.urlencode }}&format=pdf" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-file-pdf me-1"></i>PDF
    </a>
</div>

{% include 'payments_app/includes/payment_skeleton.html' %}
<div id="payment-results" style="display:none;">
<!-- Payment List -->
{% if payments %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-credit-card me-2"></i>{% trans "Payment Transactions" %}
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th><a href="?sort=payment_id&dir={% if request.GET.sort == 'payment_id' and request.GET.dir != 'asc' %}asc{% else %}desc{% endif %}" class="text-decoration-none text-dark">{% trans "Payment ID" %}</a></th>
                        <th><a href="?sort=payment_date&dir={% if request.GET.sort == 'payment_date' and request.GET.dir != 'asc' %}asc{% else %}desc{% endif %}" class="text-decoration-none text-dark">{% trans "Date" %}</a></th>
                        <th>{% trans "Booking" %}</th>
                        <th>{% trans "Venue" %}</th>
                        <th><a href="?sort=amount_paid&dir={% if request.GET.sort == 'amount_paid' and request.GET.dir != 'asc' %}asc{% else %}desc{% endif %}" class="text-decoration-none text-dark">{% trans "Amount" %}</a></th>
                        <th>{% trans "Method" %}</th>
                        <th><a href="?sort=payment_status&dir={% if request.GET.sort == 'payment_status' and request.GET.dir != 'asc' %}asc{% else %}desc{% endif %}" class="text-decoration-none text-dark">{% trans "Status" %}</a></th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>
                                        <code id="pid{{ forloop.counter }}">{{ payment.payment_id|truncatechars:12 }}</code>
                                        <button type="button" class="btn btn-link btn-sm p-0 ms-1 btn-copy" data-copy-target="pid{{ forloop.counter }}" data-bs-toggle="tooltip" title="{% trans 'Copy ID' %}">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </td>
                                    <td>{{ payment.payment_date|date:"M d, Y H:i" }}</td>
                                    <td>
                                        <a href="{% url 'booking_cart_app:booking_detail' booking_slug=payment.booking.slug %}" class="text-decoration-none text-dark">
                                            {{ payment.booking.booking_id|truncatechars:12 }}
                                        </a>
                                    </td>
                                    <td>{{ payment.booking.venue.name|truncatechars:20 }}</td>
                                    <td class="payment-amount">${{ payment.amount_paid }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ payment.get_payment_method_display }}</span>
                                    </td>
                                    <td>
                                        {% if payment.payment_status == 'succeeded' %}
                                        <span class="badge bg-success">Succeeded</span>
                                        {% elif payment.payment_status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                        {% elif payment.payment_status == 'processing' %}
                                        <span class="badge bg-info">Processing</span>
                                        {% elif payment.payment_status == 'failed' %}
                                        <span class="badge bg-danger">Failed</span>
                                        {% elif payment.payment_status == 'refunded' %}
                                        <span class="badge bg-primary">Refunded</span>
                                        {% elif payment.payment_status == 'partially_refunded' %}
                                        <span class="badge bg-warning">Partially Refunded</span>
                                        {% else %}
                                        <span class="badge bg-primary">{{ payment.get_payment_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'payments_app:payment_detail' payment_id=payment.payment_id %}"
                                               class="btn btn-outline-primary" title="View Details" data-bs-toggle="tooltip">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if payment.payment_status == 'succeeded' %}
                                            <a href="{% url 'payments_app:payment_receipt' payment_id=payment.payment_id %}"
                                               class="btn btn-outline-secondary" title="Download Receipt" data-bs-toggle="tooltip">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% endif %}
                                            {% if payment.is_refundable and not payment.refund_requests.all %}
                                            <a href="{% url 'payments_app:refund_request' payment_id=payment.payment_id %}"
                                               class="btn btn-outline-warning" title="Request Refund" data-bs-toggle="tooltip">
                                                <i class="fas fa-undo"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Payment history pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}">Last</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

{% else %}
<!-- Empty State -->
<div class="card">
    <div class="card-body text-center py-5">
        <div class="mb-4">
            <i class="fas fa-credit-card fa-4x text-muted"></i>
        </div>
        <h4 class="mb-3">{% trans "No Payments Yet" %}</h4>
        <p class="text-muted mb-4">{% trans "You don't have any payment transactions yet. Book a service to get started!" %}</p>
        <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
            <i class="fas fa-search me-2"></i>{% trans "Browse Services" %}
        </a>
    </div>
</div>
{% endif %}

</div>
<!-- Navigation -->
<div class="d-flex justify-content-between mt-4">
    <a href="{% url 'home' %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Home" %}
    </a>
    <a href="{% url 'booking_cart_app:booking_list' %}" class="btn btn-primary">
        <i class="fas fa-calendar-check me-2"></i>{% trans "View Bookings" %}
    </a>
</div>
{% endblock %}
