# --- Django Imports ---
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError

# --- Third-party Imports ---
from cities_light.models import City, Region, Country
from address.models import Address<PERSON>ield
from geoposition.fields import Geo<PERSON><PERSON>ield
from taggit.managers import TaggableManager

# --- Local App Imports ---
from ..utils import validate_phone_number, validate_website_url


class ModernLocation(models.Model):
    """
    Modern location model using django-cities-light for standardized location data.
    This will eventually replace the custom USCity model.
    """
    
    # Basic Information
    name = models.CharField(
        _("location name"),
        max_length=200,
        help_text=_("Display name for this location")
    )
    
    # Standardized Location Data (django-cities-light)
    city = models.ForeignKey(
        City,
        on_delete=models.CASCADE,
        related_name="modern_locations",
        verbose_name=_("city"),
        help_text=_("Standardized city from cities_light")
    )
    
    # Address Information (django-address)
    address = AddressField(
        blank=True,
        null=True,
        help_text=_("Complete address information")
    )
    
    # GPS Coordinates (django-geoposition)
    position = GeopositionField(
        blank=True,
        null=True,
        help_text=_("GPS coordinates for this location")
    )
    
    # Additional Information
    description = models.TextField(
        _("description"),
        blank=True,
        help_text=_("Optional description of this location")
    )
    
    # Contact Information
    phone = models.CharField(
        _("phone number"),
        max_length=20,
        blank=True,
        help_text=_("Contact phone number")
    )
    
    website = models.URLField(
        _("website"),
        blank=True,
        help_text=_("Website URL")
    )
    
    # Tags for categorization
    tags = TaggableManager(
        blank=True,
        help_text=_("Tags for categorizing this location")
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = _("Modern Location")
        verbose_name_plural = _("Modern Locations")
        ordering = ['city__name', 'name']
        indexes = [
            models.Index(fields=['city', 'is_active']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.city.name}, {self.city.region.name}"
    
    def clean(self):
        """Validate the location data."""
        super().clean()
        
        # Validate phone number if provided
        if self.phone:
            try:
                validate_phone_number(self.phone)
            except ValidationError as e:
                raise ValidationError({'phone': e.message})
        
        # Validate website URL if provided
        if self.website:
            try:
                validate_website_url(self.website)
            except ValidationError as e:
                raise ValidationError({'website': e.message})
    
    @property
    def full_address(self):
        """Return the complete address string."""
        if self.address:
            return str(self.address)
        return f"{self.city.name}, {self.city.region.name}, {self.city.country.name}"
    
    @property
    def coordinates(self):
        """Return coordinates as tuple (latitude, longitude)."""
        if self.position:
            return (self.position.latitude, self.position.longitude)
        return None
    
    @property
    def region_name(self):
        """Return the region/state name."""
        return self.city.region.name
    
    @property
    def country_name(self):
        """Return the country name."""
        return self.city.country.name
    
    def get_nearby_locations(self, radius_km=50):
        """
        Get nearby locations within specified radius.
        This is a simplified version - for production, use PostGIS.
        """
        if not self.position:
            return ModernLocation.objects.none()
        
        # Simple bounding box calculation (approximate)
        lat_delta = radius_km / 111.0  # Rough conversion
        lng_delta = radius_km / (111.0 * abs(float(self.position.latitude)))
        
        min_lat = float(self.position.latitude) - lat_delta
        max_lat = float(self.position.latitude) + lat_delta
        min_lng = float(self.position.longitude) - lng_delta
        max_lng = float(self.position.longitude) + lng_delta
        
        return ModernLocation.objects.filter(
            position__latitude__range=(min_lat, max_lat),
            position__longitude__range=(min_lng, max_lng),
            is_active=True
        ).exclude(id=self.id)


class LocationMigrationMapping(models.Model):
    """
    Temporary model to track migration from USCity to ModernLocation.
    This helps maintain data integrity during the transition.
    """
    
    # Reference to old USCity
    uscity_id = models.CharField(
        _("USCity ID"),
        max_length=20,
        unique=True,
        help_text=_("Original USCity city_id")
    )
    
    # Reference to new ModernLocation
    modern_location = models.ForeignKey(
        ModernLocation,
        on_delete=models.CASCADE,
        related_name="migration_mappings",
        verbose_name=_("modern location")
    )
    
    # Migration metadata
    migrated_at = models.DateTimeField(auto_now_add=True)
    migration_notes = models.TextField(
        blank=True,
        help_text=_("Notes about the migration process")
    )
    
    class Meta:
        verbose_name = _("Location Migration Mapping")
        verbose_name_plural = _("Location Migration Mappings")
        ordering = ['-migrated_at']
    
    def __str__(self):
        return f"USCity {self.uscity_id} -> {self.modern_location}"


class VenueLocationHistory(models.Model):
    """
    Track venue location changes for audit purposes.
    """
    
    venue = models.ForeignKey(
        'venues_app.Venue',
        on_delete=models.CASCADE,
        related_name="location_history"
    )
    
    # Previous location data
    previous_location_type = models.CharField(
        max_length=20,
        choices=[
            ('uscity', 'USCity'),
            ('modern', 'ModernLocation'),
        ]
    )
    previous_location_id = models.CharField(max_length=50)
    
    # New location data
    new_location_type = models.CharField(
        max_length=20,
        choices=[
            ('uscity', 'USCity'),
            ('modern', 'ModernLocation'),
        ]
    )
    new_location_id = models.CharField(max_length=50)
    
    # Change metadata
    changed_at = models.DateTimeField(auto_now_add=True)
    changed_by = models.ForeignKey(
        'accounts_app.CustomUser',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    change_reason = models.TextField(
        blank=True,
        help_text=_("Reason for location change")
    )
    
    class Meta:
        verbose_name = _("Venue Location History")
        verbose_name_plural = _("Venue Location Histories")
        ordering = ['-changed_at']
    
    def __str__(self):
        return f"{self.venue.venue_name} location changed on {self.changed_at}"
