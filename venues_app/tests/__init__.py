from unittest.mock import patch

# Disable asynchronous notifications during tests to avoid database locking
patch("notifications_app.utils.run_async", lambda func, *args, **kwargs: None).start()
patch("venues_app.signals.run_async", lambda func, *args, **kwargs: None).start()

# Also patch any threading operations that might cause database locks
patch("threading.Thread.start", lambda self: None).start()
