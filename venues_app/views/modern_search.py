# --- Django Imports ---
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.db.models import Q, Count, Avg
from django.core.paginator import Paginator
from django.views.generic import ListView
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank

# --- Third-party Imports ---
import watson
from django_filters import FilterSet, Char<PERSON>ilter, NumberFilter, ChoiceFilter, ModelChoiceFilter
from cities_light.models import City, Region
from taggit.models import Tag

# --- Local App Imports ---
from ..models import Venue, Service, ModernLocation, USCity
from ..forms.search import VenueSearchForm


class VenueFilter(FilterSet):
    """
    Advanced filtering for venues using django-filter.
    """
    
    # Text search
    search = CharFilter(method='filter_search', label='Search')
    
    # Location filters
    city = ModelChoiceFilter(
        queryset=City.objects.all(),
        field_name='modern_location__city',
        empty_label="Any City"
    )
    
    region = ModelChoiceFilter(
        queryset=Region.objects.all(),
        field_name='modern_location__city__region',
        empty_label="Any State/Region"
    )
    
    # Legacy location support
    legacy_city = CharFilter(field_name='city', lookup_expr='icontains')
    legacy_state = CharFilter(field_name='state', lookup_expr='icontains')
    
    # Note: Venue model doesn't have a venue_type field
    # venue_type = ChoiceFilter(
    #     choices=Venue.VENUE_TYPE_CHOICES,
    #     empty_label="Any Type"
    # )
    
    # Capacity filters
    min_capacity = NumberFilter(field_name='max_capacity', lookup_expr='gte')
    max_capacity = NumberFilter(field_name='max_capacity', lookup_expr='lte')
    
    # Price range filters
    min_price = NumberFilter(method='filter_min_price')
    max_price = NumberFilter(method='filter_max_price')
    
    # Rating filter
    min_rating = NumberFilter(method='filter_min_rating')
    
    # Tags filter
    tags = CharFilter(method='filter_tags')
    
    class Meta:
        model = Venue
        fields = [
            'search', 'city', 'region', 'legacy_city', 'legacy_state',
            'min_capacity', 'max_capacity',
            'min_price', 'max_price', 'min_rating', 'tags'
        ]
    
    def filter_search(self, queryset, name, value):
        """Full-text search using django-watson."""
        if value:
            # Use watson for comprehensive search
            search_results = watson.search(value, models=(Venue,))
            venue_ids = [result.object.id for result in search_results if hasattr(result.object, 'id')]
            return queryset.filter(id__in=venue_ids)
        return queryset
    
    def filter_min_price(self, queryset, name, value):
        """Filter by minimum service price."""
        if value:
            return queryset.filter(services__price_min__gte=value).distinct()
        return queryset
    
    def filter_max_price(self, queryset, name, value):
        """Filter by maximum service price."""
        if value:
            return queryset.filter(services__price_max__lte=value).distinct()
        return queryset
    
    def filter_min_rating(self, queryset, name, value):
        """Filter by minimum average rating."""
        if value:
            return queryset.annotate(
                avg_rating=Avg('reviews__rating')
            ).filter(avg_rating__gte=value)
        return queryset
    
    def filter_tags(self, queryset, name, value):
        """Filter by tags (comma-separated)."""
        if value:
            tag_names = [tag.strip() for tag in value.split(',')]
            return queryset.filter(tags__name__in=tag_names).distinct()
        return queryset


class ServiceFilter(FilterSet):
    """
    Advanced filtering for services.
    """
    
    search = CharFilter(method='filter_search', label='Search Services')
    
    # Price filters
    min_price = NumberFilter(field_name='price_min', lookup_expr='gte')
    max_price = NumberFilter(field_name='price_max', lookup_expr='lte')
    
    # Note: Service model doesn't have SERVICE_TYPE_CHOICES
    # service_type = ChoiceFilter(
    #     choices=Service.SERVICE_TYPE_CHOICES,
    #     empty_label="Any Service Type"
    # )
    
    # Venue location filters
    venue_city = ModelChoiceFilter(
        queryset=City.objects.all(),
        field_name='venue__modern_location__city',
        empty_label="Any City"
    )
    
    venue_region = ModelChoiceFilter(
        queryset=Region.objects.all(),
        field_name='venue__modern_location__city__region',
        empty_label="Any State/Region"
    )
    
    # Legacy venue location support
    venue_legacy_city = CharFilter(field_name='venue__city', lookup_expr='icontains')
    venue_legacy_state = CharFilter(field_name='venue__state', lookup_expr='icontains')
    
    class Meta:
        model = Service
        fields = [
            'search', 'min_price', 'max_price',
            'venue_city', 'venue_region', 'venue_legacy_city', 'venue_legacy_state'
        ]
    
    def filter_search(self, queryset, name, value):
        """Full-text search for services using django-watson."""
        if value:
            search_results = watson.search(value, models=(Service,))
            service_ids = [result.object.id for result in search_results if hasattr(result.object, 'id')]
            return queryset.filter(id__in=service_ids)
        return queryset


class ModernSearchView(ListView):
    """
    Modern search view using django-filter and watson search.
    """
    model = Venue
    template_name = 'venues_app/search/modern_search.html'
    context_object_name = 'venues'
    paginate_by = 12
    
    def get_queryset(self):
        """Get filtered queryset."""
        queryset = Venue.objects.filter(
            is_active=True,
            approval_status='approved'
        ).select_related(
            'us_city', 'modern_location__city__region__country'
        ).prefetch_related(
            'services', 'images', 'tags', 'reviews'
        )
        
        # Apply filters
        self.filterset = VenueFilter(self.request.GET, queryset=queryset)
        return self.filterset.qs.distinct()
    
    def get_context_data(self, **kwargs):
        """Add filter form and search statistics to context."""
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset
        context['search_query'] = self.request.GET.get('search', '')
        
        # Add search statistics
        total_results = self.filterset.qs.count()
        context['total_results'] = total_results
        
        # Add popular tags
        context['popular_tags'] = Tag.objects.filter(
            taggit_taggeditem_items__content_type__model='venue'
        ).annotate(
            usage_count=Count('taggit_taggeditem_items')
        ).order_by('-usage_count')[:10]
        
        # Add popular cities
        context['popular_cities'] = City.objects.filter(
            modern_locations__isnull=False
        ).annotate(
            venue_count=Count('modern_locations')
        ).order_by('-venue_count')[:10]
        
        return context


def autocomplete_locations(request):
    """
    AJAX endpoint for location autocomplete.
    """
    query = request.GET.get('q', '')
    results = []
    
    if len(query) >= 2:
        # Search modern locations
        modern_locations = ModernLocation.objects.filter(
            Q(name__icontains=query) |
            Q(city__name__icontains=query) |
            Q(city__region__name__icontains=query)
        ).select_related('city__region__country')[:10]
        
        for location in modern_locations:
            results.append({
                'id': f'modern_{location.id}',
                'text': f"{location.name} - {location.city.name}, {location.city.region.name}",
                'type': 'modern'
            })
        
        # Search legacy USCity locations
        us_cities = USCity.objects.filter(
            Q(city__icontains=query) |
            Q(state__icontains=query)
        )[:10]
        
        for city in us_cities:
            results.append({
                'id': f'legacy_{city.city_id}',
                'text': f"{city.city}, {city.state}",
                'type': 'legacy'
            })
    
    return JsonResponse({'results': results})


def autocomplete_tags(request):
    """
    AJAX endpoint for tag autocomplete.
    """
    query = request.GET.get('q', '')
    results = []
    
    if len(query) >= 2:
        tags = Tag.objects.filter(
            name__icontains=query
        ).annotate(
            usage_count=Count('taggit_taggeditem_items')
        ).order_by('-usage_count')[:10]
        
        for tag in tags:
            results.append({
                'id': tag.name,
                'text': tag.name,
                'usage_count': tag.usage_count
            })
    
    return JsonResponse({'results': results})


def search_suggestions(request):
    """
    AJAX endpoint for search suggestions.
    """
    query = request.GET.get('q', '')
    suggestions = []
    
    if len(query) >= 2:
        # Get venue name suggestions
        venues = Venue.objects.filter(
            venue_name__icontains=query,
            is_active=True,
            approval_status='approved'
        ).values_list('venue_name', flat=True)[:5]
        
        suggestions.extend([{'text': name, 'type': 'venue'} for name in venues])
        
        # Get service suggestions
        services = Service.objects.filter(
            service_title__icontains=query,
            is_active=True
        ).values_list('service_title', flat=True)[:5]
        
        suggestions.extend([{'text': title, 'type': 'service'} for title in services])
        
        # Get tag suggestions
        tags = Tag.objects.filter(
            name__icontains=query
        ).values_list('name', flat=True)[:3]
        
        suggestions.extend([{'text': tag, 'type': 'tag'} for tag in tags])
    
    return JsonResponse({'suggestions': suggestions})
