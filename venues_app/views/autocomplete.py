# --- Django Imports ---
from django.http import JsonResponse
from django.db.models import Q, Count

# --- Third-party Imports ---
from dal import autocomplete
from cities_light.models import City, Region, Country
from taggit.models import Tag

# --- Local App Imports ---
from ..models import Venue, Service, ModernLocation, USCity


class CityAutocomplete(autocomplete.Select2QuerySetView):
    """
    Autocomplete view for cities using django-autocomplete-light.
    """
    
    def get_queryset(self):
        """Return filtered cities based on search term."""
        if not self.request.user.is_authenticated:
            return City.objects.none()
        
        qs = City.objects.select_related('region', 'country')
        
        if self.q:
            qs = qs.filter(
                Q(name__icontains=self.q) |
                Q(region__name__icontains=self.q) |
                Q(country__name__icontains=self.q)
            )
        
        return qs.order_by('name')[:20]
    
    def get_result_label(self, result):
        """Format the display label for each city."""
        return f"{result.name}, {result.region.name}, {result.country.name}"


class RegionAutocomplete(autocomplete.Select2QuerySetView):
    """
    Autocomplete view for regions/states.
    """
    
    def get_queryset(self):
        """Return filtered regions based on search term."""
        if not self.request.user.is_authenticated:
            return Region.objects.none()
        
        qs = Region.objects.select_related('country')
        
        if self.q:
            qs = qs.filter(
                Q(name__icontains=self.q) |
                Q(country__name__icontains=self.q)
            )
        
        return qs.order_by('name')[:20]
    
    def get_result_label(self, result):
        """Format the display label for each region."""
        return f"{result.name}, {result.country.name}"


class VenueAutocomplete(autocomplete.Select2QuerySetView):
    """
    Autocomplete view for venues.
    """
    
    def get_queryset(self):
        """Return filtered venues based on search term."""
        if not self.request.user.is_authenticated:
            return Venue.objects.none()
        
        qs = Venue.objects.filter(
            is_active=True,
            approval_status='approved'
        ).select_related('us_city')
        
        if self.q:
            qs = qs.filter(
                Q(venue_name__icontains=self.q) |
                Q(city__icontains=self.q) |
                Q(state__icontains=self.q)
            )
        
        return qs.order_by('venue_name')[:20]
    
    def get_result_label(self, result):
        """Format the display label for each venue."""
        return f"{result.venue_name} - {result.city}, {result.state}"


class ServiceAutocomplete(autocomplete.Select2QuerySetView):
    """
    Autocomplete view for services.
    """
    
    def get_queryset(self):
        """Return filtered services based on search term."""
        if not self.request.user.is_authenticated:
            return Service.objects.none()
        
        qs = Service.objects.filter(
            is_active=True
        ).select_related('venue')
        
        if self.q:
            qs = qs.filter(
                Q(service_title__icontains=self.q) |
                Q(venue__venue_name__icontains=self.q)
            )
        
        return qs.order_by('service_title')[:20]
    
    def get_result_label(self, result):
        """Format the display label for each service."""
        return f"{result.service_title} - {result.venue.venue_name}"


def location_autocomplete_ajax(request):
    """
    AJAX endpoint for location autocomplete (both modern and legacy).
    """
    query = request.GET.get('q', '').strip()
    results = []
    
    if len(query) >= 2:
        # Search modern locations first
        modern_locations = ModernLocation.objects.filter(
            Q(name__icontains=query) |
            Q(city__name__icontains=query) |
            Q(city__region__name__icontains=query)
        ).select_related(
            'city__region__country'
        ).order_by('city__name', 'name')[:10]
        
        for location in modern_locations:
            results.append({
                'id': f'modern_{location.id}',
                'text': f"{location.name} - {location.city.name}, {location.city.region.name}",
                'type': 'modern_location',
                'city': location.city.name,
                'region': location.city.region.name,
                'country': location.city.country.name
            })
        
        # Search legacy USCity locations
        us_cities = USCity.objects.filter(
            Q(city__icontains=query) |
            Q(state__icontains=query) |
            Q(county__icontains=query)
        ).order_by('city', 'state')[:10]
        
        for city in us_cities:
            results.append({
                'id': f'legacy_{city.city_id}',
                'text': f"{city.city}, {city.state}",
                'type': 'legacy_city',
                'city': city.city,
                'state': city.state,
                'county': city.county
            })
        
        # Search cities_light cities directly
        cities = City.objects.filter(
            Q(name__icontains=query) |
            Q(region__name__icontains=query)
        ).select_related(
            'region__country'
        ).order_by('name')[:10]
        
        for city in cities:
            results.append({
                'id': f'city_{city.id}',
                'text': f"{city.name}, {city.region.name}",
                'type': 'city',
                'city': city.name,
                'region': city.region.name,
                'country': city.country.name
            })
    
    return JsonResponse({'results': results})


def tag_autocomplete_ajax(request):
    """
    AJAX endpoint for tag autocomplete.
    """
    query = request.GET.get('q', '').strip()
    results = []
    
    if len(query) >= 1:
        tags = Tag.objects.filter(
            name__icontains=query
        ).annotate(
            usage_count=Count('taggit_taggeditem_items')
        ).order_by('-usage_count', 'name')[:15]
        
        for tag in tags:
            results.append({
                'id': tag.name,
                'text': tag.name,
                'usage_count': tag.usage_count
            })
    
    return JsonResponse({'results': results})


def search_suggestions_ajax(request):
    """
    AJAX endpoint for search suggestions.
    """
    query = request.GET.get('q', '').strip()
    suggestions = []
    
    if len(query) >= 2:
        # Venue suggestions
        venues = Venue.objects.filter(
            venue_name__icontains=query,
            is_active=True,
            approval_status='approved'
        ).values_list('venue_name', flat=True).order_by('venue_name')[:5]
        
        for venue_name in venues:
            suggestions.append({
                'text': venue_name,
                'type': 'venue',
                'category': 'Venues'
            })
        
        # Service suggestions
        services = Service.objects.filter(
            service_title__icontains=query,
            is_active=True
        ).values_list('service_title', flat=True).order_by('service_title')[:5]
        
        for service_title in services:
            suggestions.append({
                'text': service_title,
                'type': 'service',
                'category': 'Services'
            })
        
        # Location suggestions
        cities = City.objects.filter(
            name__icontains=query
        ).select_related('region').order_by('name')[:3]
        
        for city in cities:
            suggestions.append({
                'text': f"{city.name}, {city.region.name}",
                'type': 'location',
                'category': 'Locations'
            })
        
        # Tag suggestions
        tags = Tag.objects.filter(
            name__icontains=query
        ).annotate(
            usage_count=Count('taggit_taggeditem_items')
        ).order_by('-usage_count')[:3]
        
        for tag in tags:
            suggestions.append({
                'text': tag.name,
                'type': 'tag',
                'category': 'Tags'
            })
    
    return JsonResponse({
        'suggestions': suggestions,
        'query': query
    })


def popular_searches_ajax(request):
    """
    AJAX endpoint for popular searches and trending data.
    """
    # Popular venues (by view count or rating)
    popular_venues = Venue.objects.filter(
        is_active=True,
        approval_status='approved'
    ).order_by('-created_at')[:5]  # Could be ordered by view_count if available
    
    # Popular cities
    popular_cities = City.objects.annotate(
        venue_count=Count('modern_locations')
    ).filter(venue_count__gt=0).order_by('-venue_count')[:5]
    
    # Popular tags
    popular_tags = Tag.objects.annotate(
        usage_count=Count('taggit_taggeditem_items')
    ).filter(usage_count__gt=0).order_by('-usage_count')[:8]
    
    return JsonResponse({
        'popular_venues': [
            {
                'name': venue.venue_name,
                'location': f"{venue.city}, {venue.state}",
                'url': venue.get_absolute_url()
            }
            for venue in popular_venues
        ],
        'popular_cities': [
            {
                'name': f"{city.name}, {city.region.name}",
                'venue_count': city.venue_count
            }
            for city in popular_cities
        ],
        'popular_tags': [
            {
                'name': tag.name,
                'usage_count': tag.usage_count
            }
            for tag in popular_tags
        ]
    })
