# Generated by Django 5.2.3 on 2025-07-05 20:26

import address.models
import django.db.models.deletion
import taggit.managers
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("address", "0003_auto_20200830_1851"),
        ("cities_light", "0011_alter_city_country_alter_city_region_and_more"),
        (
            "taggit",
            "0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx",
        ),
        ("venues_app", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ModernLocation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Display name for this location",
                        max_length=200,
                        verbose_name="location name",
                    ),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=7,
                        help_text="Latitude coordinate",
                        max_digits=10,
                        null=True,
                        verbose_name="latitude",
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=7,
                        help_text="Longitude coordinate",
                        max_digits=10,
                        null=True,
                        verbose_name="longitude",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Optional description of this location",
                        verbose_name="description",
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        help_text="Contact phone number",
                        max_length=20,
                        verbose_name="phone number",
                    ),
                ),
                (
                    "website",
                    models.URLField(
                        blank=True, help_text="Website URL", verbose_name="website"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "address",
                    address.models.AddressField(
                        blank=True,
                        help_text="Complete address information",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="address.address",
                    ),
                ),
                (
                    "city",
                    models.ForeignKey(
                        help_text="Standardized city from cities_light",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="modern_locations",
                        to="cities_light.city",
                        verbose_name="city",
                    ),
                ),
                (
                    "tags",
                    taggit.managers.TaggableManager(
                        blank=True,
                        help_text="Tags for categorizing this location",
                        through="taggit.TaggedItem",
                        to="taggit.Tag",
                        verbose_name="Tags",
                    ),
                ),
            ],
            options={
                "verbose_name": "Modern Location",
                "verbose_name_plural": "Modern Locations",
                "ordering": ["city__name", "name"],
            },
        ),
        migrations.CreateModel(
            name="LocationMigrationMapping",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "uscity_id",
                    models.CharField(
                        help_text="Original USCity city_id",
                        max_length=20,
                        unique=True,
                        verbose_name="USCity ID",
                    ),
                ),
                ("migrated_at", models.DateTimeField(auto_now_add=True)),
                (
                    "migration_notes",
                    models.TextField(
                        blank=True, help_text="Notes about the migration process"
                    ),
                ),
                (
                    "modern_location",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="migration_mappings",
                        to="venues_app.modernlocation",
                        verbose_name="modern location",
                    ),
                ),
            ],
            options={
                "verbose_name": "Location Migration Mapping",
                "verbose_name_plural": "Location Migration Mappings",
                "ordering": ["-migrated_at"],
            },
        ),
        migrations.CreateModel(
            name="VenueLocationHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "previous_location_type",
                    models.CharField(
                        choices=[("uscity", "USCity"), ("modern", "ModernLocation")],
                        max_length=20,
                    ),
                ),
                ("previous_location_id", models.CharField(max_length=50)),
                (
                    "new_location_type",
                    models.CharField(
                        choices=[("uscity", "USCity"), ("modern", "ModernLocation")],
                        max_length=20,
                    ),
                ),
                ("new_location_id", models.CharField(max_length=50)),
                ("changed_at", models.DateTimeField(auto_now_add=True)),
                (
                    "change_reason",
                    models.TextField(
                        blank=True, help_text="Reason for location change"
                    ),
                ),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "venue",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="location_history",
                        to="venues_app.venue",
                    ),
                ),
            ],
            options={
                "verbose_name": "Venue Location History",
                "verbose_name_plural": "Venue Location Histories",
                "ordering": ["-changed_at"],
            },
        ),
        migrations.AddIndex(
            model_name="modernlocation",
            index=models.Index(
                fields=["city", "is_active"], name="venues_app__city_id_d58dba_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="modernlocation",
            index=models.Index(
                fields=["created_at"], name="venues_app__created_1cb755_idx"
            ),
        ),
    ]
