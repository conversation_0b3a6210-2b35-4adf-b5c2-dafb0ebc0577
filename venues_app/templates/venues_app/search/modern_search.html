{% extends "base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Modern Venue Search - CozyWish{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
.search-filters {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.search-results-header {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.venue-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: box-shadow 0.2s;
}

.venue-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.popular-tags .badge {
    margin: 2px;
    cursor: pointer;
}

.popular-cities .list-group-item {
    cursor: pointer;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Search Filters Sidebar -->
        <div class="col-md-3">
            <div class="search-filters">
                <h5>Search Filters</h5>
                <form method="get" id="search-form">
                    <!-- Main Search -->
                    <div class="mb-3">
                        <label for="{{ filter.form.search.id_for_label }}" class="form-label">Search</label>
                        {{ filter.form.search|add_class:"form-control" }}
                    </div>

                    <!-- Location Filters -->
                    <div class="mb-3">
                        <label for="{{ filter.form.city.id_for_label }}" class="form-label">City</label>
                        {{ filter.form.city|add_class:"form-control" }}
                    </div>

                    <div class="mb-3">
                        <label for="{{ filter.form.region.id_for_label }}" class="form-label">State/Region</label>
                        {{ filter.form.region|add_class:"form-control" }}
                    </div>

                    <!-- Legacy Location -->
                    <div class="mb-3">
                        <label for="{{ filter.form.legacy_city.id_for_label }}" class="form-label">City (Legacy)</label>
                        {{ filter.form.legacy_city|add_class:"form-control" }}
                    </div>

                    <div class="mb-3">
                        <label for="{{ filter.form.legacy_state.id_for_label }}" class="form-label">State (Legacy)</label>
                        {{ filter.form.legacy_state|add_class:"form-control" }}
                    </div>

                    <!-- Capacity Range -->
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="{{ filter.form.min_capacity.id_for_label }}" class="form-label">Min Capacity</label>
                                {{ filter.form.min_capacity|add_class:"form-control" }}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="{{ filter.form.max_capacity.id_for_label }}" class="form-label">Max Capacity</label>
                                {{ filter.form.max_capacity|add_class:"form-control" }}
                            </div>
                        </div>
                    </div>

                    <!-- Price Range -->
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="{{ filter.form.min_price.id_for_label }}" class="form-label">Min Price</label>
                                {{ filter.form.min_price|add_class:"form-control" }}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="{{ filter.form.max_price.id_for_label }}" class="form-label">Max Price</label>
                                {{ filter.form.max_price|add_class:"form-control" }}
                            </div>
                        </div>
                    </div>

                    <!-- Rating -->
                    <div class="mb-3">
                        <label for="{{ filter.form.min_rating.id_for_label }}" class="form-label">Minimum Rating</label>
                        {{ filter.form.min_rating|add_class:"form-control" }}
                    </div>

                    <!-- Tags -->
                    <div class="mb-3">
                        <label for="{{ filter.form.tags.id_for_label }}" class="form-label">Tags</label>
                        {{ filter.form.tags|add_class:"form-control" }}
                    </div>

                    <button type="submit" class="btn btn-primary w-100">Search</button>
                    <a href="{% url 'venues_app:modern_search' %}" class="btn btn-outline-secondary w-100 mt-2">Clear Filters</a>
                </form>
            </div>

            <!-- Popular Tags -->
            {% if popular_tags %}
            <div class="mt-4">
                <h6>Popular Tags</h6>
                <div class="popular-tags">
                    {% for tag in popular_tags %}
                    <span class="badge bg-secondary" onclick="addTag('{{ tag.name }}')">
                        {{ tag.name }} ({{ tag.usage_count }})
                    </span>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Popular Cities -->
            {% if popular_cities %}
            <div class="mt-4">
                <h6>Popular Cities</h6>
                <div class="list-group popular-cities">
                    {% for city in popular_cities %}
                    <a href="#" class="list-group-item list-group-item-action" onclick="selectCity('{{ city.name }}')">
                        {{ city.name }} ({{ city.venue_count }} venues)
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Search Results -->
        <div class="col-md-9">
            <div class="search-results-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4>Search Results</h4>
                        {% if search_query %}
                        <p class="text-muted">Results for "{{ search_query }}"</p>
                        {% endif %}
                        <p class="text-muted">{{ total_results }} venue{{ total_results|pluralize }} found</p>
                    </div>
                </div>
            </div>

            <!-- Results -->
            {% if venues %}
                {% for venue in venues %}
                <div class="venue-card">
                    <div class="row">
                        <div class="col-md-3">
                            {% if venue.main_image %}
                            <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" class="img-fluid rounded">
                            {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 120px;">
                                <span class="text-muted">No Image</span>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-9">
                            <h5>
                                <a href="{{ venue.get_absolute_url }}" class="text-decoration-none">
                                    {{ venue.venue_name }}
                                </a>
                            </h5>
                            <p class="text-muted">
                                <i class="fas fa-map-marker-alt"></i>
                                {{ venue.city }}, {{ venue.state }}
                            </p>
                            {% if venue.short_description %}
                            <p>{{ venue.short_description|truncatewords:20 }}</p>
                            {% endif %}
                            
                            <!-- Services -->
                            {% if venue.services.all %}
                            <div class="mb-2">
                                <small class="text-muted">Services:</small>
                                {% for service in venue.services.all|slice:":3" %}
                                <span class="badge bg-light text-dark">{{ service.service_title }}</span>
                                {% endfor %}
                                {% if venue.services.count > 3 %}
                                <span class="badge bg-light text-dark">+{{ venue.services.count|add:"-3" }} more</span>
                                {% endif %}
                            </div>
                            {% endif %}

                            <!-- Tags -->
                            {% if venue.tags.all %}
                            <div class="mb-2">
                                {% for tag in venue.tags.all|slice:":5" %}
                                <span class="badge bg-secondary">{{ tag.name }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}

                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {% if venue.max_capacity %}
                                    <small class="text-muted">
                                        <i class="fas fa-users"></i> Up to {{ venue.max_capacity }} guests
                                    </small>
                                    {% endif %}
                                </div>
                                <a href="{{ venue.get_absolute_url }}" class="btn btn-outline-primary btn-sm">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Search results pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <h5>No venues found</h5>
                    <p class="text-muted">Try adjusting your search criteria or browse all venues.</p>
                    <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">Browse All Venues</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for autocomplete fields
    $('#id_city, #id_region').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select...',
        allowClear: true
    });
});

function addTag(tagName) {
    const tagsInput = document.getElementById('id_tags');
    const currentTags = tagsInput.value;
    if (currentTags) {
        tagsInput.value = currentTags + ', ' + tagName;
    } else {
        tagsInput.value = tagName;
    }
}

function selectCity(cityName) {
    const cityInput = document.getElementById('id_legacy_city');
    cityInput.value = cityName;
    document.getElementById('search-form').submit();
}
</script>
{% endblock %}
