"""
Management command to seed venue categories with 5 core categories.
Creates essential venue categories for the CozyWish platform.
"""

from django.core.management.base import BaseCommand
from django.db import transaction

from venues_app.models import Category


class Command(BaseCommand):
    """Seed venue categories with 5 essential categories."""

    help = "Seed venues_app with 5 core venue categories"

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing categories before seeding",
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(self.style.SUCCESS("🌱 Starting venue categories seeding..."))

        if options["clear"]:
            self.clear_existing_categories()

        with transaction.atomic():
            self.create_core_categories()

        self.stdout.write(
            self.style.SUCCESS("✅ Venue categories seeding completed successfully!")
        )

    def clear_existing_categories(self):
        """Clear existing categories."""
        self.stdout.write("🧹 Clearing existing categories...")

        try:
            Category.objects.all().delete()
            self.stdout.write("   ✅ Existing categories cleared")
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"   ⚠️ Warning during category clearing: {str(e)}")
            )

    def create_core_categories(self):
        """Create 5 core venue categories."""
        self.stdout.write("🏷️ Creating core venue categories...")

        # Define the 5 core categories
        categories_data = [
            {
                "category_name": "Spa & Wellness",
                "category_description": "Full-service spa facilities offering comprehensive wellness treatments, relaxation services, and rejuvenation experiences including facials, body treatments, and holistic therapies.",
                "is_active": True,
            },
            {
                "category_name": "Massage Therapy",
                "category_description": "Professional massage therapy services including therapeutic massage, deep tissue, Swedish massage, hot stone, and specialized bodywork treatments for pain relief and relaxation.",
                "is_active": True,
            },
            {
                "category_name": "Beauty & Salon",
                "category_description": "Beauty and salon services including hair styling, nail care, makeup application, eyebrow and lash treatments, and comprehensive beauty enhancement services.",
                "is_active": True,
            },
            {
                "category_name": "Medical Spa",
                "category_description": "Medical aesthetic and wellness treatments combining medical expertise with spa luxury, including advanced skincare, anti-aging treatments, and medically supervised wellness services.",
                "is_active": True,
            },
            {
                "category_name": "Fitness & Wellness",
                "category_description": "Fitness and wellness centers offering yoga classes, fitness training, wellness coaching, meditation sessions, and integrated health and fitness programs.",
                "is_active": True,
            },
        ]

        created_count = 0
        updated_count = 0

        for category_data in categories_data:
            category, created = Category.objects.get_or_create(
                category_name=category_data["category_name"], defaults=category_data
            )

            if created:
                created_count += 1
                self.stdout.write(f"   ✅ Created category: {category.category_name}")
            else:
                # Update existing category to ensure it's active and has proper description
                if (
                    not category.is_active
                    or category.category_description
                    != category_data["category_description"]
                ):
                    category.category_description = category_data[
                        "category_description"
                    ]
                    category.is_active = category_data["is_active"]
                    category.save()
                    updated_count += 1
                    self.stdout.write(
                        f"   🔄 Updated category: {category.category_name}"
                    )
                else:
                    self.stdout.write(
                        f"   ⚠️ Category already exists: {category.category_name}"
                    )

        self.stdout.write(
            f"   📊 Summary: {created_count} created, {updated_count} updated"
        )

        # Display all active categories
        active_categories = Category.objects.filter(is_active=True).order_by(
            "category_name"
        )
        self.stdout.write(f"   🏷️  Total active categories: {active_categories.count()}")
        for category in active_categories:
            self.stdout.write(
                f"     • {category.category_name} (slug: {category.slug})"
            )
