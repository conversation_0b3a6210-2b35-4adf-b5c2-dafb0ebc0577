from django.contrib.auth.models import User
from django.core.management.base import BaseCommand

from accounts_app.models import ServiceProviderProfile
from venues_app.models import Category


class Command(BaseCommand):
    help = "Create a test service provider for venue testing"

    def handle(self, *args, **options):
        # Create test user
        user, created = User.objects.get_or_create(
            username="testprovider",
            defaults={
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "Provider",
                "is_active": True,
            },
        )

        if created:
            user.set_password("testpass123")
            user.save()
            self.stdout.write(f"Created user: {user.username}")
        else:
            self.stdout.write(f"User already exists: {user.username}")

        # Create service provider profile
        provider, created = ServiceProviderProfile.objects.get_or_create(
            user=user,
            defaults={
                "business_name": "Test Spa Business",
                "business_type": "spa",
                "phone": "************",
                "is_approved": True,
                "is_verified": True,
            },
        )

        if created:
            self.stdout.write(f"Created service provider: {provider.business_name}")
        else:
            self.stdout.write(
                f"Service provider already exists: {provider.business_name}"
            )

        # Create test categories if they don't exist
        categories_data = [
            {"name": "Spa", "slug": "spa"},
            {"name": "Wellness", "slug": "wellness"},
            {"name": "Beauty Salon", "slug": "beauty-salon"},
        ]

        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                slug=cat_data["slug"],
                defaults={"name": cat_data["name"], "is_active": True},
            )
            if created:
                self.stdout.write(f"Created category: {category.name}")

        self.stdout.write(
            self.style.SUCCESS(
                f"Test setup complete!\n"
                f"Username: {user.username}\n"
                f"Password: testpass123\n"
                f"Login URL: http://localhost:8001/accounts/provider/login/"
            )
        )
