"""Management command to demonstrate venue creation workflow."""

# --- Django Imports ---
# --- Standard Library Imports ---
import os
import tempfile

# --- Third-Party Imports ---
from PIL import Image, ImageDraw, ImageFont

from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.management.base import BaseCommand

# --- Local App Imports ---
from accounts_app.models import ServiceProviderProfile
from utils.image_utils import ALLOWED_EXTENSIONS, IMAGE_SPECS
from venues_app.forms import VenueForm
from venues_app.models import Category, Venue

User = get_user_model()


def create_demo_image(name="demo_venue.jpg", size=(1200, 800), color="blue"):
    """Create a demo image for testing."""
    image = Image.new("RGB", size, color=color)
    draw = ImageDraw.Draw(image)
    try:
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 40)
    except Exception:
        font = ImageFont.load_default()
    text = "Demo Venue Image"
    bbox = draw.textbbox((0, 0), text, font=font)
    x = (size[0] - (bbox[2] - bbox[0])) // 2
    y = (size[1] - (bbox[3] - bbox[1])) // 2
    draw.text((x, y), text, fill="white", font=font)

    tmp = tempfile.NamedTemporaryFile(suffix=".jpg", delete=False)
    image.save(tmp.name, "JPEG", quality=85)
    return tmp.name


def create_demo_user_and_profile():
    """Create a demo provider user and profile."""
    email = "<EMAIL>"
    user, _ = User.objects.get_or_create(
        email=email,
        defaults={
            "password": "demo123",
            "role": User.SERVICE_PROVIDER,
            "first_name": "Demo",
            "last_name": "Provider",
        },
    )
    profile, _ = ServiceProviderProfile.objects.get_or_create(
        user=user,
        defaults={
            "business_name": "Demo Wellness Spa",
            "dba_name": "CozyWish Demo Spa",
            "business_description": "A demo spa for testing",
            "business_phone_number": "+***********",
            "contact_person_name": "Demo Manager",
            "business_address": "123 Demo Street",
            "city": "Demo City",
            "state": "CA",
            "zip_code": "90210",
            "county": "Demo County",
            "website_url": "https://demo.cozywish.com",
            "is_visible": True,
        },
    )
    return user, profile


def create_demo_categories():
    cats = [
        ("Spa", "Full-service spa treatments"),
        ("Massage", "Professional massage therapy"),
        ("Wellness", "Holistic wellness services"),
        ("Beauty", "Beauty and skincare services"),
    ]
    created = []
    for name, desc in cats:
        cat, _ = Category.objects.get_or_create(
            name=name, defaults={"description": desc, "is_active": True}
        )
        created.append(cat)
    return created


def demo_venue_creation():
    """Demonstrate venue creation workflow."""
    user, profile = create_demo_user_and_profile()
    categories = create_demo_categories()
    image_path = create_demo_image()

    try:
        data = {
            "venue_name": "CozyWish Demo Wellness Spa",
            "short_description": "A luxurious wellness spa offering premium services.",
            "state": "California",
            "county": "Los Angeles",
            "city": "Beverly Hills",
            "street_number": "123",
            "street_name": "Wellness Boulevard",
            "latitude": "34.0736",
            "longitude": "-118.4004",
            "operating_hours": "Mon-Fri: 9am-8pm",
            "opening_notes": "Closed on holidays",
            "tags": "spa, wellness, massage, relaxation, beauty",
            "categories": [c.id for c in categories[:3]],
        }
        with open(image_path, "rb") as f:
            form_files = {
                "main_image": SimpleUploadedFile(
                    name="demo.jpg", content=f.read(), content_type="image/jpeg"
                )
            }
        form = VenueForm(data=data, files=form_files, user=user)
        if form.is_valid():
            venue = form.save()
            return venue
        else:
            raise ValueError("Form validation failed: %s" % form.errors)
    finally:
        if os.path.exists(image_path):
            os.unlink(image_path)


def demo_image_processing_info():
    for image_type, specs in IMAGE_SPECS.items():
        if "venue" in image_type:
            print(
                f"{image_type}: {specs['size'][0]}x{specs['size'][1]} {specs['format']} {specs['quality']}%"
            )
    print("Allowed Extensions:")
    for image_type, extensions in ALLOWED_EXTENSIONS.items():
        if "venue" in image_type:
            print(f"  {image_type}: {', '.join(extensions)}")


class Command(BaseCommand):
    help = "Run the demo venue creation workflow"

    def handle(self, *args, **options):
        try:
            venue = demo_venue_creation()
            demo_image_processing_info()
            self.stdout.write(self.style.SUCCESS("\nDemo completed successfully!"))
            self.stdout.write(
                f"   You can now test the venue at: http://127.0.0.1:8000/venues/{venue.slug}/"
            )
            self.stdout.write("   Login with: <EMAIL> / demo123")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Demo failed: {e}"))
            raise
