"""
Management command to link venues to USCity records for improved location validation.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone

from venues_app.models import USCity, Venue
from venues_app.utils import auto_link_venue_to_uscity, bulk_link_venues_to_uscity


class Command(BaseCommand):
    help = (
        "Link venues to USCity records for improved location validation and performance"
    )

    def add_arguments(self, parser):
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Number of venues to process in each batch (default: 100)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be done without making changes",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Re-link venues that are already linked to USCity",
        )
        parser.add_argument(
            "--venue-id",
            type=int,
            help="Link a specific venue by ID",
        )
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Show detailed progress information",
        )

    def handle(self, *args, **options):
        batch_size = options["batch_size"]
        dry_run = options["dry_run"]
        force = options["force"]
        venue_id = options["venue_id"]
        verbose = options["verbose"]

        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No changes will be made")
            )

        # Handle specific venue
        if venue_id:
            try:
                venue = Venue.objects.get(id=venue_id)
                self.link_single_venue(venue, dry_run, force, verbose)
                return
            except Venue.DoesNotExist:
                raise CommandError(f"Venue with ID {venue_id} does not exist")

        # Handle bulk linking
        self.stdout.write("Starting venue to USCity linking process...")

        # Get statistics before processing
        total_venues = Venue.objects.count()
        linked_venues = Venue.objects.filter(us_city__isnull=False).count()
        unlinked_venues = total_venues - linked_venues

        self.stdout.write(f"Total venues: {total_venues}")
        self.stdout.write(f"Already linked: {linked_venues}")
        self.stdout.write(f"Unlinked venues: {unlinked_venues}")

        if unlinked_venues == 0 and not force:
            self.stdout.write(
                self.style.SUCCESS("All venues are already linked to USCity records!")
            )
            return

        if not dry_run:
            # Process in bulk for better performance
            if force:
                # Re-link all venues
                stats = self.bulk_relink_all_venues(batch_size, verbose)
            else:
                # Link only unlinked venues
                stats = bulk_link_venues_to_uscity(batch_size)
        else:
            # Dry run simulation
            stats = self.simulate_linking(force, verbose)

        # Display results
        self.display_results(stats, dry_run)

    def link_single_venue(self, venue, dry_run, force, verbose):
        """Link a specific venue to USCity."""
        self.stdout.write(f"Processing venue: {venue.venue_name} (ID: {venue.id})")

        if venue.us_city and not force:
            self.stdout.write(
                self.style.WARNING(f"Venue already linked to: {venue.us_city}")
            )
            return

        if not all([venue.state, venue.county, venue.city]):
            self.stdout.write(self.style.ERROR("Venue missing required location data"))
            return

        if verbose:
            self.stdout.write(f"Location: {venue.city}, {venue.county}, {venue.state}")

        if not dry_run:
            success = auto_link_venue_to_uscity(venue)
            if success:
                venue.refresh_from_db()
                self.stdout.write(
                    self.style.SUCCESS(f"Successfully linked to: {venue.us_city}")
                )
            else:
                self.stdout.write(
                    self.style.ERROR("Failed to find matching USCity record")
                )
        else:
            # Simulate the linking
            from venues_app.utils import find_matching_uscity

            uscity = find_matching_uscity(venue.state, venue.county, venue.city)
            if uscity:
                self.stdout.write(self.style.SUCCESS(f"Would link to: {uscity}"))
            else:
                self.stdout.write(self.style.ERROR("No matching USCity record found"))

    def bulk_relink_all_venues(self, batch_size, verbose):
        """Re-link all venues, including those already linked."""
        stats = {
            "processed": 0,
            "linked": 0,
            "already_linked": 0,
            "failed": 0,
            "missing_data": 0,
        }

        venues = Venue.objects.all().iterator(chunk_size=batch_size)

        for venue in venues:
            stats["processed"] += 1

            if verbose and stats["processed"] % 50 == 0:
                self.stdout.write(f'Processed {stats["processed"]} venues...')

            if not all([venue.state, venue.county, venue.city]):
                stats["missing_data"] += 1
                continue

            # Clear existing link if forcing
            venue.us_city = None

            if auto_link_venue_to_uscity(venue):
                stats["linked"] += 1
            else:
                stats["failed"] += 1

        return stats

    def simulate_linking(self, force, verbose):
        """Simulate the linking process for dry run."""
        stats = {
            "processed": 0,
            "linked": 0,
            "already_linked": 0,
            "failed": 0,
            "missing_data": 0,
        }

        if force:
            venues = Venue.objects.all()
        else:
            venues = Venue.objects.filter(us_city__isnull=True)

        from venues_app.utils import find_matching_uscity

        for venue in venues:
            stats["processed"] += 1

            if verbose and stats["processed"] % 50 == 0:
                self.stdout.write(f'Simulated {stats["processed"]} venues...')

            if not all([venue.state, venue.county, venue.city]):
                stats["missing_data"] += 1
                if verbose:
                    self.stdout.write(f"Missing data: {venue.venue_name}")
                continue

            if venue.us_city and not force:
                stats["already_linked"] += 1
                continue

            # Simulate finding USCity
            uscity = find_matching_uscity(venue.state, venue.county, venue.city)
            if uscity:
                stats["linked"] += 1
                if verbose:
                    self.stdout.write(f"Would link {venue.venue_name} to {uscity}")
            else:
                stats["failed"] += 1
                if verbose:
                    self.stdout.write(
                        f"No match for {venue.venue_name}: {venue.city}, {venue.county}, {venue.state}"
                    )

        return stats

    def display_results(self, stats, dry_run):
        """Display the results of the linking process."""
        action = "Would be" if dry_run else "Were"

        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("LINKING RESULTS")
        self.stdout.write("=" * 50)
        self.stdout.write(f'Venues processed: {stats["processed"]}')
        self.stdout.write(f'Successfully linked: {stats["linked"]}')

        if stats.get("already_linked", 0) > 0:
            self.stdout.write(f'Already linked: {stats["already_linked"]}')

        if stats["failed"] > 0:
            self.stdout.write(self.style.WARNING(f'Failed to link: {stats["failed"]}'))

        if stats["missing_data"] > 0:
            self.stdout.write(
                self.style.ERROR(f'Missing location data: {stats["missing_data"]}')
            )

        # Calculate success rate
        if stats["processed"] > 0:
            success_rate = (stats["linked"] / stats["processed"]) * 100
            self.stdout.write(f"Success rate: {success_rate:.1f}%")

        if dry_run:
            self.stdout.write("\nRun without --dry-run to make actual changes")
        else:
            self.stdout.write(self.style.SUCCESS("\nVenue linking process completed!"))
