from django.urls import path

from . import views
from .views import modern_search, autocomplete

app_name = "venues_app"

urlpatterns = [
    # Modern Search System
    path("modern-search/", modern_search.ModernSearchView.as_view(), name="modern_search"),

    # Autocomplete endpoints
    path("autocomplete/cities/", autocomplete.CityAutocomplete.as_view(), name="autocomplete-cities"),
    path("autocomplete/regions/", autocomplete.RegionAutocomplete.as_view(), name="autocomplete-regions"),
    path("autocomplete/venues/", autocomplete.VenueAutocomplete.as_view(), name="autocomplete-venues"),
    path("autocomplete/services/", autocomplete.ServiceAutocomplete.as_view(), name="autocomplete-services"),

    # AJAX endpoints for search
    path("ajax/location-autocomplete/", autocomplete.location_autocomplete_ajax, name="ajax-location-autocomplete"),
    path("ajax/tag-autocomplete/", autocomplete.tag_autocomplete_ajax, name="ajax-tag-autocomplete"),
    path("ajax/search-suggestions/", autocomplete.search_suggestions_ajax, name="ajax-search-suggestions"),
    path("ajax/popular-searches/", autocomplete.popular_searches_ajax, name="ajax-popular-searches"),

    # Public views - non-slug patterns first
    path("", views.venue_search, name="venue_list"),
    path("search/", views.venue_search, name="venue_search"),
    path("home/", views.home_view, name="home"),
    # Search and filter APIs (legacy)
    path(
        "api/location-autocomplete/",
        views.location_autocomplete,
        name="location_autocomplete",
    ),
    path("api/location-data/", views.get_location_data, name="get_location_data"),
    path(
        "category/<slug:category_slug>/", views.category_venues, name="category_venues"
    ),
    # NEW: Services by category
    path(
        "services/category/<slug:category_slug>/",
        views.services_by_category,
        name="services_by_category",
    ),
    path(
        "services/tag/<slug:tag_slug>/", views.services_by_tag, name="services_by_tag"
    ),
    # Service Provider views - specific patterns before slug patterns
    path("provider/venues/", views.provider_venues, name="provider_venues"),
    path(
        "provider/venues/manage/", views.provider_venues, name="venue_manage"
    ),  # Alias for template compatibility
    path(
        "provider/venues/<int:venue_id>/",
        views.provider_venue_detail,
        name="provider_venue_detail",
    ),
    path(
        "provider/venues/<int:venue_id>/change-status/",
        views.change_venue_status,
        name="change_venue_status",
    ),
    path(
        "provider/venues/<int:venue_id>/edit/basic-information/",
        views.edit_venue_basic_information,
        name="edit_venue_basic_information",
    ),
    path(
        "provider/venues/<int:venue_id>/edit/location/",
        views.edit_venue_location,
        name="edit_venue_location",
    ),
    # Venue CRUD operations - Enhanced with wizard
    path("provider/create/", views.venue_create_view, name="venue_create"),
    path(
        "provider/create/form/",
        views.VenueCreateView.as_view(),
        name="venue_create_form",
    ),
    path(
        "provider/create/wizard/",
        views.venue_create_wizard_view,
        name="venue_create_wizard_default",
    ),
    path(
        "provider/create/wizard/<str:step>/",
        views.venue_create_wizard_view,
        name="venue_create_wizard",
    ),
    path(
        "provider/create/wizard/<str:step>/auto-save/",
        views.venue_create_wizard_view,
        name="venue_create_wizard_auto_save",
    ),
    path(
        "provider/create/wizard/upload-image/",
        views.upload_wizard_image,
        name="upload_wizard_image",
    ),
    path("provider/edit/", views.venue_edit, name="venue_edit"),
    path(
        "provider/edit/wizard/",
        views.venue_edit_wizard_view,
        name="venue_edit_wizard_default",
    ),
    path(
        "provider/edit/wizard/<str:step>/",
        views.venue_edit_wizard_view,
        name="venue_edit_wizard",
    ),
    path("provider/progress/", views.venue_progress, name="venue_progress"),
    path(
        "provider/venues/<int:venue_id>/auto-approval-check/",
        views.trigger_auto_approval_check,
        name="trigger_auto_approval_check",
    ),
    path("provider/delete/", views.venue_delete, name="venue_delete"),
    # Other provider management
    path("provider/services/", views.manage_services, name="manage_services"),
    path(
        "provider/services/bulk-actions/",
        views.bulk_service_actions,
        name="bulk_service_actions",
    ),
    path("provider/faqs/", views.manage_faqs, name="manage_faqs"),
    path("provider/faqs/<int:faq_id>/edit/", views.edit_faq, name="edit_faq"),
    path("provider/faqs/<int:faq_id>/delete/", views.delete_faq, name="delete_faq"),
    path("provider/amenities/", views.manage_amenities, name="manage_amenities"),
    path(
        "provider/amenities/<int:amenity_id>/delete/",
        views.delete_amenity,
        name="delete_amenity",
    ),
    path(
        "provider/hours/", views.manage_operating_hours, name="manage_operating_hours"
    ),
    path("provider/images/", views.manage_venue_images, name="manage_venue_images"),
    path(
        "provider/images/upload/", views.upload_venue_image, name="upload_venue_image"
    ),
    path(
        "provider/images/reorder/",
        views.reorder_venue_images,
        name="reorder_venue_images",
    ),
    path(
        "provider/images/<int:image_id>/primary/",
        views.set_primary_image,
        name="set_primary_image",
    ),
    path(
        "provider/images/<int:image_id>/delete/",
        views.delete_venue_image,
        name="delete_venue_image",
    ),
    path(
        "provider/images/<int:image_id>/reorder/",
        views.reorder_venue_image,
        name="reorder_venue_image",
    ),
    path(
        "provider/images/undo/<str:action_type>/",
        views.undo_image_action,
        name="undo_image_action",
    ),
    path(
        "provider/holidays/",
        views.manage_holiday_schedules,
        name="manage_holiday_schedules",
    ),
    path(
        "provider/holidays/<int:schedule_id>/delete/",
        views.delete_holiday_schedule,
        name="delete_holiday_schedule",
    ),
    path("provider/services/create/", views.service_create, name="service_create"),
    path("provider/services/<int:pk>/edit/", views.service_edit, name="service_edit"),
    path(
        "provider/services/<int:pk>/delete/",
        views.service_delete,
        name="service_delete",
    ),
    path(
        "provider/visibility/",
        views.manage_venue_visibility,
        name="manage_venue_visibility",
    ),
    path("provider/preview/", views.venue_preview, name="venue_preview"),
    path(
        "provider/freshness/",
        views.update_information_freshness,
        name="update_information_freshness",
    ),
    path("provider/sync-contact/", views.sync_contact_info, name="sync_contact_info"),
    path(
        "provider/verify-email/",
        views.send_email_verification,
        name="send_email_verification",
    ),
    path(
        "provider/verify-email/<str:token>/",
        views.verify_venue_email,
        name="verify_venue_email",
    ),
    # Authentication paths
    path("login/", views.login_view, name="login"),
    # AJAX endpoints
    path("ajax/validate-field/", views.validate_field_ajax, name="validate_field_ajax"),
    path("ajax/auto-save/", views.auto_save_progress, name="auto_save_progress"),
    path("ajax/reorder-faqs/", views.reorder_faqs, name="reorder_faqs"),
    path(
        "ajax/image-preview/",
        views.validate_venue_image_preview,
        name="validate_venue_image_preview",
    ),
    # Flag venue - This should come before venue detail
    path("<slug:venue_slug>/flag/", views.flag_venue, name="flag_venue"),
    # Admin views
    path(
        "admin/approval-dashboard/",
        views.admin_venue_approval_dashboard,
        name="admin_venue_approval_dashboard",
    ),
    path("admin/venues/", views.admin_venue_list, name="admin_venue_list"),
    path("admin/pending/", views.admin_pending_venues, name="admin_pending_venues"),
    path(
        "admin/venues/<int:venue_id>/",
        views.admin_venue_detail,
        name="admin_venue_detail",
    ),
    path(
        "admin/venues/<int:venue_id>/approve/",
        views.admin_venue_approval,
        name="admin_venue_approval",
    ),
    path(
        "admin/venues/<int:venue_id>/changes/",
        views.admin_venue_changes_comparison,
        name="admin_venue_changes_comparison",
    ),
    path("admin/flagged/", views.admin_flagged_venues, name="admin_flagged_venues"),
    # Admin URLs - Category Management
    path("admin/categories/", views.admin_category_list, name="admin_category_list"),
    path(
        "admin/categories/create/",
        views.admin_category_create,
        name="admin_category_create",
    ),
    path(
        "admin/categories/<int:category_id>/",
        views.admin_category_edit,
        name="admin_category_edit",
    ),
    path(
        "admin/categories/<int:category_id>/delete/",
        views.admin_category_delete,
        name="admin_category_delete",
    ),
    path(
        "admin/categories/<int:category_id>/toggle/",
        views.admin_category_toggle_status,
        name="admin_category_toggle_status",
    ),
    # Admin URLs - Service Category Management
    path(
        "admin/service-categories/",
        views.admin_service_category_list,
        name="admin_service_category_list",
    ),
    path(
        "admin/service-categories/create/",
        views.admin_service_category_create,
        name="admin_service_category_create",
    ),
    path(
        "admin/service-categories/<int:category_id>/",
        views.admin_service_category_edit,
        name="admin_service_category_edit",
    ),
    path(
        "admin/service-categories/<int:category_id>/delete/",
        views.admin_service_category_delete,
        name="admin_service_category_delete",
    ),
    path(
        "admin/service-categories/<int:category_id>/toggle/",
        views.admin_service_category_toggle_status,
        name="admin_service_category_toggle_status",
    ),
    # Venue detail and service detail - Must be LAST due to slug pattern
    path(
        "<slug:venue_slug>/services/<slug:service_slug>/",
        views.service_detail,
        name="service_detail",
    ),
    path("<slug:venue_slug>/", views.venue_detail, name="venue_detail"),
]
