# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from accounts_app.models import ServiceProviderProfile

from ..models import (
    Category,
    OperatingHours,
    ServiceCategory,
    USCity,
    Venue,
    VenueImage,
)

# --- Enhanced Validation Imports ---
from ..utils import (
    find_matching_uscity,
    generate_email_verification_token,
    get_location_suggestions_with_fuzzy_matching,
    sync_contact_from_service_provider,
    validate_business_email,
    validate_location_combination,
    validate_phone_number,
    validate_social_media_url,
    validate_website_url,
)
from .operating_hours import OperatingHoursFormSetFactory


class VenueBasicInformationEditForm(forms.ModelForm):
    """Dedicated form for editing venue basic information (name, description, categories, contact info)."""

    categories = forms.ModelMultipleChoiceField(
        queryset=Category.objects.filter(is_active=True),
        widget=forms.CheckboxSelectMultiple(
            attrs={
                "class": "form-check-input category-checkbox",
                "data-toggle": "tooltip",
                "data-placement": "top",
            }
        ),
        required=True,
        help_text=_("Select up to 3 categories that best describe your venue"),
        error_messages={
            "required": _("Please select at least one category for your venue"),
            "invalid_choice": _("Please select valid categories"),
        },
    )

    class Meta:
        model = Venue
        fields = [
            "venue_name",
            "short_description",
            "phone",
            "email",
            "website_url",
            "categories",
        ]
        widgets = {
            "venue_name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": _("Enter your venue name"),
                    "maxlength": "255",
                }
            ),
            "short_description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 4,
                    "placeholder": _("Describe your venue and what makes it special"),
                    "maxlength": "500",
                }
            ),
            "phone": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": _("(*************"),
                    "pattern": r"[\d\s\-\(\)\+]+",
                    "title": _("Enter a valid phone number"),
                }
            ),
            "email": forms.EmailInput(
                attrs={"class": "form-control", "placeholder": _("<EMAIL>")}
            ),
            "website_url": forms.URLInput(
                attrs={
                    "class": "form-control",
                    "placeholder": _("https://www.yourvenue.com"),
                }
            ),
        }
        help_texts = {
            "venue_name": _("The name of your venue or business"),
            "short_description": _(
                "Brief description of your venue (max 500 characters)"
            ),
            "phone": _("Primary contact phone number for your venue"),
            "email": _("Primary contact email for your venue"),
            "website_url": _("Your venue website URL (optional)"),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        # Set required fields
        self.fields["venue_name"].required = True
        self.fields["short_description"].required = True
        self.fields["categories"].required = True

        # Order categories alphabetically for better UX
        self.fields["categories"].queryset = Category.objects.filter(
            is_active=True
        ).order_by("category_name")

    def clean_venue_name(self):
        """Validate venue name."""
        venue_name = self.cleaned_data.get("venue_name", "").strip()

        if not venue_name:
            raise ValidationError(_("Venue name is required."))

        if len(venue_name) < 3:
            raise ValidationError(_("Venue name must be at least 3 characters long."))

        # Check for duplicate venue names for the same provider (excluding current venue)
        if self.user and hasattr(self.user, "service_provider_profile"):
            existing_venues = Venue.objects.filter(
                service_provider=self.user.service_provider_profile,
                venue_name__iexact=venue_name,
                is_deleted=False,
            )
            if self.instance and self.instance.pk:
                existing_venues = existing_venues.exclude(pk=self.instance.pk)

            if existing_venues.exists():
                raise ValidationError(_("You already have a venue with this name."))

        return venue_name

    def clean_short_description(self):
        """Validate venue description."""
        description = self.cleaned_data.get("short_description", "").strip()

        if not description:
            raise ValidationError(_("Venue description is required."))

        if len(description) < 20:
            raise ValidationError(_("Description must be at least 20 characters long."))

        return description

    def clean_phone(self):
        """Validate phone number."""
        phone = self.cleaned_data.get("phone", "").strip()

        if phone:
            try:
                return validate_phone_number(phone)
            except ValidationError as e:
                raise ValidationError(str(e))

        return phone

    def clean_email(self):
        """Validate business email."""
        email = self.cleaned_data.get("email", "").strip()

        if email:
            try:
                return validate_business_email(email)
            except ValidationError as e:
                raise ValidationError(str(e))

        return email

    def clean_website_url(self):
        """Validate website URL."""
        website_url = self.cleaned_data.get("website_url", "").strip()

        if website_url:
            try:
                return validate_website_url(website_url)
            except ValidationError as e:
                raise ValidationError(str(e))

        return website_url

    def clean_categories(self):
        """Validate venue categories."""
        categories = self.cleaned_data.get("categories")

        if not categories:
            raise ValidationError(
                _("Please select at least one category for your venue.")
            )

        # Convert categories to a list if it's a QuerySet
        if hasattr(categories, "count"):
            category_count = categories.count()
        elif isinstance(categories, list):
            category_count = len(categories)
        else:
            category_count = 1 if categories else 0

        if category_count > 3:
            raise ValidationError(_("You can select up to 3 categories maximum."))

        return categories


class VenueLocationEditForm(forms.ModelForm):
    """Dedicated form for editing venue location details (address, city, state, coordinates)."""

    class Meta:
        model = Venue
        fields = [
            "street_number",
            "street_name",
            "city",
            "county",
            "state",
            "latitude",
            "longitude",
        ]
        widgets = {
            "street_number": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": _("123"),
                    "maxlength": "10",
                }
            ),
            "street_name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": _("Main Street"),
                    "maxlength": "255",
                }
            ),
            "city": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": _("Enter city name"),
                    "maxlength": "100",
                    "data-autocomplete": "city",
                }
            ),
            "county": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": _("Enter county name"),
                    "maxlength": "100",
                }
            ),
            "state": forms.Select(attrs={"class": "form-select"}),
            "latitude": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "placeholder": _("40.7128"),
                    "step": "any",
                    "readonly": True,
                }
            ),
            "longitude": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "placeholder": _("-74.0060"),
                    "step": "any",
                    "readonly": True,
                }
            ),
        }
        help_texts = {
            "street_number": _("Street number or building number"),
            "street_name": _("Street name or address line"),
            "city": _("City where your venue is located"),
            "county": _("County or administrative area"),
            "state": _("State or province"),
            "latitude": _("Latitude coordinate (auto-filled from address)"),
            "longitude": _("Longitude coordinate (auto-filled from address)"),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        # Set required fields
        self.fields["street_number"].required = True
        self.fields["street_name"].required = True
        self.fields["city"].required = True
        self.fields["county"].required = True
        self.fields["state"].required = True

        # Set up state choices from model STATE_CHOICES
        from ..models import Venue

        self.fields["state"].choices = [("", "Select state")] + list(
            Venue.STATE_CHOICES
        )

    def clean_street_number(self):
        """Validate street number."""
        street_number = self.cleaned_data.get("street_number", "").strip()

        if not street_number:
            raise ValidationError(_("Street number is required."))

        return street_number

    def clean_street_name(self):
        """Validate street name."""
        street_name = self.cleaned_data.get("street_name", "").strip()

        if not street_name:
            raise ValidationError(_("Street name is required."))

        if len(street_name) < 3:
            raise ValidationError(_("Street name must be at least 3 characters long."))

        return street_name

    def clean_city(self):
        """Validate city name."""
        city = self.cleaned_data.get("city", "").strip()

        if not city:
            raise ValidationError(_("City is required."))

        if len(city) < 2:
            raise ValidationError(_("City name must be at least 2 characters long."))

        return city

    def clean_county(self):
        """Validate county name."""
        county = self.cleaned_data.get("county", "").strip()

        if not county:
            raise ValidationError(_("County is required."))

        return county

    def clean_state(self):
        """Validate state selection."""
        state = self.cleaned_data.get("state", "").strip()

        if not state:
            raise ValidationError(_("State is required."))

        return state

    def clean(self):
        """Validate the complete address combination."""
        cleaned_data = super().clean()

        street_number = cleaned_data.get("street_number")
        street_name = cleaned_data.get("street_name")
        city = cleaned_data.get("city")
        county = cleaned_data.get("county")
        state = cleaned_data.get("state")

        if all([street_number, street_name, city, county, state]):
            try:
                # Validate location combination
                location_data = validate_location_combination(
                    street_number, street_name, city, county, state
                )

                # Update coordinates if validation provides them
                if location_data and "coordinates" in location_data:
                    cleaned_data["latitude"] = location_data["coordinates"].get(
                        "latitude"
                    )
                    cleaned_data["longitude"] = location_data["coordinates"].get(
                        "longitude"
                    )

            except ValidationError as e:
                raise ValidationError(str(e))

        return cleaned_data


# --- Image Processing Imports ---
try:
    from utils.forms import ImageUploadForm
    from utils.image_service import ImageService
    from utils.image_utils import (
        process_image,
        validate_image_comprehensive,
        validate_image_extension,
        validate_image_mime_type,
        validate_image_size,
    )
    from utils.sanitization import sanitize_html
except ImportError:
    ImageUploadForm = None
    ImageService = None
    sanitize_html = lambda x: x


class VenueCreateWizardMixin:
    """Mixin to provide multi-step wizard functionality for venue creation"""

    STEP_CHOICES = [
        ("basic", _("Basic Information")),
        ("location", _("Location")),
        ("services", _("Services")),
        ("gallery", _("Gallery")),
        ("details", _("Details")),
    ]

    def __init__(self, *args, **kwargs):
        # Extract current_step from kwargs, but allow subclasses to override
        passed_current_step = kwargs.pop("current_step", None)
        super().__init__(*args, **kwargs)

        # Use passed current_step if provided, otherwise use what subclass set, otherwise default
        if passed_current_step:
            self.current_step = passed_current_step
        elif not hasattr(self, "current_step"):
            self.current_step = "basic"

        self._configure_step_fields()

        # Handle categories from draft data
        if "initial" in kwargs and kwargs["initial"]:
            initial_data = kwargs["initial"]
            if "categories" in initial_data and isinstance(
                initial_data["categories"], list
            ):
                # Convert category IDs to Category objects for form initialization
                try:
                    category_ids = [
                        int(cat_id) for cat_id in initial_data["categories"] if cat_id
                    ]
                    self.initial["categories"] = Category.objects.filter(
                        id__in=category_ids
                    )
                except (ValueError, TypeError):
                    pass

    def _configure_step_fields(self):
        """Configure which fields are required/visible for current step"""
        step_fields = self.get_step_fields()

        # Only require fields for current step
        for field_name, field in self.fields.items():
            if field_name not in step_fields:
                field.required = False
                # Don't hide fields in template, let template handle visibility
                # field.widget.attrs['style'] = 'display: none;'
            else:
                # Add step-specific styling and help text
                self._enhance_field_for_step(field_name, field)

    def get_step_fields(self):
        """Get fields for current step"""
        step_mapping = {
            "basic": ["venue_name", "short_description", "categories"],
            "location": [
                "state",
                "county",
                "city",
                "street_number",
                "street_name",
                "zip_code",
                "latitude",
                "longitude",
                "phone",
                "email",
                "website_url",
                "instagram_url",
                "facebook_url",
                "twitter_url",
                "linkedin_url",
            ],
            "services": ["services", "service_pricing", "service_discounts"],
            "gallery": ["images", "main_image"],
            "details": [
                "operating_hours",
                "amenities",
                "faqs",
                "cancellation_policy",
                "booking_policy",
                "special_instructions",
                "venue_status",
            ],
        }
        return step_mapping.get(self.current_step, [])

    def _enhance_field_for_step(self, field_name, field):
        """Add step-specific enhancements to fields"""
        # Add character counters for text fields
        if isinstance(field.widget, forms.Textarea):
            field.widget.attrs.update(
                {
                    "data-character-count": "true",
                    "data-max-length": field.widget.attrs.get("maxlength", ""),
                }
            )

        # Add autocomplete and validation attributes
        if field_name in ["venue_name"]:
            field.widget.attrs.update(
                {
                    "data-validation": "real-time",
                    "data-min-length": "2",
                }
            )
        elif field_name in ["short_description"]:
            field.widget.attrs.update(
                {
                    "data-validation": "real-time",
                    "data-min-length": "10",
                    "data-character-count": "true",
                }
            )

    def validate_step(self):
        """Validate only the current step's fields"""
        step_fields = self.get_step_fields()
        step_errors = {}

        for field_name in step_fields:
            if field_name in self.fields:
                try:
                    value = self.cleaned_data.get(field_name)
                    # Run field-specific validation
                    clean_method = getattr(self, f"clean_{field_name}", None)
                    if clean_method:
                        clean_method()
                except ValidationError as e:
                    step_errors[field_name] = e.messages

        # Add step-specific validation
        step_errors.update(self.validate_step_specific())

        return step_errors

    def validate_step_specific(self):
        """Step-specific validation logic"""
        errors = {}

        if self.current_step == "basic":
            # Validate basic information
            if not self.cleaned_data.get("venue_name"):
                errors["venue_name"] = [_("Venue name is required.")]
            if not self.cleaned_data.get("short_description"):
                errors["short_description"] = [_("Description is required.")]
            if not self.cleaned_data.get("categories"):
                errors["categories"] = [_("At least one category is required.")]

        elif self.current_step == "location":
            # Validate location information
            if not self.cleaned_data.get("state"):
                errors["state"] = [_("State is required.")]
            if not self.cleaned_data.get("city"):
                errors["city"] = [_("City is required.")]
            if not self.cleaned_data.get("street_name"):
                errors["street_name"] = [_("Street name is required.")]

        elif self.current_step == "contact":
            # Validate contact information
            if not self.cleaned_data.get("phone"):
                errors["phone"] = [_("Phone number is required.")]
            if not self.cleaned_data.get("email"):
                errors["email"] = [_("Email is required.")]

        elif self.current_step == "hours_amenities":
            # Validate operating hours
            operating_hours = self.cleaned_data.get("operating_hours")
            if not operating_hours:
                errors["operating_hours"] = [_("Operating hours are required.")]

        elif self.current_step == "services":
            # Validate services
            services = self.cleaned_data.get("services")
            if not services:
                errors["services"] = [_("At least one service is required.")]

        elif self.current_step == "gallery_team":
            # Gallery and team are optional, but validate if provided
            pass

        elif self.current_step == "review_submit":
            # Final validation - ensure all required steps are complete
            pass

        return errors

    def get_progress_percentage(self):
        """Calculate progress percentage for 5-step wizard"""
        if hasattr(self, "instance") and hasattr(self.instance, "completed_steps"):
            total_steps = 5
            completed_count = len(self.instance.completed_steps)
            return int((completed_count / total_steps) * 100) if total_steps > 0 else 0
        return 0

    def get_step_number(self):
        """Get the current step number (1-based)"""
        step_order = ["basic", "location", "services", "gallery", "details"]
        try:
            return step_order.index(self.current_step) + 1
        except ValueError:
            return 1


# ===== NEW 7-STEP WIZARD FORM CLASSES =====


class VenueBasicInfoForm(VenueCreateWizardMixin, forms.Form):
    """Step 1: Basic Information - Name, description, and categories"""

    venue_name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your venue name"),
                "data-character-count": "true",
                "maxlength": "255",
            }
        ),
        help_text=_("The name of your venue or business"),
    )

    short_description = forms.CharField(
        max_length=500,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Describe your venue and what makes it special"),
                "data-character-count": "true",
                "maxlength": "500",
            }
        ),
        help_text=_("Brief description of your venue (max 500 characters)"),
    )

    categories = forms.ModelMultipleChoiceField(
        queryset=None,  # Will be set in __init__
        widget=forms.CheckboxSelectMultiple(attrs={"class": "form-check-input"}),
        required=True,
        help_text=_("Select up to 3 categories that best describe your venue"),
        error_messages={
            "required": _("Please select at least one category for your venue"),
            "invalid_choice": _("Please select valid categories"),
        },
    )

    def __init__(self, *args, **kwargs):
        # Handle categories in initial data before calling super()
        if (
            "initial" in kwargs
            and kwargs["initial"]
            and "categories" in kwargs["initial"]
        ):
            categories_data = kwargs["initial"]["categories"]
            if categories_data:
                # Convert string IDs to integers if needed
                if isinstance(categories_data, list):
                    try:
                        # Convert all items to integers
                        category_ids = []
                        for cat_id in categories_data:
                            if isinstance(cat_id, str) and cat_id.isdigit():
                                category_ids.append(int(cat_id))
                            elif isinstance(cat_id, int):
                                category_ids.append(cat_id)
                            elif hasattr(cat_id, "id"):
                                category_ids.append(cat_id.id)
                        kwargs["initial"]["categories"] = category_ids
                    except (ValueError, TypeError):
                        # If conversion fails, clear the categories
                        kwargs["initial"]["categories"] = []

        super().__init__(*args, **kwargs)
        from ..models import Category

        self.fields["categories"].queryset = Category.objects.filter(is_active=True)

    def clean_categories(self):
        """Validate venue categories."""
        categories = self.cleaned_data.get("categories")

        if not categories:
            raise forms.ValidationError(
                _("Please select at least one category for your venue.")
            )

        # Convert categories to a list if it's a QuerySet
        if hasattr(categories, "count"):
            category_count = categories.count()
        elif isinstance(categories, list):
            category_count = len(categories)
        else:
            category_count = 1 if categories else 0

        if category_count > 3:
            raise forms.ValidationError(_("You can select up to 3 categories maximum."))

        # Validate that all selected categories are active
        for category in categories:
            if hasattr(category, "is_active") and not category.is_active:
                raise forms.ValidationError(
                    f'Category "{category.category_name}" is no longer available.'
                )

        return categories


class VenueLocationForm(VenueCreateWizardMixin, forms.Form):
    """Step 2: Location & Address - Complete address details"""

    state = forms.ChoiceField(
        choices=[],  # Will be populated in __init__
        widget=forms.Select(attrs={"class": "form-control"}),
        help_text=_("State where your venue is located"),
    )

    county = forms.CharField(
        max_length=100,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Enter county name")}
        ),
        help_text=_("County where your venue is located"),
    )

    city = forms.CharField(
        max_length=100,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Enter city name")}
        ),
        help_text=_("City where your venue is located"),
    )

    street_number = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("123")}
        ),
        help_text=_("Street number (optional)"),
    )

    street_name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Main Street")}
        ),
        help_text=_("Street name"),
    )

    zip_code = forms.CharField(
        max_length=10,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("12345")}
        ),
        help_text=_("ZIP/Postal code"),
    )

    latitude = forms.DecimalField(
        max_digits=10, decimal_places=8, required=False, widget=forms.HiddenInput()
    )

    longitude = forms.DecimalField(
        max_digits=11, decimal_places=8, required=False, widget=forms.HiddenInput()
    )

    def clean_latitude(self):
        """Clean latitude field to handle empty strings"""
        latitude = self.cleaned_data.get("latitude")
        if latitude == "" or latitude is None:
            return None
        return latitude

    def clean_longitude(self):
        """Clean longitude field to handle empty strings"""
        longitude = self.cleaned_data.get("longitude")
        if longitude == "" or longitude is None:
            return None
        return longitude

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from ..models import Venue

        self.fields["state"].choices = Venue.STATE_CHOICES
        self.current_step = "location"


class VenueContactForm(VenueCreateWizardMixin, forms.Form):
    """Step 3: Contact & Communication - Contact info and social media"""

    phone = forms.CharField(
        max_length=20,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("(*************"),
                "type": "tel",
            }
        ),
        help_text=_("Primary contact phone number"),
    )

    email = forms.EmailField(
        widget=forms.EmailInput(
            attrs={"class": "form-control", "placeholder": _("<EMAIL>")}
        ),
        help_text=_("Primary contact email address"),
    )

    website_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://www.yourvenue.com"),
            }
        ),
        help_text=_("Your venue website (optional)"),
    )

    instagram_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://instagram.com/yourvenue"),
            }
        ),
        help_text=_("Instagram profile URL (optional)"),
    )

    facebook_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://facebook.com/yourvenue"),
            }
        ),
        help_text=_("Facebook page URL (optional)"),
    )

    twitter_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://twitter.com/yourvenue"),
            }
        ),
        help_text=_("Twitter profile URL (optional)"),
    )

    linkedin_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://linkedin.com/company/yourvenue"),
            }
        ),
        help_text=_("LinkedIn profile URL (optional)"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.current_step = "contact"


class VenueHoursAmenitiesForm(VenueCreateWizardMixin, forms.Form):
    """Step 4: Operating Hours & Amenities - Schedule and venue features"""

    # Operating hours will be handled with JavaScript and stored as JSON
    operating_hours = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Operating hours for each day of the week"),
    )

    # Amenities selection
    amenities = forms.MultipleChoiceField(
        choices=[],  # Will be populated in __init__
        widget=forms.CheckboxSelectMultiple(attrs={"class": "form-check-input"}),
        required=False,
        help_text=_("Select all amenities available at your venue"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from ..models import VenueAmenity

        self.fields["amenities"].choices = VenueAmenity.AMENITY_CHOICES
        # Only set current_step if not already set by mixin
        if not hasattr(self, "current_step") or self.current_step != "details":
            self.current_step = "details"


class VenueServicesForm(VenueCreateWizardMixin, forms.Form):
    """Step 5: Services & Pricing - Services with pricing and discounts"""

    # Services will be handled with JavaScript and stored as JSON
    services = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Services offered with pricing information"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only set current_step if not already set by mixin
        if not hasattr(self, "current_step") or self.current_step != "services":
            self.current_step = "services"


class VenueGalleryTeamForm(VenueCreateWizardMixin, forms.Form):
    """Step 6: Gallery & Team - Images and staff information"""

    # Images will be handled with JavaScript and stored as JSON
    images = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Gallery images with ordering information"),
    )

    # Team members will be handled with JavaScript and stored as JSON
    team_members = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Team member information"),
    )

    # Main image upload
    main_image = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={"class": "form-control", "accept": "image/*"}),
        help_text=_("Main featured image for your venue (optional)"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only set current_step if not already set by mixin
        if not hasattr(self, "current_step") or self.current_step != "gallery":
            self.current_step = "gallery"


class VenueReviewSubmitForm(VenueCreateWizardMixin, forms.Form):
    """Step 7: Review & Submit - FAQs, policies, and final submission"""

    # FAQs will be handled with JavaScript and stored as JSON
    faqs = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Frequently asked questions"),
    )

    cancellation_policy = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Describe your cancellation policy"),
                "data-character-count": "true",
                "maxlength": "1000",
            }
        ),
        help_text=_("Your venue cancellation policy (optional)"),
    )

    booking_policy = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Describe your booking policy"),
                "data-character-count": "true",
                "maxlength": "1000",
            }
        ),
        help_text=_("Your venue booking policy (optional)"),
    )

    special_instructions = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 3,
                "placeholder": _("Any special instructions for customers"),
                "data-character-count": "true",
                "maxlength": "500",
            }
        ),
        help_text=_("Special instructions for customers (optional)"),
    )

    # Venue status selection field
    VENUE_STATUS_CHOICES = [
        ("draft", _("Save as Draft")),
        ("pending", _("Submit for Admin Approval")),
    ]

    venue_status = forms.ChoiceField(
        choices=VENUE_STATUS_CHOICES,
        widget=forms.RadioSelect(attrs={"class": "form-check-input"}),
        initial="draft",
        help_text=_("Choose whether to save as draft or submit for approval"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only set current_step if not already set by mixin
        if not hasattr(self, "current_step") or self.current_step != "details":
            self.current_step = "details"


# ===== NEW 5-STEP WIZARD FORM CLASSES =====


class VenueLocationContactForm(VenueCreateWizardMixin, forms.Form):
    """Step 2: Location - Combined location and contact information"""

    # Location fields
    state = forms.ChoiceField(
        choices=[],  # Will be populated in __init__
        widget=forms.Select(attrs={"class": "form-control"}),
        help_text=_("Select your state"),
    )
    county = forms.CharField(
        max_length=100,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Enter county name")}
        ),
        help_text=_("County where your venue is located"),
    )
    city = forms.CharField(
        max_length=100,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Enter city name")}
        ),
        help_text=_("City where your venue is located"),
    )
    street_number = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("123")}
        ),
        help_text=_("Street number (optional)"),
    )
    street_name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Main Street")}
        ),
        help_text=_("Street name"),
    )
    zip_code = forms.CharField(
        max_length=10,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("12345")}
        ),
        help_text=_("ZIP/Postal code"),
    )
    latitude = forms.DecimalField(
        max_digits=10, decimal_places=7, required=False, widget=forms.HiddenInput()
    )
    longitude = forms.DecimalField(
        max_digits=10, decimal_places=7, required=False, widget=forms.HiddenInput()
    )

    # Contact fields
    phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("(*************")}
        ),
        help_text=_("Contact phone number"),
    )
    email = forms.EmailField(
        required=False,
        widget=forms.EmailInput(
            attrs={"class": "form-control", "placeholder": _("<EMAIL>")}
        ),
        help_text=_("Contact email address"),
    )
    website_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={"class": "form-control", "placeholder": _("https://yourvenue.com")}
        ),
        help_text=_("Website URL"),
    )

    # Social Media Links
    instagram_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://instagram.com/yourvenue"),
            }
        ),
        help_text=_("Instagram profile URL (optional)"),
    )

    facebook_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://facebook.com/yourvenue"),
            }
        ),
        help_text=_("Facebook page URL (optional)"),
    )

    twitter_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://twitter.com/yourvenue"),
            }
        ),
        help_text=_("Twitter profile URL (optional)"),
    )

    linkedin_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://linkedin.com/company/yourvenue"),
            }
        ),
        help_text=_("LinkedIn profile URL (optional)"),
    )

    def clean_latitude(self):
        """Clean latitude field to handle empty strings"""
        latitude = self.cleaned_data.get("latitude")
        if latitude == "" or latitude is None:
            return None
        return latitude

    def clean_longitude(self):
        """Clean longitude field to handle empty strings"""
        longitude = self.cleaned_data.get("longitude")
        if longitude == "" or longitude is None:
            return None
        return longitude

    def clean_instagram_url(self):
        """Validate Instagram URL if provided."""
        instagram_url = self.cleaned_data.get("instagram_url", "").strip()
        if instagram_url:
            try:
                instagram_url = validate_social_media_url(instagram_url, "instagram")
            except ValidationError as e:
                raise ValidationError(str(e))
        return instagram_url

    def clean_facebook_url(self):
        """Validate Facebook URL if provided."""
        facebook_url = self.cleaned_data.get("facebook_url", "").strip()
        if facebook_url:
            try:
                facebook_url = validate_social_media_url(facebook_url, "facebook")
            except ValidationError as e:
                raise ValidationError(str(e))
        return facebook_url

    def clean_twitter_url(self):
        """Validate Twitter URL if provided."""
        twitter_url = self.cleaned_data.get("twitter_url", "").strip()
        if twitter_url:
            try:
                twitter_url = validate_social_media_url(twitter_url, "twitter")
            except ValidationError as e:
                raise ValidationError(str(e))
        return twitter_url

    def clean_linkedin_url(self):
        """Validate LinkedIn URL if provided."""
        linkedin_url = self.cleaned_data.get("linkedin_url", "").strip()
        if linkedin_url:
            try:
                linkedin_url = validate_social_media_url(linkedin_url, "linkedin")
            except ValidationError as e:
                raise ValidationError(str(e))
        return linkedin_url

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from ..models import Venue

        self.fields["state"].choices = Venue.STATE_CHOICES
        self.current_step = "location"


class VenueGalleryOnlyForm(VenueCreateWizardMixin, forms.Form):
    """Step 4: Gallery - Images only (no team members)"""

    # Images will be handled with JavaScript and stored as JSON
    images = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Venue images data (handled by JavaScript)"),
    )

    # Main image selection
    main_image = forms.CharField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Selected main image identifier"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only set current_step if not already set by mixin
        if not hasattr(self, "current_step") or self.current_step != "gallery":
            self.current_step = "gallery"


class VenueDetailsForm(VenueCreateWizardMixin, forms.Form):
    """Step 5: Details - Operating hours, amenities, FAQs, and policies"""

    # Operating hours will be handled with JavaScript and stored as JSON
    operating_hours = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Operating hours data (handled by JavaScript)"),
    )

    # Amenities selection
    amenities = forms.MultipleChoiceField(
        choices=[],  # Will be populated in __init__
        required=False,
        widget=forms.CheckboxSelectMultiple(),
        help_text=_("Select all amenities available at your venue"),
    )

    # FAQs will be handled with JavaScript and stored as JSON
    faqs = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("FAQs data (handled by JavaScript)"),
    )

    # Policies
    cancellation_policy = forms.CharField(
        required=False,
        max_length=1000,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Describe your cancellation policy..."),
            }
        ),
        help_text=_("Your venue cancellation policy (optional)"),
    )

    booking_policy = forms.CharField(
        required=False,
        max_length=1000,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Describe your booking policy..."),
            }
        ),
        help_text=_("Your venue booking policy (optional)"),
    )

    special_instructions = forms.CharField(
        required=False,
        max_length=1000,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Any special instructions for customers..."),
            }
        ),
        help_text=_("Special instructions for customers (optional)"),
    )

    # Venue status selection
    VENUE_STATUS_CHOICES = [
        ("draft", _("Save as Draft")),
        ("pending", _("Submit for Admin Approval")),
    ]

    venue_status = forms.ChoiceField(
        choices=VENUE_STATUS_CHOICES,
        widget=forms.RadioSelect(attrs={"class": "form-check-input"}),
        initial="draft",
        help_text=_("Choose whether to save as draft or submit for approval"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from ..models import VenueAmenity

        self.fields["amenities"].choices = VenueAmenity.AMENITY_CHOICES
        # Only set current_step if not already set by mixin
        if not hasattr(self, "current_step") or self.current_step != "details":
            self.current_step = "details"


class VenueCreateForm(VenueCreateWizardMixin, forms.ModelForm):
    """Enhanced multi-step form for creating venues with progress saving."""

    # Venue status selection field
    VENUE_STATUS_CHOICES = [
        ("draft", _("Save as Draft")),
        ("pending", _("Submit for Admin Approval")),
    ]

    venue_status = forms.ChoiceField(
        choices=VENUE_STATUS_CHOICES,
        widget=forms.RadioSelect(attrs={"class": "form-check-input"}),
        initial="draft",
        help_text=_("Choose whether to save as draft or submit for approval"),
    )

    # Add categories field to create form
    categories = forms.ModelMultipleChoiceField(
        queryset=Category.objects.filter(is_active=True),
        widget=forms.CheckboxSelectMultiple(
            attrs={
                "class": "form-check-input category-checkbox",
                "data-toggle": "tooltip",
                "data-placement": "top",
                "data-max-selections": "3",
            }
        ),
        required=True,
        help_text=_("Select at least one category that best describes your venue type"),
        error_messages={
            "required": _("Please select at least one category for your venue"),
            "invalid_choice": _("Please select valid categories"),
        },
    )

    # Add optional contact fields for progressive enhancement
    phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": "Contact phone number (e.g., (*************)",
                "data-validation": "phone",
            }
        ),
        help_text=_("Optional: Contact phone number for your venue"),
    )

    email = forms.EmailField(
        required=False,
        widget=forms.EmailInput(
            attrs={
                "class": "form-control",
                "placeholder": "Contact email address",
                "data-validation": "email",
            }
        ),
        help_text=_("Optional: Contact email address for your venue"),
    )

    website_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": "Website URL (e.g., https://www.example.com)",
                "data-validation": "url",
            }
        ),
        help_text=_("Optional: Your venue website URL"),
    )

    # Social Media Links
    instagram_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": "Instagram URL (e.g., @yourbusiness or https://instagram.com/yourbusiness)",
                "data-validation": "social-media",
                "data-platform": "instagram",
            }
        ),
        help_text=_("Optional: Your venue Instagram profile"),
    )

    facebook_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": "Facebook URL (e.g., https://facebook.com/yourbusiness)",
                "data-validation": "social-media",
                "data-platform": "facebook",
            }
        ),
        help_text=_("Optional: Your venue Facebook page"),
    )

    twitter_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": "Twitter URL (e.g., @yourbusiness or https://twitter.com/yourbusiness)",
                "data-validation": "social-media",
                "data-platform": "twitter",
            }
        ),
        help_text=_("Optional: Your venue Twitter profile"),
    )

    linkedin_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": "LinkedIn URL (e.g., https://linkedin.com/company/yourbusiness)",
                "data-validation": "social-media",
                "data-platform": "linkedin",
            }
        ),
        help_text=_("Optional: Your venue LinkedIn page"),
    )

    # Contact sync option
    sync_from_provider = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(
            attrs={
                "class": "form-check-input",
                "id": "sync_from_provider",
            }
        ),
        help_text=_("Copy contact information from your service provider profile"),
        label=_("Sync contact info from profile"),
    )

    class Meta:
        model = Venue
        fields = [
            "venue_name",
            "state",
            "county",
            "city",
            "street_number",
            "street_name",
            "short_description",
            "categories",
            "phone",
            "email",
            "website_url",
            "instagram_url",
            "facebook_url",
            "twitter_url",
            "linkedin_url",
        ]
        widgets = {
            "venue_name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Enter venue name",
                    "data-validation": "real-time",
                    "data-min-length": "2",
                }
            ),
            "state": forms.Select(
                attrs={
                    "class": "form-select",
                    "data-dependent-field": "county",
                }
            ),
            "county": forms.TextInput(
                attrs={
                    "class": "form-control location-autocomplete",
                    "placeholder": "Enter county name",
                    "data-location-type": "county",
                    "data-dependent-on": "state",
                }
            ),
            "city": forms.TextInput(
                attrs={
                    "class": "form-control location-autocomplete",
                    "placeholder": "Enter city name",
                    "data-location-type": "city",
                    "data-dependent-on": "county",
                }
            ),
            "street_number": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Street number",
                }
            ),
            "street_name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Street name",
                }
            ),
            "short_description": forms.Textarea(
                attrs={
                    "class": "form-control rich-text-editor",
                    "placeholder": "Briefly describe your venue and services (minimum 10 characters)",
                    "rows": 4,
                    "maxlength": 500,
                    "data-character-count": "true",
                    "data-validation": "real-time",
                    "data-min-length": "10",
                    "data-rich-text": "true",
                    "data-seo-analysis": "true",
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        # The VenueCreateWizardMixin._configure_step_fields already handles field requirements
        # based on current_step, so we don't need to override it here.
        # Only set required fields if we're in specific steps to supplement the mixin logic.

        if hasattr(self, "current_step"):
            if self.current_step == "basic":
                self.fields["venue_name"].required = True
                self.fields["short_description"].required = True
                # Ensure location fields are NOT required in basic step
                self.fields["state"].required = False
                self.fields["county"].required = False
                self.fields["city"].required = False
                self.fields["street_number"].required = False
                self.fields["street_name"].required = False
                self.fields["categories"].required = False
            elif self.current_step == "location":
                self.fields["state"].required = True
                self.fields["county"].required = True
                self.fields["city"].required = True
                self.fields["street_number"].required = True
                self.fields["street_name"].required = True
            elif self.current_step == "categories":
                self.fields["categories"].required = True
            elif self.current_step == "final":
                # Final step requires all essential fields
                self.fields["venue_name"].required = True
                self.fields["short_description"].required = True
                self.fields["state"].required = True
                self.fields["county"].required = True
                self.fields["city"].required = True
                self.fields["street_number"].required = True
                self.fields["street_name"].required = True
                self.fields["categories"].required = True

        # Set up state choices from model STATE_CHOICES
        from ..models import Venue

        self.fields["state"].choices = [("", "Select state")] + list(
            Venue.STATE_CHOICES
        )

        # Order categories alphabetically for better UX
        self.fields["categories"].queryset = Category.objects.filter(
            is_active=True
        ).order_by("category_name")

    def clean_venue_name(self):
        """Enhanced venue name validation with sanitization and uniqueness check."""
        venue_name = self.cleaned_data.get("venue_name")

        # Only validate venue_name if we're in basic step or final step
        if hasattr(self, "current_step") and self.current_step not in [
            "basic",
            "final",
        ]:
            return venue_name

        if not venue_name:
            raise forms.ValidationError(_("Venue name is required."))

        # Sanitize the venue name
        try:
            from utils.sanitization import sanitize_text

            venue_name = sanitize_text(venue_name.strip())
        except ImportError:
            import html

            venue_name = html.escape(venue_name.strip())

        if not venue_name:
            raise forms.ValidationError(
                _("Venue name cannot be empty after sanitization.")
            )

        if len(venue_name) < 2:
            raise forms.ValidationError(
                _("Venue name must be at least 2 characters long.")
            )

        if len(venue_name) > 255:
            raise forms.ValidationError(_("Venue name cannot exceed 255 characters."))

        # Check for inappropriate content (basic profanity filter)
        inappropriate_words = ["spam", "test123", "fake", "scam"]  # Extend as needed
        if any(word in venue_name.lower() for word in inappropriate_words):
            raise forms.ValidationError(_("Venue name contains inappropriate content."))

        # Enhanced uniqueness check with better error messages
        user_service_provider = getattr(self.user, "service_provider_profile", None)
        if user_service_provider:
            # Check for exact duplicates
            exact_duplicate = Venue.objects.filter(
                venue_name__iexact=venue_name, is_deleted=False
            ).exclude(service_provider=user_service_provider)

            if self.instance and self.instance.pk:
                exact_duplicate = exact_duplicate.exclude(pk=self.instance.pk)

            if exact_duplicate.exists():
                existing_venue = exact_duplicate.first()
                raise forms.ValidationError(
                    _(
                        'A venue with the name "{}" already exists in {}, {}. '
                        "Please choose a different name or add a distinguishing suffix "
                        '(e.g., "Spa Location", "Downtown Branch").'
                    ).format(venue_name, existing_venue.city, existing_venue.state)
                )

            # Check for very similar names (fuzzy matching)
            import difflib

            similar_venues = Venue.objects.filter(is_deleted=False).exclude(
                service_provider=user_service_provider
            )

            if self.instance and self.instance.pk:
                similar_venues = similar_venues.exclude(pk=self.instance.pk)

            for venue in similar_venues:
                similarity = difflib.SequenceMatcher(
                    None, venue_name.lower(), venue.venue_name.lower()
                ).ratio()
                if similarity > 0.85:  # Very similar names
                    raise forms.ValidationError(
                        _(
                            'A venue with a very similar name "{}" already exists. '
                            "Please choose a more distinctive name."
                        ).format(venue.venue_name)
                    )

        return venue_name

    def clean_short_description(self):
        """Enhanced description validation with sanitization."""
        description = self.cleaned_data.get("short_description")

        # Only validate short_description if we're in basic step or final step
        if hasattr(self, "current_step") and self.current_step not in [
            "basic",
            "final",
        ]:
            return description

        if not description:
            raise forms.ValidationError(_("Description is required."))

        # Sanitize HTML content
        try:
            from utils.sanitization import sanitize_html

            description = sanitize_html(description.strip())
        except ImportError:
            import html

            description = html.escape(description.strip())

        if not description:
            raise forms.ValidationError(
                _("Description cannot be empty after sanitization.")
            )

        if len(description) < 10:
            raise forms.ValidationError(
                _("Description must be at least 10 characters long.")
            )

        if len(description) > 500:
            raise forms.ValidationError(_("Description cannot exceed 500 characters."))

        # Check for low-quality descriptions
        low_quality_patterns = [
            r"^test+$",
            r"^[a-z]+$",  # Only lowercase letters
            r"^(.)\1{5,}",  # Repeated characters
        ]

        import re

        for pattern in low_quality_patterns:
            if re.match(pattern, description.strip(), re.IGNORECASE):
                raise forms.ValidationError(
                    _("Please provide a more detailed and professional description.")
                )

        # Ensure minimum word count
        word_count = len(description.split())
        if word_count < 3:
            raise forms.ValidationError(_("Description must contain at least 3 words."))

        return description

    def clean_state(self):
        """Validate state selection."""
        state = self.cleaned_data.get("state", "").strip()

        # Only validate state if we're in location step or final step
        if hasattr(self, "current_step") and self.current_step not in [
            "location",
            "final",
        ]:
            return state

        if not state:
            raise ValidationError(_("State is required"))
        return state

    def clean_county(self):
        """Validate county."""
        county = self.cleaned_data.get("county", "").strip()

        # Only validate county if we're in location step or final step
        if hasattr(self, "current_step") and self.current_step not in [
            "location",
            "final",
        ]:
            return county

        if not county:
            raise ValidationError(_("County is required"))
        return sanitize_html(county)

    def clean_city(self):
        """Validate city."""
        city = self.cleaned_data.get("city", "").strip()

        # Only validate city if we're in location step or final step
        if hasattr(self, "current_step") and self.current_step not in [
            "location",
            "final",
        ]:
            return city

        if not city:
            raise ValidationError(_("City is required"))
        return sanitize_html(city)

    def clean_street_number(self):
        """Validate street number."""
        street_number = self.cleaned_data.get("street_number", "").strip()

        # Only validate street_number if we're in location step or final step
        if hasattr(self, "current_step") and self.current_step not in [
            "location",
            "final",
        ]:
            return street_number

        if not street_number:
            raise ValidationError(_("Street number is required"))
        return sanitize_html(street_number)

    def clean_street_name(self):
        """Validate street name."""
        street_name = self.cleaned_data.get("street_name", "").strip()

        # Only validate street_name if we're in location step or final step
        if hasattr(self, "current_step") and self.current_step not in [
            "location",
            "final",
        ]:
            return street_name

        if not street_name:
            raise ValidationError(_("Street name is required"))
        return sanitize_html(street_name)

    def clean_phone(self):
        """Validate phone number if provided."""
        phone = self.cleaned_data.get("phone", "").strip()
        if phone:
            if not validate_phone_number(phone):
                raise ValidationError(_("Please enter a valid phone number"))
        return phone

    def clean_email(self):
        """Validate email if provided."""
        email = self.cleaned_data.get("email", "").strip()
        if email:
            if not validate_business_email(email):
                raise ValidationError(_("Please enter a valid email address"))
        return email

    def clean_website_url(self):
        """Validate website URL if provided."""
        website_url = self.cleaned_data.get("website_url", "").strip()
        if website_url:
            try:
                website_url = validate_website_url(
                    website_url, check_accessibility=False
                )
            except ValidationError as e:
                raise ValidationError(str(e))
        return website_url

    def clean_instagram_url(self):
        """Validate Instagram URL if provided."""
        instagram_url = self.cleaned_data.get("instagram_url", "").strip()
        if instagram_url:
            try:
                instagram_url = validate_social_media_url(instagram_url, "instagram")
            except ValidationError as e:
                raise ValidationError(str(e))
        return instagram_url

    def clean_facebook_url(self):
        """Validate Facebook URL if provided."""
        facebook_url = self.cleaned_data.get("facebook_url", "").strip()
        if facebook_url:
            try:
                facebook_url = validate_social_media_url(facebook_url, "facebook")
            except ValidationError as e:
                raise ValidationError(str(e))
        return facebook_url

    def clean_twitter_url(self):
        """Validate Twitter URL if provided."""
        twitter_url = self.cleaned_data.get("twitter_url", "").strip()
        if twitter_url:
            try:
                twitter_url = validate_social_media_url(twitter_url, "twitter")
            except ValidationError as e:
                raise ValidationError(str(e))
        return twitter_url

    def clean_linkedin_url(self):
        """Validate LinkedIn URL if provided."""
        linkedin_url = self.cleaned_data.get("linkedin_url", "").strip()
        if linkedin_url:
            try:
                linkedin_url = validate_social_media_url(linkedin_url, "linkedin")
            except ValidationError as e:
                raise ValidationError(str(e))
        return linkedin_url

    def clean_categories(self):
        """Validate venue categories."""
        categories = self.cleaned_data.get("categories")

        # Only validate categories if we're in categories step or final step
        if hasattr(self, "current_step") and self.current_step not in [
            "categories",
            "final",
        ]:
            return categories

        if not categories or categories.count() == 0:
            raise forms.ValidationError(
                _("Please select at least one category for your venue.")
            )

        if categories.count() > 3:
            raise forms.ValidationError(_("You can select up to 3 categories maximum."))

        # Validate that all selected categories are active
        for category in categories:
            if not category.is_active:
                raise forms.ValidationError(
                    f'Category "{category.category_name}" is no longer available.'
                )

        return categories

    def clean(self):
        """Enhanced cross-field validation with comprehensive security and business logic checks."""
        cleaned_data = super().clean()

        # Ensure user has service provider profile
        if not hasattr(self.user, "service_provider_profile"):
            raise forms.ValidationError(
                _("You must have a service provider profile to create a venue.")
            )

        # Validate approval requirements if submitting for approval
        venue_status = cleaned_data.get("venue_status")
        if venue_status == "pending":
            # Create a temporary venue object to check requirements
            temp_venue = self.instance or Venue()

            # Set basic fields from form data
            for field_name in [
                "venue_name",
                "short_description",
                "phone",
                "email",
                "website_url",
            ]:
                if field_name in cleaned_data:
                    setattr(temp_venue, field_name, cleaned_data[field_name])

            # Check if venue would meet approval requirements
            from ..models import VenueApprovalCriteria

            is_eligible, missing_requirements = (
                VenueApprovalCriteria.check_venue_eligibility(temp_venue)
            )

            if not is_eligible:
                # Add form errors for missing requirements
                high_priority_missing = [
                    req for req in missing_requirements if req["priority"] == "high"
                ]

                if high_priority_missing:
                    error_messages = []
                    for req in high_priority_missing[
                        :3
                    ]:  # Show first 3 high priority requirements
                        error_messages.append(
                            f"{req['requirement']}: {req['description']}"
                        )

                    raise forms.ValidationError(
                        f"Cannot submit for approval. Missing critical requirements: "
                        + "; ".join(error_messages)
                        + (
                            f" and {len(high_priority_missing) - 3} more."
                            if len(high_priority_missing) > 3
                            else ""
                        )
                    )

        # Enhanced location validation - only in location or final step
        if not hasattr(self, "current_step") or self.current_step in [
            "location",
            "final",
        ]:
            state = cleaned_data.get("state")
            county = cleaned_data.get("county")
            city = cleaned_data.get("city")

            if state and county and city:
                # Validate location combination
                is_valid, matched_city, error_msg = validate_location_combination(
                    state, county, city
                )
                if not is_valid:
                    self.add_error("city", error_msg)
                elif matched_city:
                    # Store matched city for later use
                    self._matched_uscity = matched_city

            # Address completeness validation - only in location or final step
            street_number = cleaned_data.get("street_number")
            street_name = cleaned_data.get("street_name")

            if (street_number and not street_name) or (
                street_name and not street_number
            ):
                if not street_number:
                    self.add_error(
                        "street_number",
                        _("Street number is required when street name is provided."),
                    )
                if not street_name:
                    self.add_error(
                        "street_name",
                        _("Street name is required when street number is provided."),
                    )

        # Contact validation - only in contact or final step (but make it optional)
        if not hasattr(self, "current_step") or self.current_step in [
            "contact",
            "final",
        ]:
            phone = cleaned_data.get("phone")
            email = cleaned_data.get("email")
            website_url = cleaned_data.get("website_url")

            # Contact methods are optional, but we can suggest having at least one
            # Only show this suggestion in final step to avoid confusing users
            if self.current_step == "final" and not any([phone, email, website_url]):
                # Make this a warning instead of an error
                pass  # Remove the requirement since contact info is optional

        # Enhanced service provider venue limit validation - CRITICAL BUSINESS RULE
        # Only check this in the final step to avoid blocking progress through wizard
        if not hasattr(self, "current_step") or self.current_step == "final":
            service_provider = self.user.service_provider_profile
            existing_venues = Venue.objects.filter(
                service_provider=service_provider, is_deleted=False
            )

            # Exclude current venue if editing
            if self.instance and self.instance.pk:
                existing_venues = existing_venues.exclude(pk=self.instance.pk)

            if existing_venues.exists():
                raise forms.ValidationError(
                    _(
                        "You already have a venue registered. You can only have one venue per account. "
                        "Please edit your existing venue or contact support if you need to create a new one."
                    )
                )

        # Validation for business logic consistency - only in basic or final step
        if not hasattr(self, "current_step") or self.current_step in ["basic", "final"]:
            venue_name = cleaned_data.get("venue_name")
            short_description = cleaned_data.get("short_description")

            if venue_name and short_description:
                # Check if description is just a repeat of the venue name
                if (
                    venue_name.lower() in short_description.lower()
                    and len(short_description.split()) < 5
                ):
                    self.add_error(
                        "short_description",
                        _(
                            "Please provide a more detailed description beyond just the venue name."
                        ),
                    )

        # Category validation - only in categories or final step
        if not hasattr(self, "current_step") or self.current_step in [
            "categories",
            "final",
        ]:
            categories = cleaned_data.get("categories")
            if categories and len(categories) > 3:
                self.add_error(
                    "categories", _("You can select up to 3 categories only.")
                )

        # Security validation - check for suspicious patterns (all steps)
        venue_name = cleaned_data.get("venue_name")
        short_description = cleaned_data.get("short_description")
        phone = cleaned_data.get("phone")
        email = cleaned_data.get("email")

        all_text_fields = []
        if venue_name:
            all_text_fields.append(venue_name)
        if short_description:
            all_text_fields.append(short_description)
        if phone:
            all_text_fields.append(phone)
        if email:
            all_text_fields.append(email)

        suspicious_patterns = [
            r"<script",
            r"javascript:",
            r"data:",
            r"vbscript:",
            r"onload=",
            r"onerror=",
            r"eval\(",
        ]

        import re

        for field_value in all_text_fields:
            if field_value:
                for pattern in suspicious_patterns:
                    if re.search(pattern, field_value, re.IGNORECASE):
                        raise forms.ValidationError(
                            _(
                                "Suspicious content detected. Please remove any code or scripts from your input."
                            )
                        )

        return cleaned_data

    def save(self, commit=True):
        venue = super().save(commit=False)

        if not venue.pk and self.user:
            try:
                service_provider_profile = self.user.service_provider_profile
                venue.service_provider = service_provider_profile
            except ServiceProviderProfile.DoesNotExist:
                raise ValidationError(
                    _("User must have a service provider profile to create a venue")
                )

        # Handle contact sync from service provider profile
        if self.cleaned_data.get("sync_from_provider", False):
            synced_data = sync_contact_from_service_provider(venue)

        # Set approval status based on venue_status selection
        venue_status = self.cleaned_data.get("venue_status", "draft")
        if venue_status == "draft":
            venue.approval_status = Venue.DRAFT
        else:
            venue.approval_status = Venue.PENDING

        venue.visibility = Venue.ACTIVE

        # Set optional contact information
        venue.phone = self.cleaned_data.get("phone", "")
        venue.email = self.cleaned_data.get("email", "")
        venue.website_url = self.cleaned_data.get("website_url", "")

        # Set social media links
        venue.instagram_url = self.cleaned_data.get("instagram_url", "")
        venue.facebook_url = self.cleaned_data.get("facebook_url", "")
        venue.twitter_url = self.cleaned_data.get("twitter_url", "")
        venue.linkedin_url = self.cleaned_data.get("linkedin_url", "")

        # Generate email verification token if email provided
        if venue.email and not venue.email_verified:
            venue.email_verification_token = generate_email_verification_token()

        # Link to USCity and set coordinates if available
        if hasattr(self, "_matched_uscity") and self._matched_uscity:
            venue.us_city = self._matched_uscity
            # Set coordinates from USCity if not already set
            if not venue.latitude or not venue.longitude:
                venue.latitude = self._matched_uscity.latitude
                venue.longitude = self._matched_uscity.longitude

        if commit:
            venue.save()
            # Save many-to-many relationships
            self.save_m2m()

        return venue


class VenueForm(forms.ModelForm):
    """Form for creating and editing venues."""

    categories = forms.ModelMultipleChoiceField(
        queryset=Category.objects.filter(is_active=True),
        widget=forms.CheckboxSelectMultiple(
            attrs={
                "class": "form-check-input category-checkbox",
                "data-toggle": "tooltip",
                "data-placement": "top",
            }
        ),
        required=True,  # Make categories mandatory
        help_text=_("Select at least one category that best describes your venue"),
        error_messages={
            "required": _("Please select at least one category for your venue"),
            "invalid_choice": _("Please select valid categories"),
        },
    )

    class Meta:
        model = Venue
        fields = [
            "venue_name",
            "short_description",
            "phone",
            "email",
            "website_url",
            "instagram_url",
            "facebook_url",
            "twitter_url",
            "linkedin_url",
            "state",
            "county",
            "city",
            "street_number",
            "street_name",
            "latitude",
            "longitude",
            "opening_notes",
            "tags",
            "categories",
        ]
        widgets = {
            "venue_name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Enter venue name",
                }
            ),
            "short_description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 4,
                    "placeholder": "Brief description of your venue (max 500 characters)",
                    "maxlength": 500,
                }
            ),
            "phone": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Contact phone number (e.g., (*************)",
                }
            ),
            "email": forms.EmailInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Contact email address",
                }
            ),
            "website_url": forms.URLInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Website URL (e.g., https://www.example.com)",
                }
            ),
            "instagram_url": forms.URLInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Instagram URL (e.g., @yourbusiness)",
                }
            ),
            "facebook_url": forms.URLInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Facebook URL (e.g., https://facebook.com/yourbusiness)",
                }
            ),
            "twitter_url": forms.URLInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Twitter URL (e.g., @yourbusiness)",
                }
            ),
            "linkedin_url": forms.URLInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "LinkedIn URL (e.g., https://linkedin.com/company/yourbusiness)",
                }
            ),
            "state": forms.Select(attrs={"class": "form-select"}),
            "county": forms.TextInput(
                attrs={
                    "class": "form-control location-autocomplete",
                    "placeholder": "Enter county name",
                    "data-location-type": "county",
                }
            ),
            "city": forms.TextInput(
                attrs={
                    "class": "form-control location-autocomplete",
                    "placeholder": "Enter city name",
                    "data-location-type": "city",
                }
            ),
            "street_number": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Street number",
                }
            ),
            "street_name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Street name",
                }
            ),
            "latitude": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "any",
                    "placeholder": "Latitude (optional - auto-filled from location)",
                    "readonly": True,
                }
            ),
            "longitude": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "any",
                    "placeholder": "Longitude (optional - auto-filled from location)",
                    "readonly": True,
                }
            ),
            "opening_notes": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 2,
                    "placeholder": 'Additional notes about opening times (e.g., "Closed on holidays")',
                }
            ),
            "tags": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Enter up to 5 tags separated by commas (e.g., spa, massage, relaxation)",
                }
            ),
        }
        help_texts = {
            "venue_name": _("Choose a descriptive name for your venue"),
            "short_description": _(
                "Brief description highlighting what makes your venue special"
            ),
            "phone": _("Primary contact number for your venue"),
            "email": _("Contact email for venue inquiries"),
            "website_url": _("Your venue's official website (optional)"),
            "state": _("State where your venue is located"),
            "county": _("County/Parish where your venue is located"),
            "city": _("City where your venue is located"),
            "street_number": _("Street number of your venue address"),
            "street_name": _("Street name of your venue address"),
            "latitude": _("Latitude coordinate (automatically filled from location)"),
            "longitude": _("Longitude coordinate (automatically filled from location)"),
            "opening_notes": _("Any special notes about your operating hours"),
            "tags": _("Keywords that describe your venue (comma-separated, max 5)"),
            "categories": _(
                "Select categories that best describe your venue type (maximum 3)"
            ),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        # Set required fields
        self.fields["venue_name"].required = True
        self.fields["short_description"].required = True
        self.fields["state"].required = True
        self.fields["county"].required = True
        self.fields["city"].required = True
        self.fields["street_number"].required = True
        self.fields["street_name"].required = True
        self.fields["categories"].required = True

        # Set up state choices from model STATE_CHOICES
        from ..models import Venue

        self.fields["state"].choices = [("", "Select state")] + list(
            Venue.STATE_CHOICES
        )

        # Order categories alphabetically for better UX
        self.fields["categories"].queryset = Category.objects.filter(
            is_active=True
        ).order_by("category_name")

    def clean_venue_name(self):
        venue_name = self.cleaned_data.get("venue_name", "").strip()
        if not venue_name:
            raise ValidationError(_("Venue name is required"))
        if len(venue_name) < 2:
            raise ValidationError(_("Venue name must be at least 2 characters long"))
        return sanitize_html(venue_name)

    def clean_short_description(self):
        description = self.cleaned_data.get("short_description", "").strip()
        if not description:
            raise ValidationError(_("Short description is required"))
        if len(description) < 10:
            raise ValidationError(_("Description must be at least 10 characters long"))
        return sanitize_html(description)

    def clean_state(self):
        state = self.cleaned_data.get("state", "").strip()
        if not state:
            raise ValidationError(_("State is required"))
        return state

    def clean_county(self):
        county = self.cleaned_data.get("county", "").strip()
        if not county:
            raise ValidationError(_("County is required"))
        return sanitize_html(county)

    def clean_city(self):
        city = self.cleaned_data.get("city", "").strip()
        if not city:
            raise ValidationError(_("City is required"))
        return sanitize_html(city)

    def clean_street_number(self):
        street_number = self.cleaned_data.get("street_number", "").strip()
        if not street_number:
            raise ValidationError(_("Street number is required"))
        return sanitize_html(street_number)

    def clean_street_name(self):
        street_name = self.cleaned_data.get("street_name", "").strip()
        if not street_name:
            raise ValidationError(_("Street name is required"))
        return sanitize_html(street_name)

    def clean_phone(self):
        phone = self.cleaned_data.get("phone", "").strip()
        if phone:
            try:
                phone = validate_phone_number(phone)
            except ValidationError as e:
                raise ValidationError(str(e))
        return sanitize_html(phone)

    def clean_email(self):
        email = self.cleaned_data.get("email", "").strip()
        if email:
            try:
                email = validate_business_email(email)
            except ValidationError as e:
                raise ValidationError(str(e))
        return email

    def clean_website_url(self):
        website_url = self.cleaned_data.get("website_url", "").strip()
        if website_url:
            try:
                website_url = validate_website_url(
                    website_url, check_accessibility=False
                )
            except ValidationError as e:
                raise ValidationError(str(e))
        return website_url

    def clean_instagram_url(self):
        """Validate Instagram URL if provided."""
        instagram_url = self.cleaned_data.get("instagram_url", "").strip()
        if instagram_url:
            try:
                instagram_url = validate_social_media_url(instagram_url, "instagram")
            except ValidationError as e:
                raise ValidationError(str(e))
        return instagram_url

    def clean_facebook_url(self):
        """Validate Facebook URL if provided."""
        facebook_url = self.cleaned_data.get("facebook_url", "").strip()
        if facebook_url:
            try:
                facebook_url = validate_social_media_url(facebook_url, "facebook")
            except ValidationError as e:
                raise ValidationError(str(e))
        return facebook_url

    def clean_twitter_url(self):
        """Validate Twitter URL if provided."""
        twitter_url = self.cleaned_data.get("twitter_url", "").strip()
        if twitter_url:
            try:
                twitter_url = validate_social_media_url(twitter_url, "twitter")
            except ValidationError as e:
                raise ValidationError(str(e))
        return twitter_url

    def clean_linkedin_url(self):
        """Validate LinkedIn URL if provided."""
        linkedin_url = self.cleaned_data.get("linkedin_url", "").strip()
        if linkedin_url:
            try:
                linkedin_url = validate_social_media_url(linkedin_url, "linkedin")
            except ValidationError as e:
                raise ValidationError(str(e))
        return linkedin_url

    def clean_categories(self):
        categories = self.cleaned_data.get("categories", [])
        if not categories:
            raise ValidationError(_("Please select at least one category"))
        if len(categories) > 3:
            raise ValidationError(_("You can select up to 3 categories only"))
        return categories

    def clean_tags(self):
        tags = self.cleaned_data.get("tags", "").strip()
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
            if len(tag_list) > 5:
                raise ValidationError(_("Maximum 5 tags allowed"))
            return ", ".join(tag_list)
        return ""

    def clean_opening_notes(self):
        return sanitize_html(self.cleaned_data.get("opening_notes", ""))

    def clean(self):
        """Enhanced validation with USCity integration."""
        cleaned_data = super().clean()
        state = cleaned_data.get("state")
        county = cleaned_data.get("county")
        city = cleaned_data.get("city")

        # Validate location combination against USCity database
        if state and county and city:
            is_valid, uscity, error_message = validate_location_combination(
                state, county, city
            )
            if not is_valid:
                raise ValidationError({"city": error_message})
            # Store the matched USCity for later use
            self._matched_uscity = uscity

        return cleaned_data

    def save(self, commit=True):
        venue = super().save(commit=False)

        if not venue.pk and self.user:
            try:
                service_provider_profile = self.user.service_provider_profile
                venue.service_provider = service_provider_profile
            except ServiceProviderProfile.DoesNotExist:
                raise ValidationError(
                    _("User must have a service provider profile to create a venue")
                )

        if not venue.pk:
            venue.approval_status = Venue.PENDING
            venue.visibility = Venue.ACTIVE

        # Link to USCity and set coordinates if available
        if hasattr(self, "_matched_uscity") and self._matched_uscity:
            venue.us_city = self._matched_uscity
            # Set coordinates from USCity if not already set
            if not venue.latitude or not venue.longitude:
                venue.latitude = self._matched_uscity.latitude
                venue.longitude = self._matched_uscity.longitude

        if commit:
            venue.save()
            if "categories" in self.cleaned_data:
                # Handle categories through VenueCategory model since it uses 'through'
                from ..models import VenueCategory

                # Clear existing categories for this venue
                VenueCategory.objects.filter(venue=venue).delete()
                # Add new categories
                for category in self.cleaned_data["categories"]:
                    VenueCategory.objects.create(venue=venue, category=category)

        return venue

    @staticmethod
    def get_description_templates():
        """Get predefined description templates for different venue types."""
        return {
            "spa": [
                {
                    "title": "Luxury Spa Experience",
                    "template": "Experience ultimate relaxation at our luxury spa, where expert therapists provide rejuvenating treatments in a serene environment. We offer a full range of services including massages, facials, body treatments, and wellness packages designed to restore your mind, body, and spirit.",
                    "keywords": [
                        "luxury spa",
                        "massage",
                        "facials",
                        "relaxation",
                        "wellness",
                        "rejuvenating",
                    ],
                },
                {
                    "title": "Holistic Wellness Center",
                    "template": "Our holistic wellness center combines traditional healing practices with modern spa amenities. Discover personalized treatments that address your unique wellness needs, from therapeutic massages to organic skincare, all in our peaceful sanctuary.",
                    "keywords": [
                        "holistic wellness",
                        "therapeutic massage",
                        "organic skincare",
                        "healing",
                        "personalized",
                    ],
                },
                {
                    "title": "Day Spa Retreat",
                    "template": "Escape the everyday stress at our day spa, featuring expert massage therapy, revitalizing facials, and specialized treatments. Our tranquil atmosphere and skilled practitioners ensure a memorable wellness experience.",
                    "keywords": [
                        "day spa",
                        "massage therapy",
                        "facials",
                        "stress relief",
                        "tranquil",
                    ],
                },
            ],
            "salon": [
                {
                    "title": "Full-Service Hair Salon",
                    "template": "Transform your look at our full-service salon, where skilled stylists provide expert cuts, coloring, styling, and treatments. We use premium products and stay current with the latest trends to help you achieve your perfect style.",
                    "keywords": [
                        "hair salon",
                        "hair cuts",
                        "hair coloring",
                        "styling",
                        "premium products",
                    ],
                },
                {
                    "title": "Beauty & Hair Studio",
                    "template": "Our beauty studio offers comprehensive hair and beauty services including precision cuts, professional coloring, makeup application, and special event styling. Experience personalized service in our modern, welcoming environment.",
                    "keywords": [
                        "beauty studio",
                        "precision cuts",
                        "professional coloring",
                        "makeup",
                        "special events",
                    ],
                },
                {
                    "title": "Modern Hair Design",
                    "template": "Discover contemporary hair design at our modern salon, where creativity meets expertise. From classic cuts to avant-garde styles, our talented team delivers exceptional results using the finest products and techniques.",
                    "keywords": [
                        "modern salon",
                        "hair design",
                        "contemporary",
                        "creative styling",
                        "exceptional",
                    ],
                },
            ],
            "massage": [
                {
                    "title": "Therapeutic Massage Center",
                    "template": "Find relief and relaxation at our therapeutic massage center, where licensed therapists specialize in Swedish, deep tissue, sports, and specialized massage techniques. Each session is tailored to address your specific needs and wellness goals.",
                    "keywords": [
                        "therapeutic massage",
                        "licensed therapists",
                        "Swedish massage",
                        "deep tissue",
                        "sports massage",
                    ],
                },
                {
                    "title": "Wellness Massage Studio",
                    "template": "Our massage studio focuses on promoting overall wellness through skilled therapeutic touch. We offer a variety of massage modalities in a peaceful setting, helping you achieve physical relief and mental relaxation.",
                    "keywords": [
                        "wellness massage",
                        "therapeutic touch",
                        "massage modalities",
                        "physical relief",
                        "mental relaxation",
                    ],
                },
                {
                    "title": "Healing Touch Massage",
                    "template": "Experience the healing power of professional massage therapy at our dedicated studio. Our certified therapists provide customized treatments designed to reduce stress, alleviate pain, and enhance your overall well-being.",
                    "keywords": [
                        "healing touch",
                        "professional massage",
                        "certified therapists",
                        "stress reduction",
                        "pain relief",
                    ],
                },
            ],
            "fitness": [
                {
                    "title": "Complete Fitness Center",
                    "template": "Achieve your fitness goals at our state-of-the-art facility featuring modern equipment, group classes, and personal training. Whether you're a beginner or advanced athlete, our supportive environment helps you succeed.",
                    "keywords": [
                        "fitness center",
                        "modern equipment",
                        "group classes",
                        "personal training",
                        "supportive environment",
                    ],
                },
                {
                    "title": "Health & Wellness Gym",
                    "template": "Transform your health at our comprehensive fitness facility, offering strength training, cardio equipment, group fitness classes, and wellness programs. Our expert trainers are here to guide you on your fitness journey.",
                    "keywords": [
                        "health wellness gym",
                        "strength training",
                        "cardio equipment",
                        "group fitness",
                        "expert trainers",
                    ],
                },
                {
                    "title": "Community Fitness Studio",
                    "template": "Join our vibrant fitness community where members support each other in achieving wellness goals. We offer diverse workout options, from high-intensity training to yoga, all in a friendly, motivating atmosphere.",
                    "keywords": [
                        "community fitness",
                        "vibrant community",
                        "diverse workouts",
                        "high-intensity training",
                        "motivating",
                    ],
                },
            ],
            "wellness": [
                {
                    "title": "Integrative Wellness Center",
                    "template": "Our integrative wellness center combines traditional and alternative healing approaches to support your complete well-being. We offer personalized consultations, holistic treatments, and educational wellness programs.",
                    "keywords": [
                        "integrative wellness",
                        "holistic treatments",
                        "alternative healing",
                        "personalized consultations",
                        "well-being",
                    ],
                },
                {
                    "title": "Mind-Body Wellness Studio",
                    "template": "Nurture your mind, body, and spirit at our wellness studio, where we specialize in stress management, mindfulness practices, and natural healing approaches. Discover a path to balanced living.",
                    "keywords": [
                        "mind-body wellness",
                        "stress management",
                        "mindfulness practices",
                        "natural healing",
                        "balanced living",
                    ],
                },
                {
                    "title": "Holistic Health Center",
                    "template": "Experience comprehensive wellness at our holistic health center, offering natural therapies, nutritional counseling, and lifestyle coaching. We take a whole-person approach to help you achieve optimal health.",
                    "keywords": [
                        "holistic health",
                        "natural therapies",
                        "nutritional counseling",
                        "lifestyle coaching",
                        "optimal health",
                    ],
                },
            ],
        }

    @staticmethod
    def get_templates_by_category(categories):
        """Get appropriate description templates based on venue categories."""
        templates = VenueForm.get_description_templates()

        # Map category names to template keys
        category_mapping = {
            "spa": "spa",
            "salon": "salon",
            "massage": "massage",
            "fitness": "fitness",
            "wellness": "wellness",
            "beauty": "salon",
            "health": "wellness",
            "therapy": "wellness",
        }

        # Get the first matching template set
        for category in categories:
            category_name = category.category_name.lower()
            for key, template_key in category_mapping.items():
                if key in category_name:
                    return templates.get(template_key, [])

        # Return spa templates as default
        return templates.get("spa", [])

    @staticmethod
    def analyze_description_seo(description, venue_name="", categories=None):
        """Analyze description for SEO and provide suggestions."""
        if not description:
            return {
                "score": 0,
                "suggestions": ["Add a description to improve SEO"],
                "keywords": [],
                "length_status": "too_short",
            }

        # Basic metrics
        word_count = len(description.split())
        char_count = len(description)

        # SEO Analysis
        suggestions = []
        score = 0
        keywords = []

        # Length analysis
        if char_count < 100:
            suggestions.append(
                "Description should be at least 100 characters for better SEO"
            )
            length_status = "too_short"
        elif char_count > 400:
            suggestions.append("Consider shortening description for better readability")
            length_status = "too_long"
            score += 10
        else:
            score += 20
            length_status = "good"

        # Keyword analysis
        description_lower = description.lower()

        # Check for venue name
        if venue_name and venue_name.lower() in description_lower:
            score += 15
        else:
            suggestions.append(
                f'Consider including your venue name "{venue_name}" in the description'
            )

        # Check for category-related keywords
        if categories:
            category_keywords = []
            for category in categories:
                cat_name = category.category_name.lower()
                category_keywords.append(cat_name)
                if cat_name in description_lower:
                    score += 10
                    keywords.append(cat_name)

            if not keywords:
                suggestions.append(
                    f'Include keywords related to your categories: {", ".join(category_keywords)}'
                )

        # Check for action words
        action_words = [
            "experience",
            "discover",
            "enjoy",
            "relax",
            "transform",
            "achieve",
            "expert",
            "professional",
            "quality",
        ]
        found_actions = [word for word in action_words if word in description_lower]
        if found_actions:
            score += len(found_actions) * 2
            keywords.extend(found_actions)
        else:
            suggestions.append(
                'Add action words like "experience", "discover", or "enjoy" to make it more engaging'
            )

        # Check for location indicators
        location_words = ["located", "visit", "come", "center", "studio", "clinic"]
        found_locations = [word for word in location_words if word in description_lower]
        if found_locations:
            score += 5

        # Check readability
        sentences = (
            description.count(".") + description.count("!") + description.count("?")
        )
        if sentences == 0:
            suggestions.append("Add punctuation to improve readability")
        elif sentences > 0 and word_count / sentences > 20:
            suggestions.append(
                "Consider breaking up long sentences for better readability"
            )
        else:
            score += 5

        # Check for common SEO issues
        if description.count("!") > 2:
            suggestions.append("Limit exclamation marks for professional tone")

        if description.isupper():
            suggestions.append("Avoid all uppercase text")

        # Final score calculation
        score = min(score, 100)

        # Overall assessment
        if score >= 80:
            overall = "excellent"
        elif score >= 60:
            overall = "good"
        elif score >= 40:
            overall = "fair"
        else:
            overall = "needs_improvement"

        if not suggestions:
            suggestions.append("Great! Your description is well-optimized for SEO.")

        return {
            "score": score,
            "overall": overall,
            "suggestions": suggestions,
            "keywords": list(set(keywords)),
            "length_status": length_status,
            "word_count": word_count,
            "char_count": char_count,
        }


def validate_image_dimensions(
    image_file, min_width=300, min_height=200, max_width=4000, max_height=4000
):
    """Validate image dimensions for venue images."""
    from PIL import Image

    try:
        with Image.open(image_file) as img:
            width, height = img.size

            # Check minimum dimensions
            if width < min_width or height < min_height:
                raise ValidationError(
                    f"Image is too small. Minimum size is {min_width}x{min_height} pixels. "
                    f"Your image is {width}x{height} pixels. Please use a higher resolution image "
                    f"for better display quality."
                )

            # Check maximum dimensions
            if width > max_width or height > max_height:
                raise ValidationError(
                    f"Image is too large. Maximum size is {max_width}x{max_height} pixels. "
                    f"Your image is {width}x{height} pixels. Please resize the image or "
                    f"use a smaller resolution."
                )

            # Check aspect ratio for venue images (should be reasonably close to 3:2)
            aspect_ratio = width / height
            if aspect_ratio < 0.75 or aspect_ratio > 2.0:
                # This is just a warning, not an error
                pass  # We'll handle this in the form's clean method

            return width, height, aspect_ratio

    except Exception as e:
        raise ValidationError(f"Unable to process image: {str(e)}")


class VenueImageForm(forms.ModelForm):
    """Form for uploading additional venue images with enhanced processing."""

    class Meta:
        model = VenueImage
        fields = ["image", "caption", "order"]
        widgets = {
            "image": forms.FileInput(
                attrs={
                    "class": "form-control",
                    "accept": "image/jpeg,image/jpg,image/png,image/webp",
                    "data-preview": "true",
                    "data-max-size": "5242880",  # 5MB in bytes
                    "data-min-width": "300",
                    "data-min-height": "200",
                }
            ),
            "caption": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Optional caption for this image",
                    "maxlength": "255",
                }
            ),
            "order": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "min": 1,
                    "max": 5,
                }
            ),
        }
        help_texts = {
            "image": _(
                "Upload venue image (JPEG, PNG, WebP - max 5MB, min 300x200px). WebP recommended for best quality and compression."
            ),
            "order": _("Display order (1-5)"),
        }

    def clean_image(self):
        image = self.cleaned_data.get("image")
        if image:
            try:
                # Comprehensive validation including file extension, MIME type, and size
                validate_image_comprehensive(
                    image, "venue_gallery", max_size_kb=5120
                )  # 5MB in KB

                # Additional dimension validation for venue images
                width, height, aspect_ratio = validate_image_dimensions(
                    image,
                    min_width=300,
                    min_height=200,
                    max_width=4000,
                    max_height=4000,
                )

                # Aspect ratio warning (not blocking)
                if aspect_ratio < 1.0 or aspect_ratio > 2.5:
                    # Store a warning message that can be displayed to the user
                    if not hasattr(self, "_warnings"):
                        self._warnings = []
                    self._warnings.append(
                        f"Image aspect ratio ({aspect_ratio:.2f}:1) is unusual for venue photos. "
                        f"For best display, consider using images with a 3:2 or 4:3 aspect ratio."
                    )

                # File efficiency check
                file_size_mb = image.size / (1024 * 1024)
                pixels = width * height
                bytes_per_pixel = image.size / pixels

                if bytes_per_pixel > 20:  # Very inefficient compression
                    if not hasattr(self, "_warnings"):
                        self._warnings = []
                    self._warnings.append(
                        f"Image file size ({file_size_mb:.1f}MB) is quite large for its dimensions. "
                        f"Consider compressing the image or using WebP format for better efficiency."
                    )

            except ValidationError as e:
                raise ValidationError(str(e))
            except ImportError:
                # Fallback to basic validation if utils not available
                try:
                    validate_image_extension(image.name, "venue_gallery")
                    validate_image_size(image, max_size_kb=5120)  # 5MB in KB
                    validate_image_dimensions(image)  # Basic dimension check
                except ValidationError as e:
                    raise ValidationError(str(e))
        return image

    def get_warnings(self):
        """Return any validation warnings (non-blocking issues)."""
        return getattr(self, "_warnings", [])

    def save(self, commit=True):
        venue_image = super().save(commit=False)

        if commit:
            try:
                # Use the sophisticated image processing pipeline
                if ImageService and self.cleaned_data.get("image"):
                    image_file = self.cleaned_data["image"]

                    # Process the image with optimization, EXIF stripping, and thumbnail generation
                    processed_data = process_image(
                        image_file=image_file,
                        image_type="venue_gallery",
                        entity_type="venues",
                        entity_id=venue_image.venue.id if venue_image.venue else None,
                    )

                    # Save the processed image
                    image_path, metadata = ImageService.save_image(
                        processed_data, user=getattr(self, "user", None)
                    )

                    # Update the venue image with the processed image path
                    venue_image.image.name = image_path
                    venue_image.save()

                    # Store metadata reference for future use
                    venue_image._image_metadata = metadata

                else:
                    # Fallback to direct save if image processing not available
                    venue_image.save()

            except Exception as e:
                # Log the error for debugging
                import logging

                logger = logging.getLogger("venues_app")
                logger.error(f"Failed to process venue image: {str(e)}", exc_info=True)

                # Try direct save as fallback
                try:
                    venue_image.save()
                except Exception as fallback_error:
                    logger.error(
                        f"Fallback save also failed: {str(fallback_error)}",
                        exc_info=True,
                    )
                    raise ValidationError(
                        f"Failed to upload image: {str(fallback_error)}"
                    )

        return venue_image


class MultipleFileInput(forms.ClearableFileInput):
    """Custom widget for multiple file uploads."""

    allow_multiple_selected = True

    def __init__(self, attrs=None):
        if attrs is None:
            attrs = {}
        attrs.setdefault("multiple", True)
        super().__init__(attrs)


class VenueGalleryImagesForm(forms.Form):
    """Form for handling 5 individual image uploads during venue creation."""

    image_1 = forms.ImageField(
        required=False,
        widget=forms.FileInput(
            attrs={
                "class": "form-control image-upload-input",
                "accept": "image/jpeg,image/png,image/webp",
                "data-slot": "1",
            }
        ),
        help_text=_(
            "Upload venue image (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression."
        ),
    )

    image_2 = forms.ImageField(
        required=False,
        widget=forms.FileInput(
            attrs={
                "class": "form-control image-upload-input",
                "accept": "image/jpeg,image/png,image/webp",
                "data-slot": "2",
            }
        ),
        help_text=_(
            "Upload venue image (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression."
        ),
    )

    image_3 = forms.ImageField(
        required=False,
        widget=forms.FileInput(
            attrs={
                "class": "form-control image-upload-input",
                "accept": "image/jpeg,image/png,image/webp",
                "data-slot": "3",
            }
        ),
        help_text=_(
            "Upload venue image (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression."
        ),
    )

    image_4 = forms.ImageField(
        required=False,
        widget=forms.FileInput(
            attrs={
                "class": "form-control image-upload-input",
                "accept": "image/jpeg,image/png,image/webp",
                "data-slot": "4",
            }
        ),
        help_text=_(
            "Upload venue image (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression."
        ),
    )

    image_5 = forms.ImageField(
        required=False,
        widget=forms.FileInput(
            attrs={
                "class": "form-control image-upload-input",
                "accept": "image/jpeg,image/png,image/webp",
                "data-slot": "5",
            }
        ),
        help_text=_(
            "Upload venue image (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression."
        ),
    )

    def clean_image_1(self):
        return self._validate_image(self.cleaned_data.get("image_1"), "Image 1")

    def clean_image_2(self):
        return self._validate_image(self.cleaned_data.get("image_2"), "Image 2")

    def clean_image_3(self):
        return self._validate_image(self.cleaned_data.get("image_3"), "Image 3")

    def clean_image_4(self):
        return self._validate_image(self.cleaned_data.get("image_4"), "Image 4")

    def clean_image_5(self):
        return self._validate_image(self.cleaned_data.get("image_5"), "Image 5")

    def _validate_image(self, image, field_name):
        """Validate individual image field with comprehensive validation."""
        if image:
            try:
                # Use comprehensive validation for both file extension and MIME type
                validate_image_comprehensive(
                    image, "venue_gallery", max_size_kb=5120
                )  # 5MB in KB

                # Additional dimension validation
                validate_image_dimensions(
                    image,
                    min_width=300,
                    min_height=200,
                    max_width=4000,
                    max_height=4000,
                )

            except ValidationError as e:
                raise ValidationError(
                    _("{field}: {error}").format(field=field_name, error=str(e))
                )
            except ImportError:
                # Fallback to old validation if utils not available
                try:
                    validate_image_extension(image.name, "venue_gallery")
                    validate_image_size(image, max_size_kb=5120)  # 5MB in KB
                    validate_image_dimensions(image)  # Basic dimension check
                except ValidationError as e:
                    raise ValidationError(
                        _("{field}: {error}").format(field=field_name, error=str(e))
                    )
        return image

    def get_uploaded_images(self):
        """Return list of uploaded images with their slot numbers."""
        images = []
        if not hasattr(self, "cleaned_data"):
            return images

        for i in range(1, 6):
            field_name = f"image_{i}"
            image = self.cleaned_data.get(field_name)
            if image:
                images.append((i, image))
        return images


class VenueWithOperatingHoursForm:
    """Combined form for venue creation with operating hours."""

    def __init__(self, data=None, files=None, instance=None, user=None):
        self.user = user
        self.venue_form = VenueForm(
            data=data, files=files, instance=instance, user=user
        )
        self.gallery_images_form = VenueGalleryImagesForm(data=data, files=files)

        # Initialize operating hours formset
        if instance and instance.pk:
            # For editing existing venue
            existing_hours = OperatingHours.objects.filter(venue=instance).order_by(
                "day"
            )
            initial_data = []
            for day in range(7):  # 0-6 for Monday-Sunday
                hour = existing_hours.filter(day=day).first()
                if hour:
                    initial_data.append(
                        {
                            "day": hour.day,
                            "opening": hour.opening,
                            "closing": hour.closing,
                            "is_closed": hour.is_closed,
                        }
                    )
                else:
                    initial_data.append(
                        {
                            "day": day,
                            "opening": None,
                            "closing": None,
                            "is_closed": True,
                        }
                    )
            self.operating_hours_formset = OperatingHoursFormSetFactory(
                data=data, initial=initial_data, prefix="operating_hours"
            )
        else:
            # For creating new venue - initialize with default closed days
            initial_data = [
                {"day": day, "opening": None, "closing": None, "is_closed": True}
                for day in range(7)
            ]
            self.operating_hours_formset = OperatingHoursFormSetFactory(
                data=data, initial=initial_data, prefix="operating_hours"
            )

    def is_valid(self):
        """Check if venue form, operating hours formset, and gallery images are valid."""
        venue_valid = self.venue_form.is_valid()
        hours_valid = self.operating_hours_formset.is_valid()
        gallery_valid = self.gallery_images_form.is_valid()
        return venue_valid and hours_valid and gallery_valid

    @property
    def errors(self):
        """Combine errors from all forms."""
        errors = {}
        if self.venue_form.errors:
            errors.update(self.venue_form.errors)
        if self.operating_hours_formset.errors:
            errors["operating_hours"] = self.operating_hours_formset.errors
        if self.gallery_images_form.errors:
            errors.update(self.gallery_images_form.errors)
        return errors

    def save(self, commit=True):
        """Save venue, operating hours, and gallery images with improved image management."""
        venue = self.venue_form.save(commit=commit)

        if commit:
            # Ensure venue has been saved and has a primary key
            if not venue.pk:
                venue.save()

            # Delete existing operating hours (for updates)
            if venue.pk:
                OperatingHours.objects.filter(venue=venue).delete()

            # Save new operating hours (only for non-closed days)
            for form in self.operating_hours_formset:
                if form.cleaned_data and not form.cleaned_data.get("DELETE", False):
                    # Only save if the day is not closed
                    if not form.cleaned_data.get("is_closed", True):
                        operating_hour = form.save(commit=False)
                        operating_hour.venue = venue
                        operating_hour.save()

            # Handle image management improvements
            # If main_image is provided but no gallery images exist, convert main_image to first gallery image
            if venue.main_image and not venue.images.filter(is_active=True).exists():
                # Create a gallery image from main_image
                from ..models import VenueImage

                gallery_image = VenueImage(
                    venue=venue,
                    image=venue.main_image,
                    order=1,
                    is_primary=True,
                    is_active=True,
                    caption="Main venue image",
                )
                gallery_image.save()

            # Save gallery images from individual slots
            uploaded_images = self.gallery_images_form.get_uploaded_images()
            existing_count = venue.images.filter(is_active=True).count()

            for order, image in uploaded_images:
                venue_image = VenueImage(
                    venue=venue,
                    image=image,
                    order=existing_count + order,  # Ensure sequential ordering
                    is_primary=(
                        order == 1 and existing_count == 0
                    ),  # First image is primary only if no existing images
                    is_active=True,
                )
                venue_image.save()

            # Ensure there's always a primary image if gallery images exist
            venue.ensure_primary_image()

        return venue


class VenueFilterForm(forms.Form):
    """Enhanced form for filtering venue search results."""

    SORT_CHOICES = [
        ("", _("Best Match")),
        ("newest", _("Newest First")),
        ("name", _("Name A-Z")),
        ("price_low", _("Price: Low to High")),
        ("price_high", _("Price: High to Low")),
        ("rating_high", _("Highest Rated")),
        ("rating_low", _("Lowest Rated")),
        ("discount", _("Special Offers")),
    ]

    VENUE_TYPE_CHOICES = [
        ("", _("All Types")),
        ("spa", _("Spa & Wellness")),
        ("salon", _("Beauty Salon")),
        ("clinic", _("Medical Clinic")),
        ("studio", _("Private Studio")),
        ("mobile", _("Mobile Service")),
        ("fitness", _("Fitness Center")),
        ("other", _("Other")),
    ]

    sort_by = forms.ChoiceField(
        choices=SORT_CHOICES,
        required=False,
        widget=forms.Select(
            attrs={"class": "form-control", "onchange": "this.form.submit();"}
        ),
    )

    venue_type = forms.ChoiceField(
        choices=VENUE_TYPE_CHOICES,
        required=False,
        widget=forms.Select(
            attrs={"class": "form-control", "onchange": "this.form.submit();"}
        ),
    )

    categories = forms.ModelMultipleChoiceField(
        queryset=Category.objects.filter(is_active=True).order_by("category_name"),
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"class": "form-check-input"}),
    )

    # NEW: Service category filtering
    service_categories = forms.ModelMultipleChoiceField(
        queryset=ServiceCategory.objects.filter(is_active=True).order_by(
            "sort_order", "name"
        ),
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"class": "form-check-input"}),
        label=_("Service Categories"),
    )

    state = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter state"),
                "list": "states-list",
            }
        ),
    )

    county = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Enter county")}
        ),
    )

    city = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Enter city")}
        ),
    )

    has_discount = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(
            attrs={"class": "form-check-input", "onchange": "this.form.submit();"}
        ),
        label=_("Special Offers Available"),
    )


class VenueEditWizardMixin:
    """Mixin to provide multi-step wizard functionality for venue editing"""

    STEP_CHOICES = [
        ("basic", _("Basic Information")),
        ("location", _("Location & Contact")),
        ("services", _("Services & Pricing")),
        ("gallery", _("Gallery & Images")),
        ("details", _("Details & Policies")),
    ]

    def __init__(self, *args, **kwargs):
        # Extract current_step and venue instance from kwargs
        passed_current_step = kwargs.pop("current_step", None)
        self.venue = kwargs.pop("venue", None)
        super().__init__(*args, **kwargs)

        # Use passed current_step if provided, otherwise use what subclass set, otherwise default
        if passed_current_step:
            self.current_step = passed_current_step
        elif not hasattr(self, "current_step"):
            self.current_step = "basic"

        self._configure_step_fields()

        # Handle categories from venue data
        if self.venue and "initial" in kwargs and kwargs["initial"]:
            initial_data = kwargs["initial"]
            if "categories" in initial_data and isinstance(
                initial_data["categories"], list
            ):
                # Convert category IDs to Category objects for form initialization
                try:
                    category_ids = [
                        int(cat_id) for cat_id in initial_data["categories"] if cat_id
                    ]
                    self.initial["categories"] = Category.objects.filter(
                        id__in=category_ids
                    )
                except (ValueError, TypeError):
                    pass

    def _configure_step_fields(self):
        """Configure which fields are required/visible for current step"""
        step_fields = self.get_step_fields()

        # Only require fields for current step
        for field_name, field in self.fields.items():
            if field_name not in step_fields:
                field.required = False
            else:
                # Add step-specific styling and help text
                self._enhance_field_for_step(field_name, field)

    def get_step_fields(self):
        """Get fields for current step"""
        step_mapping = {
            "basic": ["venue_name", "short_description", "categories"],
            "location": [
                "state",
                "county",
                "city",
                "street_number",
                "street_name",
                "zip_code",
                "latitude",
                "longitude",
                "phone",
                "email",
                "website_url",
                "instagram_url",
                "facebook_url",
                "twitter_url",
                "linkedin_url",
            ],
            "services": ["services", "service_pricing", "service_discounts"],
            "gallery": ["images", "main_image"],
            "details": [
                "operating_hours",
                "amenities",
                "faqs",
                "cancellation_policy",
                "booking_policy",
                "special_instructions",
                "venue_status",
            ],
        }
        return step_mapping.get(self.current_step, [])

    def _enhance_field_for_step(self, field_name, field):
        """Add step-specific styling and help text to fields"""
        if self.current_step == "basic":
            if field_name == "venue_name":
                field.widget.attrs.update(
                    {
                        "class": "form-control venue-name-input",
                        "data-step": "basic",
                        "placeholder": _("Enter your venue name"),
                    }
                )
            elif field_name == "short_description":
                field.widget.attrs.update(
                    {
                        "class": "form-control description-input",
                        "data-step": "basic",
                        "rows": 4,
                        "placeholder": _(
                            "Describe your venue and what makes it special"
                        ),
                    }
                )
        elif self.current_step == "location":
            if field_name in ["state", "county", "city"]:
                field.widget.attrs.update(
                    {"class": "form-control location-input", "data-step": "location"}
                )
        elif self.current_step == "services":
            if field_name == "services":
                field.widget.attrs.update(
                    {"class": "services-json-input", "data-step": "services"}
                )
        elif self.current_step == "gallery":
            if field_name == "images":
                field.widget.attrs.update(
                    {"class": "gallery-json-input", "data-step": "gallery"}
                )
        elif self.current_step == "details":
            if field_name in [
                "cancellation_policy",
                "booking_policy",
                "special_instructions",
            ]:
                field.widget.attrs.update(
                    {
                        "class": "form-control policy-input",
                        "data-step": "details",
                        "rows": 4,
                    }
                )


# ===== VENUE EDIT WIZARD FORM CLASSES =====


class VenueEditBasicInfoForm(VenueEditWizardMixin, forms.Form):
    """Step 1: Basic Information - Name, description, and categories"""

    venue_name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your venue name"),
                "data-character-count": "true",
                "maxlength": "255",
            }
        ),
        help_text=_("The name of your venue or business"),
    )

    short_description = forms.CharField(
        max_length=500,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Describe your venue and what makes it special"),
                "data-character-count": "true",
                "maxlength": "500",
            }
        ),
        help_text=_("Brief description of your venue (max 500 characters)"),
    )

    categories = forms.ModelMultipleChoiceField(
        queryset=None,  # Will be set in __init__
        widget=forms.CheckboxSelectMultiple(attrs={"class": "form-check-input"}),
        required=True,
        help_text=_("Select up to 3 categories that best describe your venue"),
        error_messages={
            "required": _("Please select at least one category for your venue"),
            "invalid_choice": _("Please select valid categories"),
        },
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from ..models import Category

        self.fields["categories"].queryset = Category.objects.filter(
            is_active=True
        ).order_by("category_name")

        # Pre-populate with venue data if available
        if self.venue:
            self.initial.update(
                {
                    "venue_name": self.venue.venue_name,
                    "short_description": self.venue.short_description,
                    "categories": self.venue.categories.all(),
                }
            )

        # If data is provided and contains serialized category IDs, convert them back
        if self.data and "categories" in self.data:
            if isinstance(self.data["categories"], list) and all(
                isinstance(x, int) for x in self.data["categories"]
            ):
                # This is serialized data from session, convert IDs back to objects
                from ..models import Category

                category_ids = self.data["categories"]
                self.initial["categories"] = Category.objects.filter(
                    id__in=category_ids
                )


class VenueEditLocationForm(VenueEditWizardMixin, forms.Form):
    """Step 2: Location & Address - Complete address details"""

    state = forms.ChoiceField(
        choices=[],  # Will be populated in __init__
        widget=forms.Select(attrs={"class": "form-control"}),
        help_text=_("State where your venue is located"),
    )

    county = forms.CharField(
        max_length=100,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Enter county name")}
        ),
        help_text=_("County where your venue is located"),
    )

    city = forms.CharField(
        max_length=100,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Enter city name")}
        ),
        help_text=_("City where your venue is located"),
    )

    street_number = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("123")}
        ),
        help_text=_("Street number (optional)"),
    )

    street_name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("Main Street")}
        ),
        help_text=_("Street name"),
    )

    zip_code = forms.CharField(
        max_length=10,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("12345")}
        ),
        help_text=_("ZIP/Postal code"),
    )

    latitude = forms.DecimalField(
        max_digits=10, decimal_places=8, required=False, widget=forms.HiddenInput()
    )

    longitude = forms.DecimalField(
        max_digits=11, decimal_places=8, required=False, widget=forms.HiddenInput()
    )

    # Contact Information
    phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": _("(*************")}
        ),
        help_text=_("Contact phone number (optional)"),
    )

    email = forms.EmailField(
        required=False,
        widget=forms.EmailInput(
            attrs={"class": "form-control", "placeholder": _("<EMAIL>")}
        ),
        help_text=_("Contact email address (optional)"),
    )

    website_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://www.yourvenue.com"),
            }
        ),
        help_text=_("Your venue website URL (optional)"),
    )

    # Social Media Links
    instagram_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://instagram.com/yourvenue"),
            }
        ),
        help_text=_("Instagram profile URL (optional)"),
    )

    facebook_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://facebook.com/yourvenue"),
            }
        ),
        help_text=_("Facebook page URL (optional)"),
    )

    twitter_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://twitter.com/yourvenue"),
            }
        ),
        help_text=_("Twitter profile URL (optional)"),
    )

    linkedin_url = forms.URLField(
        required=False,
        widget=forms.URLInput(
            attrs={
                "class": "form-control",
                "placeholder": _("https://linkedin.com/company/yourvenue"),
            }
        ),
        help_text=_("LinkedIn page URL (optional)"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from ..models import Venue

        self.fields["state"].choices = [("", "Select state")] + list(
            Venue.STATE_CHOICES
        )

        # Pre-populate with venue data if available
        if self.venue:
            self.initial.update(
                {
                    "state": getattr(self.venue, "state", ""),
                    "county": getattr(self.venue, "county", ""),
                    "city": getattr(self.venue, "city", ""),
                    "street_number": getattr(self.venue, "street_number", ""),
                    "street_name": getattr(self.venue, "street_name", ""),
                    # Note: zip_code field is not available in the main Venue model
                    "zip_code": "",  # Will be empty for existing venues
                    "latitude": getattr(self.venue, "latitude", ""),
                    "longitude": getattr(self.venue, "longitude", ""),
                    "phone": getattr(self.venue, "phone", ""),
                    "email": getattr(self.venue, "email", ""),
                    "website_url": getattr(self.venue, "website_url", ""),
                    "instagram_url": getattr(self.venue, "instagram_url", ""),
                    "facebook_url": getattr(self.venue, "facebook_url", ""),
                    "twitter_url": getattr(self.venue, "twitter_url", ""),
                    "linkedin_url": getattr(self.venue, "linkedin_url", ""),
                }
            )


class VenueEditServicesForm(VenueEditWizardMixin, forms.Form):
    """Step 3: Services & Pricing - Services with pricing and discounts"""

    # Services will be handled with JavaScript and stored as JSON
    services = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Services offered with pricing information"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only set current_step if not already set by mixin
        if not hasattr(self, "current_step") or self.current_step != "services":
            self.current_step = "services"

        # Pre-populate with venue services if available
        if self.venue:
            # Convert venue services to JSON format for the form
            services_data = []
            for service in self.venue.services.all():
                services_data.append(
                    {
                        "id": service.id,
                        "title": service.service_title,
                        "description": service.short_description,
                        "price": str(service.price_min),
                        "duration": service.duration_minutes,
                        "category": (
                            service.service_category.name
                            if service.service_category
                            else ""
                        ),
                        "is_featured": service.is_featured,
                        "is_active": service.is_active,
                    }
                )
            self.initial["services"] = services_data


class VenueEditGalleryForm(VenueEditWizardMixin, forms.Form):
    """Step 4: Gallery & Images - Images and main image"""

    # Images will be handled with JavaScript and stored as JSON
    images = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Gallery images with ordering information"),
    )

    # Main image upload
    main_image = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={"class": "form-control", "accept": "image/*"}),
        help_text=_("Main featured image for your venue (optional)"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only set current_step if not already set by mixin
        if not hasattr(self, "current_step") or self.current_step != "gallery":
            self.current_step = "gallery"

        # Pre-populate with venue images if available
        if self.venue:
            # Convert venue images to JSON format for the form
            images_data = []
            for image in self.venue.images.filter(is_active=True).order_by(
                "-is_primary", "order"
            ):
                images_data.append(
                    {
                        "id": image.id,
                        "url": image.image.url if image.image else "",
                        "is_primary": image.is_primary,
                        "order": image.order,
                        "caption": image.caption or "",
                    }
                )
            self.initial["images"] = images_data


class VenueEditDetailsForm(VenueEditWizardMixin, forms.Form):
    """Step 5: Details & Policies - Operating hours, amenities, FAQs, and policies"""

    # Operating hours will be handled with JavaScript and stored as JSON
    operating_hours = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Operating hours data (handled by JavaScript)"),
    )

    # Amenities selection
    amenities = forms.MultipleChoiceField(
        choices=[],  # Will be populated in __init__
        required=False,
        widget=forms.CheckboxSelectMultiple(),
        help_text=_("Select all amenities available at your venue"),
    )

    # FAQs will be handled with JavaScript and stored as JSON
    faqs = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("FAQs data (handled by JavaScript)"),
    )

    # Policies
    cancellation_policy = forms.CharField(
        required=False,
        max_length=1000,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Describe your cancellation policy..."),
            }
        ),
        help_text=_("Your venue cancellation policy (optional)"),
    )

    booking_policy = forms.CharField(
        required=False,
        max_length=1000,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Describe your booking policy..."),
            }
        ),
        help_text=_("Your venue booking policy (optional)"),
    )

    special_instructions = forms.CharField(
        required=False,
        max_length=1000,
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 4,
                "placeholder": _("Any special instructions for customers..."),
            }
        ),
        help_text=_("Special instructions for customers (optional)"),
    )

    # Venue status selection
    VENUE_STATUS_CHOICES = [
        ("draft", _("Save as Draft")),
        ("pending", _("Submit for Admin Approval")),
    ]

    venue_status = forms.ChoiceField(
        choices=VENUE_STATUS_CHOICES,
        widget=forms.RadioSelect(attrs={"class": "form-check-input"}),
        initial="draft",
        help_text=_("Choose whether to save as draft or submit for approval"),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from ..models import VenueAmenity

        self.fields["amenities"].choices = VenueAmenity.AMENITY_CHOICES
        # Only set current_step if not already set by mixin
        if not hasattr(self, "current_step") or self.current_step != "details":
            self.current_step = "details"

        # Pre-populate with venue data if available
        if self.venue:
            # Set initial venue status based on current approval status
            if self.venue.approval_status == Venue.PENDING:
                self.initial["venue_status"] = "pending"
            else:
                self.initial["venue_status"] = "draft"

            # Set amenities
            venue_amenities = [
                amenity.amenity_type for amenity in self.venue.amenities.all()
            ]
            self.initial["amenities"] = venue_amenities

            # If data is provided and contains serialized amenities, use that instead
            if self.data and "amenities" in self.data:
                if isinstance(self.data["amenities"], list):
                    self.initial["amenities"] = self.data["amenities"]

            # Note: Policy fields are not available in the main Venue model
            # They are only available during venue creation via VenueCreationDraft
            # These fields will be empty for existing venues
            self.initial.update(
                {
                    "cancellation_policy": "",
                    "booking_policy": "",
                    "special_instructions": "",
                }
            )

            # Convert operating hours to JSON format
            # Use the structured OperatingHours model instead of the text field
            operating_hours_data = []
            for hours in self.venue.operating_hours_set.all().order_by("day"):
                operating_hours_data.append(
                    {
                        "day": hours.day,
                        "open": (
                            hours.opening.strftime("%H:%M") if hours.opening else ""
                        ),
                        "close": (
                            hours.closing.strftime("%H:%M") if hours.closing else ""
                        ),
                        "is_closed": hours.is_closed,
                        "is_24_hours": hours.is_24_hours,
                        "is_overnight": hours.is_overnight,
                    }
                )
            self.initial["operating_hours"] = operating_hours_data

            # Convert FAQs to JSON format
            faqs_data = []
            for faq in self.venue.faqs.all():
                faqs_data.append(
                    {
                        "id": faq.id,
                        "question": faq.question,
                        "answer": faq.answer,
                        "order": faq.order,
                    }
                )
            self.initial["faqs"] = faqs_data
