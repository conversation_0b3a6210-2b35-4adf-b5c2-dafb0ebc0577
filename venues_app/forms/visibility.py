"""Forms for managing venue visibility settings."""

from django import forms
from django.utils.translation import gettext_lazy as _

from ..models import Venue


class VenueVisibilityForm(forms.ModelForm):
    """Form for managing what information is visible to customers on venue profile."""

    class Meta:
        model = Venue
        fields = [
            "show_contact_info",
            "show_operating_hours",
            "show_amenities",
            "show_faqs",
            "show_team_members",
            "show_social_media",
        ]

        widgets = {
            "show_contact_info": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input visibility-toggle",
                    "data-section": "contact",
                }
            ),
            "show_operating_hours": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input visibility-toggle",
                    "data-section": "hours",
                }
            ),
            "show_amenities": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input visibility-toggle",
                    "data-section": "amenities",
                }
            ),
            "show_faqs": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input visibility-toggle",
                    "data-section": "faqs",
                }
            ),
            "show_team_members": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input visibility-toggle",
                    "data-section": "team",
                }
            ),
            "show_social_media": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input visibility-toggle",
                    "data-section": "social",
                }
            ),
        }

        labels = {
            "show_contact_info": _("Contact Information"),
            "show_operating_hours": _("Operating Hours"),
            "show_amenities": _("Amenities & Features"),
            "show_faqs": _("Frequently Asked Questions"),
            "show_team_members": _("Team Members"),
            "show_social_media": _("Social Media Links"),
        }

        help_texts = {
            "show_contact_info": _("Display phone, email, and website to customers"),
            "show_operating_hours": _("Display your business hours and availability"),
            "show_amenities": _("Display venue amenities and special features"),
            "show_faqs": _("Display frequently asked questions section"),
            "show_team_members": _("Display team member profiles and information"),
            "show_social_media": _("Display social media links and handles"),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add CSS classes and enhance field styling
        for field_name, field in self.fields.items():
            field.widget.attrs.update(
                {"data-toggle": "visibility-section", "data-preview": "true"}
            )

    def get_visibility_summary(self):
        """Get a summary of what information will be visible."""
        if not self.is_valid():
            return {}

        visible_sections = []
        hidden_sections = []

        section_mapping = {
            "show_contact_info": "Contact Information",
            "show_operating_hours": "Operating Hours",
            "show_amenities": "Amenities & Features",
            "show_faqs": "FAQs",
            "show_team_members": "Team Members",
            "show_social_media": "Social Media",
        }

        for field_name, section_name in section_mapping.items():
            if self.cleaned_data.get(field_name, False):
                visible_sections.append(section_name)
            else:
                hidden_sections.append(section_name)

        return {
            "visible": visible_sections,
            "hidden": hidden_sections,
            "visibility_count": len(visible_sections),
            "total_sections": len(section_mapping),
        }
