"""Forms for venue service management."""

# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import Service, ServiceCategory, ServiceTag

# --- Utilities ---
try:
    from utils.sanitization import sanitize_html
except ImportError:
    sanitize_html = lambda x: x


class ServiceForm(forms.ModelForm):
    """Form for creating and editing venue services."""

    # Add a non-database field for similar service warnings
    similar_services_override = forms.BooleanField(
        required=False,
        widget=forms.HiddenInput(),
        help_text=_("Override similar services warning"),
    )

    class Meta:
        model = Service
        fields = [
            "service_title",
            "custom_slug",
            "short_description",
            "service_category",
            "price_min",
            "price_max",
            "duration_minutes",
            "has_custom_availability",
            "requires_booking",
            "max_advance_booking_days",
            "min_advance_booking_hours",
            "service_image",
            "tags",
            "is_active",
            "is_featured",
        ]
        widgets = {
            "service_title": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "e.g., Deep Tissue Massage, Swedish Relaxation",
                    "maxlength": 255,
                }
            ),
            "custom_slug": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "custom-url-slug (optional)",
                    "pattern": "[a-z0-9-]+",
                    "maxlength": 50,
                }
            ),
            "short_description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 3,
                    "placeholder": "Describe what this service includes, techniques used, and benefits customers can expect...",
                    "maxlength": 500,
                }
            ),
            "service_category": forms.Select(
                attrs={
                    "class": "form-control",
                }
            ),
            "price_min": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "0.01",
                    "min": "0.01",
                    "placeholder": "0.00",
                }
            ),
            "price_max": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "0.01",
                    "min": "0.01",
                    "placeholder": "0.00 (optional)",
                }
            ),
            "duration_minutes": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "min": "15",
                    "max": "480",
                    "placeholder": "60",
                }
            ),
            "max_advance_booking_days": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "min": "1",
                    "max": "365",
                    "placeholder": "60",
                }
            ),
            "min_advance_booking_hours": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "min": "1",
                    "max": "168",
                    "placeholder": "2",
                }
            ),
            "service_image": forms.FileInput(
                attrs={
                    "class": "form-control",
                    "accept": "image/*",
                }
            ),
            "tags": forms.CheckboxSelectMultiple(
                attrs={
                    "class": "form-check-input",
                }
            ),
            "has_custom_availability": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input",
                    "style": "transform: scale(1.2);",
                }
            ),
            "requires_booking": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input",
                    "style": "transform: scale(1.2);",
                }
            ),
            "is_active": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input",
                    "style": "transform: scale(1.2);",
                }
            ),
            "is_featured": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input",
                    "style": "transform: scale(1.2);",
                }
            ),
        }
        labels = {
            "service_title": _("Service Name"),
            "custom_slug": _("Custom URL Slug"),
            "short_description": _("Service Description"),
            "service_category": _("Service Category"),
            "price_min": _("Minimum Price ($)"),
            "price_max": _("Maximum Price ($)"),
            "duration_minutes": _("Duration (minutes)"),
            "has_custom_availability": _("Custom Availability"),
            "requires_booking": _("Requires Booking"),
            "max_advance_booking_days": _("Max Advance Booking"),
            "min_advance_booking_hours": _("Min Advance Booking"),
            "service_image": _("Service Image"),
            "tags": _("Service Tags"),
            "is_active": _("Active"),
            "is_featured": _("Featured Service"),
        }
        help_texts = {
            "service_title": _(
                "Enter a clear, descriptive name that differentiates this service from others"
            ),
            "custom_slug": _(
                "Optional custom URL slug. Use lowercase letters, numbers, and hyphens only (3-50 characters). Leave blank to auto-generate."
            ),
            "short_description": _(
                "Provide detailed information about what the service includes, techniques used, and benefits (minimum 10 characters)"
            ),
            "service_category": _(
                "Choose the category that best describes this service"
            ),
            "price_min": _("Starting price for this service (required)"),
            "price_max": _(
                "Highest price for variable pricing (leave blank for fixed price)"
            ),
            "duration_minutes": _(
                "Typical duration (15-480 minutes). Must fit within your venue operating hours."
            ),
            "has_custom_availability": _(
                "Check if this service has different hours than venue hours"
            ),
            "requires_booking": _("Check if customers must book in advance"),
            "max_advance_booking_days": _(
                "Maximum days customers can book ahead (1-365)"
            ),
            "min_advance_booking_hours": _(
                "Minimum hours customers must book ahead (1-168)"
            ),
            "service_image": _(
                "Optional image for this service (JPEG, PNG, WebP - max 2MB)"
            ),
            "tags": _(
                "Select tags that describe this service for better searchability"
            ),
            "is_active": _("Whether customers can currently book this service"),
            "is_featured": _("Highlight this service as a featured offering"),
        }

    def __init__(self, *args, **kwargs):
        self.venue = kwargs.pop("venue", None)
        self.warnings = []
        super().__init__(*args, **kwargs)

        # Populate service category choices
        self.fields["service_category"].queryset = ServiceCategory.objects.filter(
            is_active=True
        ).order_by("sort_order", "name")
        self.fields["service_category"].empty_label = "Select a category (optional)"

        # Populate service tags choices
        self.fields["tags"].queryset = ServiceTag.objects.filter(
            is_active=True
        ).order_by("tag_type", "name")

        # Set default values for new services
        if not self.instance.pk:
            self.fields["duration_minutes"].initial = 60
            self.fields["max_advance_booking_days"].initial = 60
            self.fields["min_advance_booking_hours"].initial = 2
            self.fields["requires_booking"].initial = True
            self.fields["is_active"].initial = True

        # Make service_category required for better organization
        self.fields["service_category"].required = True
        self.fields["service_category"].empty_label = "Select a category"

    def clean_service_title(self):
        """Enhanced service title validation."""
        title = self.cleaned_data.get("service_title")
        if title:
            title = title.strip()
            if len(title) < 3:
                raise ValidationError(
                    _("Service title must be at least 3 characters long.")
                )
            if len(title) > 255:
                raise ValidationError(_("Service title cannot exceed 255 characters."))

            # Check for basic formatting issues
            if title.lower() == title and title.count(" ") > 0:
                # If all lowercase with spaces, suggest proper capitalization
                self.warnings.append(
                    _(
                        'Consider using proper capitalization for your service title (e.g., "Deep Tissue Massage").'
                    )
                )

        return title

    def clean_custom_slug(self):
        """Validate custom slug format."""
        slug = self.cleaned_data.get("custom_slug")
        if slug:
            slug = slug.lower().strip()

            # Basic format validation
            import re

            if not re.match(r"^[a-z0-9-]+$", slug):
                raise ValidationError(
                    _(
                        "Custom slug can only contain lowercase letters, numbers, and hyphens."
                    )
                )

            if len(slug) < 3:
                raise ValidationError(
                    _("Custom slug must be at least 3 characters long.")
                )

            if len(slug) > 50:
                raise ValidationError(_("Custom slug cannot exceed 50 characters."))

            if slug.startswith("-") or slug.endswith("-"):
                raise ValidationError(
                    _("Custom slug cannot start or end with a hyphen.")
                )

            if "--" in slug:
                raise ValidationError(
                    _("Custom slug cannot contain consecutive hyphens.")
                )

            # Reserved words check
            reserved_words = [
                "admin",
                "api",
                "www",
                "create",
                "edit",
                "delete",
                "new",
                "service",
                "booking",
            ]
            if slug in reserved_words:
                raise ValidationError(
                    _("This slug is reserved. Please choose a different one.")
                )

        return slug

    def clean_short_description(self):
        """Enhanced description validation."""
        description = self.cleaned_data.get("short_description")
        if description:
            description = sanitize_html(description.strip())
            if len(description) < 10:
                raise ValidationError(
                    _("Service description must be at least 10 characters long.")
                )

            # Check for placeholder text or very generic descriptions
            generic_phrases = [
                "lorem ipsum",
                "placeholder",
                "description here",
                "coming soon",
            ]
            if any(phrase in description.lower() for phrase in generic_phrases):
                raise ValidationError(
                    _(
                        "Please provide a specific description of your service instead of placeholder text."
                    )
                )

        return description

    def clean_duration_minutes(self):
        """Enhanced duration validation."""
        duration = self.cleaned_data.get("duration_minutes")
        if duration:
            if duration < 15:
                raise ValidationError(
                    _("Service duration must be at least 15 minutes.")
                )
            if duration > 480:  # 8 hours
                raise ValidationError(
                    _("Service duration cannot exceed 8 hours (480 minutes).")
                )

            # Suggest common durations
            if duration not in [15, 30, 45, 60, 75, 90, 120, 150, 180, 240]:
                common_durations = [30, 60, 90, 120]
                closest = min(common_durations, key=lambda x: abs(x - duration))
                self.warnings.append(
                    _(
                        "Consider using a standard duration like {} minutes for easier scheduling."
                    ).format(closest)
                )

        return duration

    def clean(self):
        """Enhanced form validation with business rule checks."""
        cleaned_data = super().clean()

        # Store venue for model validation
        if self.venue:
            # Temporarily set venue for validation
            if not self.instance.pk:
                self.instance.venue = self.venue

        # Price validation
        price_min = cleaned_data.get("price_min")
        price_max = cleaned_data.get("price_max")

        if price_min and price_max:
            if price_max < price_min:
                raise ValidationError(
                    _("Maximum price cannot be less than minimum price.")
                )

            # Warn about large price ranges
            if price_max > price_min * 2:
                self.warnings.append(
                    _(
                        "Large price range detected. Consider offering this as separate service tiers."
                    )
                )

        # Duration vs. booking time validation
        duration_minutes = cleaned_data.get("duration_minutes")
        min_advance_hours = cleaned_data.get("min_advance_booking_hours")

        if duration_minutes and min_advance_hours:
            if (
                min_advance_hours * 60 > duration_minutes * 10
            ):  # If booking time is much longer than service
                self.warnings.append(
                    _(
                        "Minimum booking time seems long relative to service duration. Consider reducing it for better customer experience."
                    )
                )

        # Service overlap validation (as warning, not error)
        service_title = cleaned_data.get("service_title")
        if (
            service_title
            and self.venue
            and not cleaned_data.get("similar_services_override")
        ):
            # Create a temporary instance for similarity checking
            temp_service = Service(
                venue=self.venue,
                service_title=service_title,
                pk=self.instance.pk if self.instance else None,
            )

            similar_warning = temp_service.get_similar_services_warning()
            if similar_warning["has_similar"]:
                # Add warning instead of raising validation error
                similar_names = [s["title"] for s in similar_warning["services"]]
                self.warnings.append(
                    _(
                        "Similar services found: {}. Consider using more specific names to differentiate your offerings."
                    ).format(", ".join(similar_names))
                )

        # Category-specific validation
        service_category = cleaned_data.get("service_category")
        if service_category and duration_minutes:
            # Suggest appropriate durations based on category
            category_suggestions = {
                "massage": [60, 90, 120],
                "facial": [30, 45, 60, 90],
                "hair": [30, 45, 60, 120, 180],
                "nail": [30, 45, 60],
                "wellness": [45, 60, 90],
            }

            category_name = service_category.name.lower()
            for key, suggested_durations in category_suggestions.items():
                if key in category_name:
                    if duration_minutes not in suggested_durations:
                        closest = min(
                            suggested_durations, key=lambda x: abs(x - duration_minutes)
                        )
                        self.warnings.append(
                            _(
                                "For {} services, {} minutes is more common. Consider {} minutes instead."
                            ).format(service_category.name, closest, closest)
                        )
                    break

        return cleaned_data

    def get_warnings(self):
        """Return any warnings generated during validation."""
        return getattr(self, "warnings", [])

    def save(self, commit=True):
        """Enhanced save with venue assignment and defaults."""
        service = super().save(commit=False)

        # Assign venue if not already set
        if self.venue and not service.venue_id:
            service.venue = self.venue

        # Set defaults if not provided
        if not service.max_advance_booking_days:
            service.max_advance_booking_days = 60
        if not service.min_advance_booking_hours:
            service.min_advance_booking_hours = 2

        if commit:
            service.save()
        return service


class ServiceCategoryForm(forms.ModelForm):
    """Form for creating and editing service categories."""

    class Meta:
        model = ServiceCategory
        fields = [
            "name",
            "description",
            "icon_class",
            "color_code",
            "is_active",
            "sort_order",
        ]
        widgets = {
            "name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Category name",
                }
            ),
            "description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 3,
                    "placeholder": "Brief description of the category",
                }
            ),
            "icon_class": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "fas fa-spa",
                }
            ),
            "color_code": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "type": "color",
                }
            ),
            "sort_order": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "min": "0",
                    "placeholder": "0",
                }
            ),
            "is_active": forms.CheckboxInput(
                attrs={
                    "class": "form-check-input",
                    "style": "transform: scale(1.2);",
                }
            ),
        }
        labels = {
            "name": _("Category Name"),
            "description": _("Description"),
            "icon_class": _("Icon Class"),
            "color_code": _("Color"),
            "sort_order": _("Sort Order"),
            "is_active": _("Active"),
        }
        help_texts = {
            "name": _("Enter a clear name for this service category"),
            "description": _("Describe what types of services belong in this category"),
            "icon_class": _("CSS class for icon (e.g., fas fa-spa, fas fa-cut)"),
            "color_code": _("Color to represent this category"),
            "sort_order": _("Lower numbers appear first (0 = first)"),
            "is_active": _("Whether this category is available for selection"),
        }

    def clean_name(self):
        """Clean and validate category name."""
        name = self.cleaned_data.get("name")
        if name:
            name = name.strip()
            if len(name) < 2:
                raise ValidationError(
                    _("Category name must be at least 2 characters long.")
                )
        return name

    def clean_color_code(self):
        """Validate color code format."""
        color_code = self.cleaned_data.get("color_code")
        if color_code:
            if not color_code.startswith("#") or len(color_code) != 7:
                raise ValidationError(
                    _("Color code must be in hex format (e.g., #FF5722).")
                )
        return color_code


class ServiceBulkUpdateForm(forms.Form):
    """Form for bulk updating multiple services."""

    ACTION_CHOICES = [
        ("", _("Select an action")),
        ("activate", _("Activate selected services")),
        ("deactivate", _("Deactivate selected services")),
        ("feature", _("Mark as featured")),
        ("unfeature", _("Remove from featured")),
        ("update_category", _("Update category")),
        ("update_booking_settings", _("Update booking settings")),
    ]

    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={"class": "form-control"}),
        label=_("Action"),
    )

    # Optional fields for specific actions
    new_category = forms.ModelChoiceField(
        queryset=ServiceCategory.objects.filter(is_active=True),
        required=False,
        empty_label=_("Select category"),
        widget=forms.Select(attrs={"class": "form-control"}),
        label=_("New Category"),
    )

    requires_booking = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
        label=_("Requires Booking"),
    )

    max_advance_booking_days = forms.IntegerField(
        required=False,
        min_value=1,
        max_value=365,
        widget=forms.NumberInput(attrs={"class": "form-control"}),
        label=_("Max Advance Booking Days"),
    )

    def clean(self):
        """Validate action-specific fields."""
        cleaned_data = super().clean()
        action = cleaned_data.get("action")

        if action == "update_category" and not cleaned_data.get("new_category"):
            raise ValidationError(_("Category is required when updating category."))

        if action == "update_booking_settings":
            if "requires_booking" not in cleaned_data and not cleaned_data.get(
                "max_advance_booking_days"
            ):
                raise ValidationError(
                    _("At least one booking setting must be specified.")
                )

        return cleaned_data
