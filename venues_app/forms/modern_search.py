# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError

# --- Third-party Imports ---
from cities_light.models import City, Region
from dal import autocomplete
from taggit.models import Tag

# --- Local App Imports ---
from ..models import Venue, Service, ModernLocation


class ModernVenueSearchForm(forms.Form):
    """
    Modern venue search form with autocomplete and advanced filtering.
    """
    
    # Main search field
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-lg',
            'placeholder': 'Search venues, services, or locations...',
            'autocomplete': 'off',
            'data-search-suggestions': 'true'
        }),
        label='Search'
    )
    
    # Location fields with autocomplete
    city = forms.ModelChoiceField(
        queryset=City.objects.all(),
        required=False,
        empty_label="Any City",
        widget=autocomplete.ModelSelect2(
            url='venues:autocomplete-cities',
            attrs={
                'class': 'form-control',
                'data-placeholder': 'Select a city...'
            }
        ),
        label='City'
    )
    
    region = forms.ModelChoiceField(
        queryset=Region.objects.all(),
        required=False,
        empty_label="Any State/Region",
        widget=autocomplete.ModelSelect2(
            url='venues:autocomplete-regions',
            attrs={
                'class': 'form-control',
                'data-placeholder': 'Select a state/region...'
            }
        ),
        label='State/Region'
    )
    
    # Legacy location support
    legacy_location = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'City, State (legacy search)',
            'data-autocomplete': 'legacy-locations'
        }),
        label='Location (Legacy)'
    )
    
    # Note: Venue model doesn't have venue_type field
    # venue_type = forms.ChoiceField(
    #     choices=[('', 'Any Type')] + list(Venue.VENUE_TYPE_CHOICES),
    #     required=False,
    #     widget=forms.Select(attrs={
    #         'class': 'form-control'
    #     }),
    #     label='Venue Type'
    # )
    
    # Capacity range
    min_capacity = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Min capacity'
        }),
        label='Minimum Capacity'
    )
    
    max_capacity = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Max capacity'
        }),
        label='Maximum Capacity'
    )
    
    # Price range
    min_price = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Min price ($)'
        }),
        label='Minimum Price'
    )
    
    max_price = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Max price ($)'
        }),
        label='Maximum Price'
    )
    
    # Rating filter
    min_rating = forms.DecimalField(
        required=False,
        min_value=1,
        max_value=5,
        decimal_places=1,
        widget=forms.Select(
            choices=[
                ('', 'Any Rating'),
                ('4.5', '4.5+ Stars'),
                ('4.0', '4.0+ Stars'),
                ('3.5', '3.5+ Stars'),
                ('3.0', '3.0+ Stars'),
            ],
            attrs={'class': 'form-control'}
        ),
        label='Minimum Rating'
    )
    
    # Tags with autocomplete
    tags = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter tags separated by commas',
            'data-autocomplete': 'tags'
        }),
        label='Tags',
        help_text='Enter tags separated by commas'
    )
    
    # Sort options
    sort_by = forms.ChoiceField(
        choices=[
            ('relevance', 'Relevance'),
            ('name', 'Name A-Z'),
            ('-name', 'Name Z-A'),
            ('city', 'City A-Z'),
            ('-created_at', 'Newest First'),
            ('created_at', 'Oldest First'),
            ('-avg_rating', 'Highest Rated'),
            ('min_price', 'Lowest Price'),
            ('-max_capacity', 'Largest Capacity'),
        ],
        required=False,
        initial='relevance',
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='Sort By'
    )
    
    def clean(self):
        """Validate form data."""
        cleaned_data = super().clean()
        
        # Validate capacity range
        min_capacity = cleaned_data.get('min_capacity')
        max_capacity = cleaned_data.get('max_capacity')
        
        if min_capacity and max_capacity and min_capacity > max_capacity:
            raise ValidationError(
                "Minimum capacity cannot be greater than maximum capacity."
            )
        
        # Validate price range
        min_price = cleaned_data.get('min_price')
        max_price = cleaned_data.get('max_price')
        
        if min_price and max_price and min_price > max_price:
            raise ValidationError(
                "Minimum price cannot be greater than maximum price."
            )
        
        return cleaned_data


class ModernServiceSearchForm(forms.Form):
    """
    Modern service search form.
    """
    
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-lg',
            'placeholder': 'Search services...',
            'autocomplete': 'off'
        }),
        label='Search Services'
    )
    
    # Note: Service model doesn't have SERVICE_TYPE_CHOICES
    # service_type = forms.ChoiceField(
    #     choices=[('', 'Any Service Type')] + list(Service.SERVICE_TYPE_CHOICES),
    #     required=False,
    #     widget=forms.Select(attrs={
    #         'class': 'form-control'
    #     }),
    #     label='Service Type'
    # )
    
    min_price = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Min price ($)'
        }),
        label='Minimum Price'
    )
    
    max_price = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Max price ($)'
        }),
        label='Maximum Price'
    )
    
    venue_city = forms.ModelChoiceField(
        queryset=City.objects.all(),
        required=False,
        empty_label="Any City",
        widget=autocomplete.ModelSelect2(
            url='venues:autocomplete-cities',
            attrs={
                'class': 'form-control',
                'data-placeholder': 'Select a city...'
            }
        ),
        label='Venue City'
    )
    
    venue_region = forms.ModelChoiceField(
        queryset=Region.objects.all(),
        required=False,
        empty_label="Any State/Region",
        widget=autocomplete.ModelSelect2(
            url='venues:autocomplete-regions',
            attrs={
                'class': 'form-control',
                'data-placeholder': 'Select a state/region...'
            }
        ),
        label='Venue State/Region'
    )
    
    sort_by = forms.ChoiceField(
        choices=[
            ('relevance', 'Relevance'),
            ('service_title', 'Title A-Z'),
            ('-service_title', 'Title Z-A'),
            ('price_min', 'Lowest Price'),
            ('-price_max', 'Highest Price'),
            ('-created_at', 'Newest First'),
            ('venue__venue_name', 'Venue Name A-Z'),
        ],
        required=False,
        initial='relevance',
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='Sort By'
    )
    
    def clean(self):
        """Validate service search form."""
        cleaned_data = super().clean()
        
        min_price = cleaned_data.get('min_price')
        max_price = cleaned_data.get('max_price')
        
        if min_price and max_price and min_price > max_price:
            raise ValidationError(
                "Minimum price cannot be greater than maximum price."
            )
        
        return cleaned_data


class QuickSearchForm(forms.Form):
    """
    Simple quick search form for header/navbar.
    """
    
    q = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search venues and services...',
            'autocomplete': 'off',
            'data-search-suggestions': 'true'
        }),
        label=''
    )
    
    location = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Location',
            'data-autocomplete': 'locations'
        }),
        label=''
    )
