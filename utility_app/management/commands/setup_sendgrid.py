"""
Management command to help set up SendGrid email configuration.
"""

import os
from pathlib import Path

from django.conf import settings
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    """Help set up SendGrid email configuration."""

    help = "Set up SendGrid email configuration for CozyWish"

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument("--api-key", type=str, help="Your SendGrid API key")
        parser.add_argument(
            "--test-email",
            type=str,
            help="Email address to send test email to after setup",
        )
        parser.add_argument(
            "--force-production",
            action="store_true",
            help="Force email sending even in development mode",
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(self.style.SUCCESS("🔧 SendGrid Email Setup for CozyWish"))
        self.stdout.write("=" * 50)

        # Check current configuration
        self.check_current_config()

        # Get API key
        api_key = options.get("api_key")
        if not api_key:
            self.stdout.write("\n📝 To complete setup, you need a SendGrid API key.")
            self.stdout.write("   Visit: https://app.sendgrid.com/settings/api_keys")
            self.stdout.write('   Create a new API key with "Mail Send" permissions.')
            return

        # Update .env file
        self.update_env_file(api_key, options.get("force_production", False))

        # Test email if requested
        test_email = options.get("test_email")
        if test_email:
            self.test_email_setup(test_email)

    def check_current_config(self):
        """Check and display current email configuration."""
        self.stdout.write("\n📋 Current Email Configuration:")
        self.stdout.write(f"   Backend: {settings.EMAIL_BACKEND}")
        self.stdout.write(f"   Host: {settings.EMAIL_HOST}")
        self.stdout.write(f"   Port: {settings.EMAIL_PORT}")
        self.stdout.write(f"   TLS: {settings.EMAIL_USE_TLS}")
        self.stdout.write(f"   From Email: {settings.DEFAULT_FROM_EMAIL}")

        if hasattr(settings, "EMAIL_HOST_PASSWORD") and settings.EMAIL_HOST_PASSWORD:
            self.stdout.write("   API Key: ✅ Configured")
        else:
            self.stdout.write("   API Key: ❌ Not configured")

    def update_env_file(self, api_key, force_production):
        """Update or create .env file with SendGrid configuration."""
        env_path = Path(".env")

        # Read existing .env or create new one
        env_content = []
        if env_path.exists():
            with open(env_path, "r") as f:
                env_content = f.readlines()

        # Update or add email configuration
        email_vars = {
            "EMAIL_HOST_PASSWORD": api_key,
            "EMAIL_HOST": "smtp.sendgrid.net",
            "EMAIL_HOST_USER": "apikey",
            "EMAIL_PORT": "587",
            "EMAIL_USE_TLS": "True",
            "DEFAULT_FROM_EMAIL": "<EMAIL>",
            "SERVER_EMAIL": "<EMAIL>",
        }

        if force_production:
            email_vars["FORCE_EMAIL_BACKEND"] = "True"

        # Update existing lines or add new ones
        updated_vars = set()
        for i, line in enumerate(env_content):
            if "=" in line and not line.strip().startswith("#"):
                var_name = line.split("=")[0].strip()
                if var_name in email_vars:
                    env_content[i] = f"{var_name}={email_vars[var_name]}\n"
                    updated_vars.add(var_name)

        # Add new variables that weren't updated
        for var_name, value in email_vars.items():
            if var_name not in updated_vars:
                env_content.append(f"{var_name}={value}\n")

        # Write updated .env file
        with open(env_path, "w") as f:
            f.writelines(env_content)

        self.stdout.write(
            self.style.SUCCESS(f"\n✅ Updated .env file with SendGrid configuration")
        )
        self.stdout.write(
            "   You may need to restart the Django server for changes to take effect."
        )

    def test_email_setup(self, test_email):
        """Test the email setup by sending a test email."""
        self.stdout.write(f"\n📧 Testing email setup by sending to: {test_email}")

        try:
            from django.core.management import call_command

            call_command("send_test_email", test_email)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test email failed: {str(e)}"))
