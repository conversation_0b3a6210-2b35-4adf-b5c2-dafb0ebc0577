"""
Management command to test SendGrid and AWS S3 configurations.
"""

import os
import tempfile
from pathlib import Path

import boto3
from botocore.exceptions import ClientError
from decouple import Config, RepositoryEnv, config

from django.conf import settings
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.core.mail import send_mail
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    """Test SendGrid email and AWS S3 file storage configurations."""

    help = "Test SendGrid and AWS S3 configurations"

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            "--email", type=str, help="Email address to send test email to"
        )
        parser.add_argument(
            "--skip-email", action="store_true", help="Skip email testing"
        )
        parser.add_argument("--skip-s3", action="store_true", help="Skip S3 testing")

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(self.style.SUCCESS("Testing CozyWish Service Configurations"))
        self.stdout.write("=" * 50)

        # Test SendGrid Email
        if not options["skip_email"]:
            self.test_sendgrid_email(options.get("email"))

        # Test AWS S3
        if not options["skip_s3"]:
            self.test_aws_s3()

        self.stdout.write(
            self.style.SUCCESS("\nService configuration testing completed!")
        )

    def test_sendgrid_email(self, test_email=None):
        """Test SendGrid email configuration."""
        self.stdout.write("\n1. Testing SendGrid Email Configuration...")

        try:
            # Check if SendGrid is configured
            if not settings.EMAIL_HOST_PASSWORD:
                self.stdout.write(
                    self.style.WARNING("   ⚠️  SendGrid API key not configured")
                )
                return

            # Use provided email or default
            recipient_email = test_email or "<EMAIL>"

            # Send test email
            subject = "CozyWish - SendGrid Test Email"
            message = """
            Hello from CozyWish!

            This is a test email to verify that SendGrid is properly configured.

            If you receive this email, your SendGrid integration is working correctly.

            Best regards,
            CozyWish Team
            """
            from_email = "<EMAIL>"

            send_mail(
                subject=subject,
                message=message,
                from_email=from_email,
                recipient_list=[recipient_email],
                fail_silently=False,
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f"   ✅ Test email sent successfully to {recipient_email}"
                )
            )
            self.stdout.write(f"   📧 Subject: {subject}")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"   ❌ SendGrid test failed: {str(e)}"))

    def test_aws_s3(self):
        """Test AWS S3 configuration."""
        self.stdout.write("\n2. Testing AWS S3 Configuration...")

        try:
            # Check if S3 is configured via settings
            aws_key = getattr(settings, "AWS_ACCESS_KEY_ID", None)
            aws_secret = getattr(settings, "AWS_SECRET_ACCESS_KEY", None)
            aws_bucket = getattr(settings, "AWS_STORAGE_BUCKET_NAME", None)
            aws_region = getattr(settings, "AWS_S3_REGION_NAME", "us-east-1")

            if not all([aws_key, aws_secret, aws_bucket]):
                self.stdout.write(
                    self.style.WARNING(
                        "   ⚠️  AWS S3 credentials not configured in settings"
                    )
                )
                return

            # Test S3 connection
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=aws_key,
                aws_secret_access_key=aws_secret,
                region_name=aws_region,
            )

            # Check if bucket exists and is accessible
            try:
                s3_client.head_bucket(Bucket=aws_bucket)
                self.stdout.write(
                    self.style.SUCCESS(f'   ✅ S3 bucket "{aws_bucket}" is accessible')
                )
            except ClientError as e:
                error_code = e.response["Error"]["Code"]
                if error_code == "404":
                    self.stdout.write(
                        self.style.ERROR(f'   ❌ S3 bucket "{aws_bucket}" not found')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f"   ❌ S3 bucket access error: {error_code}")
                    )
                return

            # Test file upload using Django's default storage
            test_content = "This is a test file for CozyWish S3 configuration."
            test_file_name = "test/cozywish_s3_test.txt"

            # Upload test file
            file_path = default_storage.save(
                test_file_name, ContentFile(test_content.encode("utf-8"))
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f"   ✅ Test file uploaded successfully: {file_path}"
                )
            )

            # Get file URL
            file_url = default_storage.url(file_path)
            self.stdout.write(f"   🔗 File URL: {file_url}")

            # Test file exists
            if default_storage.exists(file_path):
                self.stdout.write(
                    self.style.SUCCESS("   ✅ File existence check passed")
                )
            else:
                self.stdout.write(self.style.ERROR("   ❌ File existence check failed"))

            # Clean up test file
            default_storage.delete(file_path)
            self.stdout.write(
                self.style.SUCCESS("   ✅ Test file cleaned up successfully")
            )

            # Display S3 configuration summary
            self.stdout.write("\n   📋 S3 Configuration Summary:")
            self.stdout.write(f"      Bucket: {aws_bucket}")
            self.stdout.write(f"      Region: {aws_region}")
            custom_domain = getattr(settings, "AWS_S3_CUSTOM_DOMAIN", None)
            if custom_domain:
                self.stdout.write(f"      Custom Domain: {custom_domain}")
            media_url = getattr(settings, "MEDIA_URL", "/media/")
            self.stdout.write(f"      Media URL: {media_url}")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"   ❌ S3 test failed: {str(e)}"))
