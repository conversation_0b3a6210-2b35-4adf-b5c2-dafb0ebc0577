from decimal import Decimal

from django.utils.deprecation import MiddlewareMixin

from .utils import get_best_discount


class DiscountMiddleware(MiddlewareMixin):
    """Apply best available discount info to services in template responses."""

    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return self.process_template_response(request, response)

    def process_template_response(self, request, response):
        if not hasattr(response, "context_data"):
            return response

        user = getattr(request, "user", None)
        context = response.context_data

        if context is None:
            return response

        service = context.get("service")
        if service:
            self._apply_discount_to_service(service, user)

        services = context.get("services")
        if services:
            if isinstance(services, (list, tuple)):
                for svc in services:
                    self._apply_discount_to_service(svc, user)
            else:
                # Non-iterable service list; just ignore
                pass
        return response

    def _apply_discount_to_service(self, service, user):
        if not hasattr(service, "price_min") and not hasattr(service, "price"):
            return

        best = get_best_discount(service, user)
        if not best:
            setattr(service, "discounted_price", None)
            setattr(service, "discount_info", None)
            return

        discount, discount_amount, final_price = best
        original_price = getattr(service, "price_min", getattr(service, "price", None))
        service.discounted_price = final_price
        service.discount_info = {
            "name": discount.name,
            "type": discount.discount_type,
            "value": discount.discount_value,
            "amount": discount_amount,
            "original_price": (
                Decimal(str(original_price)) if original_price is not None else None
            ),
            "final_price": final_price,
        }
