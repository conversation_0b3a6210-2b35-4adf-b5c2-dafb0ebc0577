from datetime import timedelta
from decimal import Decimal
from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.test import Client, TestCase
from django.urls import reverse
from django.utils import timezone

from discount_app.models import DiscountType, ServiceDiscount
from discount_app.tests.test_views import DiscountViewBaseTest
from notifications_app.models import Notification

User = get_user_model()


class AdminApprovalWorkflowIntegrationTest(DiscountViewBaseTest):
    """Integration tests for admin approval workflow with notifications."""

    def setUp(self):
        super().setUp()
        self.client.login(email="<EMAIL>", password="adminpass123")

    @patch("discount_app.views.admin.send_notification_email")
    def test_reject_discount_sends_notification(self, mock_email):
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name="Reject Me",
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal("10.00"),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False,
        )

        url = reverse(
            "discount_app:admin_approve_discount",
            kwargs={"discount_type": "service", "discount_id": discount.pk},
        )
        response = self.client.post(
            url, {"is_approved": "False", "rejection_reason": "Invalid"}
        )
        self.assertIn(response.status_code, [302, 301])

        discount.refresh_from_db()
        self.assertFalse(discount.is_approved)

        notification = Notification.objects.filter(
            user=self.provider_user, title="Discount Proposal Rejected"
        ).first()
        self.assertIsNotNone(notification)
        mock_email.assert_called_once()

    @patch("discount_app.views.admin.send_notification_email")
    def test_approve_discount_no_notification(self, mock_email):
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name="Approve Me",
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal("15.00"),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False,
        )

        url = reverse(
            "discount_app:admin_approve_discount",
            kwargs={"discount_type": "service", "discount_id": discount.pk},
        )
        response = self.client.post(url, {"is_approved": "True"})
        self.assertIn(response.status_code, [302, 301])

        discount.refresh_from_db()
        self.assertTrue(discount.is_approved)

        notification_exists = Notification.objects.filter(
            user=self.provider_user, title="Discount Proposal Rejected"
        ).exists()
        self.assertFalse(notification_exists)
        mock_email.assert_not_called()
