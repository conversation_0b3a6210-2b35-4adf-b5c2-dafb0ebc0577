"""
Unit tests for discount_app middleware.

This module contains comprehensive unit tests for the DiscountMiddleware class,
testing discount application to services in template responses.
"""

# Standard library imports
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from unittest.mock import Mock, patch

from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.template.response import TemplateResponse

# Django imports
from django.test import RequestFactory, TestCase
from django.utils import timezone

from accounts_app.models import ServiceProviderProfile

# Local imports
from discount_app.middleware import DiscountMiddleware
from discount_app.models import (
    DiscountType,
    PlatformDiscount,
    ServiceDiscount,
    VenueDiscount,
)
from venues_app.models import Category, Service, Venue

User = get_user_model()


class DiscountMiddlewareTest(TestCase):
    """Test the DiscountMiddleware functionality."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        self.middleware = DiscountMiddleware(get_response=Mock())

        # Create users
        self.customer_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )
        self.customer_user.role = User.CUSTOMER
        self.customer_user.save()

        self.provider_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )
        self.provider_user.role = User.SERVICE_PROVIDER
        self.provider_user.save()

        # Create category
        self.category = Category.objects.create(
            category_name="Spa Services",
            category_description="Relaxing spa treatments",
            is_active=True,
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider_user,
            legal_name="Test Spa",
            phone="+**********",
            contact_name="John Doe",
            address="123 Business St",
            city="New York",
            state="NY",
            zip_code="10001",
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa Venue",
            short_description="A relaxing spa venue for test purposes",
            street_number="123",
            street_name="Spa St",
            city="New York",
            state="NY",
            county="New York County",
            phone="+**********",
            approval_status=Venue.APPROVED,
        )

        # Add category to venue through many-to-many relationship
        self.venue.categories.add(self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Massage Therapy",
            short_description="Relaxing massage",
            price_min=Decimal("100.00"),
            price_max=Decimal("200.00"),
            duration_minutes=60,
            is_active=True,
        )

        # Create service discount
        self.service_discount = ServiceDiscount.objects.create(
            service=self.service,
            name="Massage Special",
            description="Special discount for massage therapy",
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal("20.00"),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True,
        )

    def test_middleware_init(self):
        """Test middleware initialization."""
        get_response_mock = Mock()
        middleware = DiscountMiddleware(get_response_mock)
        self.assertEqual(middleware.get_response, get_response_mock)

    def test_middleware_call(self):
        """Test middleware __call__ method."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Mock get_response to return a simple HttpResponse
        self.middleware.get_response = Mock(return_value=HttpResponse("Test response"))

        response = self.middleware(request)

        self.assertIsInstance(response, HttpResponse)
        self.middleware.get_response.assert_called_once_with(request)

    def test_process_template_response_no_context_data(self):
        """Test process_template_response with response that has no context_data."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Create response without context_data
        response = HttpResponse("Test response")

        result = self.middleware.process_template_response(request, response)

        # Should return the response unchanged
        self.assertEqual(result, response)

    def test_process_template_response_with_single_service(self):
        """Test process_template_response with single service in context."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Create template response with service in context
        response = TemplateResponse(
            request, "test_template.html", {"service": self.service}
        )

        result = self.middleware.process_template_response(request, response)

        # Check that discount was applied to service
        self.assertIsNotNone(self.service.discounted_price)
        self.assertEqual(
            self.service.discounted_price, Decimal("80.00")
        )  # 100 - 20% = 80
        self.assertIsNotNone(self.service.discount_info)
        self.assertEqual(self.service.discount_info["name"], "Massage Special")
        self.assertEqual(self.service.discount_info["type"], DiscountType.PERCENTAGE)
        self.assertEqual(self.service.discount_info["value"], Decimal("20.00"))
        self.assertEqual(self.service.discount_info["amount"], Decimal("20.00"))
        self.assertEqual(
            self.service.discount_info["original_price"], Decimal("100.00")
        )
        self.assertEqual(self.service.discount_info["final_price"], Decimal("80.00"))

    def test_process_template_response_with_service_list(self):
        """Test process_template_response with list of services in context."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Create another service without discount
        service2 = Service.objects.create(
            venue=self.venue,
            service_title="Facial Treatment",
            short_description="Relaxing facial",
            price_min=Decimal("80.00"),
            price_max=Decimal("120.00"),
            duration_minutes=45,
            is_active=True,
        )

        # Create template response with services list in context
        response = TemplateResponse(
            request, "test_template.html", {"services": [self.service, service2]}
        )

        result = self.middleware.process_template_response(request, response)

        # Check that discount was applied to first service
        self.assertIsNotNone(self.service.discounted_price)
        self.assertEqual(self.service.discounted_price, Decimal("80.00"))
        self.assertIsNotNone(self.service.discount_info)

        # Check that no discount was applied to second service
        self.assertIsNone(service2.discounted_price)
        self.assertIsNone(service2.discount_info)

    def test_process_template_response_service_without_price(self):
        """Test process_template_response with service that has no price attribute."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Create mock service without price attribute
        mock_service = Mock()
        del mock_service.price  # Remove price attribute

        # Create template response with mock service
        response = TemplateResponse(
            request, "test_template.html", {"service": mock_service}
        )

        result = self.middleware.process_template_response(request, response)

        # Should not raise an error and return response unchanged
        self.assertEqual(result, response)

    def test_apply_discount_to_service_with_discount(self):
        """Test _apply_discount_to_service method with available discount."""
        # Mock get_best_discount to return a discount
        with patch("discount_app.middleware.get_best_discount") as mock_get_best:
            mock_get_best.return_value = (
                self.service_discount,
                Decimal("20.00"),  # discount_amount
                Decimal("80.00"),  # final_price
            )

            self.middleware._apply_discount_to_service(self.service, self.customer_user)

            # Check that discount was applied
            self.assertEqual(self.service.discounted_price, Decimal("80.00"))
            self.assertIsNotNone(self.service.discount_info)
            self.assertEqual(self.service.discount_info["name"], "Massage Special")

            mock_get_best.assert_called_once_with(self.service, self.customer_user)

    def test_apply_discount_to_service_without_discount(self):
        """Test _apply_discount_to_service method with no available discount."""
        # Mock get_best_discount to return None
        with patch("discount_app.middleware.get_best_discount") as mock_get_best:
            mock_get_best.return_value = None

            self.middleware._apply_discount_to_service(self.service, self.customer_user)

            # Check that no discount was applied
            self.assertIsNone(self.service.discounted_price)
            self.assertIsNone(self.service.discount_info)

            mock_get_best.assert_called_once_with(self.service, self.customer_user)

    def test_apply_discount_to_service_anonymous_user(self):
        """Test _apply_discount_to_service method with anonymous user."""
        # Create anonymous user
        from django.contrib.auth.models import AnonymousUser

        anonymous_user = AnonymousUser()

        # Mock get_best_discount to return a discount
        with patch("discount_app.middleware.get_best_discount") as mock_get_best:
            mock_get_best.return_value = (
                self.service_discount,
                Decimal("20.00"),
                Decimal("80.00"),
            )

            self.middleware._apply_discount_to_service(self.service, anonymous_user)

            # Check that discount was applied even for anonymous user
            self.assertEqual(self.service.discounted_price, Decimal("80.00"))
            mock_get_best.assert_called_once_with(self.service, anonymous_user)

    def test_process_template_response_with_venue_discount(self):
        """Test process_template_response with venue discount."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Create venue discount (better than service discount)
        venue_discount = VenueDiscount.objects.create(
            venue=self.venue,
            name="Venue Special",
            description="Special venue discount",
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal("30.00"),  # Better than 20% service discount
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            min_booking_value=Decimal("50.00"),
            created_by=self.provider_user,
            is_approved=True,
        )

        # Create template response with service in context
        response = TemplateResponse(
            request, "test_template.html", {"service": self.service}
        )

        result = self.middleware.process_template_response(request, response)

        # Check that better venue discount was applied
        self.assertEqual(
            self.service.discounted_price, Decimal("70.00")
        )  # 100 - 30% = 70
        self.assertEqual(self.service.discount_info["name"], "Venue Special")
        self.assertEqual(self.service.discount_info["value"], Decimal("30.00"))

    def test_process_template_response_with_platform_discount(self):
        """Test process_template_response with platform discount."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Create platform discount (better than service discount)
        platform_discount = PlatformDiscount.objects.create(
            name="Platform Special",
            description="Special platform discount",
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal("25.00"),  # Better than 20% service discount
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            category=self.category,
            min_booking_value=Decimal("50.00"),
            created_by=self.provider_user,
        )

        # Create template response with service in context
        response = TemplateResponse(
            request, "test_template.html", {"service": self.service}
        )

        result = self.middleware.process_template_response(request, response)

        # Check that better platform discount was applied
        self.assertEqual(
            self.service.discounted_price, Decimal("75.00")
        )  # 100 - 25% = 75
        self.assertEqual(self.service.discount_info["name"], "Platform Special")
        self.assertEqual(self.service.discount_info["value"], Decimal("25.00"))

    def test_process_template_response_with_expired_discount(self):
        """Test process_template_response with expired discount."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Make the service discount expired
        self.service_discount.end_date = timezone.now() - timedelta(hours=1)
        self.service_discount.save()

        # Create template response with service in context
        response = TemplateResponse(
            request, "test_template.html", {"service": self.service}
        )

        result = self.middleware.process_template_response(request, response)

        # Check that no discount was applied (expired)
        self.assertIsNone(self.service.discounted_price)
        self.assertIsNone(self.service.discount_info)

    def test_process_template_response_with_unapproved_discount(self):
        """Test process_template_response with unapproved discount."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Make the service discount unapproved
        self.service_discount.is_approved = False
        self.service_discount.save()

        # Create template response with service in context
        response = TemplateResponse(
            request, "test_template.html", {"service": self.service}
        )

        result = self.middleware.process_template_response(request, response)

        # Check that no discount was applied (unapproved)
        self.assertIsNone(self.service.discounted_price)
        self.assertIsNone(self.service.discount_info)

    def test_middleware_performance_with_many_services(self):
        """Test middleware performance with many services."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Create many services
        services = []
        for i in range(50):
            service = Service.objects.create(
                venue=self.venue,
                service_title=f"Service {i}",
                short_description=f"Service {i} description",
                price_min=Decimal("100.00"),
                price_max=Decimal("200.00"),
                duration_minutes=60,
                is_active=True,
            )
            services.append(service)

        # Create template response with many services
        response = TemplateResponse(
            request, "test_template.html", {"services": services}
        )

        import time

        start_time = time.time()
        result = self.middleware.process_template_response(request, response)
        end_time = time.time()

        # Should complete in reasonable time (less than 1 second)
        self.assertLess(end_time - start_time, 1.0)

    def test_middleware_with_non_iterable_services(self):
        """Test middleware with non-iterable services context."""
        request = self.factory.get("/")
        request.user = self.customer_user

        # Create template response with non-iterable services
        response = TemplateResponse(
            request, "test_template.html", {"services": "not_iterable"}
        )

        # Should not raise an error
        result = self.middleware.process_template_response(request, response)
        self.assertEqual(result, response)
