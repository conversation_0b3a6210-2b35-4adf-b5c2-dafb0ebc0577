"""
Template tags for discount functionality.
Provides easy access to discount information in templates.
"""

from decimal import Decimal

from django import template
from django.utils import timezone

from discount_app.models import PlatformDiscount, ServiceDiscount, VenueDiscount
from discount_app.utils import get_applicable_discounts, get_best_discount

register = template.Library()


@register.simple_tag
def get_service_discounts(service):
    """
    Get all applicable discounts for a service.
    Returns a list of discount objects.
    """
    if not service:
        return []

    applicable_discounts = get_applicable_discounts(service)
    return [discount_tuple[0] for discount_tuple in applicable_discounts]


@register.simple_tag
def get_service_best_discount(service):
    """
    Get the best discount for a service.
    Returns the discount object with the highest savings.
    """
    if not service:
        return None

    best_discount_tuple = get_best_discount(service)
    return best_discount_tuple[0] if best_discount_tuple else None


@register.simple_tag
def get_service_discounted_price(service):
    """
    Get the discounted price for a service.
    Returns the lowest price after applying the best discount.
    """
    if not service:
        return None

    best_discount_tuple = get_best_discount(service)
    return best_discount_tuple[2] if best_discount_tuple else service.price


@register.simple_tag
def get_service_savings(service):
    """
    Get the savings amount for a service.
    Returns the amount saved with the best discount.
    """
    if not service:
        return Decimal("0.00")

    best_discount_tuple = get_best_discount(service)
    if best_discount_tuple:
        return service.price - best_discount_tuple[2]
    return Decimal("0.00")


@register.simple_tag
def has_service_discount(service):
    """
    Check if a service has any applicable discounts.
    Returns True if discounts are available, False otherwise.
    """
    if not service:
        return False

    applicable_discounts = get_applicable_discounts(service)
    return len(applicable_discounts) > 0


@register.filter
def has_service_discount(service):
    """
    Filter version: Check if a service has any applicable discounts.
    Returns True if discounts are available, False otherwise.
    """
    if not service:
        return False

    applicable_discounts = get_applicable_discounts(service)
    return len(applicable_discounts) > 0


@register.filter
def get_service_discounted_price(service):
    """
    Filter version: Get the discounted price for a service.
    Returns the lowest price after applying the best discount.
    """
    if not service:
        return None

    best_discount_tuple = get_best_discount(service)
    return best_discount_tuple[2] if best_discount_tuple else service.price


@register.filter
def get_service_savings(service):
    """
    Filter version: Get the savings amount for a service.
    Returns the amount saved with the best discount.
    """
    if not service:
        return Decimal("0.00")

    best_discount_tuple = get_best_discount(service)
    if best_discount_tuple:
        return service.price - best_discount_tuple[2]
    return Decimal("0.00")


@register.filter
def get_service_best_discount(service):
    """
    Filter version: Get the best discount for a service.
    Returns the discount object with the highest savings.
    """
    if not service:
        return None

    best_discount_tuple = get_best_discount(service)
    return best_discount_tuple[0] if best_discount_tuple else None


@register.simple_tag
def get_venue_discounts(venue):
    """
    Get all active venue-wide discounts for a venue.
    Returns a list of VenueDiscount objects.
    """
    if not venue:
        return []

    now = timezone.now()
    return VenueDiscount.objects.filter(
        venue=venue, start_date__lte=now, end_date__gte=now, is_approved=True
    )


@register.simple_tag
def has_venue_discounts(venue):
    """
    Check if a venue has any active venue-wide discounts.
    Returns True if discounts are available, False otherwise.
    """
    venue_discounts = get_venue_discounts(venue)
    return len(venue_discounts) > 0


@register.filter
def has_venue_discounts(venue):
    """
    Filter version: Check if a venue has any active venue-wide discounts.
    Returns True if discounts are available, False otherwise.
    """
    venue_discounts = get_venue_discounts(venue)
    return len(venue_discounts) > 0


@register.filter
def has_platform_discounts(venue):
    """
    Filter version: Check if there are any active platform-wide discounts.
    Returns True if platform discounts are available, False otherwise.
    """
    now = timezone.now()
    platform_discounts = PlatformDiscount.objects.filter(
        start_date__lte=now, end_date__gte=now
    )
    return platform_discounts.exists()


@register.simple_tag
def get_platform_discounts():
    """
    Get all active platform-wide discounts.
    Returns a list of PlatformDiscount objects.
    """
    now = timezone.now()
    return PlatformDiscount.objects.filter(start_date__lte=now, end_date__gte=now)


@register.simple_tag
def has_platform_discounts():
    """
    Check if there are any active platform-wide discounts.
    Returns True if discounts are available, False otherwise.
    """
    platform_discounts = get_platform_discounts()
    return len(platform_discounts) > 0


@register.simple_tag
def get_discount_badge_class(discount_type):
    """
    Get the appropriate Bootstrap badge class for a discount type.
    Returns the CSS class string.
    """
    badge_classes = {
        "service": "bg-primary",
        "venue": "bg-success",
        "platform": "bg-warning text-dark",
    }
    return badge_classes.get(discount_type, "bg-secondary")


@register.simple_tag
def format_discount_value(discount):
    """
    Format discount value for display.
    Returns a formatted string like "20% off" or "$50 off".
    """
    if not discount:
        return ""

    if discount.discount_type == "percentage":
        return f"{discount.discount_value}% off"
    else:
        return f"${discount.discount_value} off"


@register.filter
def format_discount_value(discount):
    """
    Filter version: Format discount value for display.
    Returns a formatted string like "20% off" or "$50 off".
    """
    if not discount:
        return ""

    if discount.discount_type == "percentage":
        return f"{discount.discount_value}% off"
    else:
        return f"${discount.discount_value} off"


@register.simple_tag
def calculate_discount_percentage(original_price, discounted_price):
    """
    Calculate the percentage discount between original and discounted price.
    Returns the percentage as an integer.
    """
    if not original_price or not discounted_price or original_price <= 0:
        return 0

    savings = original_price - discounted_price
    percentage = (savings / original_price) * 100
    return int(round(percentage))


def _calculate_discount_percentage(original_price, discounted_price):
    """
    Helper function to calculate discount percentage.
    """
    if not original_price or not discounted_price or original_price <= 0:
        return 0

    savings = original_price - discounted_price
    percentage = (savings / original_price) * 100
    return int(round(percentage))


@register.inclusion_tag("discount_app/tags/discount_badge.html")
def discount_badge(service):
    """
    Render a discount badge for a service.
    Shows the best discount available.
    """
    best_discount_tuple = get_best_discount(service) if service else None

    context = {
        "service": service,
        "has_discount": best_discount_tuple is not None,
    }

    if best_discount_tuple:
        discount, discount_amount, final_price = best_discount_tuple
        savings_percentage = _calculate_discount_percentage(service.price, final_price)

        context.update(
            {
                "discount": discount,
                "original_price": service.price,
                "discounted_price": final_price,
                "savings_amount": discount_amount,
                "savings_percentage": savings_percentage,
            }
        )

    return context


@register.inclusion_tag("discount_app/tags/venue_discount_summary.html")
def venue_discount_summary(venue):
    """
    Render a summary of all discounts available at a venue.
    Shows counts of different discount types.
    """
    if not venue:
        return {"venue": None}

    now = timezone.now()

    # Count service discounts
    service_discounts_count = ServiceDiscount.objects.filter(
        service__venue=venue,
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True,
        service__is_active=True,
    ).count()

    # Count venue discounts
    venue_discounts_count = VenueDiscount.objects.filter(
        venue=venue, start_date__lte=now, end_date__gte=now, is_approved=True
    ).count()

    # Count platform discounts
    platform_discounts_count = PlatformDiscount.objects.filter(
        start_date__lte=now, end_date__gte=now
    ).count()

    total_discounts = (
        service_discounts_count + venue_discounts_count + platform_discounts_count
    )

    return {
        "venue": venue,
        "service_discounts_count": service_discounts_count,
        "venue_discounts_count": venue_discounts_count,
        "platform_discounts_count": platform_discounts_count,
        "total_discounts": total_discounts,
        "has_discounts": total_discounts > 0,
    }


@register.filter
def calculate_discounted_price(discount, original_price):
    """
    Calculate the discounted price for a given original price.
    Usage: {{ discount|calculate_discounted_price:service.price_min }}
    """
    if not discount or not original_price:
        return original_price

    try:
        return discount.calculate_discounted_price(Decimal(str(original_price)))
    except (ValueError, TypeError):
        return original_price


@register.filter
def calculate_savings(discount, original_price):
    """
    Calculate the savings amount for a given original price.
    Usage: {{ discount|calculate_savings:service.price_min }}
    """
    if not discount or not original_price:
        return Decimal("0.00")

    try:
        original = Decimal(str(original_price))
        discounted = discount.calculate_discounted_price(original)
        return original - discounted
    except (ValueError, TypeError):
        return Decimal("0.00")


@register.filter
def days_until_expiry(discount):
    """
    Calculate days until discount expires.
    Usage: {{ discount|days_until_expiry }}
    """
    if not discount or not discount.end_date:
        return None

    now = timezone.now()
    if discount.end_date <= now:
        return 0

    return (discount.end_date.date() - now.date()).days


@register.filter
def is_expiring_soon(discount, days=7):
    """
    Check if discount is expiring within specified days.
    Usage: {{ discount|is_expiring_soon:3 }}
    """
    days_left = days_until_expiry(discount)
    if days_left is None:
        return False

    try:
        threshold = int(days)
        return 0 < days_left <= threshold
    except (ValueError, TypeError):
        return False


@register.simple_tag
def get_discount_urgency_class(discount):
    """
    Get CSS class based on discount urgency.
    Returns appropriate class for styling urgent discounts.
    """
    days_left = days_until_expiry(discount)
    if days_left is None:
        return ""

    if days_left <= 1:
        return "urgent-expiring"
    elif days_left <= 3:
        return "expiring-soon"
    elif days_left <= 7:
        return "expiring-this-week"

    return ""


@register.filter
def discount_usage_percentage(discount):
    """
    Calculate what percentage of max uses have been consumed.
    Usage: {{ discount|discount_usage_percentage }}
    """
    if not discount or not discount.max_uses:
        return 0

    usage_count = discount.get_usage_count()
    return int((usage_count / discount.max_uses) * 100)


@register.filter
def is_usage_limited(discount):
    """
    Check if discount has usage limits.
    Usage: {{ discount|is_usage_limited }}
    """
    return discount and discount.max_uses is not None


@register.filter
def usage_remaining(discount):
    """
    Get remaining usage count for discount.
    Usage: {{ discount|usage_remaining }}
    """
    if not discount:
        return None

    return discount.remaining_uses()


@register.filter
def get_item(dictionary, key):
    """
    Get item from dictionary by key.
    Usage: {{ dict|get_item:key }}
    """
    if not dictionary or key is None:
        return None

    try:
        return dictionary.get(key)
    except (AttributeError, TypeError):
        return None
