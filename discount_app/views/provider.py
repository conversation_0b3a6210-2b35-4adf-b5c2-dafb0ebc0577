"""Views for discount management by service providers."""

# --- Standard Library Imports ---
import json
from datetime import datetime, timedelta

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Avg, Count, F, Q, Sum
from django.http import Http404, HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.http import require_http_methods

from notifications_app.models import Notification
from notifications_app.utils import create_notification, send_notification_email

# --- Logging Imports ---
from utils.logging_utils import (
    get_app_logger,
    get_client_info,
    log_audit_event,
    log_error,
    log_performance,
    log_security_event,
    log_user_activity,
)
from venues_app.models import Category, Service, Venue

from ..forms import (
    DiscountApprovalForm,
    DiscountFilterForm,
    PlatformDiscountForm,
    QuickServiceDiscountForm,
    QuickVenueDiscountForm,
    ServiceDiscountForm,
    VenueDiscountForm,
)
from ..logging_utils import (
    log_discount_approval,
    log_discount_creation,
    log_discount_deletion,
    log_discount_error,
    log_discount_search,
    log_discount_update,
    log_unauthorized_discount_access,
)
from ..models import DiscountUsage, PlatformDiscount, ServiceDiscount, VenueDiscount
from ..utils import get_applicable_discounts, get_best_discount

# --- Local App Imports ---
from .common import service_provider_required


@login_required
@service_provider_required
def provider_discount_list(request):
    """
    Display list of all discounts created by the service provider.
    Includes both service and venue discounts with filtering and pagination.
    """
    try:
        venue = request.user.service_provider_profile.venue

        # Log provider accessing discount list
        log_user_activity(
            app_name="discount_app",
            activity_type="provider_discount_list_access",
            user=request.user,
            request=request,
            details={
                "venue_id": venue.id,
                "venue_name": venue.venue_name,
                "filters": {
                    "type": request.GET.get("type", "all"),
                    "status": request.GET.get("status", "all"),
                    "search": request.GET.get("search", "").strip(),
                },
            },
        )

    except Exception as e:
        log_error(
            app_name="discount_app",
            error_type="venue_access_error",
            error_message="Provider attempted to access discounts without venue",
            user=request.user,
            request=request,
            exception=e,
        )
        context = {
            "feature_name": "Discounts",
            "feature_description": "Create and manage discounts to attract more customers and increase bookings.",
            "feature_benefits": [
                "Offer special promotions and deals to customers",
                "Create service-specific or venue-wide discounts",
                "Track discount usage and effectiveness",
                "Increase customer engagement and bookings",
            ],
        }
        return render(request, "dashboard_app/provider/feature_locked.html", context)

    # Get filter parameters
    discount_type_filter = request.GET.get("type", "all")  # all, service, venue
    status_filter = request.GET.get("status", "all")  # all, active, scheduled, expired
    search_query = request.GET.get("search", "").strip()
    start_date_str = request.GET.get("start_date", "")
    end_date_str = request.GET.get("end_date", "")

    # Get service discounts
    service_discounts = ServiceDiscount.objects.filter(
        service__venue=venue
    ).select_related("service", "created_by")

    # Get venue discounts
    venue_discounts = VenueDiscount.objects.filter(venue=venue).select_related(
        "venue", "created_by"
    )

    # Apply search filter
    if search_query:
        service_discounts = service_discounts.filter(
            Q(name__icontains=search_query)
            | Q(description__icontains=search_query)
            | Q(service__service_title__icontains=search_query)
        )
        venue_discounts = venue_discounts.filter(
            Q(name__icontains=search_query) | Q(description__icontains=search_query)
        )

    # Apply status filter
    now = timezone.now()

    # Parse date range
    start_date = None
    end_date = None
    if start_date_str:
        try:
            start_date = timezone.make_aware(
                datetime.strptime(start_date_str, "%Y-%m-%d")
            )
        except ValueError:
            start_date = None
    if end_date_str:
        try:
            end_date = timezone.make_aware(
                datetime.strptime(end_date_str, "%Y-%m-%d")
            ) + timedelta(days=1)
        except ValueError:
            end_date = None
    if status_filter == "active":
        service_discounts = service_discounts.filter(
            start_date__lte=now, end_date__gte=now
        )
        venue_discounts = venue_discounts.filter(start_date__lte=now, end_date__gte=now)
    elif status_filter == "scheduled":
        service_discounts = service_discounts.filter(start_date__gt=now)
        venue_discounts = venue_discounts.filter(start_date__gt=now)
    elif status_filter == "expired":
        service_discounts = service_discounts.filter(end_date__lt=now)
        venue_discounts = venue_discounts.filter(end_date__lt=now)

    # Combine and sort discounts
    all_discounts = []

    if discount_type_filter in ["all", "service"]:
        for discount in service_discounts:
            all_discounts.append(
                {
                    "type": "service",
                    "object": discount,
                    "service_name": discount.service.service_title,
                    "status": discount.get_status(),
                    "is_visible": discount.is_visible,
                    "created_at": discount.created_at,
                }
            )

    if discount_type_filter in ["all", "venue"]:
        for discount in venue_discounts:
            all_discounts.append(
                {
                    "type": "venue",
                    "object": discount,
                    "service_name": "All Services",
                    "status": discount.get_status(),
                    "is_visible": discount.is_visible,
                    "created_at": discount.created_at,
                }
            )

    # Sort by creation date (newest first)
    all_discounts.sort(key=lambda x: x["created_at"], reverse=True)

    # Pagination
    paginator = Paginator(all_discounts, 10)  # 10 discounts per page
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    # Get venue services for the "Create Service Discount" dropdown
    venue_services = Service.objects.filter(venue=venue, is_active=True).order_by(
        "service_title"
    )

    context = {
        "page_obj": page_obj,
        "venue": venue,
        "venue_services": venue_services,
        "discount_type_filter": discount_type_filter,
        "status_filter": status_filter,
        "search_query": search_query,
        "total_discounts": len(all_discounts),
    }

    return render(request, "discount_app/provider/discount_list.html", context)


@login_required
@service_provider_required
def provider_discount_analytics(request):
    """
    Enhanced analytics dashboard for provider discount performance.
    """
    try:
        venue = request.user.service_provider_profile.venue
    except Exception as e:
        log_error(
            app_name="discount_app",
            error_type="venue_access_error",
            error_message="Provider attempted to access analytics without venue",
            user=request.user,
            request=request,
            exception=e,
        )
        context = {
            "feature_name": "Discount Analytics",
            "feature_description": "Analyze the performance of your discounts and understand their impact on your business.",
            "feature_benefits": [
                "Track discount usage and effectiveness",
                "Analyze customer response to promotions",
                "Identify your most successful discount strategies",
                "Make data-driven decisions to optimize your offers",
            ],
        }
        return render(request, "dashboard_app/provider/feature_locked.html", context)

    # Date range for analytics (last 30 days by default)
    end_date = timezone.now()
    start_date = end_date - timedelta(days=30)

    # Get date range from request if provided
    date_range = request.GET.get("range", "30")
    if date_range == "7":
        start_date = end_date - timedelta(days=7)
    elif date_range == "90":
        start_date = end_date - timedelta(days=90)
    elif date_range == "365":
        start_date = end_date - timedelta(days=365)

    # Get all discounts for this venue
    service_discounts = ServiceDiscount.objects.filter(
        service__venue=venue
    ).select_related("service")

    venue_discounts = VenueDiscount.objects.filter(venue=venue)

    # Calculate overall statistics
    total_discounts = service_discounts.count() + venue_discounts.count()
    active_discounts = (
        service_discounts.filter(
            start_date__lte=end_date, end_date__gte=end_date, is_approved=True
        ).count()
        + venue_discounts.filter(
            start_date__lte=end_date, end_date__gte=end_date, is_approved=True
        ).count()
    )

    # Get usage statistics
    service_usage = DiscountUsage.objects.filter(
        discount_type="ServiceDiscount",
        discount_id__in=service_discounts.values_list("id", flat=True),
        used_at__gte=start_date,
        used_at__lte=end_date,
    ).aggregate(
        total_usage=Count("id"),
        total_savings=Sum("discount_amount"),
        total_revenue=Sum("final_price"),
    )

    venue_usage = DiscountUsage.objects.filter(
        discount_type="VenueDiscount",
        discount_id__in=venue_discounts.values_list("id", flat=True),
        used_at__gte=start_date,
        used_at__lte=end_date,
    ).aggregate(
        total_usage=Count("id"),
        total_savings=Sum("discount_amount"),
        total_revenue=Sum("final_price"),
    )

    # Combine usage statistics
    total_usage = (service_usage["total_usage"] or 0) + (
        venue_usage["total_usage"] or 0
    )
    total_savings = (service_usage["total_savings"] or 0) + (
        venue_usage["total_savings"] or 0
    )
    total_revenue = (service_usage["total_revenue"] or 0) + (
        venue_usage["total_revenue"] or 0
    )

    # Top performing discounts
    top_service_discounts = []
    for discount in service_discounts[:5]:
        usage_count = DiscountUsage.objects.filter(
            discount_type="ServiceDiscount",
            discount_id=discount.id,
            used_at__gte=start_date,
            used_at__lte=end_date,
        ).count()

        savings_total = (
            DiscountUsage.objects.filter(
                discount_type="ServiceDiscount",
                discount_id=discount.id,
                used_at__gte=start_date,
                used_at__lte=end_date,
            ).aggregate(total=Sum("discount_amount"))["total"]
            or 0
        )

        top_service_discounts.append(
            {
                "discount": discount,
                "usage_count": usage_count,
                "savings_total": savings_total,
                "type": "service",
            }
        )

    top_venue_discounts = []
    for discount in venue_discounts[:5]:
        usage_count = DiscountUsage.objects.filter(
            discount_type="VenueDiscount",
            discount_id=discount.id,
            used_at__gte=start_date,
            used_at__lte=end_date,
        ).count()

        savings_total = (
            DiscountUsage.objects.filter(
                discount_type="VenueDiscount",
                discount_id=discount.id,
                used_at__gte=start_date,
                used_at__lte=end_date,
            ).aggregate(total=Sum("discount_amount"))["total"]
            or 0
        )

        top_venue_discounts.append(
            {
                "discount": discount,
                "usage_count": usage_count,
                "savings_total": savings_total,
                "type": "venue",
            }
        )

    # Combine and sort top discounts
    all_top_discounts = top_service_discounts + top_venue_discounts
    all_top_discounts.sort(key=lambda x: x["usage_count"], reverse=True)
    top_discounts = all_top_discounts[:10]

    # Log analytics access
    log_user_activity(
        app_name="discount_app",
        activity_type="provider_analytics_access",
        user=request.user,
        request=request,
        details={
            "venue_id": venue.id,
            "date_range": date_range,
            "total_discounts": total_discounts,
            "total_usage": total_usage,
        },
    )

    context = {
        "venue": venue,
        "date_range": date_range,
        "start_date": start_date,
        "end_date": end_date,
        "total_discounts": total_discounts,
        "active_discounts": active_discounts,
        "total_usage": total_usage,
        "total_savings": total_savings,
        "total_revenue": total_revenue,
        "top_discounts": top_discounts,
        "service_discounts_count": service_discounts.count(),
        "venue_discounts_count": venue_discounts.count(),
    }

    return render(request, "discount_app/provider/discount_analytics.html", context)


@login_required
@service_provider_required
def provider_discount_dashboard(request):
    """
    Simplified discount management dashboard for providers.
    Easy interface for creating discounts on individual services and venue-wide discounts.
    """
    try:
        venue = request.user.service_provider_profile.venue
    except Exception as e:
        log_error(
            app_name="discount_app",
            error_type="venue_access_error",
            error_message="Provider attempted to access discount dashboard without venue",
            user=request.user,
            request=request,
            exception=e,
        )
        context = {
            "feature_name": "Discounts",
            "feature_description": "Create and manage discounts to attract more customers and increase bookings.",
            "feature_benefits": [
                "Offer special promotions and deals to customers",
                "Create service-specific or venue-wide discounts",
                "Track discount usage and effectiveness",
                "Increase customer engagement and bookings",
            ],
        }
        return render(request, "dashboard_app/provider/feature_locked.html", context)

    # Get all services for this venue with their discounts
    services = venue.services.filter(is_active=True).order_by("service_title")

    # Get current discounts
    now = timezone.now()

    # Active service discounts
    active_service_discounts = ServiceDiscount.objects.filter(
        service__venue=venue, start_date__lte=now, end_date__gte=now, is_approved=True
    ).select_related("service")

    # Active venue discounts
    active_venue_discounts = VenueDiscount.objects.filter(
        venue=venue, start_date__lte=now, end_date__gte=now, is_approved=True
    )

    # Pending discounts (awaiting approval)
    pending_service_discounts = ServiceDiscount.objects.filter(
        service__venue=venue, is_approved=False
    ).select_related("service")

    pending_venue_discounts = VenueDiscount.objects.filter(
        venue=venue, is_approved=False
    )

    # Create service list with discount information
    services_with_discounts = []
    service_discounts_dict = {d.service.id: d for d in active_service_discounts}

    for service in services:
        service_data = {
            "service": service,
            "has_discount": service.id in service_discounts_dict,
            "discount": service_discounts_dict.get(service.id),
        }
        services_with_discounts.append(service_data)

    # Calculate quick stats
    total_active_discounts = (
        active_service_discounts.count() + active_venue_discounts.count()
    )
    total_pending_discounts = (
        pending_service_discounts.count() + pending_venue_discounts.count()
    )

    # Get usage statistics for last 30 days
    thirty_days_ago = now - timedelta(days=30)
    recent_usage = DiscountUsage.objects.filter(
        Q(
            discount_type="ServiceDiscount",
            discount_id__in=active_service_discounts.values_list("id", flat=True),
        )
        | Q(
            discount_type="VenueDiscount",
            discount_id__in=active_venue_discounts.values_list("id", flat=True),
        ),
        used_at__gte=thirty_days_ago,
    ).aggregate(total_uses=Count("id"), total_savings=Sum("discount_amount"))

    # Log dashboard access
    log_user_activity(
        app_name="discount_app",
        activity_type="provider_discount_dashboard_access",
        user=request.user,
        request=request,
        details={
            "venue_id": venue.id,
            "total_services": services.count(),
            "active_discounts": total_active_discounts,
            "pending_discounts": total_pending_discounts,
        },
    )

    context = {
        "venue": venue,
        "services": services,
        "services_with_discounts": services_with_discounts,
        "active_service_discounts": active_service_discounts,
        "active_venue_discounts": active_venue_discounts,
        "pending_service_discounts": pending_service_discounts,
        "pending_venue_discounts": pending_venue_discounts,
        "total_active_discounts": total_active_discounts,
        "total_pending_discounts": total_pending_discounts,
        "recent_usage": recent_usage,
    }

    return render(request, "discount_app/provider/discount_dashboard.html", context)


@login_required
@service_provider_required
@require_http_methods(["GET", "POST"])
def create_service_discount(request):
    """
    Create a new service discount.
    """
    venue = request.user.service_provider_profile.venue

    if request.method == "POST":
        # Skip throttle during testing
        if not getattr(settings, "TESTING", False):
            key = f"create_discount_{request.user.id}"
            last_time = cache.get(key)
            if last_time and timezone.now() - last_time < timedelta(minutes=1):
                messages.error(request, "Please wait before creating another discount.")
                return redirect("discount_app:provider_discount_list")

        form = ServiceDiscountForm(request.POST, user=request.user)
        if form.is_valid():
            try:
                service_discount = form.save()

                # Log successful discount creation
                log_discount_creation(
                    user=request.user,
                    discount=service_discount,
                    request=request,
                    discount_type="service",
                )

                messages.success(
                    request,
                    f'Service discount "{service_discount.name}" successfully created! '
                    "It will be visible to customers once approved by admin.",
                )
                # Set cache only if not testing
                if not getattr(settings, "TESTING", False):
                    cache.set(key, timezone.now(), 60)
                return redirect("discount_app:provider_discount_list")

            except Exception as e:
                log_discount_error(
                    error_type="service_discount_creation_error",
                    error_message="Failed to create service discount",
                    user=request.user,
                    request=request,
                    exception=e,
                    discount_context={"form_data": form.cleaned_data},
                )
                messages.error(
                    request,
                    "An error occurred while creating the discount. Please try again.",
                )
                return redirect("discount_app:provider_discount_list")
        else:
            # Log form validation errors
            log_user_activity(
                app_name="discount_app",
                activity_type="service_discount_creation_failed",
                user=request.user,
                request=request,
                details={
                    "form_errors": form.errors.as_json(),
                    "form_data": request.POST.dict(),
                },
            )
            # Don't redirect on form errors, render the form again
    else:
        # Log access to create form
        log_user_activity(
            app_name="discount_app",
            activity_type="service_discount_create_form_access",
            user=request.user,
            request=request,
        )

        # Check if a specific service is pre-selected
        initial_data = {}
        service_id = request.GET.get("service")
        if service_id:
            try:
                service = Service.objects.get(
                    id=service_id, venue=venue, is_active=True
                )
                initial_data["service"] = service
            except Service.DoesNotExist:
                pass

        form = ServiceDiscountForm(user=request.user, initial=initial_data)

    services_qs = Service.objects.filter(venue=venue, is_active=True).only(
        "id", "price_min"
    )
    service_prices = {str(s.id): str(s.price_min) for s in services_qs}

    context = {
        "form": form,
        "title": "Create Service Discount",
        "action": "Create",
        "service_prices_json": json.dumps(service_prices),
    }

    return render(request, "discount_app/provider/service_discount_form.html", context)


@login_required
@service_provider_required
@require_http_methods(["GET", "POST"])
def edit_service_discount(request, discount_slug):
    """
    Edit an existing service discount.
    """
    # Get the discount and ensure it belongs to the current provider
    try:
        venue = request.user.service_provider_profile.venue
        service_discount = get_object_or_404(
            ServiceDiscount, slug=discount_slug, service__venue=venue
        )

        # Log access to edit form
        if request.method == "GET":
            log_user_activity(
                app_name="discount_app",
                activity_type="service_discount_edit_form_access",
                user=request.user,
                request=request,
                details={
                    "discount_id": service_discount.id,
                    "discount_name": service_discount.name,
                    "service_id": service_discount.service.id,
                    "venue_id": venue.id,
                },
            )

    except Exception as e:
        log_security_event(
            app_name="discount_app",
            event_type="unauthorized_discount_edit_attempt",
            user_email=(
                request.user.email if request.user.is_authenticated else "anonymous"
            ),
            request=request,
            details={
                "discount_id": discount_slug,
                "attempted_action": "edit_service_discount",
            },
        )
        messages.error(
            request, "Discount not found or you do not have permission to edit it."
        )
        return redirect("discount_app:provider_discount_list")

    if request.method == "POST":
        # Store original values for logging
        original_values = {
            "name": service_discount.name,
            "discount_type": service_discount.discount_type,
            "discount_value": float(service_discount.discount_value),
            "start_date": service_discount.start_date.isoformat(),
            "end_date": service_discount.end_date.isoformat(),
        }

        form = ServiceDiscountForm(
            request.POST, instance=service_discount, user=request.user
        )
        if form.is_valid():
            try:
                updated_discount = form.save()

                # Log successful update
                log_user_activity(
                    app_name="discount_app",
                    activity_type="service_discount_updated",
                    user=request.user,
                    request=request,
                    details={
                        "discount_id": updated_discount.id,
                        "discount_name": updated_discount.name,
                        "service_id": updated_discount.service.id,
                        "venue_id": venue.id,
                        "original_values": original_values,
                        "updated_values": {
                            "name": updated_discount.name,
                            "discount_type": updated_discount.discount_type,
                            "discount_value": float(updated_discount.discount_value),
                            "start_date": updated_discount.start_date.isoformat(),
                            "end_date": updated_discount.end_date.isoformat(),
                        },
                    },
                )

                messages.success(
                    request,
                    f'Service discount "{updated_discount.name}" updated successfully!',
                )
                return redirect("discount_app:provider_discount_list")

            except Exception as e:
                log_error(
                    app_name="discount_app",
                    error_type="service_discount_update_error",
                    error_message="Failed to update service discount",
                    user=request.user,
                    request=request,
                    exception=e,
                    details={
                        "discount_id": service_discount.id,
                        "form_data": form.cleaned_data,
                    },
                )
                messages.error(
                    request,
                    "An error occurred while updating the discount. Please try again.",
                )
        else:
            # Log form validation errors
            log_user_activity(
                app_name="discount_app",
                activity_type="service_discount_update_failed",
                user=request.user,
                request=request,
                details={
                    "discount_id": service_discount.id,
                    "form_errors": form.errors.as_json(),
                    "form_data": request.POST.dict(),
                },
            )
    else:
        form = ServiceDiscountForm(instance=service_discount, user=request.user)

    services_qs = Service.objects.filter(venue=venue, is_active=True).only(
        "id", "price_min"
    )
    service_prices = {str(s.id): str(s.price_min) for s in services_qs}

    context = {
        "form": form,
        "title": "Update Service Discount",
        "action": "Update",
        "discount": service_discount,
        "service_prices_json": json.dumps(service_prices),
    }

    return render(request, "discount_app/provider/service_discount_form.html", context)


@login_required
@service_provider_required
@require_http_methods(["GET", "POST"])
def create_venue_discount(request):
    """
    Create a new venue-wide discount.
    """
    if request.method == "POST":
        # Skip throttle during testing
        if not getattr(settings, "TESTING", False):
            key = f"create_discount_{request.user.id}"
            last_time = cache.get(key)
            if last_time and timezone.now() - last_time < timedelta(minutes=1):
                messages.error(request, "Please wait before creating another discount.")
                return redirect("discount_app:provider_discount_list")

        form = VenueDiscountForm(request.POST, user=request.user)
        if form.is_valid():
            venue_discount = form.save()

            # Log successful discount creation with client IP
            log_discount_creation(
                user=request.user,
                discount=venue_discount,
                request=request,
                discount_type="venue",
            )

            messages.success(
                request,
                f'Venue discount "{venue_discount.name}" successfully created! '
                "It will be visible to customers once approved by admin.",
            )
            # Set cache only if not testing
            if not getattr(settings, "TESTING", False):
                cache.set(key, timezone.now(), 60)
            return redirect("discount_app:provider_discount_list")
        else:
            # Log form validation errors for venue discount
            log_user_activity(
                app_name="discount_app",
                activity_type="venue_discount_creation_failed",
                user=request.user,
                request=request,
                details={
                    "form_errors": form.errors.as_json(),
                    "form_data": request.POST.dict(),
                },
            )
            # Don't redirect on form errors, render the form again
    else:
        form = VenueDiscountForm(user=request.user)

    context = {
        "form": form,
        "title": "Create Venue Discount",
        "action": "Create",
    }

    return render(request, "discount_app/provider/venue_discount_form.html", context)


@login_required
@service_provider_required
@require_http_methods(["GET", "POST"])
def edit_venue_discount(request, discount_slug):
    """
    Edit an existing venue discount.
    """
    # Get the discount and ensure it belongs to the current provider
    try:
        venue = request.user.service_provider_profile.venue
        venue_discount = get_object_or_404(
            VenueDiscount, slug=discount_slug, venue=venue
        )
    except Exception:
        messages.error(
            request, "Discount not found or you do not have permission to edit it."
        )
        return redirect("discount_app:provider_discount_list")

    if request.method == "POST":
        form = VenueDiscountForm(
            request.POST, instance=venue_discount, user=request.user
        )
        if form.is_valid():
            venue_discount = form.save()
            messages.success(
                request, f'Venue discount "{venue_discount.name}" updated successfully!'
            )
            return redirect("discount_app:provider_discount_list")
    else:
        form = VenueDiscountForm(instance=venue_discount, user=request.user)

    context = {
        "form": form,
        "title": "Edit Venue Discount",
        "action": "Edit",
        "discount": venue_discount,
    }

    return render(request, "discount_app/provider/venue_discount_form.html", context)


@login_required
@service_provider_required
def service_discount_detail(request, discount_slug):
    """
    View details of a specific service discount.
    """
    try:
        venue = request.user.service_provider_profile.venue
        service_discount = get_object_or_404(
            ServiceDiscount, slug=discount_slug, service__venue=venue
        )
    except Exception:
        messages.error(
            request, "Discount not found or you do not have permission to view it."
        )
        return redirect("discount_app:provider_discount_list")

    # Get usage statistics for this discount
    usage_stats = DiscountUsage.objects.filter(
        discount_type="ServiceDiscount", discount_id=service_discount.id
    ).order_by("-used_at")[
        :10
    ]  # Last 10 uses

    context = {
        "discount": service_discount,
        "usage_stats": usage_stats,
        "discount_type": "service",
    }

    return render(request, "discount_app/provider/discount_detail.html", context)


@login_required
@service_provider_required
def venue_discount_detail(request, discount_slug):
    """
    View details of a specific venue discount.
    """
    try:
        venue = request.user.service_provider_profile.venue
        venue_discount = get_object_or_404(
            VenueDiscount, slug=discount_slug, venue=venue
        )
    except Exception:
        messages.error(
            request, "Discount not found or you do not have permission to view it."
        )
        return redirect("discount_app:provider_discount_list")

    # Get usage statistics for this discount
    usage_stats = DiscountUsage.objects.filter(
        discount_type="VenueDiscount", discount_id=venue_discount.id
    ).order_by("-used_at")[
        :10
    ]  # Last 10 uses

    context = {
        "discount": venue_discount,
        "usage_stats": usage_stats,
        "discount_type": "venue",
    }

    return render(request, "discount_app/provider/discount_detail.html", context)


@login_required
@service_provider_required
def delete_service_discount(request, discount_slug):
    """
    Delete a service discount. GET shows confirmation, POST performs deletion.
    """
    try:
        venue = request.user.service_provider_profile.venue
        service_discount = get_object_or_404(
            ServiceDiscount, slug=discount_slug, service__venue=venue
        )

        if request.method == "GET":
            # Show confirmation page
            context = {
                "discount": service_discount,
                "discount_type": "service",
                "venue": venue,
            }
            return render(
                request, "discount_app/provider/discount_delete_confirm.html", context
            )

        elif request.method == "POST":
            # Store discount details for logging before deletion
            discount_details = {
                "discount_id": service_discount.id,
                "discount_name": service_discount.name,
                "service_id": service_discount.service.id,
                "service_name": service_discount.service.service_title,
                "venue_id": venue.id,
                "venue_name": venue.venue_name,
                "discount_type": service_discount.discount_type,
                "discount_value": float(service_discount.discount_value),
                "was_approved": service_discount.is_approved,
            }

            discount_name = service_discount.name
            service_discount.delete()

            # Log successful deletion
            log_discount_deletion(
                user=request.user,
                discount_details=discount_details,
                request=request,
                discount_type="service",
            )

            messages.success(
                request, f'Service discount "{discount_name}" deleted successfully!'
            )
            return redirect("discount_app:provider_discount_list")

    except Exception as e:
        log_unauthorized_discount_access(
            user_email=(
                request.user.email if request.user.is_authenticated else "anonymous"
            ),
            attempted_action="delete_service_discount",
            discount_id=discount_slug,
            request=request,
            additional_details={"error": str(e)},
        )
        messages.error(
            request, "Discount not found or you do not have permission to delete it."
        )
        return redirect("discount_app:provider_discount_list")


@login_required
@service_provider_required
def delete_venue_discount(request, discount_slug):
    """
    Delete a venue discount. GET shows confirmation, POST performs deletion.
    """
    try:
        venue = request.user.service_provider_profile.venue
        venue_discount = get_object_or_404(
            VenueDiscount, slug=discount_slug, venue=venue
        )

        if request.method == "GET":
            # Show confirmation page
            context = {
                "discount": venue_discount,
                "discount_type": "venue",
                "venue": venue,
            }
            return render(
                request, "discount_app/provider/discount_delete_confirm.html", context
            )

        elif request.method == "POST":
            discount_name = venue_discount.name
            venue_discount.delete()
            messages.success(
                request, f'Venue discount "{discount_name}" deleted successfully!'
            )
            return redirect("discount_app:provider_discount_list")

    except Exception:
        messages.error(
            request, "Discount not found or you do not have permission to delete it."
        )
        return redirect("discount_app:provider_discount_list")


@login_required
@service_provider_required
@require_http_methods(["POST"])
def quick_edit_discount(request, discount_type, discount_slug):
    """Inline update for discounts from the list page."""
    venue = request.user.service_provider_profile.venue

    if discount_type == "service":
        discount = get_object_or_404(
            ServiceDiscount, slug=discount_slug, service__venue=venue
        )
        form = QuickServiceDiscountForm(request.POST, instance=discount)
    else:
        discount = get_object_or_404(VenueDiscount, slug=discount_slug, venue=venue)
        form = QuickVenueDiscountForm(request.POST, instance=discount)

    if form.is_valid():
        form.save()
        messages.success(request, "Discount updated successfully.")
    else:
        messages.error(request, "Unable to update discount. Please check the values.")

    return redirect("discount_app:provider_discount_list")
