"""Administrative views for discount management."""

# --- Standard Library Imports ---
import json
from datetime import datetime, timedelta

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Avg, Count, F, Q, Sum
from django.http import Http404, HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.http import require_http_methods

from notifications_app.models import Notification
from notifications_app.utils import create_notification, send_notification_email

# --- Logging Imports ---
from utils.logging_utils import (
    get_app_logger,
    get_client_info,
    log_audit_event,
    log_error,
    log_performance,
    log_security_event,
    log_user_activity,
)
from venues_app.models import Category, Service, Venue

# --- Local App Imports ---
from ..forms import (
    DiscountApprovalForm,
    DiscountFilterForm,
    PlatformDiscountForm,
    QuickServiceDiscountForm,
    QuickVenueDiscountForm,
    ServiceDiscountForm,
    VenueDiscountForm,
)
from ..logging_utils import (
    log_discount_approval,
    log_discount_creation,
    log_discount_deletion,
    log_discount_error,
    log_discount_search,
    log_discount_update,
    log_unauthorized_discount_access,
)
from ..models import DiscountUsage, PlatformDiscount, ServiceDiscount, VenueDiscount
from ..utils import get_applicable_discounts, get_best_discount


def is_admin(user):
    """
    Check if user is admin/staff member.
    Raises PermissionDenied instead of returning False for better test compatibility.
    """
    if not user.is_authenticated:
        from django.core.exceptions import PermissionDenied

        raise PermissionDenied("Authentication required")
    if not user.is_staff:
        from django.core.exceptions import PermissionDenied

        raise PermissionDenied("Admin privileges required")
    return True


@login_required
@user_passes_test(is_admin)
def admin_discount_dashboard(request):
    """
    Enhanced admin dashboard for discount management with comprehensive analytics.
    """
    # Log admin accessing dashboard
    log_user_activity(
        app_name="discount_app",
        activity_type="admin_dashboard_access",
        user=request.user,
        request=request,
        details={
            "admin_email": request.user.email,
            "access_time": timezone.now().isoformat(),
        },
    )

    now = timezone.now()

    # Basic counts
    total_venue_discounts = VenueDiscount.objects.count()
    total_service_discounts = ServiceDiscount.objects.count()
    total_platform_discounts = PlatformDiscount.objects.count()
    total_discount_usages = DiscountUsage.objects.count()

    # Active discounts
    active_venue_discounts = VenueDiscount.objects.filter(
        start_date__lte=now, end_date__gte=now, is_approved=True
    ).count()
    active_service_discounts = ServiceDiscount.objects.filter(
        start_date__lte=now, end_date__gte=now, is_approved=True
    ).count()
    active_platform_discounts = PlatformDiscount.objects.filter(
        start_date__lte=now, end_date__gte=now
    ).count()

    # Pending approvals
    pending_venue_discounts = VenueDiscount.objects.filter(is_approved=False).count()
    pending_service_discounts = ServiceDiscount.objects.filter(
        is_approved=False
    ).count()

    # Usage analytics for the last 30 days
    thirty_days_ago = now - timedelta(days=30)
    recent_usage_stats = DiscountUsage.objects.filter(
        used_at__gte=thirty_days_ago
    ).aggregate(
        total_usage=Count("id"),
        total_savings=Sum("discount_amount"),
        avg_discount=Avg("discount_amount"),
    )

    # Most popular discounts (by usage count) - calculate manually
    # Get usage counts for each discount type
    venue_usage_counts = {}
    service_usage_counts = {}
    platform_usage_counts = {}

    # Calculate usage counts for venue discounts
    venue_usages = (
        DiscountUsage.objects.filter(
            discount_type="VenueDiscount", used_at__gte=thirty_days_ago
        )
        .values("discount_id")
        .annotate(count=Count("id"))
        .order_by("-count")
    )

    for usage in venue_usages:
        venue_usage_counts[usage["discount_id"]] = usage["count"]

    # Calculate usage counts for service discounts
    service_usages = (
        DiscountUsage.objects.filter(
            discount_type="ServiceDiscount", used_at__gte=thirty_days_ago
        )
        .values("discount_id")
        .annotate(count=Count("id"))
        .order_by("-count")
    )

    for usage in service_usages:
        service_usage_counts[usage["discount_id"]] = usage["count"]

    # Calculate usage counts for platform discounts
    platform_usages = (
        DiscountUsage.objects.filter(
            discount_type="PlatformDiscount", used_at__gte=thirty_days_ago
        )
        .values("discount_id")
        .annotate(count=Count("id"))
        .order_by("-count")
    )

    for usage in platform_usages:
        platform_usage_counts[usage["discount_id"]] = usage["count"]

    # Get top discounts with usage counts
    popular_venue_discounts = []
    if venue_usage_counts:
        top_venue_ids = sorted(
            venue_usage_counts.keys(), key=lambda x: venue_usage_counts[x], reverse=True
        )[:5]
        for venue_id in top_venue_ids:
            try:
                discount = VenueDiscount.objects.get(id=venue_id)
                discount.usage_count = venue_usage_counts[venue_id]
                popular_venue_discounts.append(discount)
            except VenueDiscount.DoesNotExist:
                continue

    popular_service_discounts = []
    if service_usage_counts:
        top_service_ids = sorted(
            service_usage_counts.keys(),
            key=lambda x: service_usage_counts[x],
            reverse=True,
        )[:5]
        for service_id in top_service_ids:
            try:
                discount = ServiceDiscount.objects.get(id=service_id)
                discount.usage_count = service_usage_counts[service_id]
                popular_service_discounts.append(discount)
            except ServiceDiscount.DoesNotExist:
                continue

    popular_platform_discounts = []
    if platform_usage_counts:
        top_platform_ids = sorted(
            platform_usage_counts.keys(),
            key=lambda x: platform_usage_counts[x],
            reverse=True,
        )[:5]
        for platform_id in top_platform_ids:
            try:
                discount = PlatformDiscount.objects.get(id=platform_id)
                discount.usage_count = platform_usage_counts[platform_id]
                popular_platform_discounts.append(discount)
            except PlatformDiscount.DoesNotExist:
                continue

    # Revenue impact analytics
    revenue_impact = DiscountUsage.objects.filter(
        used_at__gte=thirty_days_ago
    ).aggregate(
        total_original_value=Sum("original_price"),
        total_final_value=Sum("final_price"),
        total_discount_given=Sum("discount_amount"),
    )

    # Calculate percentage savings
    if revenue_impact["total_original_value"]:
        avg_savings_percentage = (
            revenue_impact["total_discount_given"]
            / revenue_impact["total_original_value"]
        ) * 100
    else:
        avg_savings_percentage = 0

    context = {
        # Basic counts
        "total_venue_discounts": total_venue_discounts,
        "total_service_discounts": total_service_discounts,
        "total_platform_discounts": total_platform_discounts,
        "total_discount_usages": total_discount_usages,
        # Active discounts
        "active_venue_discounts": active_venue_discounts,
        "active_service_discounts": active_service_discounts,
        "active_platform_discounts": active_platform_discounts,
        # Pending approvals
        "pending_venue_discounts": pending_venue_discounts,
        "pending_service_discounts": pending_service_discounts,
        "total_pending": pending_venue_discounts + pending_service_discounts,
        # Usage analytics
        "recent_usage_stats": recent_usage_stats,
        "popular_venue_discounts": popular_venue_discounts,
        "popular_service_discounts": popular_service_discounts,
        "popular_platform_discounts": popular_platform_discounts,
        # Revenue impact
        "revenue_impact": revenue_impact,
        "avg_savings_percentage": round(avg_savings_percentage, 2),
        # Date range for analytics
        "analytics_period": "30 days",
        "analytics_start_date": thirty_days_ago.strftime("%Y-%m-%d"),
    }

    return render(request, "discount_app/admin/discount_dashboard.html", context)


@login_required
@user_passes_test(is_admin)
def admin_discount_list(request, discount_type):
    """
    Admin view to list discounts by type with filtering and search.
    """
    # Validate discount type
    if discount_type not in ["venue", "service", "platform"]:
        messages.error(request, "Invalid discount type.")
        return redirect("discount_app:admin_discount_dashboard")

    # Get filter parameters
    filter_form = DiscountFilterForm(request.GET)
    search_query = request.GET.get("search", "").strip()
    status_filter = request.GET.get("status", "")
    discount_type_filter = request.GET.get("discount_type", "")

    # Base queryset based on discount type
    if discount_type == "venue":
        discounts = VenueDiscount.objects.select_related(
            "venue", "created_by", "approved_by"
        )
        title = "Venue Discounts"
    elif discount_type == "service":
        discounts = ServiceDiscount.objects.select_related(
            "service", "service__venue", "created_by", "approved_by"
        )
        title = "Service Discounts"
    else:  # platform
        discounts = PlatformDiscount.objects.select_related("category", "created_by")
        title = "Platform Discounts"

    # Apply search filter
    if search_query:
        if discount_type == "venue":
            discounts = discounts.filter(
                Q(name__icontains=search_query)
                | Q(description__icontains=search_query)
                | Q(venue__venue_name__icontains=search_query)
            )
        elif discount_type == "service":
            discounts = discounts.filter(
                Q(name__icontains=search_query)
                | Q(description__icontains=search_query)
                | Q(service__service_title__icontains=search_query)
                | Q(service__venue__venue_name__icontains=search_query)
            )
        else:  # platform
            discounts = discounts.filter(
                Q(name__icontains=search_query) | Q(description__icontains=search_query)
            )

    # Apply status filter
    now = timezone.now()
    if status_filter == "active":
        discounts = discounts.filter(start_date__lte=now, end_date__gte=now)
        if discount_type in ["venue", "service"]:
            discounts = discounts.filter(is_approved=True)
    elif status_filter == "scheduled":
        discounts = discounts.filter(start_date__gt=now)
    elif status_filter == "expired":
        discounts = discounts.filter(end_date__lt=now)
    elif status_filter == "pending" and discount_type in ["venue", "service"]:
        discounts = discounts.filter(is_approved=False)

    # Apply discount type filter
    if discount_type_filter:
        discounts = discounts.filter(discount_type=discount_type_filter)

    # Apply form filters if valid
    if filter_form.is_valid():
        min_value = filter_form.cleaned_data.get("min_value")
        max_value = filter_form.cleaned_data.get("max_value")

        if min_value is not None:
            discounts = discounts.filter(discount_value__gte=min_value)
        if max_value is not None:
            discounts = discounts.filter(discount_value__lte=max_value)

    # Order by creation date (newest first)
    discounts = discounts.order_by("-created_at")

    # Add usage statistics for each discount
    discounts_with_stats = []
    for discount in discounts:
        usage_count = DiscountUsage.objects.filter(
            discount_type=f"{discount_type.capitalize()}Discount",
            discount_id=discount.id,
        ).count()

        total_savings = (
            DiscountUsage.objects.filter(
                discount_type=f"{discount_type.capitalize()}Discount",
                discount_id=discount.id,
            ).aggregate(total=Sum("discount_amount"))["total"]
            or 0
        )

        discounts_with_stats.append(
            {
                "discount": discount,
                "usage_count": usage_count,
                "total_savings": total_savings,
            }
        )

    # Pagination
    paginator = Paginator(discounts_with_stats, 20)  # 20 discounts per page
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    context = {
        "page_obj": page_obj,
        "discount_type": discount_type,
        "title": title,
        "filter_form": filter_form,
        "search_query": search_query,
        "status_filter": status_filter,
        "discount_type_filter": discount_type_filter,
        "total_discounts": len(discounts_with_stats),
    }

    return render(request, "discount_app/admin/discount_list.html", context)


@login_required
@user_passes_test(is_admin)
def admin_discount_detail(request, discount_type, discount_id):
    """
    Admin view to see detailed discount information with usage analytics.
    """
    # Validate discount type and get discount object
    if discount_type == "venue":
        discount = get_object_or_404(VenueDiscount, id=discount_id)
        related_entity = discount.venue
        entity_type = "venue"
    elif discount_type == "service":
        discount = get_object_or_404(ServiceDiscount, id=discount_id)
        related_entity = discount.service
        entity_type = "service"
    elif discount_type == "platform":
        discount = get_object_or_404(PlatformDiscount, id=discount_id)
        related_entity = discount.category
        entity_type = "category"
    else:
        messages.error(request, "Invalid discount type.")
        return redirect("discount_app:admin_discount_dashboard")

    # Get usage statistics
    usage_queryset = DiscountUsage.objects.filter(
        discount_type=f"{discount_type.capitalize()}Discount", discount_id=discount_id
    )

    # Overall usage stats
    usage_stats = usage_queryset.aggregate(
        total_usage=Count("id"),
        total_savings=Sum("discount_amount"),
        avg_discount=Avg("discount_amount"),
        total_original_value=Sum("original_price"),
        total_final_value=Sum("final_price"),
    )

    # Calculate savings percentage
    if usage_stats["total_original_value"]:
        savings_percentage = (
            usage_stats["total_savings"] / usage_stats["total_original_value"]
        ) * 100
    else:
        savings_percentage = 0

    # Recent usage history (last 20 uses)
    recent_usages = usage_queryset.select_related("user").order_by("-used_at")[:20]

    # Usage by month for the last 6 months - using TruncMonth for database portability
    from django.db.models.functions import TruncMonth

    six_months_ago = timezone.now() - timedelta(days=180)
    monthly_usage = (
        usage_queryset.filter(used_at__gte=six_months_ago)
        .annotate(month=TruncMonth("used_at"))
        .values("month")
        .annotate(usage_count=Count("id"), total_savings=Sum("discount_amount"))
        .order_by("month")
    )

    # Top customers using this discount
    top_customers = (
        usage_queryset.values("user__email", "user__first_name", "user__last_name")
        .annotate(usage_count=Count("id"), total_savings=Sum("discount_amount"))
        .order_by("-usage_count")[:10]
    )

    context = {
        "discount": discount,
        "discount_type": discount_type,
        "related_entity": related_entity,
        "entity_type": entity_type,
        "usage_stats": usage_stats,
        "savings_percentage": round(savings_percentage, 2),
        "recent_usages": recent_usages,
        "monthly_usage": list(monthly_usage),
        "top_customers": top_customers,
    }

    return render(request, "discount_app/admin/discount_detail.html", context)


@login_required
@user_passes_test(is_admin)
def admin_create_platform_discount(request):
    """
    Admin view to create a platform-wide discount.
    """
    if request.method == "POST":
        form = PlatformDiscountForm(request.POST)
        if form.is_valid():
            discount = form.save(commit=False)
            discount.created_by = request.user
            discount.save()

            # Log creation with client IP
            log_discount_creation(
                user=request.user,
                discount=discount,
                request=request,
                discount_type="platform",
            )

            messages.success(
                request,
                f'Platform discount "{discount.name}" has been created successfully!',
            )
            return redirect(
                "discount_app:admin_discount_list", discount_type="platform"
            )
    else:
        form = PlatformDiscountForm()

    context = {
        "form": form,
        "title": "Create Platform Discount",
        "action": "Create",
    }

    return render(request, "discount_app/admin/platform_discount_form.html", context)


@login_required
@user_passes_test(is_admin)
def admin_edit_platform_discount(request, discount_id):
    """
    Admin view to edit a platform-wide discount.
    """
    discount = get_object_or_404(PlatformDiscount, id=discount_id)

    if request.method == "POST":
        form = PlatformDiscountForm(request.POST, instance=discount)
        if form.is_valid():
            form.save()

            messages.success(
                request,
                f'Platform discount "{discount.name}" has been updated successfully!',
            )
            return redirect(
                "discount_app:admin_discount_list", discount_type="platform"
            )
    else:
        form = PlatformDiscountForm(instance=discount)

    context = {
        "form": form,
        "title": "Update Platform Discount",
        "action": "Update",
        "discount": discount,
    }

    return render(request, "discount_app/admin/platform_discount_form.html", context)


@login_required
@user_passes_test(is_admin)
def admin_delete_platform_discount(request, discount_id):
    """
    Admin view to delete a platform-wide discount.
    """
    discount = get_object_or_404(PlatformDiscount, id=discount_id)

    if request.method == "POST":
        discount_name = discount.name
        discount.delete()

        messages.success(
            request,
            f'Platform discount "{discount_name}" has been deleted successfully!',
        )
        return redirect("discount_app:admin_discount_list", discount_type="platform")

    context = {
        "discount": discount,
        "discount_type": "platform",
    }

    return render(request, "discount_app/admin/discount_delete_confirm.html", context)


@login_required
@user_passes_test(is_admin)
def admin_approve_discount(request, discount_type, discount_id):
    """
    Admin view to approve or reject venue/service discounts.
    """
    # Validate discount type and get discount object
    if discount_type == "venue":
        discount = get_object_or_404(VenueDiscount, id=discount_id)
    elif discount_type == "service":
        discount = get_object_or_404(ServiceDiscount, id=discount_id)
    else:
        log_error(
            app_name="discount_app",
            error_type="invalid_discount_type",
            error_message=f"Invalid discount type for approval: {discount_type}",
            user=request.user,
            request=request,
            details={"discount_type": discount_type, "discount_id": discount_id},
        )
        messages.error(request, "Invalid discount type for approval.")
        return redirect("discount_app:admin_discount_dashboard")

    # Log access to approval form
    if request.method == "GET":
        log_user_activity(
            app_name="discount_app",
            activity_type="admin_discount_approval_form_access",
            user=request.user,
            request=request,
            details={
                "discount_type": discount_type,
                "discount_id": discount_id,
                "discount_name": discount.name,
                "current_approval_status": discount.is_approved,
            },
        )

    if request.method == "POST":
        # Handle both form submission and action parameter for test compatibility
        action = request.POST.get("action")
        if action in ["approve", "reject"]:
            # Handle action-based submission (for tests)
            is_approved = action == "approve"
            rejection_reason = request.POST.get("rejection_reason", "")
        else:
            # Handle form-based submission
            form = DiscountApprovalForm(request.POST)
            if form.is_valid():
                is_approved = form.cleaned_data["is_approved"]
                rejection_reason = form.cleaned_data.get("rejection_reason")
            else:
                # Log form validation errors
                log_user_activity(
                    app_name="discount_app",
                    activity_type="admin_discount_approval_failed",
                    user=request.user,
                    request=request,
                    details={
                        "discount_type": discount_type,
                        "discount_id": discount_id,
                        "form_errors": form.errors.as_json(),
                    },
                )
                context = {
                    "form": form,
                    "discount": discount,
                    "discount_type": discount_type,
                }
                return render(
                    request, "discount_app/admin/discount_approval.html", context
                )

        # Process approval/rejection

        # Store original status for logging
        original_status = discount.is_approved

        discount.is_approved = is_approved
        if is_approved:
            discount.approved_by = request.user
            discount.approved_at = timezone.now()

            # Log approval
            log_discount_approval(
                admin_user=request.user,
                discount=discount,
                is_approved=True,
                target_user=discount.created_by,
                request=request,
                discount_type=discount_type,
            )

            messages.success(
                request, f'Discount "{discount.name}" has been approved successfully!'
            )
        else:
            # Log rejection
            log_discount_approval(
                admin_user=request.user,
                discount=discount,
                is_approved=False,
                target_user=discount.created_by,
                rejection_reason=rejection_reason,
                request=request,
                discount_type=discount_type,
            )

            messages.success(request, f'Discount "{discount.name}" has been rejected.')
            try:
                title = "Discount Proposal Rejected"
                message = f"Your discount '{discount.name}' was rejected."
                if rejection_reason:
                    message += f" Reason: {rejection_reason}"
                action_url = reverse("discount_app:provider_discount_list")

                create_notification(
                    user=discount.created_by,
                    notification_type=Notification.SYSTEM,
                    title=title,
                    message=message,
                    action_url=action_url,
                    related_object_id=discount.id,
                    related_object_type=discount_type.capitalize() + "Discount",
                )
                send_notification_email(discount.created_by, title, message, action_url)
            except Exception as e:
                log_error(
                    app_name="discount_app",
                    error_type="discount_rejection_notification_failed",
                    error_message=str(e),
                    user=request.user,
                    request=request,
                    exception=e,
                )

        discount.save()
        return redirect("discount_app:admin_discount_list", discount_type=discount_type)
    else:
        form = DiscountApprovalForm()

    context = {
        "form": form,
        "discount": discount,
        "discount_type": discount_type,
    }

    return render(request, "discount_app/admin/discount_approval.html", context)


@login_required
@user_passes_test(is_admin)
def admin_usage_analytics(request):
    """
    Comprehensive usage analytics dashboard for all discounts.
    """
    # Date range filter
    date_range = request.GET.get("range", "30")  # Default to 30 days

    try:
        days = int(date_range)
    except ValueError:
        days = 30

    start_date = timezone.now() - timedelta(days=days)

    # Overall usage statistics
    overall_stats = DiscountUsage.objects.filter(used_at__gte=start_date).aggregate(
        total_usage=Count("id"),
        total_savings=Sum("discount_amount"),
        total_original_value=Sum("original_price"),
        total_final_value=Sum("final_price"),
        avg_discount=Avg("discount_amount"),
    )

    # Usage by discount type
    usage_by_type = (
        DiscountUsage.objects.filter(used_at__gte=start_date)
        .values("discount_type")
        .annotate(usage_count=Count("id"), total_savings=Sum("discount_amount"))
        .order_by("-usage_count")
    )

    # Top performing discounts
    top_venue_discounts = (
        VenueDiscount.objects.annotate(
            usage_count=Count(
                "discountusage", filter=Q(discountusage__used_at__gte=start_date)
            ),
            total_savings=Sum(
                "discountusage__discount_amount",
                filter=Q(discountusage__used_at__gte=start_date),
            ),
        )
        .filter(usage_count__gt=0)
        .order_by("-usage_count")[:10]
    )

    top_service_discounts = (
        ServiceDiscount.objects.annotate(
            usage_count=Count(
                "discountusage", filter=Q(discountusage__used_at__gte=start_date)
            ),
            total_savings=Sum(
                "discountusage__discount_amount",
                filter=Q(discountusage__used_at__gte=start_date),
            ),
        )
        .filter(usage_count__gt=0)
        .order_by("-usage_count")[:10]
    )

    top_platform_discounts = (
        PlatformDiscount.objects.annotate(
            usage_count=Count(
                "discountusage", filter=Q(discountusage__used_at__gte=start_date)
            ),
            total_savings=Sum(
                "discountusage__discount_amount",
                filter=Q(discountusage__used_at__gte=start_date),
            ),
        )
        .filter(usage_count__gt=0)
        .order_by("-usage_count")[:10]
    )

    # Daily usage trend
    daily_usage = (
        DiscountUsage.objects.filter(used_at__gte=start_date)
        .extra(select={"day": "DATE(used_at)"})
        .values("day")
        .annotate(usage_count=Count("id"), total_savings=Sum("discount_amount"))
        .order_by("day")
    )

    # Top customers by discount usage
    top_customers = (
        DiscountUsage.objects.filter(used_at__gte=start_date)
        .values("user__email", "user__first_name", "user__last_name")
        .annotate(usage_count=Count("id"), total_savings=Sum("discount_amount"))
        .order_by("-usage_count")[:20]
    )

    # Calculate overall savings percentage
    if overall_stats["total_original_value"]:
        overall_savings_percentage = (
            overall_stats["total_savings"] / overall_stats["total_original_value"]
        ) * 100
    else:
        overall_savings_percentage = 0

    context = {
        "overall_stats": overall_stats,
        "overall_savings_percentage": round(overall_savings_percentage, 2),
        "usage_by_type": usage_by_type,
        "top_venue_discounts": top_venue_discounts,
        "top_service_discounts": top_service_discounts,
        "top_platform_discounts": top_platform_discounts,
        "daily_usage": list(daily_usage),
        "top_customers": top_customers,
        "date_range": date_range,
        "start_date": start_date.strftime("%Y-%m-%d"),
        "end_date": timezone.now().strftime("%Y-%m-%d"),
    }

    return render(request, "discount_app/admin/usage_analytics.html", context)
