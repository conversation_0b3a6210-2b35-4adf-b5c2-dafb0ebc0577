"""Admin configuration for discount management."""

# --- Third-Party Imports ---
from django.contrib import admin
from django.utils import timezone
from django.utils.html import format_html

# --- Local App Imports ---
from .models import DiscountUsage, PlatformDiscount, ServiceDiscount, VenueDiscount


@admin.register(VenueDiscount)
class VenueDiscountAdmin(admin.ModelAdmin):
    """Admin interface for venue discounts."""

    list_display = [
        "name",
        "venue",
        "discount_display",
        "status_display",
        "is_approved",
        "start_date",
        "end_date",
        "created_at",
    ]
    list_filter = [
        "discount_type",
        "is_approved",
        "start_date",
        "end_date",
        "created_at",
    ]
    search_fields = ["name", "venue__venue_name", "description"]
    readonly_fields = ["created_at", "updated_at", "approved_at"]

    fieldsets = (
        ("Basic Information", {"fields": ("name", "description", "venue")}),
        (
            "Discount Configuration",
            {
                "fields": (
                    "discount_type",
                    "discount_value",
                    "min_booking_value",
                    "max_discount_amount",
                )
            },
        ),
        ("Schedule", {"fields": ("start_date", "end_date")}),
        ("Approval", {"fields": ("is_approved", "approved_by", "approved_at")}),
        (
            "Metadata",
            {
                "fields": ("created_by", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def discount_display(self, obj):
        """Display discount value with type."""
        if obj.discount_type == "percentage":
            return f"{obj.discount_value}%"
        return f"${obj.discount_value}"

    discount_display.short_description = "Discount"

    def status_display(self, obj):
        """Display current status with color coding."""
        status = obj.get_status()
        colors = {
            "active": "green",
            "scheduled": "orange",
            "expired": "red",
            "cancelled": "gray",
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(status, "black"),
            status.title(),
        )

    status_display.short_description = "Status"


@admin.register(ServiceDiscount)
class ServiceDiscountAdmin(admin.ModelAdmin):
    """Admin interface for service discounts."""

    list_display = [
        "name",
        "service",
        "discount_display",
        "status_display",
        "is_approved",
        "start_date",
        "end_date",
        "created_at",
    ]
    list_filter = [
        "discount_type",
        "is_approved",
        "start_date",
        "end_date",
        "created_at",
    ]
    search_fields = [
        "name",
        "service__service_title",
        "service__venue__venue_name",
        "description",
    ]
    readonly_fields = ["created_at", "updated_at", "approved_at"]

    fieldsets = (
        ("Basic Information", {"fields": ("name", "description", "service")}),
        ("Discount Configuration", {"fields": ("discount_type", "discount_value")}),
        ("Schedule", {"fields": ("start_date", "end_date")}),
        ("Approval", {"fields": ("is_approved", "approved_by", "approved_at")}),
        (
            "Metadata",
            {
                "fields": ("created_by", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def discount_display(self, obj):
        """Display discount value with type."""
        if obj.discount_type == "percentage":
            return f"{obj.discount_value}%"
        return f"${obj.discount_value}"

    discount_display.short_description = "Discount"

    def status_display(self, obj):
        """Display current status with color coding."""
        status = obj.get_status()
        colors = {
            "active": "green",
            "scheduled": "orange",
            "expired": "red",
            "cancelled": "gray",
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(status, "black"),
            status.title(),
        )

    status_display.short_description = "Status"


@admin.register(PlatformDiscount)
class PlatformDiscountAdmin(admin.ModelAdmin):
    """Admin interface for platform discounts."""

    list_display = [
        "name",
        "category",
        "discount_display",
        "status_display",
        "is_featured",
        "start_date",
        "end_date",
        "created_at",
    ]
    list_filter = [
        "discount_type",
        "is_featured",
        "category",
        "start_date",
        "end_date",
        "created_at",
    ]
    search_fields = ["name", "description", "category__name"]
    readonly_fields = ["created_at", "updated_at"]

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("name", "description", "category", "is_featured")},
        ),
        (
            "Discount Configuration",
            {
                "fields": (
                    "discount_type",
                    "discount_value",
                    "min_booking_value",
                    "max_discount_amount",
                )
            },
        ),
        ("Schedule", {"fields": ("start_date", "end_date")}),
        (
            "Metadata",
            {
                "fields": ("created_by", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def discount_display(self, obj):
        """Display discount value with type."""
        if obj.discount_type == "percentage":
            return f"{obj.discount_value}%"
        return f"${obj.discount_value}"

    discount_display.short_description = "Discount"

    def status_display(self, obj):
        """Display current status with color coding."""
        status = obj.get_status()
        colors = {
            "active": "green",
            "scheduled": "orange",
            "expired": "red",
            "cancelled": "gray",
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(status, "black"),
            status.title(),
        )

    status_display.short_description = "Status"


@admin.register(DiscountUsage)
class DiscountUsageAdmin(admin.ModelAdmin):
    """Admin interface for discount usage tracking."""

    list_display = [
        "user",
        "discount_type",
        "discount_name",
        "original_price",
        "discount_amount",
        "final_price",
        "savings_percentage",
        "used_at",
    ]
    list_filter = ["discount_type", "used_at"]
    search_fields = ["user__email", "booking_reference"]
    readonly_fields = ["used_at", "savings_percentage", "savings_amount"]

    fieldsets = (
        (
            "Usage Information",
            {"fields": ("user", "discount_type", "discount_id", "booking_reference")},
        ),
        (
            "Financial Details",
            {
                "fields": (
                    "original_price",
                    "discount_amount",
                    "final_price",
                    "savings_amount",
                    "savings_percentage",
                )
            },
        ),
        ("Metadata", {"fields": ("used_at",)}),
    )

    def discount_name(self, obj):
        """Get the name of the discount that was used."""
        discount = obj.get_discount_object()
        return discount.name if discount else "N/A"

    discount_name.short_description = "Discount Name"

    def savings_percentage(self, obj):
        """Display savings percentage."""
        return f"{obj.get_savings_percentage()}%"

    savings_percentage.short_description = "Savings %"

    def savings_amount(self, obj):
        """Display savings amount."""
        return f"${obj.get_savings_amount()}"

    savings_amount.short_description = "Savings Amount"
