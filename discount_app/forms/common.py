"""Common form classes and validation mixins for discounts."""

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import DiscountType


class DiscountBase(forms.ModelForm):
    """Base class for discount forms with common validation."""

    def clean(self):
        """Common validation for all discount forms."""
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")
        discount_value = cleaned_data.get("discount_value")

        # Validate date range
        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError(_("Start date must be before end date"))

            # Check if start date is not too far in the past
            if start_date < timezone.now() - timezone.timedelta(hours=1):
                raise ValidationError(
                    _("Start date cannot be more than 1 hour in the past")
                )

        # Only perform discount_value validations if it's not None
        # (None means there were validation errors that cleared the field)
        if discount_value is not None:
            self._validate_discount_value_logic(cleaned_data)

        return cleaned_data

    def _validate_discount_value_logic(self, cleaned_data):
        """Override in subclasses for specific discount value validation."""
        pass


class DiscountValueValidationMixin:
    """Shared validation for discount value fields."""

    def clean_discount_value(self):
        discount_value = self.cleaned_data.get("discount_value")
        discount_type = self.cleaned_data.get("discount_type")

        if discount_value is None:
            raise ValidationError(_("Discount value is required"))

        if discount_value <= 0:
            raise ValidationError(_("Discount value must be greater than 0"))

        if discount_type == DiscountType.PERCENTAGE:
            if discount_value > 100:
                raise ValidationError(_("Percentage cannot exceed 100%"))
            if discount_value > 80:
                raise ValidationError(_("Percentage discount cannot exceed 80%"))
        elif discount_type == DiscountType.FIXED_AMOUNT:
            if discount_value > 10000:
                raise ValidationError(_("Fixed amount discount seems too high"))

        return discount_value
