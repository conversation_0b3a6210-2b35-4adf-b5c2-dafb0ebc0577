"""Forms for service and venue discount management."""

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from venues_app.models import Service

from ..models import DiscountType, ServiceDiscount, VenueDiscount
from .common import DiscountBase, DiscountValueValidationMixin


class ServiceDiscountForm(DiscountValueValidationMixin, DiscountBase):
    """Form for creating and editing service discounts by providers."""

    class Meta:
        model = ServiceDiscount
        fields = [
            "service",
            "name",
            "description",
            "discount_type",
            "discount_value",
            "start_date",
            "end_date",
        ]
        widgets = {
            "service": forms.Select(attrs={"class": "form-select", "required": True}),
            "name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": 'Enter discount name (e.g., "Summer Special")',
                    "maxlength": 255,
                }
            ),
            "description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 3,
                    "placeholder": "Optional description of the discount",
                    "maxlength": 500,
                }
            ),
            "discount_type": forms.Select(attrs={"class": "form-select"}),
            "discount_value": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "0.01",
                    "min": "0.01",
                    "placeholder": "Enter discount value",
                }
            ),
            "start_date": forms.DateTimeInput(
                attrs={"class": "form-control", "type": "datetime-local"}
            ),
            "end_date": forms.DateTimeInput(
                attrs={"class": "form-control", "type": "datetime-local"}
            ),
        }
        labels = {
            "service": _("Service"),
            "name": _("Discount Name"),
            "description": _("Description"),
            "discount_type": _("Discount Type"),
            "discount_value": _("Discount Value"),
            "start_date": _("Start Date & Time"),
            "end_date": _("End Date & Time"),
        }
        help_texts = {
            "service": _("Select the service this discount applies to"),
            "name": _("Give your discount a memorable name"),
            "description": _("Optional: Provide additional details about the discount"),
            "discount_type": _("Choose between percentage or fixed amount discount"),
            "discount_value": _(
                "For percentage: max 80%. For fixed amount: enter dollar value"
            ),
            "start_date": _("When the discount becomes active"),
            "end_date": _("When the discount expires"),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        if self.user and hasattr(self.user, "service_provider_profile"):
            try:
                venue = self.user.service_provider_profile.venue
                self.fields["service"].queryset = Service.objects.filter(
                    venue=venue, is_active=True
                ).order_by("service_title")
            except Exception:
                self.fields["service"].queryset = Service.objects.none()
        else:
            self.fields["service"].queryset = Service.objects.none()

        self.fields["service"].required = True
        self.fields["name"].required = True
        self.fields["discount_type"].required = True
        self.fields["discount_value"].required = True
        self.fields["start_date"].required = True
        self.fields["end_date"].required = True

    def clean(self):
        cleaned_data = super().clean()
        service = cleaned_data.get("service")

        if service and self.user:
            if hasattr(self.user, "service_provider_profile"):
                try:
                    venue = self.user.service_provider_profile.venue
                    if service.venue != venue:
                        raise ValidationError(
                            _(
                                "You can only create discounts for services in your own venue"
                            )
                        )
                except Exception:
                    raise ValidationError(
                        _("You must have a venue to create service discounts")
                    )
            else:
                raise ValidationError(
                    _("Only service providers can create service discounts")
                )
        return cleaned_data

    def _validate_discount_value_logic(self, cleaned_data):
        service = cleaned_data.get("service")
        discount_type = cleaned_data.get("discount_type")
        discount_value = cleaned_data.get("discount_value")

        if (
            service
            and discount_type == DiscountType.FIXED_AMOUNT
            and discount_value
            and discount_value >= service.price_min
        ):
            raise ValidationError(
                _(
                    "Fixed amount discount (${}) cannot be equal to or greater than the service price (${})"
                ).format(discount_value, service.price_min)
            )

    def save(self, commit=True):
        service_discount = super().save(commit=False)
        if self.user:
            service_discount.created_by = self.user
        if commit:
            service_discount.save()
        return service_discount


class VenueDiscountForm(DiscountValueValidationMixin, DiscountBase):
    """Form for creating and editing venue-wide discounts by providers."""

    class Meta:
        model = VenueDiscount
        fields = [
            "name",
            "description",
            "discount_type",
            "discount_value",
            "start_date",
            "end_date",
            "min_booking_value",
            "max_discount_amount",
        ]
        widgets = {
            "name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": 'Enter discount name (e.g., "Grand Opening Special")',
                    "maxlength": 255,
                }
            ),
            "description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 3,
                    "placeholder": "Optional description of the discount",
                    "maxlength": 500,
                }
            ),
            "discount_type": forms.Select(attrs={"class": "form-select"}),
            "discount_value": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "0.01",
                    "min": "0.01",
                    "placeholder": "Enter discount value",
                }
            ),
            "start_date": forms.DateTimeInput(
                attrs={"class": "form-control", "type": "datetime-local"}
            ),
            "end_date": forms.DateTimeInput(
                attrs={"class": "form-control", "type": "datetime-local"}
            ),
            "min_booking_value": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "0.01",
                    "min": "0.00",
                    "placeholder": "Minimum booking amount (optional)",
                }
            ),
            "max_discount_amount": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "0.01",
                    "min": "0.01",
                    "placeholder": "Maximum discount cap (optional)",
                }
            ),
        }
        labels = {
            "name": _("Discount Name"),
            "description": _("Description"),
            "discount_type": _("Discount Type"),
            "discount_value": _("Discount Value"),
            "start_date": _("Start Date & Time"),
            "end_date": _("End Date & Time"),
            "min_booking_value": _("Minimum Booking Value"),
            "max_discount_amount": _("Maximum Discount Amount"),
        }
        help_texts = {
            "name": _("Give your venue discount a memorable name"),
            "description": _("Optional: Provide additional details about the discount"),
            "discount_type": _("Choose between percentage or fixed amount discount"),
            "discount_value": _(
                "For percentage: max 80%. For fixed amount: enter dollar value"
            ),
            "start_date": _("When the discount becomes active"),
            "end_date": _("When the discount expires"),
            "min_booking_value": _(
                "Optional: Minimum total booking value to qualify for discount"
            ),
            "max_discount_amount": _(
                "Optional: Cap the maximum discount amount for percentage discounts"
            ),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        self.fields["name"].required = True
        self.fields["discount_type"].required = True
        self.fields["discount_value"].required = True
        self.fields["start_date"].required = True
        self.fields["end_date"].required = True

        self.fields["min_booking_value"].required = False
        self.fields["max_discount_amount"].required = False

    def _validate_discount_value_logic(self, cleaned_data):
        min_booking_value = cleaned_data.get("min_booking_value")
        max_discount_amount = cleaned_data.get("max_discount_amount")
        discount_type = cleaned_data.get("discount_type")
        discount_value = cleaned_data.get("discount_value")

        if min_booking_value is not None and min_booking_value < 0:
            raise ValidationError(_("Minimum booking value cannot be negative"))

        if max_discount_amount is not None and max_discount_amount <= 0:
            raise ValidationError(_("Maximum discount amount must be greater than 0"))

        if (
            discount_type == DiscountType.PERCENTAGE
            and max_discount_amount
            and min_booking_value
            and discount_value
        ):
            theoretical_max = (min_booking_value * discount_value) / 100
            if max_discount_amount > theoretical_max * 10:
                raise ValidationError(
                    _(
                        "Maximum discount amount seems too high compared to minimum booking value and percentage"
                    )
                )

    def save(self, commit=True):
        venue_discount = super().save(commit=False)

        if self.user:
            venue_discount.created_by = self.user
            if hasattr(self.user, "service_provider_profile"):
                try:
                    venue_discount.venue = self.user.service_provider_profile.venue
                except Exception:
                    raise ValidationError(
                        _("You must have a venue to create venue discounts")
                    )
            else:
                raise ValidationError(
                    _(
                        "You must have a service provider profile to create venue discounts"
                    )
                )

        if commit:
            venue_discount.save()
        return venue_discount


class QuickServiceDiscountForm(forms.ModelForm):
    """Form for inline editing of service discounts."""

    class Meta:
        model = ServiceDiscount
        fields = ["discount_value", "start_date", "end_date"]
        widgets = {
            "discount_value": forms.NumberInput(
                attrs={
                    "class": "form-control form-control-sm",
                    "step": "0.01",
                    "min": "0.01",
                }
            ),
            "start_date": forms.DateTimeInput(
                attrs={
                    "class": "form-control form-control-sm",
                    "type": "datetime-local",
                }
            ),
            "end_date": forms.DateTimeInput(
                attrs={
                    "class": "form-control form-control-sm",
                    "type": "datetime-local",
                }
            ),
        }


class QuickVenueDiscountForm(forms.ModelForm):
    """Form for inline editing of venue discounts."""

    class Meta:
        model = VenueDiscount
        fields = ["discount_value", "start_date", "end_date"]
        widgets = {
            "discount_value": forms.NumberInput(
                attrs={
                    "class": "form-control form-control-sm",
                    "step": "0.01",
                    "min": "0.01",
                }
            ),
            "start_date": forms.DateTimeInput(
                attrs={
                    "class": "form-control form-control-sm",
                    "type": "datetime-local",
                }
            ),
            "end_date": forms.DateTimeInput(
                attrs={
                    "class": "form-control form-control-sm",
                    "type": "datetime-local",
                }
            ),
        }
