"""Admin forms for creating and managing discounts."""

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from venues_app.models import Category

from ..models import DiscountType, PlatformDiscount, ServiceDiscount, VenueDiscount
from .common import DiscountBase


class PlatformDiscountForm(forms.ModelForm):
    """Form for creating and editing platform-wide discounts by admins."""

    class Meta:
        model = PlatformDiscount
        fields = [
            "name",
            "description",
            "discount_type",
            "discount_value",
            "start_date",
            "end_date",
            "category",
            "min_booking_value",
            "max_discount_amount",
            "is_featured",
        ]
        widgets = {
            "name": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Enter platform discount name",
                    "maxlength": 255,
                }
            ),
            "description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 3,
                    "placeholder": "Describe this platform-wide discount",
                    "maxlength": 500,
                }
            ),
            "discount_type": forms.Select(attrs={"class": "form-select"}),
            "discount_value": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "0.01",
                    "min": "0.01",
                    "placeholder": "Enter discount value",
                }
            ),
            "start_date": forms.DateTimeInput(
                attrs={"class": "form-control", "type": "datetime-local"}
            ),
            "end_date": forms.DateTimeInput(
                attrs={"class": "form-control", "type": "datetime-local"}
            ),
            "category": forms.Select(attrs={"class": "form-select"}),
            "min_booking_value": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "0.01",
                    "min": "0.00",
                    "placeholder": "Minimum booking amount (optional)",
                }
            ),
            "max_discount_amount": forms.NumberInput(
                attrs={
                    "class": "form-control",
                    "step": "0.01",
                    "min": "0.01",
                    "placeholder": "Maximum discount cap (optional)",
                }
            ),
            "is_featured": forms.CheckboxInput(attrs={"class": "form-check-input"}),
        }
        labels = {
            "name": _("Discount Name"),
            "description": _("Description"),
            "discount_type": _("Discount Type"),
            "discount_value": _("Discount Value"),
            "start_date": _("Start Date & Time"),
            "end_date": _("End Date & Time"),
            "category": _("Category"),
            "min_booking_value": _("Minimum Booking Value"),
            "max_discount_amount": _("Maximum Discount Amount"),
            "is_featured": _("Featured Discount"),
        }
        help_texts = {
            "name": _("Give this platform discount a memorable name"),
            "description": _("Provide details about this platform-wide discount"),
            "discount_type": _("Choose between percentage or fixed amount discount"),
            "discount_value": _(
                "For percentage: max 100%. For fixed amount: enter dollar value"
            ),
            "start_date": _("When the discount becomes active"),
            "end_date": _("When the discount expires"),
            "category": _("Optional: Limit this discount to a specific category"),
            "min_booking_value": _("Optional: Minimum total booking value to qualify"),
            "max_discount_amount": _("Optional: Cap the maximum discount amount"),
            "is_featured": _("Display this discount prominently on the homepage"),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["category"].queryset = Category.objects.filter(
            is_active=True
        ).order_by("category_name")
        self.fields["category"].empty_label = "All Categories"
        self.fields["category"].required = False

        self.fields["name"].required = True
        self.fields["description"].required = True
        self.fields["discount_type"].required = True
        self.fields["discount_value"].required = True
        self.fields["start_date"].required = True
        self.fields["end_date"].required = True

        self.fields["min_booking_value"].required = False
        self.fields["max_discount_amount"].required = False
        self.fields["is_featured"].required = False

    def clean_discount_value(self):
        discount_value = self.cleaned_data.get("discount_value")
        discount_type = self.cleaned_data.get("discount_type")

        if discount_value is None:
            raise ValidationError(_("Discount value is required"))

        if discount_value <= 0:
            raise ValidationError(_("Discount value must be greater than 0"))

        if discount_type == DiscountType.PERCENTAGE:
            if discount_value > 100:
                raise ValidationError(_("Percentage discount cannot exceed 100%"))
        elif discount_type == DiscountType.FIXED_AMOUNT:
            if discount_value > 10000:
                raise ValidationError(_("Fixed amount discount seems too high"))

        return discount_value

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")
        min_booking_value = cleaned_data.get("min_booking_value")
        max_discount_amount = cleaned_data.get("max_discount_amount")

        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError(_("Start date must be before end date"))
            if start_date < timezone.now() - timezone.timedelta(hours=1):
                raise ValidationError(
                    _("Start date cannot be more than 1 hour in the past")
                )

        if min_booking_value is not None and min_booking_value < 0:
            raise ValidationError(_("Minimum booking value cannot be negative"))

        if max_discount_amount is not None and max_discount_amount <= 0:
            raise ValidationError(_("Maximum discount amount must be greater than 0"))

        return cleaned_data


class DiscountFilterForm(forms.Form):
    """Form for filtering discounts in admin views."""

    STATUS_CHOICES = [
        ("", "All Statuses"),
        ("active", "Active"),
        ("scheduled", "Scheduled"),
        ("expired", "Expired"),
        ("pending", "Pending Approval"),
    ]
    TYPE_CHOICES = [
        ("", "All Types"),
        ("percentage", "Percentage"),
        ("fixed_amount", "Fixed Amount"),
    ]

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": "Search discounts..."}
        ),
    )
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={"class": "form-select"}),
    )
    discount_type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={"class": "form-select"}),
    )
    min_value = forms.DecimalField(
        required=False,
        min_value=0,
        widget=forms.NumberInput(
            attrs={"class": "form-control", "placeholder": "Min Value"}
        ),
    )
    max_value = forms.DecimalField(
        required=False,
        min_value=0,
        widget=forms.NumberInput(
            attrs={"class": "form-control", "placeholder": "Max Value"}
        ),
    )


class DiscountApprovalForm(forms.Form):
    """Form for approving or rejecting venue/service discounts."""

    APPROVAL_CHOICES = [
        (True, "Approve"),
        (False, "Reject"),
    ]

    is_approved = forms.ChoiceField(
        choices=APPROVAL_CHOICES,
        widget=forms.RadioSelect(attrs={"class": "form-check-input"}),
        label=_("Approval Decision"),
    )
    rejection_reason = forms.CharField(
        widget=forms.Textarea(
            {
                "class": "form-control",
                "rows": 3,
                "placeholder": "Please provide a reason for rejection (required if rejecting)",
            }
        ),
        required=False,
        label=_("Reason for Rejection"),
        help_text=_("Required if rejecting the discount"),
    )

    def clean(self):
        cleaned_data = super().clean()
        is_approved = cleaned_data.get("is_approved")
        rejection_reason = cleaned_data.get("rejection_reason")

        is_approved = is_approved == "True"
        cleaned_data["is_approved"] = is_approved

        if not is_approved and not rejection_reason:
            self.add_error(
                "rejection_reason", _("Please provide a reason for rejection.")
            )

        return cleaned_data
