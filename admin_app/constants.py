"""User-facing messages used throughout the admin interface."""

from django.utils.translation import gettext_lazy as _

# Authentication messages
ADMIN_LOGIN_SUCCESS = _("Welcome to the admin panel!")
ADMIN_LOGOUT_SUCCESS = _("You have been logged out successfully.")
PERMISSION_DENIED_MESSAGE = _("You do not have permission to access this area.")
INVALID_CREDENTIALS = _("Invalid credentials or insufficient permissions.")
MISSING_FIELDS_ERROR = _("Please provide both email and password.")

# General success messages
BULK_ACTION_SUCCESS = _("Bulk action completed successfully.")
CONTENT_SAVED_SUCCESS = _("Content saved successfully.")
CONTENT_DELETED_SUCCESS = _("Content deleted successfully.")
STAFF_PRIVILEGES_GRANTED = _("Staff privileges granted successfully.")
STAFF_PRIVILEGES_REMOVED = _("Staff privileges removed successfully.")
SERVICE_PROVIDER_VISIBLE = _("Service provider made visible successfully.")
SERVICE_PROVIDER_INVISIBLE = _("Service provider made invisible successfully.")
SITE_CONFIG_UPDATED = _("Site configuration updated successfully.")
SYSTEM_HEALTH_RESOLVED = _("System health event resolved successfully.")
MEDIA_UPLOAD_SUCCESS = _("Media file uploaded successfully.")
USER_STATUS_UPDATED = _("User {status} successfully.")

# Error messages
DASHBOARD_ERROR = _("Error loading dashboard data.")
ANALYTICS_ERROR = _("Error loading analytics data.")
EXPORT_ERROR = _("Error exporting analytics data.")
BULK_ACTION_ERROR = _("An error occurred while performing the bulk action.")
INVALID_BULK_ACTION = _("Invalid bulk action request.")
