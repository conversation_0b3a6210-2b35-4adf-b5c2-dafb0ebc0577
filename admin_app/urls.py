"""URL configuration for the admin interface."""

# --- Third-Party Imports ---
from django.urls import path

# --- Local App Imports ---
from . import views

app_name = "admin_app"

urlpatterns = [
    # Authentication
    path("login/", views.admin_login_view, name="admin_login"),
    path("logout/", views.admin_logout_view, name="admin_logout"),
    # Public Home Page (accessible via /admin-panel/home/<USER>
    path("home/", views.home_view, name="home"),
    # Dashboard
    path("", views.admin_dashboard_view, name="admin_dashboard"),
    # User Management
    path("users/", views.AdminUserListView.as_view(), name="user_list"),
    path(
        "users/<int:user_id>/", views.AdminUserDetailView.as_view(), name="user_detail"
    ),
    path("users/<int:user_id>/edit/", views.admin_user_edit_view, name="user_edit"),
    path(
        "users/bulk-actions/",
        views.admin_bulk_user_actions_view,
        name="bulk_user_actions",
    ),
    path(
        "users/<int:user_id>/provider-approval/",
        views.admin_provider_approval_view,
        name="provider_approval",
    ),
    path(
        "users/pending-providers/",
        views.admin_pending_providers_view,
        name="pending_providers",
    ),
    # Content Management - Static Pages
    path(
        "content/pages/",
        views.AdminStaticPageListView.as_view(),
        name="static_page_list",
    ),
    path(
        "content/pages/create/",
        views.AdminStaticPageCreateView.as_view(),
        name="static_page_create",
    ),
    path(
        "content/pages/<slug:slug>/",
        views.AdminStaticPageDetailView.as_view(),
        name="static_page_detail",
    ),
    path(
        "content/pages/<slug:slug>/edit/",
        views.AdminStaticPageUpdateView.as_view(),
        name="static_page_edit",
    ),
    path(
        "content/pages/<slug:slug>/delete/",
        views.AdminStaticPageDeleteView.as_view(),
        name="static_page_delete",
    ),
    # Content Management - Blog
    path(
        "content/blog/categories/",
        views.AdminBlogCategoryListView.as_view(),
        name="blog_category_list",
    ),
    path(
        "content/blog/categories/create/",
        views.AdminBlogCategoryCreateView.as_view(),
        name="blog_category_create",
    ),
    path(
        "content/blog/posts/",
        views.AdminBlogPostListView.as_view(),
        name="blog_post_list",
    ),
    path(
        "content/blog/posts/create/",
        views.AdminBlogPostCreateView.as_view(),
        name="blog_post_create",
    ),
    path(
        "content/blog/posts/<slug:slug>/edit/",
        views.AdminBlogPostUpdateView.as_view(),
        name="blog_post_edit",
    ),
    path(
        "content/blog/posts/<slug:slug>/delete/",
        views.AdminBlogPostDeleteView.as_view(),
        name="blog_post_delete",
    ),
    # Content Management - Media
    path(
        "content/media/", views.AdminMediaFileListView.as_view(), name="media_file_list"
    ),
    path(
        "content/media/upload/",
        views.AdminMediaFileCreateView.as_view(),
        name="media_file_upload",
    ),
    # Content Management - Homepage Blocks
    path(
        "content/homepage-blocks/",
        views.AdminHomepageBlockListView.as_view(),
        name="homepage_block_list",
    ),
    path(
        "content/homepage-blocks/create/",
        views.AdminHomepageBlockCreateView.as_view(),
        name="homepage_block_create",
    ),
    # System Configuration
    path(
        "system/configuration/",
        views.admin_site_configuration_view,
        name="site_configuration",
    ),
    path(
        "system/announcements/",
        views.AdminAnnouncementListView.as_view(),
        name="announcement_list",
    ),
    path(
        "system/announcements/create/",
        views.AdminAnnouncementCreateView.as_view(),
        name="announcement_create",
    ),
    path(
        "system/health-logs/",
        views.AdminSystemHealthListView.as_view(),
        name="system_health_logs",
    ),
    path(
        "system/health-logs/<int:event_id>/resolve/",
        views.admin_resolve_health_event_view,
        name="resolve_health_event",
    ),
    # Lightweight health check for monitoring tools
    path("health/", views.admin_health_check, name="health_check"),
    # Global search
    path("search/", views.admin_global_search_view, name="admin_search"),
    # Analytics and Reporting
    path(
        "analytics/", views.admin_analytics_dashboard_view, name="analytics_dashboard"
    ),
    path(
        "analytics/export/", views.admin_export_analytics_view, name="export_analytics"
    ),
]
