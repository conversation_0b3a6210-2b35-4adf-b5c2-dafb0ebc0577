"""Custom context processors for admin templates."""

from typing import Any, Dict

from django.http import HttpRequest


def recent_admin_links(request: HttpRequest) -> Dict[str, Any]:
    """Expose recently viewed admin links to templates."""
    # Only provide admin links if session is available and user is staff
    if (
        hasattr(request, "session")
        and hasattr(request, "user")
        and request.user.is_authenticated
        and request.user.is_staff
    ):
        return {"recent_admin_links": request.session.get("admin_recent", [])}
    return {"recent_admin_links": []}
