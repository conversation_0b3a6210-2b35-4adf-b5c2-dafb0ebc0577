"""Forms for editing global site configuration values."""

# --- Third-Party Imports ---
from django import forms
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from utils.forms import AriaLabelMixin

from ..models import SiteConfiguration


class SiteConfigurationForm(AriaLabelMixin, forms.ModelForm):
    """Form for editing site-wide configuration settings."""

    class Meta:
        model = SiteConfiguration
        fields = [
            "site_name",
            "site_tagline",
            "site_description",
            "contact_email",
            "contact_phone",
            "contact_address",
            "facebook_url",
            "twitter_url",
            "instagram_url",
            "linkedin_url",
            "default_meta_title",
            "default_meta_description",
            "default_meta_keywords",
            "google_analytics_id",
            "facebook_pixel_id",
            "maintenance_mode",
            "maintenance_message",
            "allow_user_registration",
            "require_email_verification",
        ]
        widgets = {
            "site_name": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Website name"}
            ),
            "site_tagline": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Short tagline or slogan",
                }
            ),
            "site_description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 4,
                    "placeholder": "Website description for SEO",
                }
            ),
            "contact_email": forms.EmailInput(
                attrs={"class": "form-control", "placeholder": "Primary contact email"}
            ),
            "contact_phone": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Primary contact phone"}
            ),
            "contact_address": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 3,
                    "placeholder": "Physical business address",
                }
            ),
            "facebook_url": forms.URLInput(
                attrs={"class": "form-control", "placeholder": "Facebook page URL"}
            ),
            "twitter_url": forms.URLInput(
                attrs={"class": "form-control", "placeholder": "Twitter profile URL"}
            ),
            "instagram_url": forms.URLInput(
                attrs={"class": "form-control", "placeholder": "Instagram profile URL"}
            ),
            "linkedin_url": forms.URLInput(
                attrs={"class": "form-control", "placeholder": "LinkedIn profile URL"}
            ),
            "default_meta_title": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Default meta title (max 60 characters)",
                    "maxlength": 60,
                }
            ),
            "default_meta_description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 3,
                    "placeholder": "Default meta description (max 160 characters)",
                    "maxlength": 160,
                }
            ),
            "default_meta_keywords": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Default meta keywords, comma-separated",
                }
            ),
            "google_analytics_id": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Google Analytics ID (e.g., GA-XXXXXXXXX-X)",
                }
            ),
            "facebook_pixel_id": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Facebook Pixel ID"}
            ),
            "maintenance_mode": forms.CheckboxInput(
                attrs={"class": "form-check-input"}
            ),
            "maintenance_message": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 4,
                    "placeholder": "Message to display during maintenance",
                }
            ),
            "allow_user_registration": forms.CheckboxInput(
                attrs={"class": "form-check-input"}
            ),
            "require_email_verification": forms.CheckboxInput(
                attrs={"class": "form-check-input"}
            ),
        }


__all__ = ["SiteConfigurationForm"]
