"""Form definitions for creating and editing announcements."""

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from utils.forms import AriaLabelMixin
from utils.sanitization import sanitize_html

from ..models import Announcement


class AnnouncementForm(AriaLabelMixin, forms.ModelForm):
    """Form for creating and editing site announcements."""

    class Meta:
        model = Announcement
        fields = [
            "title",
            "slug",
            "content",
            "announcement_type",
            "display_location",
            "is_active",
            "is_dismissible",
            "start_date",
            "end_date",
            "target_user_roles",
            "priority",
        ]
        widgets = {
            "title": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Enter announcement title",
                }
            ),
            "slug": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "URL slug (auto-generated if empty)",
                }
            ),
            "content": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 6,
                    "placeholder": "Enter announcement content",
                }
            ),
            "announcement_type": forms.Select(attrs={"class": "form-select"}),
            "display_location": forms.Select(attrs={"class": "form-select"}),
            "is_active": forms.CheckboxInput(attrs={"class": "form-check-input"}),
            "is_dismissible": forms.CheckboxInput(attrs={"class": "form-check-input"}),
            "start_date": forms.DateTimeInput(
                attrs={"class": "form-control", "type": "datetime-local"}
            ),
            "end_date": forms.DateTimeInput(
                attrs={"class": "form-control", "type": "datetime-local"}
            ),
            "target_user_roles": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Comma-separated user roles (empty = all users)",
                }
            ),
            "priority": forms.NumberInput(attrs={"class": "form-control", "min": 0}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["slug"].required = False

    def clean_content(self):
        """Sanitize HTML content."""
        content = self.cleaned_data.get("content", "")
        return sanitize_html(content)

    def clean(self):
        """Validate date range."""
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")
        if start_date and end_date and end_date <= start_date:
            raise ValidationError(_("End date must be after start date."))
        return cleaned_data


__all__ = ["AnnouncementForm"]
