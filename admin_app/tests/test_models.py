"""
Unit tests for admin_app models.

This module contains comprehensive unit tests for all model classes in the admin_app,
including StaticPage, BlogCategory, BlogPost, HomepageBlock, MediaFile, BulkActionLog,
SystemHealthLog, SiteConfiguration, and Announcement.
"""

import os

# Standard library imports
import tempfile
from datetime import <PERSON><PERSON><PERSON>
from unittest.mock import Mock, patch

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db import IntegrityError

# Django imports
from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone

# Local imports
from admin_app.models import (
    Announcement,
    BlogCategory,
    BlogPost,
    BulkActionLog,
    HomepageBlock,
    MediaFile,
    SiteConfiguration,
    StaticPage,
    SystemHealthLog,
)

User = get_user_model()


class StaticPageModelTest(TestCase):
    """Test the StaticPage model functionality."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", is_staff=True
        )

    def test_create_static_page(self):
        """Test creating a static page."""
        page = StaticPage.objects.create(
            title="About Us",
            content="This is our about page content.",
            status="published",
            created_by=self.admin_user,
        )

        self.assertEqual(page.title, "About Us")
        self.assertEqual(page.content, "This is our about page content.")
        self.assertEqual(page.status, "published")
        self.assertEqual(page.created_by, self.admin_user)
        self.assertFalse(page.is_featured)
        self.assertIsNotNone(page.created_at)
        self.assertIsNotNone(page.updated_at)

    def test_static_page_string_representation(self):
        """Test static page string representation."""
        page = StaticPage.objects.create(
            title="Privacy Policy",
            content="Privacy policy content.",
            created_by=self.admin_user,
        )
        self.assertEqual(str(page), "Privacy Policy")

    def test_slug_auto_generation(self):
        """Test that slug is auto-generated from title."""
        page = StaticPage.objects.create(
            title="Terms of Service",
            content="Terms content.",
            created_by=self.admin_user,
        )
        self.assertEqual(page.slug, "terms-of-service")

    def test_meta_title_auto_generation(self):
        """Test that meta title is auto-generated from title."""
        page = StaticPage.objects.create(
            title="Contact Information",
            content="Contact content.",
            created_by=self.admin_user,
        )
        self.assertEqual(page.meta_title, "Contact Information")

    def test_meta_description_auto_generation(self):
        """Test that meta description is auto-generated from content."""
        content = (
            "This is a long content that should be truncated for meta description. " * 5
        )
        page = StaticPage.objects.create(
            title="Test Page", content=content, created_by=self.admin_user
        )
        self.assertTrue(len(page.meta_description) <= 160)
        self.assertTrue(page.meta_description.startswith("This is a long content"))

    def test_get_absolute_url(self):
        """Test get_absolute_url method."""
        page = StaticPage.objects.create(
            title="FAQ", content="FAQ content.", created_by=self.admin_user
        )
        expected_url = reverse("admin_app:static_page_edit", kwargs={"slug": page.slug})
        self.assertEqual(page.get_absolute_url(), expected_url)

    def test_status_choices(self):
        """Test that status choices are properly defined."""
        expected_statuses = ["draft", "published", "archived"]

        for status in expected_statuses:
            page = StaticPage.objects.create(
                title=f"Test Page {status}",
                content="Test content.",
                status=status,
                created_by=self.admin_user,
            )
            self.assertEqual(page.status, status)

    def test_default_status_is_draft(self):
        """Test that default status is draft."""
        page = StaticPage.objects.create(
            title="Test Page", content="Test content.", created_by=self.admin_user
        )
        self.assertEqual(page.status, "draft")

    def test_slug_uniqueness(self):
        """Test that slug must be unique."""
        StaticPage.objects.create(
            title="Test Page",
            slug="test-page",
            content="Test content.",
            created_by=self.admin_user,
        )

        with self.assertRaises(IntegrityError):
            StaticPage.objects.create(
                title="Another Test Page",
                slug="test-page",
                content="Another test content.",
                created_by=self.admin_user,
            )


class BlogCategoryModelTest(TestCase):
    """Test the BlogCategory model functionality."""

    def setUp(self):
        """Set up test data."""
        pass

    def test_create_blog_category(self):
        """Test creating a blog category."""
        category = BlogCategory.objects.create(
            name="Wellness Tips", description="Tips for wellness and health."
        )

        self.assertEqual(category.name, "Wellness Tips")
        self.assertEqual(category.description, "Tips for wellness and health.")
        self.assertTrue(category.is_active)
        self.assertIsNotNone(category.created_at)
        self.assertIsNotNone(category.updated_at)

    def test_blog_category_string_representation(self):
        """Test blog category string representation."""
        category = BlogCategory.objects.create(name="Spa Reviews")
        self.assertEqual(str(category), "Spa Reviews")

    def test_slug_auto_generation(self):
        """Test that slug is auto-generated from name."""
        category = BlogCategory.objects.create(name="Beauty & Skincare")
        self.assertEqual(category.slug, "beauty-skincare")

    def test_get_absolute_url(self):
        """Test get_absolute_url method."""
        category = BlogCategory.objects.create(name="Health Tips")
        expected_url = reverse("admin_app:blog_category_list")
        self.assertEqual(category.get_absolute_url(), expected_url)

    def test_name_uniqueness(self):
        """Test that category name must be unique."""
        BlogCategory.objects.create(name="Wellness")

        with self.assertRaises(IntegrityError):
            BlogCategory.objects.create(name="Wellness")

    def test_slug_uniqueness(self):
        """Test that category slug must be unique."""
        BlogCategory.objects.create(name="Test Category", slug="test-category")

        with self.assertRaises(IntegrityError):
            BlogCategory.objects.create(
                name="Another Test Category", slug="test-category"
            )

    def test_default_is_active_true(self):
        """Test that default is_active is True."""
        category = BlogCategory.objects.create(name="Test Category")
        self.assertTrue(category.is_active)

    def test_post_count_property(self):
        """Test post_count property."""
        category = BlogCategory.objects.create(name="Test Category")
        # This will be tested more thoroughly when BlogPost tests are added
        self.assertEqual(category.post_count, 0)


class BlogPostModelTest(TestCase):
    """Test the BlogPost model functionality."""

    def setUp(self):
        """Set up test data."""
        self.author = User.objects.create_user(
            email="<EMAIL>", password="testpass123", is_staff=True
        )
        self.category = BlogCategory.objects.create(name="Test Category")

    def test_create_blog_post(self):
        """Test creating a blog post."""
        post = BlogPost.objects.create(
            title="How to Relax at a Spa",
            content="This is a comprehensive guide to spa relaxation.",
            excerpt="Learn how to maximize your spa experience.",
            category=self.category,
            author=self.author,
            status="published",
        )

        self.assertEqual(post.title, "How to Relax at a Spa")
        self.assertEqual(
            post.content, "This is a comprehensive guide to spa relaxation."
        )
        self.assertEqual(post.excerpt, "Learn how to maximize your spa experience.")
        self.assertEqual(post.category, self.category)
        self.assertEqual(post.author, self.author)
        self.assertEqual(post.status, "published")
        self.assertFalse(post.is_featured)
        self.assertIsNotNone(post.created_at)
        self.assertIsNotNone(post.updated_at)

    def test_blog_post_string_representation(self):
        """Test blog post string representation."""
        post = BlogPost.objects.create(
            title="Spa Benefits",
            content="Content about spa benefits.",
            author=self.author,
        )
        self.assertEqual(str(post), "Spa Benefits")

    def test_slug_auto_generation(self):
        """Test that slug is auto-generated from title."""
        post = BlogPost.objects.create(
            title="Top 10 Spa Treatments",
            content="List of best spa treatments.",
            author=self.author,
        )
        self.assertEqual(post.slug, "top-10-spa-treatments")

    def test_meta_title_auto_generation(self):
        """Test that meta title is auto-generated from title."""
        post = BlogPost.objects.create(
            title="Massage Therapy Benefits",
            content="Benefits of massage therapy.",
            author=self.author,
        )
        self.assertEqual(post.meta_title, "Massage Therapy Benefits")

    def test_meta_description_from_excerpt(self):
        """Test that meta description is generated from excerpt when available."""
        post = BlogPost.objects.create(
            title="Test Post",
            content="Long content here.",
            excerpt="This is a short excerpt.",
            author=self.author,
        )
        self.assertEqual(post.meta_description, "This is a short excerpt.")

    def test_meta_description_from_content(self):
        """Test that meta description is generated from content when excerpt is empty."""
        content = "This is the beginning of the content. " * 10
        post = BlogPost.objects.create(
            title="Test Post", content=content, author=self.author
        )
        self.assertTrue(len(post.meta_description) <= 160)
        self.assertTrue(post.meta_description.startswith("This is the beginning"))

    def test_published_at_set_when_published(self):
        """Test that published_at is set when status changes to published."""
        post = BlogPost.objects.create(
            title="Test Post",
            content="Test content.",
            author=self.author,
            status="draft",
        )
        self.assertIsNone(post.published_at)

        post.status = "published"
        post.save()
        self.assertIsNotNone(post.published_at)

    def test_published_at_cleared_when_unpublished(self):
        """Test that published_at is cleared when status changes from published."""
        post = BlogPost.objects.create(
            title="Test Post",
            content="Test content.",
            author=self.author,
            status="published",
        )
        self.assertIsNotNone(post.published_at)

        post.status = "draft"
        post.save()
        self.assertIsNone(post.published_at)

    def test_get_absolute_url(self):
        """Test get_absolute_url method."""
        post = BlogPost.objects.create(
            title="Test Post", content="Test content.", author=self.author
        )
        expected_url = reverse("admin_app:blog_post_edit", kwargs={"slug": post.slug})
        self.assertEqual(post.get_absolute_url(), expected_url)

    def test_slug_uniqueness(self):
        """Test that slug must be unique."""
        BlogPost.objects.create(
            title="Test Post",
            slug="test-post",
            content="Test content.",
            author=self.author,
        )

        with self.assertRaises(IntegrityError):
            BlogPost.objects.create(
                title="Another Test Post",
                slug="test-post",
                content="Another test content.",
                author=self.author,
            )

    def test_default_status_is_draft(self):
        """Test that default status is draft."""
        post = BlogPost.objects.create(
            title="Test Post", content="Test content.", author=self.author
        )
        self.assertEqual(post.status, "draft")


class HomepageBlockModelTest(TestCase):
    """Test the HomepageBlock model functionality."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", is_staff=True
        )

    def test_create_homepage_block(self):
        """Test creating a homepage block."""
        block = HomepageBlock.objects.create(
            block_type="hero",
            title="Welcome to CozyWish",
            subtitle="Find your perfect spa experience",
            content="Discover amazing spa deals in your area.",
            button_text="Get Started",
            button_url="https://example.com/signup",
            updated_by=self.admin_user,
        )

        self.assertEqual(block.block_type, "hero")
        self.assertEqual(block.title, "Welcome to CozyWish")
        self.assertEqual(block.subtitle, "Find your perfect spa experience")
        self.assertEqual(block.content, "Discover amazing spa deals in your area.")
        self.assertEqual(block.button_text, "Get Started")
        self.assertEqual(block.button_url, "https://example.com/signup")
        self.assertTrue(block.is_active)
        self.assertEqual(block.display_order, 0)
        self.assertEqual(block.updated_by, self.admin_user)

    def test_homepage_block_string_representation(self):
        """Test homepage block string representation."""
        block = HomepageBlock.objects.create(
            block_type="features", title="Our Features", updated_by=self.admin_user
        )
        self.assertEqual(str(block), "Features - Our Features")

    def test_block_type_uniqueness(self):
        """Test that block_type must be unique."""
        HomepageBlock.objects.create(
            block_type="hero", title="Hero Block", updated_by=self.admin_user
        )

        with self.assertRaises(IntegrityError):
            HomepageBlock.objects.create(
                block_type="hero",
                title="Another Hero Block",
                updated_by=self.admin_user,
            )

    def test_default_is_active_true(self):
        """Test that default is_active is True."""
        block = HomepageBlock.objects.create(
            block_type="testimonials",
            title="Customer Testimonials",
            updated_by=self.admin_user,
        )
        self.assertTrue(block.is_active)

    def test_default_display_order_zero(self):
        """Test that default display_order is 0."""
        block = HomepageBlock.objects.create(
            block_type="call_to_action", title="Join Today", updated_by=self.admin_user
        )
        self.assertEqual(block.display_order, 0)

    def test_block_type_choices(self):
        """Test that block type choices are properly defined."""
        expected_types = [
            "hero",
            "how_it_works",
            "top_deals",
            "testimonials",
            "features",
            "call_to_action",
            "custom",
        ]

        for block_type in expected_types:
            block = HomepageBlock.objects.create(
                block_type=block_type,
                title=f"Test {block_type} Block",
                updated_by=self.admin_user,
            )
            self.assertEqual(block.block_type, block_type)


@override_settings(
    SECURE_SSL_REDIRECT=False,
    DEFAULT_FILE_STORAGE="django.core.files.storage.FileSystemStorage",
    MEDIA_ROOT=tempfile.mkdtemp(),
)
class MediaFileModelTest(TestCase):
    """Test the MediaFile model functionality."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", is_staff=True
        )

    def test_create_media_file(self):
        """Test creating a media file."""
        # Create a simple test file
        test_file = SimpleUploadedFile(
            "test_image.jpg", b"fake image content", content_type="image/jpeg"
        )

        media_file = MediaFile.objects.create(
            title="Test Image",
            file=test_file,
            description="A test image file",
            alt_text="Test image alt text",
            tags="test, image, sample",
            uploaded_by=self.admin_user,
        )

        self.assertEqual(media_file.title, "Test Image")
        self.assertEqual(media_file.description, "A test image file")
        self.assertEqual(media_file.alt_text, "Test image alt text")
        self.assertEqual(media_file.tags, "test, image, sample")
        self.assertEqual(media_file.uploaded_by, self.admin_user)
        self.assertTrue(media_file.is_public)
        self.assertEqual(media_file.file_type, "image")

    def test_media_file_string_representation(self):
        """Test media file string representation."""
        test_file = SimpleUploadedFile(
            "document.pdf", b"fake pdf content", content_type="application/pdf"
        )

        media_file = MediaFile.objects.create(
            title="Important Document", file=test_file, uploaded_by=self.admin_user
        )
        self.assertEqual(str(media_file), "Important Document")

    def test_file_type_auto_detection_image(self):
        """Test that file type is auto-detected for images."""
        test_file = SimpleUploadedFile(
            "test.png", b"fake image content", content_type="image/png"
        )

        media_file = MediaFile.objects.create(
            title="Test PNG", file=test_file, uploaded_by=self.admin_user
        )
        self.assertEqual(media_file.file_type, "image")

    def test_file_type_auto_detection_document(self):
        """Test that file type is auto-detected for documents."""
        test_file = SimpleUploadedFile(
            "test.pdf", b"fake pdf content", content_type="application/pdf"
        )

        media_file = MediaFile.objects.create(
            title="Test PDF", file=test_file, uploaded_by=self.admin_user
        )
        self.assertEqual(media_file.file_type, "document")

    def test_file_type_auto_detection_video(self):
        """Test that file type is auto-detected for videos."""
        test_file = SimpleUploadedFile(
            "test.mp4", b"fake video content", content_type="video/mp4"
        )

        media_file = MediaFile.objects.create(
            title="Test Video", file=test_file, uploaded_by=self.admin_user
        )
        self.assertEqual(media_file.file_type, "video")

    def test_file_type_auto_detection_other(self):
        """Test that file type is auto-detected as other for unknown types."""
        test_file = SimpleUploadedFile(
            "test.xyz", b"fake content", content_type="application/octet-stream"
        )

        media_file = MediaFile.objects.create(
            title="Test Unknown", file=test_file, uploaded_by=self.admin_user
        )
        self.assertEqual(media_file.file_type, "other")

    def test_file_extension_property(self):
        """Test file_extension property."""
        test_file = SimpleUploadedFile(
            "test.JPEG", b"fake image content", content_type="image/jpeg"
        )

        media_file = MediaFile.objects.create(
            title="Test JPEG", file=test_file, uploaded_by=self.admin_user
        )
        self.assertEqual(media_file.file_extension, ".jpeg")

    def test_file_size_property(self):
        """Test file_size property."""
        test_content = b"fake content with some length"
        test_file = SimpleUploadedFile(
            "test.txt", test_content, content_type="text/plain"
        )

        media_file = MediaFile.objects.create(
            title="Test File", file=test_file, uploaded_by=self.admin_user
        )
        self.assertEqual(media_file.file_size, len(test_content))

    def test_file_size_formatted_property(self):
        """Test file_size_formatted property."""
        # Test bytes
        test_file = SimpleUploadedFile("test.txt", b"small", content_type="text/plain")
        media_file = MediaFile.objects.create(
            title="Small File", file=test_file, uploaded_by=self.admin_user
        )
        self.assertTrue(media_file.file_size_formatted.endswith(" B"))

    def test_default_is_public_true(self):
        """Test that default is_public is True."""
        test_file = SimpleUploadedFile(
            "test.txt", b"test content", content_type="text/plain"
        )

        media_file = MediaFile.objects.create(
            title="Test File", file=test_file, uploaded_by=self.admin_user
        )
        self.assertTrue(media_file.is_public)


class BulkActionLogModelTest(TestCase):
    """Test the BulkActionLog model functionality."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", is_staff=True
        )

    def test_create_bulk_action_log(self):
        """Test creating a bulk action log."""
        log = BulkActionLog.objects.create(
            action_type="user_activation",
            description="Activated 5 user accounts",
            affected_count=5,
            affected_model="CustomUser",
            affected_ids="1,2,3,4,5",
            executed_by=self.admin_user,
            ip_address="***********",
            user_agent="Mozilla/5.0 Test Browser",
        )

        self.assertEqual(log.action_type, "user_activation")
        self.assertEqual(log.description, "Activated 5 user accounts")
        self.assertEqual(log.affected_count, 5)
        self.assertEqual(log.affected_model, "CustomUser")
        self.assertEqual(log.affected_ids, "1,2,3,4,5")
        self.assertEqual(log.executed_by, self.admin_user)
        self.assertEqual(log.ip_address, "***********")
        self.assertEqual(log.user_agent, "Mozilla/5.0 Test Browser")

    def test_bulk_action_log_string_representation(self):
        """Test bulk action log string representation."""
        log = BulkActionLog.objects.create(
            action_type="user_deletion",
            description="Deleted 3 user accounts",
            affected_count=3,
            executed_by=self.admin_user,
        )
        expected_str = f"User Deletion - 3 items - {log.executed_at}"
        self.assertEqual(str(log), expected_str)

    def test_get_affected_ids_list(self):
        """Test get_affected_ids_list method."""
        log = BulkActionLog.objects.create(
            action_type="user_activation",
            description="Test action",
            affected_ids="1, 2, 3, 4",
            executed_by=self.admin_user,
        )
        expected_ids = ["1", "2", "3", "4"]
        self.assertEqual(log.get_affected_ids_list(), expected_ids)

    def test_get_affected_ids_list_empty(self):
        """Test get_affected_ids_list method with empty string."""
        log = BulkActionLog.objects.create(
            action_type="user_activation",
            description="Test action",
            affected_ids="",
            executed_by=self.admin_user,
        )
        self.assertEqual(log.get_affected_ids_list(), [])

    def test_set_affected_ids_list(self):
        """Test set_affected_ids_list method."""
        log = BulkActionLog.objects.create(
            action_type="user_activation",
            description="Test action",
            executed_by=self.admin_user,
        )
        ids_list = [1, 2, 3, 4, 5]
        log.set_affected_ids_list(ids_list)

        self.assertEqual(log.affected_ids, "1,2,3,4,5")
        self.assertEqual(log.affected_count, 5)

    def test_action_type_choices(self):
        """Test that action type choices are properly defined."""
        expected_types = [
            "user_activation",
            "user_deactivation",
            "user_deletion",
            "provider_approval",
            "provider_rejection",
            "content_publish",
            "content_unpublish",
            "content_deletion",
            "other",
        ]

        for action_type in expected_types:
            log = BulkActionLog.objects.create(
                action_type=action_type,
                description=f"Test {action_type} action",
                executed_by=self.admin_user,
            )
            self.assertEqual(log.action_type, action_type)


class SystemHealthLogModelTest(TestCase):
    """Test the SystemHealthLog model functionality."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", is_staff=True
        )
        self.regular_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

    def test_create_system_health_log(self):
        """Test creating a system health log."""
        log = SystemHealthLog.objects.create(
            event_type="error",
            severity="high",
            title="Database Connection Error",
            description="Failed to connect to the database server",
            affected_user=self.regular_user,
            ip_address="***********00",
            user_agent="Mozilla/5.0 Test Browser",
            additional_data={"error_code": 500, "retry_count": 3},
        )

        self.assertEqual(log.event_type, "error")
        self.assertEqual(log.severity, "high")
        self.assertEqual(log.title, "Database Connection Error")
        self.assertEqual(log.description, "Failed to connect to the database server")
        self.assertEqual(log.affected_user, self.regular_user)
        self.assertEqual(log.ip_address, "***********00")
        self.assertEqual(log.user_agent, "Mozilla/5.0 Test Browser")
        self.assertEqual(log.additional_data["error_code"], 500)
        self.assertFalse(log.is_resolved)

    def test_system_health_log_string_representation(self):
        """Test system health log string representation."""
        log = SystemHealthLog.objects.create(
            event_type="warning",
            severity="medium",
            title="High Memory Usage",
            description="Memory usage is above 80%",
        )
        expected_str = f"Warning - High Memory Usage - {log.recorded_at}"
        self.assertEqual(str(log), expected_str)

    def test_resolve_method(self):
        """Test resolve method."""
        log = SystemHealthLog.objects.create(
            event_type="error",
            severity="critical",
            title="System Error",
            description="Critical system error occurred",
        )

        self.assertFalse(log.is_resolved)
        self.assertIsNone(log.resolved_by)
        self.assertIsNone(log.resolved_at)

        result = log.resolve(self.admin_user, "Fixed by restarting service")

        self.assertTrue(result)
        self.assertTrue(log.is_resolved)
        self.assertEqual(log.resolved_by, self.admin_user)
        self.assertIsNotNone(log.resolved_at)
        self.assertEqual(log.resolution_notes, "Fixed by restarting service")

    def test_event_type_choices(self):
        """Test that event type choices are properly defined."""
        expected_types = [
            "error",
            "warning",
            "info",
            "login_attempt",
            "failed_login",
            "security_alert",
            "uptime",
            "downtime",
            "performance",
            "maintenance",
        ]

        for event_type in expected_types:
            log = SystemHealthLog.objects.create(
                event_type=event_type,
                severity="low",
                title=f"Test {event_type} Event",
                description=f"Test {event_type} description",
            )
            self.assertEqual(log.event_type, event_type)

    def test_severity_choices(self):
        """Test that severity choices are properly defined."""
        expected_severities = ["low", "medium", "high", "critical"]

        for severity in expected_severities:
            log = SystemHealthLog.objects.create(
                event_type="info",
                severity=severity,
                title=f"Test {severity} Event",
                description=f"Test {severity} description",
            )
            self.assertEqual(log.severity, severity)

    def test_default_severity_is_low(self):
        """Test that default severity is low."""
        log = SystemHealthLog.objects.create(
            event_type="info", title="Test Event", description="Test description"
        )
        self.assertEqual(log.severity, "low")

    def test_default_is_resolved_false(self):
        """Test that default is_resolved is False."""
        log = SystemHealthLog.objects.create(
            event_type="error",
            severity="medium",
            title="Test Error",
            description="Test error description",
        )
        self.assertFalse(log.is_resolved)


class SiteConfigurationModelTest(TestCase):
    """Test the SiteConfiguration model functionality."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", is_staff=True
        )

    def test_create_site_configuration(self):
        """Test creating a site configuration."""
        config = SiteConfiguration.objects.create(
            site_name="CozyWish Test",
            site_tagline="Your wellness destination",
            site_description="Find the best spa and wellness deals",
            contact_email="<EMAIL>",
            contact_phone="******-123-4567",
            contact_address="123 Wellness St, Spa City, SC 12345",
            facebook_url="https://facebook.com/cozywish",
            twitter_url="https://twitter.com/cozywish",
            instagram_url="https://instagram.com/cozywish",
            linkedin_url="https://linkedin.com/company/cozywish",
            default_meta_title="CozyWish - Spa & Wellness Deals",
            default_meta_description="Discover amazing spa and wellness deals in your area",
            default_meta_keywords="spa, wellness, massage, deals, discounts",
            google_analytics_id="GA-*********-1",
            facebook_pixel_id="*********",
            maintenance_mode=False,
            maintenance_message="Site under maintenance",
            allow_user_registration=True,
            require_email_verification=True,
            updated_by=self.admin_user,
        )

        self.assertEqual(config.site_name, "CozyWish Test")
        self.assertEqual(config.site_tagline, "Your wellness destination")
        self.assertEqual(config.contact_email, "<EMAIL>")
        self.assertEqual(config.contact_phone, "******-123-4567")
        self.assertEqual(config.facebook_url, "https://facebook.com/cozywish")
        self.assertEqual(config.default_meta_title, "CozyWish - Spa & Wellness Deals")
        self.assertEqual(config.google_analytics_id, "GA-*********-1")
        self.assertFalse(config.maintenance_mode)
        self.assertTrue(config.allow_user_registration)
        self.assertTrue(config.require_email_verification)
        self.assertEqual(config.updated_by, self.admin_user)

    def test_site_configuration_string_representation(self):
        """Test site configuration string representation."""
        config = SiteConfiguration.objects.create(
            site_name="Test Site", updated_by=self.admin_user
        )
        self.assertEqual(str(config), "Test Site Configuration")

    def test_singleton_pattern(self):
        """Test that only one SiteConfiguration instance can exist."""
        config1 = SiteConfiguration.objects.create(
            site_name="First Config", updated_by=self.admin_user
        )

        config2 = SiteConfiguration.objects.create(
            site_name="Second Config", updated_by=self.admin_user
        )

        # Only one instance should exist
        self.assertEqual(SiteConfiguration.objects.count(), 1)

        # The second instance should have replaced the first
        remaining_config = SiteConfiguration.objects.first()
        self.assertEqual(remaining_config.site_name, "Second Config")

    def test_get_instance_method(self):
        """Test get_instance class method."""
        # Should create instance if none exists
        config = SiteConfiguration.get_instance()
        self.assertIsInstance(config, SiteConfiguration)
        self.assertEqual(config.pk, 1)

        # Should return existing instance
        config2 = SiteConfiguration.get_instance()
        self.assertEqual(config.pk, config2.pk)

    def test_default_values(self):
        """Test default values for site configuration."""
        config = SiteConfiguration.objects.create(updated_by=self.admin_user)

        self.assertEqual(config.site_name, "CozyWish")
        self.assertEqual(config.contact_email, "<EMAIL>")
        self.assertFalse(config.maintenance_mode)
        self.assertTrue(config.allow_user_registration)
        self.assertTrue(config.require_email_verification)


class AnnouncementModelTest(TestCase):
    """Test the Announcement model functionality."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", is_staff=True
        )
        self.regular_user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

    def test_create_announcement(self):
        """Test creating an announcement."""
        start_date = timezone.now()
        end_date = start_date + timedelta(days=7)

        announcement = Announcement.objects.create(
            title="Welcome to CozyWish!",
            content="We are excited to launch our new spa booking platform.",
            announcement_type="info",
            display_location="homepage",
            is_active=True,
            is_dismissible=True,
            start_date=start_date,
            end_date=end_date,
            target_user_roles="customer,provider",
            priority=5,
            created_by=self.admin_user,
            updated_by=self.admin_user,
        )

        self.assertEqual(announcement.title, "Welcome to CozyWish!")
        self.assertEqual(
            announcement.content,
            "We are excited to launch our new spa booking platform.",
        )
        self.assertEqual(announcement.announcement_type, "info")
        self.assertEqual(announcement.display_location, "homepage")
        self.assertTrue(announcement.is_active)
        self.assertTrue(announcement.is_dismissible)
        self.assertEqual(announcement.start_date, start_date)
        self.assertEqual(announcement.end_date, end_date)
        self.assertEqual(announcement.target_user_roles, "customer,provider")
        self.assertEqual(announcement.priority, 5)
        self.assertEqual(announcement.created_by, self.admin_user)

    def test_announcement_string_representation(self):
        """Test announcement string representation."""
        announcement = Announcement.objects.create(
            title="System Maintenance",
            content="Scheduled maintenance tonight.",
            created_by=self.admin_user,
        )
        self.assertEqual(str(announcement), "System Maintenance")

    def test_is_current_property_active(self):
        """Test is_current property when announcement is active and within date range."""
        start_date = timezone.now() - timedelta(days=1)
        end_date = timezone.now() + timedelta(days=1)

        announcement = Announcement.objects.create(
            title="Current Announcement",
            content="This is currently active.",
            is_active=True,
            start_date=start_date,
            end_date=end_date,
            created_by=self.admin_user,
        )
        self.assertTrue(announcement.is_current)

    def test_is_current_property_inactive(self):
        """Test is_current property when announcement is inactive."""
        announcement = Announcement.objects.create(
            title="Inactive Announcement",
            content="This is inactive.",
            is_active=False,
            created_by=self.admin_user,
        )
        self.assertFalse(announcement.is_current)

    def test_is_current_property_future(self):
        """Test is_current property when announcement is in the future."""
        start_date = timezone.now() + timedelta(days=1)
        end_date = timezone.now() + timedelta(days=7)

        announcement = Announcement.objects.create(
            title="Future Announcement",
            content="This will be active later.",
            is_active=True,
            start_date=start_date,
            end_date=end_date,
            created_by=self.admin_user,
        )
        self.assertFalse(announcement.is_current)

    def test_is_current_property_expired(self):
        """Test is_current property when announcement has expired."""
        start_date = timezone.now() - timedelta(days=7)
        end_date = timezone.now() - timedelta(days=1)

        announcement = Announcement.objects.create(
            title="Expired Announcement",
            content="This has expired.",
            is_active=True,
            start_date=start_date,
            end_date=end_date,
            created_by=self.admin_user,
        )
        self.assertFalse(announcement.is_current)

    def test_is_visible_to_user_all_users(self):
        """Test is_visible_to_user when no target roles specified."""
        announcement = Announcement.objects.create(
            title="For All Users",
            content="This is for everyone.",
            is_active=True,
            target_user_roles="",
            created_by=self.admin_user,
        )
        self.assertTrue(announcement.is_visible_to_user(self.regular_user))

    def test_is_visible_to_user_specific_role(self):
        """Test is_visible_to_user with specific target roles."""
        # Set user role
        self.regular_user.role = "customer"
        self.regular_user.save()

        announcement = Announcement.objects.create(
            title="For Customers",
            content="This is for customers only.",
            is_active=True,
            target_user_roles="customer",
            created_by=self.admin_user,
        )
        self.assertTrue(announcement.is_visible_to_user(self.regular_user))

    def test_is_visible_to_user_wrong_role(self):
        """Test is_visible_to_user with wrong user role."""
        # Set user role
        self.regular_user.role = "customer"
        self.regular_user.save()

        announcement = Announcement.objects.create(
            title="For Providers",
            content="This is for providers only.",
            is_active=True,
            target_user_roles="provider",
            created_by=self.admin_user,
        )
        self.assertFalse(announcement.is_visible_to_user(self.regular_user))

    def test_default_values(self):
        """Test default values for announcement."""
        announcement = Announcement.objects.create(
            title="Test Announcement",
            content="Test content.",
            created_by=self.admin_user,
        )

        self.assertEqual(announcement.announcement_type, "info")
        self.assertEqual(announcement.display_location, "top_banner")
        self.assertTrue(announcement.is_active)
        self.assertTrue(announcement.is_dismissible)
        self.assertEqual(announcement.priority, 0)

    def test_slug_auto_generation(self):
        """Test that slug is auto-generated from title."""
        announcement = Announcement.objects.create(
            title="Grand Opening Special", content="Details", created_by=self.admin_user
        )
        self.assertEqual(announcement.slug, "grand-opening-special")

    def test_title_unique_constraint(self):
        """Titles should be unique."""
        Announcement.objects.create(
            title="Unique Title", content="One", created_by=self.admin_user
        )
        with self.assertRaises(IntegrityError):
            Announcement.objects.create(
                title="Unique Title", content="Two", created_by=self.admin_user
            )

    def test_announcement_type_choices(self):
        """Test that announcement type choices are properly defined."""
        expected_types = ["info", "success", "warning", "danger", "promotion"]

        for announcement_type in expected_types:
            announcement = Announcement.objects.create(
                title=f"Test {announcement_type} Announcement",
                content="Test content.",
                announcement_type=announcement_type,
                created_by=self.admin_user,
            )
            self.assertEqual(announcement.announcement_type, announcement_type)

    def test_display_location_choices(self):
        """Test that display location choices are properly defined."""
        expected_locations = ["top_banner", "homepage", "dashboard", "all_pages"]

        for location in expected_locations:
            announcement = Announcement.objects.create(
                title=f"Test {location} Announcement",
                content="Test content.",
                display_location=location,
                created_by=self.admin_user,
            )
            self.assertEqual(announcement.display_location, location)
