"""
Test utilities for admin_app tests.

This module provides utility functions for creating test data and mock objects
used across the admin_app test suite.
"""

import io

from PIL import Image

from django.core.files.uploadedfile import SimpleUploadedFile


def create_test_image(
    filename="test_image.jpg", format="JPEG", size=(100, 100), color="red"
):
    """
    Create a test image file for use in tests.

    Args:
        filename (str): Name of the image file
        format (str): Image format (JPEG, PNG, etc.)
        size (tuple): Image dimensions (width, height)
        color (str): Image color

    Returns:
        SimpleUploadedFile: A test image file
    """
    # Create a simple image using PIL
    image = Image.new("RGB", size, color)

    # Save to BytesIO buffer
    buffer = io.BytesIO()
    image.save(buffer, format=format)
    buffer.seek(0)

    # Determine content type
    content_type = f"image/{format.lower()}"
    if format.upper() == "JPEG":
        content_type = "image/jpeg"

    return SimpleUploadedFile(filename, buffer.getvalue(), content_type=content_type)


def create_large_test_image(filename="large_image.jpg", size_mb=6):
    """
    Create a large test image file for testing file size validation.

    Args:
        filename (str): Name of the image file
        size_mb (int): Approximate size in MB

    Returns:
        SimpleUploadedFile: A large test image file
    """
    # Create a small valid image first
    image = Image.new("RGB", (100, 100), "red")
    buffer = io.BytesIO()
    image.save(buffer, format="JPEG")

    # Get the image data
    image_data = buffer.getvalue()

    # Create a large file by padding with extra data to reach the desired size
    target_size = size_mb * 1024 * 1024
    padding_size = target_size - len(image_data)

    # Create large content by repeating the image data
    large_content = image_data + (b"0" * padding_size)

    return SimpleUploadedFile(filename, large_content, content_type="image/jpeg")


def create_invalid_image_file(filename="invalid.txt"):
    """
    Create an invalid image file for testing validation.

    Args:
        filename (str): Name of the file

    Returns:
        SimpleUploadedFile: A non-image file
    """
    return SimpleUploadedFile(
        filename, b"This is not an image file", content_type="text/plain"
    )
