"""View package for admin_app.

This package organizes the monolithic views module into
feature-specific submodules for better maintainability.
All views are re-exported here for backward compatibility.
"""

# Analytics
from .analytics import admin_analytics_dashboard_view, admin_export_analytics_view

# Authentication
from .auth import admin_login_view, admin_logout_view

# Blog management
from .blog import (
    AdminBlogCategoryCreateView,
    AdminBlogCategoryListView,
    AdminBlogPostCreateView,
    AdminBlogPostDeleteView,
    AdminBlogPostListView,
    AdminBlogPostUpdateView,
)

# Common utilities
from .common import ITEMS_PER_PAGE, User, admin_required, home_view, is_admin_user

# Content management
from .content import (
    AdminHomepageBlockCreateView,
    AdminHomepageBlockListView,
    AdminMediaFileCreateView,
    AdminMediaFileListView,
    AdminStaticPageCreateView,
    AdminStaticPageDeleteView,
    AdminStaticPageDetailView,
    AdminStaticPageListView,
    AdminStaticPageUpdateView,
)

# Dashboard
from .dashboard import admin_dashboard_view

# System health and misc utilities
from .health import (
    AdminSystemHealthListView,
    admin_global_search_view,
    admin_health_check,
    admin_resolve_health_event_view,
)

# Media management
from .media import AdminMediaFileCreateView, AdminMediaFileListView

# System configuration
from .system import (
    AdminAnnouncementCreateView,
    AdminAnnouncementListView,
    admin_site_configuration_view,
)

# User management
from .users import (
    AdminUserDetailView,
    AdminUserListView,
    admin_bulk_user_actions_view,
    admin_pending_providers_view,
    admin_provider_approval_view,
    admin_user_edit_view,
)

__all__ = [
    "admin_required",
    "is_admin_user",
    "ITEMS_PER_PAGE",
    "User",
    "home_view",
    "admin_login_view",
    "admin_logout_view",
    "admin_dashboard_view",
    "AdminUserListView",
    "AdminUserDetailView",
    "admin_user_edit_view",
    "admin_bulk_user_actions_view",
    "admin_provider_approval_view",
    "admin_pending_providers_view",
    "AdminStaticPageListView",
    "AdminStaticPageCreateView",
    "AdminStaticPageDetailView",
    "AdminStaticPageUpdateView",
    "AdminStaticPageDeleteView",
    "AdminHomepageBlockListView",
    "AdminHomepageBlockCreateView",
    "AdminBlogCategoryListView",
    "AdminBlogCategoryCreateView",
    "AdminBlogPostListView",
    "AdminBlogPostCreateView",
    "AdminBlogPostUpdateView",
    "AdminBlogPostDeleteView",
    "admin_site_configuration_view",
    "AdminAnnouncementListView",
    "AdminAnnouncementCreateView",
    "admin_analytics_dashboard_view",
    "admin_export_analytics_view",
    "AdminSystemHealthListView",
    "admin_resolve_health_event_view",
    "admin_health_check",
    "admin_global_search_view",
    "AdminMediaFileListView",
    "AdminMediaFileCreateView",
]
