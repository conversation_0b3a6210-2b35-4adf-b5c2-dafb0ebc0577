"""System health and search views for the admin panel."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.http import require_http_methods, require_POST
from django.views.generic import ListView

# --- Local App Imports ---
from admin_app.constants import SYSTEM_HEALTH_RESOLVED
from admin_app.logging_utils import log_admin_activity
from admin_app.models import Announcement, StaticPage, SystemHealthLog

from .common import ITEMS_PER_PAGE, admin_required, is_admin_user

User = get_user_model()
logger = logging.getLogger(__name__)


# System Health Monitoring Views
class AdminSystemHealthListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List view for system health events."""

    model = SystemHealthLog
    template_name = "admin_app/system/health_logs.html"
    context_object_name = "events"
    paginate_by = ITEMS_PER_PAGE
    login_url = "admin_app:admin_login"

    def test_func(self):
        return is_admin_user(self.request.user)

    def get_queryset(self):
        """Filter events based on search parameters."""
        queryset = SystemHealthLog.objects.order_by("-recorded_at")

        event_type = self.request.GET.get("event_type", "").strip()
        severity = self.request.GET.get("severity", "").strip()
        resolved = self.request.GET.get("resolved", "").strip()

        if event_type:
            queryset = queryset.filter(event_type=event_type)

        if severity:
            queryset = queryset.filter(severity=severity)

        if resolved == "true":
            queryset = queryset.filter(is_resolved=True)
        elif resolved == "false":
            queryset = queryset.filter(is_resolved=False)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add filter options
        context["event_types"] = SystemHealthLog.EVENT_TYPE_CHOICES
        context["severities"] = SystemHealthLog.SEVERITY_CHOICES

        # Add statistics
        context["total_events"] = self.get_queryset().count()
        context["unresolved_critical"] = SystemHealthLog.objects.filter(
            severity="critical", is_resolved=False
        ).count()
        context["unresolved_high"] = SystemHealthLog.objects.filter(
            severity="high", is_resolved=False
        ).count()

        return context


@admin_required
@require_POST
def admin_resolve_health_event_view(request, event_id):
    """Resolve a system health event."""
    event = get_object_or_404(SystemHealthLog, id=event_id)
    resolution_notes = request.POST.get("resolution_notes", "").strip()

    event.resolve(request.user, resolution_notes)

    log_admin_activity(
        admin_user=request.user,
        activity_type="health_event_resolved",
        request=request,
        details={"event_title": event.title, "resolution_notes": resolution_notes},
    )
    messages.success(request, SYSTEM_HEALTH_RESOLVED)

    return redirect("admin_app:system_health_logs")


@require_http_methods(["GET"])
def admin_health_check(request):
    """Simple health check endpoint for monitoring tools."""
    return JsonResponse({"status": "ok"})


@admin_required
def admin_global_search_view(request):
    """Search across users, pages, and announcements."""
    query = request.GET.get("q", "").strip()
    users = StaticPage.objects.none()
    pages = StaticPage.objects.none()
    announcements = Announcement.objects.none()

    if query:
        users = User.objects.filter(
            Q(email__icontains=query)
            | Q(first_name__icontains=query)
            | Q(last_name__icontains=query)
        )[:10]
        pages = StaticPage.objects.filter(title__icontains=query)[:10]
        announcements = Announcement.objects.filter(title__icontains=query)[:10]

    context = {
        "query": query,
        "user_results": users,
        "page_results": pages,
        "announcement_results": announcements,
    }
    return render(request, "admin_app/search_results.html", context)
