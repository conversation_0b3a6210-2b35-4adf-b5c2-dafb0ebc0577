"""Views for editing site settings and announcements."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.shortcuts import redirect, render
from django.urls import reverse_lazy
from django.views.generic import CreateView, ListView

# --- Local App Imports ---
from admin_app.constants import CONTENT_SAVED_SUCCESS, SITE_CONFIG_UPDATED
from admin_app.forms import AnnouncementForm, SiteConfigurationForm
from admin_app.logging_utils import log_admin_activity, log_system_configuration_event
from admin_app.models import Announcement, SiteConfiguration

from .common import ITEMS_PER_PAGE, admin_required, is_admin_user

logger = logging.getLogger(__name__)


# System Configuration Views
@admin_required
def admin_site_configuration_view(request):
    """Manage site-wide configuration settings."""
    site_config = SiteConfiguration.get_instance()

    if request.method == "POST":
        form = SiteConfigurationForm(request.POST, instance=site_config)

        if form.is_valid():
            # Capture changes for logging
            old_values = {}
            new_values = {}

            for field in form.changed_data:
                old_values[field] = getattr(site_config, field)
                new_values[field] = form.cleaned_data[field]

            form.instance.updated_by = request.user
            form.save()

            # Log system configuration event
            log_system_configuration_event(
                action_type="site_config_updated",
                admin_user=request.user,
                config_section="site_settings",
                request=request,
                details={
                    "updated_fields": form.changed_data,
                    "config_type": "site_configuration",
                    "update_method": "admin_panel",
                },
                changes={"old_values": old_values, "new_values": new_values},
            )

            messages.success(request, SITE_CONFIG_UPDATED)
            return redirect("admin_app:site_configuration")
    else:
        form = SiteConfigurationForm(instance=site_config)

    context = {
        "form": form,
        "site_config": site_config,
    }

    return render(request, "admin_app/system/site_configuration.html", context)


class AdminAnnouncementListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List view for managing announcements."""

    model = Announcement
    template_name = "admin_app/system/announcement_list.html"
    context_object_name = "announcements"
    paginate_by = ITEMS_PER_PAGE
    login_url = "admin_app:admin_login"

    def test_func(self):
        return is_admin_user(self.request.user)

    def get_queryset(self):
        return Announcement.objects.order_by("-priority", "-created_at")


class AdminAnnouncementCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """Create view for announcements."""

    model = Announcement
    form_class = AnnouncementForm
    template_name = "admin_app/system/announcement_create.html"
    success_url = reverse_lazy("admin_app:announcement_list")
    login_url = "admin_app:admin_login"

    def test_func(self):
        return is_admin_user(self.request.user)

    def form_valid(self, form):
        """Set the created_by and updated_by fields."""
        form.instance.created_by = self.request.user
        form.instance.updated_by = self.request.user

        response = super().form_valid(form)

        log_admin_activity(
            admin_user=self.request.user,
            activity_type="announcement_created",
            request=self.request,
            details={
                "announcement_title": self.object.title,
                "announcement_type": self.object.announcement_type,
            },
        )
        messages.success(self.request, CONTENT_SAVED_SUCCESS)

        return response
