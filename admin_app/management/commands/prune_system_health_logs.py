"""Management command to prune old system health log entries."""

# --- Standard Library Imports ---
from datetime import timedelta

# --- Third-Party Imports ---
from django.core.management.base import BaseCommand
from django.utils import timezone

# --- Local App Imports ---
from admin_app.models import SystemHealthLog


class Command(BaseCommand):
    """Delete system health logs older than the specified number of days."""

    help = "Prune SystemHealthLog entries older than the given number of days"

    def add_arguments(self, parser):
        parser.add_argument(
            "--days",
            type=int,
            default=180,
            help="Number of days to keep logs (default: 180)",
        )

    def handle(self, *args, **options):
        days = options["days"]
        cutoff = timezone.now() - timedelta(days=days)
        old_logs = SystemHealthLog.objects.filter(recorded_at__lt=cutoff)
        deleted, _ = old_logs.delete()
        self.stdout.write(
            self.style.SUCCESS(
                f"Deleted {deleted} system health log(s) older than {days} days."
            )
        )
