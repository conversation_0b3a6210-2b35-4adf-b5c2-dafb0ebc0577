"""
Management command to seed admin_app with realistic test data.
Creates static pages, blog posts, homepage blocks, and system configurations.
"""

import random
from datetime import timedelta

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone

from admin_app.models import (
    Announcement,
    BlogCategory,
    BlogPost,
    HomepageBlock,
    SiteConfiguration,
    StaticPage,
)

User = get_user_model()


class Command(BaseCommand):
    """Seed admin_app with realistic test data."""

    help = "Seed admin_app with static pages, blog content, and configurations"

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing admin app data before seeding",
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(self.style.SUCCESS("🌱 Starting admin_app data seeding..."))

        if options["clear"]:
            self.clear_existing_data()

        with transaction.atomic():
            self.create_static_pages()
            self.create_blog_categories()
            self.create_blog_posts()
            self.create_homepage_blocks()
            self.create_site_configuration()
            self.create_announcements()

        self.stdout.write(
            self.style.SUCCESS("✅ Admin app data seeding completed successfully!")
        )

    def clear_existing_data(self):
        """Clear existing admin app data."""
        self.stdout.write("🧹 Clearing existing admin app data...")

        try:
            BlogPost.objects.all().delete()
            BlogCategory.objects.all().delete()
            StaticPage.objects.all().delete()
            HomepageBlock.objects.all().delete()
            SiteConfiguration.objects.all().delete()
            Announcement.objects.all().delete()
            self.stdout.write("   ✅ Existing admin app data cleared")
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"   ⚠️ Warning during data clearing: {str(e)}")
            )

    def create_static_pages(self):
        """Create static pages for the website."""
        self.stdout.write("📄 Creating static pages...")

        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.filter(role=User.ADMIN).first()

        static_pages = [
            {
                "title": "About CozyWish",
                "slug": "about",
                "content": """
                <h2>Welcome to CozyWish</h2>
                <p>CozyWish is your premier destination for discovering and booking exceptional spa and wellness services. We connect you with the finest wellness providers in your area, offering everything from relaxing massages to rejuvenating beauty treatments.</p>

                <h3>Our Mission</h3>
                <p>To make wellness accessible and affordable for everyone while supporting local spa and wellness businesses.</p>

                <h3>Why Choose CozyWish?</h3>
                <ul>
                    <li>Curated selection of verified wellness providers</li>
                    <li>Exclusive discounts and special offers</li>
                    <li>Easy online booking system</li>
                    <li>Secure payment processing</li>
                    <li>Customer reviews and ratings</li>
                </ul>
                """,
                "meta_description": "Learn about CozyWish, your premier spa and wellness booking platform.",
                "status": "published",
            },
            {
                "title": "Privacy Policy",
                "slug": "privacy-policy",
                "content": """
                <h2>Privacy Policy</h2>
                <p><strong>Last updated:</strong> December 2024</p>

                <h3>Information We Collect</h3>
                <p>We collect information you provide directly to us, such as when you create an account, make a booking, or contact us for support.</p>

                <h3>How We Use Your Information</h3>
                <ul>
                    <li>To provide and maintain our services</li>
                    <li>To process bookings and payments</li>
                    <li>To communicate with you about your account</li>
                    <li>To improve our services</li>
                </ul>

                <h3>Information Sharing</h3>
                <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

                <h3>Contact Us</h3>
                <p>If you have questions about this Privacy Policy, please contact <NAME_EMAIL></p>
                """,
                "meta_description": "CozyWish Privacy Policy - How we collect, use, and protect your personal information.",
                "status": "published",
            },
            {
                "title": "Terms of Service",
                "slug": "terms-of-service",
                "content": """
                <h2>Terms of Service</h2>
                <p><strong>Last updated:</strong> December 2024</p>

                <h3>Acceptance of Terms</h3>
                <p>By accessing and using CozyWish, you accept and agree to be bound by the terms and provision of this agreement.</p>

                <h3>Use License</h3>
                <p>Permission is granted to temporarily use CozyWish for personal, non-commercial transitory viewing only.</p>

                <h3>Booking Terms</h3>
                <ul>
                    <li>All bookings are subject to availability</li>
                    <li>Cancellation policies vary by provider</li>
                    <li>Payment is required at time of booking</li>
                    <li>Refunds are subject to provider policies</li>
                </ul>

                <h3>User Conduct</h3>
                <p>Users agree to use the platform responsibly and not engage in any prohibited activities.</p>
                """,
                "meta_description": "CozyWish Terms of Service - Rules and guidelines for using our platform.",
                "status": "published",
            },
            {
                "title": "FAQ",
                "slug": "faq",
                "content": """
                <h2>Frequently Asked Questions</h2>

                <h3>How do I book a service?</h3>
                <p>Simply browse our providers, select your desired service, choose a date and time, and complete your booking with secure payment.</p>

                <h3>Can I cancel my booking?</h3>
                <p>Cancellation policies vary by provider. Most allow cancellations up to 24 hours before your appointment.</p>

                <h3>How do I become a service provider?</h3>
                <p>Click "Become a Provider" and complete our simple registration process. We'll review your application within 24-48 hours.</p>

                <h3>Are payments secure?</h3>
                <p>Yes, we use industry-standard encryption and secure payment processing to protect your financial information.</p>

                <h3>What if I'm not satisfied with a service?</h3>
                <p>We encourage open communication with providers. If issues persist, our customer support team is here to help.</p>
                """,
                "meta_description": "Frequently asked questions about using CozyWish spa and wellness booking platform.",
                "status": "published",
            },
            {
                "title": "Contact Us",
                "slug": "contact",
                "content": """
                <h2>Contact CozyWish</h2>
                <p>We'd love to hear from you! Get in touch with our team for any questions or support needs.</p>

                <h3>Customer Support</h3>
                <p><strong>Email:</strong> <EMAIL><br>
                <strong>Phone:</strong> 1-800-COZYWISH<br>
                <strong>Hours:</strong> Monday-Friday, 9 AM - 6 PM EST</p>

                <h3>Business Inquiries</h3>
                <p><strong>Email:</strong> <EMAIL></p>

                <h3>Provider Support</h3>
                <p><strong>Email:</strong> <EMAIL></p>

                <h3>Media & Press</h3>
                <p><strong>Email:</strong> <EMAIL></p>
                """,
                "meta_description": "Contact CozyWish customer support and business inquiries.",
                "status": "published",
            },
        ]

        for page_data in static_pages:
            page = StaticPage.objects.create(created_by=admin_user, **page_data)
            self.stdout.write(f"   ✅ Created static page: {page.title}")

    def create_blog_categories(self):
        """Create blog categories."""
        self.stdout.write("📂 Creating blog categories...")

        categories = [
            {
                "name": "Wellness Tips",
                "description": "Expert advice and tips for maintaining wellness and health",
                "is_active": True,
            },
            {
                "name": "Spa Treatments",
                "description": "Information about different spa treatments and their benefits",
                "is_active": True,
            },
            {
                "name": "Beauty & Skincare",
                "description": "Beauty tips, skincare routines, and product recommendations",
                "is_active": True,
            },
            {
                "name": "Relaxation",
                "description": "Techniques and practices for stress relief and relaxation",
                "is_active": True,
            },
            {
                "name": "Company News",
                "description": "Updates and news from the CozyWish team",
                "is_active": True,
            },
        ]

        for category_data in categories:
            category = BlogCategory.objects.create(**category_data)
            self.stdout.write(f"   ✅ Created blog category: {category.name}")

    def create_blog_posts(self):
        """Create blog posts."""
        self.stdout.write("📝 Creating blog posts...")

        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write("   ⚠️ No admin user found, skipping blog posts")
            return

        categories = list(BlogCategory.objects.all())

        if not categories:
            self.stdout.write("   ⚠️ No blog categories found, skipping blog posts")
            return

        blog_posts = [
            {
                "title": "5 Benefits of Regular Massage Therapy",
                "content": """
                <p>Regular massage therapy offers numerous benefits for both physical and mental health. Here are five key advantages:</p>

                <h3>1. Stress Reduction</h3>
                <p>Massage therapy helps reduce cortisol levels and promotes relaxation, leading to decreased stress and anxiety.</p>

                <h3>2. Pain Relief</h3>
                <p>Therapeutic massage can help alleviate chronic pain conditions and muscle tension.</p>

                <h3>3. Improved Circulation</h3>
                <p>Massage stimulates blood flow, which helps deliver oxygen and nutrients to muscles and tissues.</p>

                <h3>4. Better Sleep Quality</h3>
                <p>Regular massage can improve sleep patterns and help you achieve deeper, more restful sleep.</p>

                <h3>5. Enhanced Immune Function</h3>
                <p>Studies show that massage therapy can boost immune system function by increasing white blood cell count.</p>
                """,
                "excerpt": "Discover the top five benefits of incorporating regular massage therapy into your wellness routine.",
                "category": "Wellness Tips",
                "status": "published",
                "is_featured": True,
            },
            {
                "title": "The Ultimate Guide to Facial Treatments",
                "content": """
                <p>Facial treatments are essential for maintaining healthy, glowing skin. This comprehensive guide covers everything you need to know.</p>

                <h3>Types of Facial Treatments</h3>
                <ul>
                    <li><strong>Classic Facial:</strong> Deep cleansing and moisturizing</li>
                    <li><strong>Anti-Aging Facial:</strong> Targets fine lines and wrinkles</li>
                    <li><strong>Acne Treatment:</strong> Addresses breakouts and blemishes</li>
                    <li><strong>Hydrating Facial:</strong> Restores moisture to dry skin</li>
                </ul>

                <h3>How Often Should You Get a Facial?</h3>
                <p>Most skincare experts recommend getting a professional facial every 4-6 weeks to maintain optimal skin health.</p>
                """,
                "excerpt": "Everything you need to know about facial treatments and how to choose the right one for your skin.",
                "category": "Beauty & Skincare",
                "status": "published",
                "is_featured": False,
            },
            {
                "title": "Creating a Relaxing Home Spa Experience",
                "content": """
                <p>Transform your home into a peaceful spa retreat with these simple tips and techniques.</p>

                <h3>Setting the Mood</h3>
                <ul>
                    <li>Dim the lights or use candles</li>
                    <li>Play soft, calming music</li>
                    <li>Use essential oils or aromatherapy</li>
                    <li>Ensure the room is at a comfortable temperature</li>
                </ul>

                <h3>DIY Spa Treatments</h3>
                <p>Try these simple at-home treatments:</p>
                <ul>
                    <li>Honey and oatmeal face mask</li>
                    <li>Epsom salt bath soak</li>
                    <li>Sugar scrub for exfoliation</li>
                    <li>Cucumber eye treatment</li>
                </ul>
                """,
                "excerpt": "Learn how to create a relaxing spa experience in the comfort of your own home.",
                "category": "Relaxation",
                "status": "published",
                "is_featured": True,
            },
        ]

        for post_data in blog_posts:
            category_name = post_data.pop("category")
            category = next(
                (c for c in categories if c.name == category_name), categories[0]
            )

            post = BlogPost.objects.create(
                author=admin_user,
                category=category,
                published_at=timezone.now() - timedelta(days=random.randint(1, 30)),
                **post_data,
            )

            featured_status = "⭐ featured" if post.is_featured else "📝 regular"
            self.stdout.write(f"   {featured_status} blog post: {post.title}")

    def create_homepage_blocks(self):
        """Create homepage content blocks."""
        self.stdout.write("🏠 Creating homepage blocks...")

        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write("   ⚠️ No admin user found, skipping homepage blocks")
            return

        homepage_blocks = [
            {
                "title": "Welcome to CozyWish",
                "content": "Discover amazing spa and wellness services near you. Book instantly and enjoy exclusive discounts.",
                "block_type": "hero",
                "display_order": 1,
                "is_active": True,
            },
            {
                "title": "Featured Services",
                "content": "Explore our most popular spa and wellness treatments.",
                "block_type": "features",
                "display_order": 2,
                "is_active": True,
            },
            {
                "title": "Special Offers",
                "content": "Don't miss our limited-time promotions and discounts.",
                "block_type": "call_to_action",
                "display_order": 3,
                "is_active": True,
            },
            {
                "title": "Customer Reviews",
                "content": "See what our customers are saying about their experiences.",
                "block_type": "testimonials",
                "display_order": 4,
                "is_active": True,
            },
        ]

        for block_data in homepage_blocks:
            block = HomepageBlock.objects.create(updated_by=admin_user, **block_data)
            self.stdout.write(f"   ✅ Created homepage block: {block.title}")

    def create_site_configuration(self):
        """Create site configuration settings."""
        self.stdout.write("⚙️ Creating site configuration...")

        config, created = SiteConfiguration.objects.get_or_create(
            defaults={
                "site_name": "CozyWish",
                "site_tagline": "Your premier spa and wellness booking platform",
                "site_description": "Discover amazing spa and wellness services near you. Book instantly and enjoy exclusive discounts.",
                "contact_email": "<EMAIL>",
                "contact_phone": "1-800-COZYWISH",
                "contact_address": "123 Wellness Street, Spa City, SC 12345",
                "facebook_url": "https://facebook.com/cozywish",
                "instagram_url": "https://instagram.com/cozywish",
                "twitter_url": "https://twitter.com/cozywish",
                "linkedin_url": "https://linkedin.com/company/cozywish",
                "default_meta_title": "CozyWish - Spa & Wellness Booking Platform",
                "default_meta_description": "Find and book the best spa and wellness services in your area with exclusive discounts.",
                "default_meta_keywords": "spa, wellness, massage, beauty, booking, discounts",
                "maintenance_mode": False,
                "allow_user_registration": True,
                "require_email_verification": True,
            }
        )

        if created:
            self.stdout.write("   ✅ Created site configuration")
        else:
            self.stdout.write("   ℹ️ Site configuration already exists")

    def create_announcements(self):
        """Create system announcements."""
        self.stdout.write("📢 Creating announcements...")

        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write("   ⚠️ No admin user found, skipping announcements")
            return

        announcements = [
            {
                "title": "Welcome to CozyWish!",
                "content": "Thank you for joining our wellness community. Start exploring amazing spa services today!",
                "announcement_type": "info",
                "is_active": True,
                "start_date": timezone.now() - timedelta(days=30),
                "end_date": timezone.now() + timedelta(days=30),
            },
            {
                "title": "Holiday Special Offers",
                "content": "Enjoy up to 30% off on selected spa services this holiday season!",
                "announcement_type": "promotion",
                "is_active": True,
                "start_date": timezone.now() - timedelta(days=7),
                "end_date": timezone.now() + timedelta(days=45),
            },
        ]

        for announcement_data in announcements:
            announcement = Announcement.objects.create(
                created_by=admin_user, **announcement_data
            )

            status = "🟢 active" if announcement.is_active else "🔴 inactive"
            self.stdout.write(f"   {status} announcement: {announcement.title}")
