"""App configuration for the admin_app module."""

from django.apps import AppConfig


class AdminAppConfig(AppConfig):
    """Configuration for the admin_app Django application."""

    default_auto_field = "django.db.models.BigAutoField"
    name = "admin_app"
    verbose_name = "Admin & CMS Management"

    def ready(self):
        """Import signals when the app is ready."""
        try:
            import admin_app.signals  # noqa F401
        except ImportError:
            pass
