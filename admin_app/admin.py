"""Admin site registrations and customization."""

# --- Third-Party Imports ---
from django.contrib import admin
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .models import (
    Announcement,
    BlogCategory,
    BlogPost,
    BulkActionLog,
    HomepageBlock,
    MediaFile,
    SiteConfiguration,
    StaticPage,
    SystemHealthLog,
)

User = get_user_model()


@admin.register(StaticPage)
class StaticPageAdmin(admin.ModelAdmin):
    """Admin configuration for StaticPage model."""

    list_display = (
        "title",
        "slug",
        "status",
        "is_featured",
        "created_at",
        "updated_at",
    )
    list_filter = ("status", "is_featured", "created_at", "updated_at")
    search_fields = ("title", "slug", "content", "meta_description")
    prepopulated_fields = {"slug": ("title",)}
    readonly_fields = ("created_at", "updated_at", "created_by", "updated_by")

    fieldsets = (
        (
            _("Page Content"),
            {
                "fields": (
                    "title",
                    "slug",
                    "content",
                    "featured_image",
                    "status",
                    "is_featured",
                )
            },
        ),
        (
            _("SEO Settings"),
            {
                "fields": ("meta_title", "meta_description", "meta_keywords"),
                "classes": ("collapse",),
            },
        ),
        (
            _("Metadata"),
            {
                "fields": ("created_at", "updated_at", "created_by", "updated_by"),
                "classes": ("collapse",),
            },
        ),
    )

    actions = ["publish_pages", "unpublish_pages", "feature_pages", "unfeature_pages"]

    def save_model(self, request, obj, form, change):
        """Set created_by and updated_by fields."""
        if not change:
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def publish_pages(self, request, queryset):
        """Bulk action to publish selected pages."""
        updated = queryset.update(status="published")
        self.message_user(request, f"{updated} pages were successfully published.")

    publish_pages.short_description = _("Publish selected pages")

    def unpublish_pages(self, request, queryset):
        """Bulk action to unpublish selected pages."""
        updated = queryset.update(status="draft")
        self.message_user(request, f"{updated} pages were unpublished.")

    unpublish_pages.short_description = _("Unpublish selected pages")

    def feature_pages(self, request, queryset):
        """Bulk action to feature selected pages."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f"{updated} pages were featured.")

    feature_pages.short_description = _("Feature selected pages")

    def unfeature_pages(self, request, queryset):
        """Bulk action to unfeature selected pages."""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f"{updated} pages were unfeatured.")

    unfeature_pages.short_description = _("Unfeature selected pages")


@admin.register(BlogCategory)
class BlogCategoryAdmin(admin.ModelAdmin):
    """Admin configuration for BlogCategory model."""

    list_display = ("name", "slug", "is_active", "post_count", "created_at")
    list_filter = ("is_active", "created_at")
    search_fields = ("name", "description")
    prepopulated_fields = {"slug": ("name",)}
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (
            _("Category Information"),
            {"fields": ("name", "slug", "description", "is_active")},
        ),
        (
            _("Metadata"),
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    actions = ["activate_categories", "deactivate_categories"]

    def post_count(self, obj):
        """Display the number of published posts in this category."""
        return obj.post_count

    post_count.short_description = _("Published Posts")

    def activate_categories(self, request, queryset):
        """Bulk action to activate selected categories."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} categories were activated.")

    activate_categories.short_description = _("Activate selected categories")

    def deactivate_categories(self, request, queryset):
        """Bulk action to deactivate selected categories."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} categories were deactivated.")

    deactivate_categories.short_description = _("Deactivate selected categories")


@admin.register(BlogPost)
class BlogPostAdmin(admin.ModelAdmin):
    """Admin configuration for BlogPost model."""

    list_display = (
        "title",
        "category",
        "author",
        "status",
        "is_featured",
        "published_at",
        "created_at",
    )
    list_filter = ("status", "is_featured", "category", "created_at", "published_at")
    search_fields = ("title", "content", "excerpt", "meta_description")
    prepopulated_fields = {"slug": ("title",)}
    readonly_fields = ("created_at", "updated_at", "published_at", "updated_by")

    fieldsets = (
        (
            _("Post Content"),
            {
                "fields": (
                    "title",
                    "slug",
                    "content",
                    "excerpt",
                    "category",
                    "featured_image",
                )
            },
        ),
        (_("Publication Settings"), {"fields": ("status", "is_featured", "author")}),
        (
            _("SEO Settings"),
            {
                "fields": ("meta_title", "meta_description", "meta_keywords"),
                "classes": ("collapse",),
            },
        ),
        (
            _("Metadata"),
            {
                "fields": ("created_at", "updated_at", "published_at", "updated_by"),
                "classes": ("collapse",),
            },
        ),
    )

    actions = ["publish_posts", "unpublish_posts", "feature_posts", "unfeature_posts"]

    def save_model(self, request, obj, form, change):
        """Set author and updated_by fields."""
        if not change:
            obj.author = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def publish_posts(self, request, queryset):
        """Bulk action to publish selected posts."""
        updated = queryset.update(status="published")
        self.message_user(request, f"{updated} posts were successfully published.")

    publish_posts.short_description = _("Publish selected posts")

    def unpublish_posts(self, request, queryset):
        """Bulk action to unpublish selected posts."""
        updated = queryset.update(status="draft")
        self.message_user(request, f"{updated} posts were unpublished.")

    unpublish_posts.short_description = _("Unpublish selected posts")

    def feature_posts(self, request, queryset):
        """Bulk action to feature selected posts."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f"{updated} posts were featured.")

    feature_posts.short_description = _("Feature selected posts")

    def unfeature_posts(self, request, queryset):
        """Bulk action to unfeature selected posts."""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f"{updated} posts were unfeatured.")

    unfeature_posts.short_description = _("Unfeature selected posts")


@admin.register(HomepageBlock)
class HomepageBlockAdmin(admin.ModelAdmin):
    """Admin configuration for HomepageBlock model."""

    list_display = ("block_type", "title", "is_active", "display_order", "updated_at")
    list_filter = ("block_type", "is_active", "updated_at")
    search_fields = ("title", "subtitle", "content")
    readonly_fields = ("created_at", "updated_at", "updated_by")
    ordering = ("display_order", "block_type")

    fieldsets = (
        (
            _("Block Information"),
            {"fields": ("block_type", "title", "subtitle", "content", "image")},
        ),
        (
            _("Call to Action"),
            {"fields": ("button_text", "button_url"), "classes": ("collapse",)},
        ),
        (_("Display Settings"), {"fields": ("is_active", "display_order")}),
        (
            _("Metadata"),
            {
                "fields": ("created_at", "updated_at", "updated_by"),
                "classes": ("collapse",),
            },
        ),
    )

    actions = ["activate_blocks", "deactivate_blocks"]

    def save_model(self, request, obj, form, change):
        """Set updated_by field."""
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def activate_blocks(self, request, queryset):
        """Bulk action to activate selected blocks."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} blocks were activated.")

    activate_blocks.short_description = _("Activate selected blocks")

    def deactivate_blocks(self, request, queryset):
        """Bulk action to deactivate selected blocks."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} blocks were deactivated.")

    deactivate_blocks.short_description = _("Deactivate selected blocks")


@admin.register(MediaFile)
class MediaFileAdmin(admin.ModelAdmin):
    """Admin configuration for MediaFile model."""

    list_display = (
        "title",
        "file_type",
        "file_size_formatted",
        "uploaded_by",
        "is_public",
        "created_at",
    )
    list_filter = ("file_type", "is_public", "created_at", "uploaded_by")
    search_fields = ("title", "description", "tags", "alt_text")
    readonly_fields = (
        "created_at",
        "file_size_formatted",
        "file_extension",
        "file_url",
    )

    fieldsets = (
        (
            _("File Information"),
            {"fields": ("title", "file", "file_type", "description")},
        ),
        (_("Image Settings"), {"fields": ("alt_text",), "classes": ("collapse",)}),
        (_("Organization"), {"fields": ("tags", "is_public")}),
        (
            _("File Details"),
            {
                "fields": ("file_size_formatted", "file_extension", "file_url"),
                "classes": ("collapse",),
            },
        ),
        (
            _("Metadata"),
            {"fields": ("uploaded_by", "created_at"), "classes": ("collapse",)},
        ),
    )

    actions = ["make_public", "make_private"]

    def save_model(self, request, obj, form, change):
        """Set uploaded_by field."""
        if not change:
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)

    def make_public(self, request, queryset):
        """Bulk action to make selected files public."""
        updated = queryset.update(is_public=True)
        self.message_user(request, f"{updated} files were made public.")

    make_public.short_description = _("Make selected files public")

    def make_private(self, request, queryset):
        """Bulk action to make selected files private."""
        updated = queryset.update(is_public=False)
        self.message_user(request, f"{updated} files were made private.")

    make_private.short_description = _("Make selected files private")


@admin.register(BulkActionLog)
class BulkActionLogAdmin(admin.ModelAdmin):
    """Admin configuration for BulkActionLog model."""

    list_display = (
        "action_type",
        "affected_count",
        "executed_by",
        "executed_at",
        "affected_model",
    )
    list_filter = ("action_type", "affected_model", "executed_at", "executed_by")
    search_fields = ("description", "affected_ids")
    readonly_fields = (
        "action_type",
        "description",
        "affected_count",
        "affected_model",
        "affected_ids",
        "executed_by",
        "executed_at",
        "ip_address",
        "user_agent",
    )
    date_hierarchy = "executed_at"

    fieldsets = (
        (
            _("Action Details"),
            {
                "fields": (
                    "action_type",
                    "description",
                    "affected_count",
                    "affected_model",
                )
            },
        ),
        (_("Affected Items"), {"fields": ("affected_ids",), "classes": ("collapse",)}),
        (
            _("Execution Details"),
            {
                "fields": ("executed_by", "executed_at", "ip_address", "user_agent"),
                "classes": ("collapse",),
            },
        ),
    )

    def has_add_permission(self, request):
        """Disable manual creation of bulk action logs."""
        return False

    def has_change_permission(self, request, obj=None):
        """Disable editing of bulk action logs."""
        return False

    def has_delete_permission(self, request, obj=None):
        """Allow deletion for cleanup purposes."""
        return request.user.is_superuser


@admin.register(SystemHealthLog)
class SystemHealthLogAdmin(admin.ModelAdmin):
    """Admin configuration for SystemHealthLog model."""

    list_display = (
        "event_type",
        "severity",
        "title",
        "affected_user",
        "is_resolved",
        "recorded_at",
    )
    list_filter = ("event_type", "severity", "is_resolved", "recorded_at")
    search_fields = ("title", "description", "resolution_notes")
    readonly_fields = ("recorded_at",)
    date_hierarchy = "recorded_at"

    fieldsets = (
        (
            _("Event Details"),
            {"fields": ("event_type", "severity", "title", "description")},
        ),
        (
            _("Affected User"),
            {
                "fields": ("affected_user", "ip_address", "user_agent"),
                "classes": ("collapse",),
            },
        ),
        (
            _("Additional Data"),
            {"fields": ("additional_data",), "classes": ("collapse",)},
        ),
        (
            _("Resolution"),
            {
                "fields": (
                    "is_resolved",
                    "resolved_by",
                    "resolved_at",
                    "resolution_notes",
                )
            },
        ),
        (_("Metadata"), {"fields": ("recorded_at",), "classes": ("collapse",)}),
    )

    actions = ["mark_resolved", "mark_unresolved"]

    def mark_resolved(self, request, queryset):
        """Bulk action to mark selected events as resolved."""
        updated = 0
        for event in queryset.filter(is_resolved=False):
            event.resolve(request.user, "Bulk resolved by admin")
            updated += 1
        self.message_user(request, f"{updated} events were marked as resolved.")

    mark_resolved.short_description = _("Mark selected events as resolved")

    def mark_unresolved(self, request, queryset):
        """Bulk action to mark selected events as unresolved."""
        updated = queryset.update(
            is_resolved=False, resolved_by=None, resolved_at=None, resolution_notes=""
        )
        self.message_user(request, f"{updated} events were marked as unresolved.")

    mark_unresolved.short_description = _("Mark selected events as unresolved")


@admin.register(SiteConfiguration)
class SiteConfigurationAdmin(admin.ModelAdmin):
    """Admin configuration for SiteConfiguration model."""

    list_display = (
        "site_name",
        "contact_email",
        "maintenance_mode",
        "updated_at",
        "updated_by",
    )
    readonly_fields = ("updated_at", "updated_by")

    fieldsets = (
        (
            _("Site Information"),
            {"fields": ("site_name", "site_tagline", "site_description")},
        ),
        (
            _("Contact Information"),
            {"fields": ("contact_email", "contact_phone", "contact_address")},
        ),
        (
            _("Social Media"),
            {
                "fields": (
                    "facebook_url",
                    "twitter_url",
                    "instagram_url",
                    "linkedin_url",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            _("SEO Settings"),
            {
                "fields": (
                    "default_meta_title",
                    "default_meta_description",
                    "default_meta_keywords",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            _("Analytics & Tracking"),
            {
                "fields": ("google_analytics_id", "facebook_pixel_id"),
                "classes": ("collapse",),
            },
        ),
        (
            _("Maintenance Mode"),
            {
                "fields": ("maintenance_mode", "maintenance_message"),
                "classes": ("collapse",),
            },
        ),
        (
            _("System Settings"),
            {
                "fields": ("allow_user_registration", "require_email_verification"),
                "classes": ("collapse",),
            },
        ),
        (
            _("Metadata"),
            {"fields": ("updated_at", "updated_by"), "classes": ("collapse",)},
        ),
    )

    def save_model(self, request, obj, form, change):
        """Set updated_by field."""
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def has_add_permission(self, request):
        """Only allow one site configuration instance."""
        return not SiteConfiguration.objects.exists()

    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of site configuration."""
        return False


@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    """Admin configuration for Announcement model."""

    list_display = (
        "title",
        "slug",
        "announcement_type",
        "display_location",
        "is_active",
        "is_current",
        "priority",
        "start_date",
        "end_date",
        "created_by",
    )
    list_filter = (
        "announcement_type",
        "display_location",
        "is_active",
        "is_dismissible",
        "start_date",
        "end_date",
        "created_at",
    )
    search_fields = ("title", "slug", "content", "target_user_roles")
    prepopulated_fields = {"slug": ("title",)}
    readonly_fields = ("created_at", "updated_at", "created_by", "updated_by")
    date_hierarchy = "start_date"
    ordering = ("-priority", "-created_at")

    fieldsets = (
        (
            _("Announcement Content"),
            {"fields": ("title", "slug", "content", "announcement_type")},
        ),
        (
            _("Display Settings"),
            {"fields": ("display_location", "is_active", "is_dismissible", "priority")},
        ),
        (_("Scheduling"), {"fields": ("start_date", "end_date")}),
        (_("Targeting"), {"fields": ("target_user_roles",), "classes": ("collapse",)}),
        (
            _("Metadata"),
            {
                "fields": ("created_at", "updated_at", "created_by", "updated_by"),
                "classes": ("collapse",),
            },
        ),
    )

    actions = [
        "activate_announcements",
        "deactivate_announcements",
        "extend_announcements",
    ]

    def save_model(self, request, obj, form, change):
        """Set created_by and updated_by fields."""
        if not change:
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def is_current(self, obj):
        """Display whether the announcement is currently active."""
        return obj.is_current

    is_current.boolean = True
    is_current.short_description = _("Currently Active")

    def activate_announcements(self, request, queryset):
        """Bulk action to activate selected announcements."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} announcements were activated.")

    activate_announcements.short_description = _("Activate selected announcements")

    def deactivate_announcements(self, request, queryset):
        """Bulk action to deactivate selected announcements."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} announcements were deactivated.")

    deactivate_announcements.short_description = _("Deactivate selected announcements")

    def extend_announcements(self, request, queryset):
        """Bulk action to extend announcement end dates by 7 days."""
        from datetime import timedelta

        from django.utils import timezone

        updated = 0
        for announcement in queryset:
            if announcement.end_date:
                announcement.end_date += timedelta(days=7)
            else:
                announcement.end_date = timezone.now() + timedelta(days=7)
            announcement.save()
            updated += 1

        self.message_user(request, f"{updated} announcements were extended by 7 days.")

    extend_announcements.short_description = _(
        "Extend selected announcements by 7 days"
    )


# Custom admin site configuration
admin.site.site_header = _("CozyWish Admin")
admin.site.site_title = _("CozyWish Admin")
admin.site.index_title = _("Welcome to CozyWish Administration")
