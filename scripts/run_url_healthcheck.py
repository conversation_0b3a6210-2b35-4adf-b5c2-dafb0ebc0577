#!/usr/bin/env python
"""
URL Health Check Runner Script for CozyWish Django Project.

This script provides an easy way to run the comprehensive URL health check
test with various options and configurations.

Usage:
    python scripts/run_url_healthcheck.py [options]

Options:
    --verbose, -v       Enable verbose output
    --json-only         Only generate JSON report, no console output
    --app APP_NAME      Test only URLs from specific app
    --critical-only     Test only critical URLs
    --no-auth           Skip authentication tests
    --help, -h          Show this help message

Examples:
    # Run full health check
    python scripts/run_url_healthcheck.py

    # Test only venues_app URLs
    python scripts/run_url_healthcheck.py --app venues_app

    # Run with verbose output
    python scripts/run_url_healthcheck.py --verbose

    # Test only critical URLs
    python scripts/run_url_healthcheck.py --critical-only
"""

import argparse
import os
import sys
from pathlib import Path

import django

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project_root.settings")
django.setup()

from django.conf import settings
from django.test.utils import get_runner

from tests.test_url_healthcheck import (
    URLHealthCheckTest,
    get_url_patterns_by_app,
    test_single_url,
)


def run_full_healthcheck(verbose=False):
    """Run the complete URL health check test suite."""
    print("🔍 Running comprehensive URL health check...")
    print("=" * 80)

    # Configure test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2 if verbose else 1)

    # Run the tests
    failures = test_runner.run_tests(["tests.test_url_healthcheck.URLHealthCheckTest"])

    return failures == 0


def run_app_specific_test(app_name, verbose=False):
    """Run URL health check for a specific app."""
    print(f"🔍 Running URL health check for {app_name}...")
    print("=" * 80)

    # Get patterns for the specific app
    patterns = get_url_patterns_by_app(app_name)

    if not patterns:
        print(f"❌ No URL patterns found for app: {app_name}")
        return False

    print(f"Found {len(patterns)} URL patterns for {app_name}")

    # Test each pattern
    success_count = 0
    error_count = 0

    for pattern in patterns:
        result = test_single_url(pattern["full_name"])

        if result["test_status"] == "SUCCESS":
            success_count += 1
            if verbose:
                print(f"✅ {pattern['full_name']}: SUCCESS")
        else:
            error_count += 1
            print(f"❌ {pattern['full_name']}: {result['test_status']}")
            if result.get("exception_message") and verbose:
                print(f"   Error: {result['exception_message']}")

    print(f"\nResults for {app_name}:")
    print(f"  Success: {success_count}")
    print(f"  Errors: {error_count}")
    print(f"  Success rate: {(success_count/len(patterns)*100):.1f}%")

    return error_count == 0


def run_critical_urls_test(verbose=False):
    """Run health check for critical URLs only."""
    print("🔍 Running critical URLs health check...")
    print("=" * 80)

    critical_urls = [
        "home",
        "accounts_app:customer_login",
        "accounts_app:service_provider_login",
        "accounts_app:customer_signup",
        "accounts_app:service_provider_signup",
        "venues_app:venue_list",
        "venues_app:venue_search",
        "admin:index",
        "admin_app:admin_dashboard",
        "dashboard_app:health_check",
    ]

    success_count = 0
    error_count = 0

    for url_name in critical_urls:
        try:
            result = test_single_url(url_name)

            if result["test_status"] in ["SUCCESS", "REDIRECT"]:
                success_count += 1
                if verbose:
                    print(f"✅ {url_name}: {result['test_status']}")
            else:
                error_count += 1
                print(f"❌ {url_name}: {result['test_status']}")
                if result.get("exception_message"):
                    print(f"   Error: {result['exception_message']}")

        except Exception as e:
            error_count += 1
            print(f"❌ {url_name}: EXCEPTION - {e}")

    print(f"\nCritical URLs Results:")
    print(f"  Success: {success_count}")
    print(f"  Errors: {error_count}")
    print(f"  Success rate: {(success_count/len(critical_urls)*100):.1f}%")

    return error_count == 0


def main():
    """Main entry point for the URL health check runner."""
    parser = argparse.ArgumentParser(
        description="Run URL health check for CozyWish Django project",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )

    parser.add_argument(
        "--json-only",
        action="store_true",
        help="Only generate JSON report, minimal console output",
    )

    parser.add_argument(
        "--app", type=str, help="Test only URLs from specific app (e.g., venues_app)"
    )

    parser.add_argument(
        "--critical-only", action="store_true", help="Test only critical URLs"
    )

    parser.add_argument(
        "--no-auth", action="store_true", help="Skip authentication-related tests"
    )

    args = parser.parse_args()

    # Set verbosity based on json-only flag
    verbose = args.verbose and not args.json_only

    try:
        # Run appropriate test based on arguments
        if args.app:
            success = run_app_specific_test(args.app, verbose)
        elif args.critical_only:
            success = run_critical_urls_test(verbose)
        else:
            success = run_full_healthcheck(verbose)

        # Exit with appropriate code
        if success:
            print("\n🎉 URL health check completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ URL health check found issues!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️  URL health check interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 URL health check failed with error: {e}")
        if verbose:
            import traceback

            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
