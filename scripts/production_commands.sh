#!/usr/bin/env bash

# Production Commands Script for Render.com
# This script provides safe ways to run database operations in production

set -e  # Exit on any error

echo "🚀 CozyWish Production Commands"
echo "==============================="
echo ""

# Function to show help
show_help() {
    echo "Available commands:"
    echo ""
    echo "  ./scripts/production_commands.sh seed_minimal"
    echo "    - Seed only essential data (categories, superuser)"
    echo ""
    echo "  ./scripts/production_commands.sh seed_full"
    echo "    - Seed full test data (WARNING: adds test data)"
    echo ""
    echo "  ./scripts/production_commands.sh migrate"
    echo "    - Apply database migrations only"
    echo ""
    echo "  ./scripts/production_commands.sh reset_and_seed"
    echo "    - Reset database and seed data (DANGEROUS)"
    echo ""
    echo "  ./scripts/production_commands.sh status"
    echo "    - Show database status"
    echo ""
}

# Function to show database status
show_status() {
    echo "📊 Database Status:"
    echo "=================="

    python manage.py shell -c "
from django.contrib.auth import get_user_model
from venues_app.models import Venue, Service, ServiceCategory
from booking_cart_app.models import Booking

User = get_user_model()

print(f'Users: {User.objects.count()}')
print(f'Superusers: {User.objects.filter(is_superuser=True).count()}')
print(f'Service Categories: {ServiceCategory.objects.count()}')
print(f'Venues: {Venue.objects.count()}')
print(f'Services: {Service.objects.count()}')
print(f'Bookings: {Booking.objects.count()}')
"
}

# Function to seed minimal data
seed_minimal() {
    echo "🌱 Seeding minimal essential data..."
    python manage.py production_seed --minimal --skip-confirmation
    echo "✅ Minimal seeding completed!"
}

# Function to seed full data
seed_full() {
    echo "⚠️  WARNING: This will add test data to your production database!"
    echo "Are you sure you want to continue? (Type 'yes' to confirm)"
    read -r confirmation

    if [ "$confirmation" = "yes" ]; then
        echo "🌱 Seeding full test data..."
        python manage.py production_seed --skip-confirmation
        echo "✅ Full seeding completed!"
    else
        echo "❌ Operation cancelled."
        exit 1
    fi
}

# Function to apply migrations
migrate_only() {
    echo "⚙️  Applying database migrations..."
    python manage.py makemigrations
    python manage.py migrate
    echo "✅ Migrations applied successfully!"
}

# Function to reset and seed
reset_and_seed() {
    echo "🚨 DANGER: This will reset your entire database!"
    echo "All data will be lost and replaced with test data."
    echo "Type 'RESET_PRODUCTION' to confirm:"
    read -r confirmation

    if [ "$confirmation" = "RESET_PRODUCTION" ]; then
        echo "🔄 Resetting database and seeding data..."
        python manage.py production_seed --reset-db --skip-confirmation
        echo "✅ Reset and seeding completed!"
    else
        echo "❌ Operation cancelled."
        exit 1
    fi
}

# Main command handling
case "${1:-help}" in
    "seed_minimal")
        seed_minimal
        ;;
    "seed_full")
        seed_full
        ;;
    "migrate")
        migrate_only
        ;;
    "reset_and_seed")
        reset_and_seed
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        show_help
        ;;
esac

echo ""
echo "🔍 Current database status:"
show_status
