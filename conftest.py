# --- Standard Library Imports ---
import os
from unittest.mock import patch

# --- Third-Party Imports ---
import pytest

import django

# --- Django Imports ---
from django.conf import settings
from django.contrib.auth import get_user_model
from django.test import Client

# --- Ensure Django is set up before importing models ---
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project_root.settings")
django.setup()

# --- Now we can safely import Django models ---
User = get_user_model()


# --- Pytest Configuration ---
def pytest_configure(config):
    if not settings.configured:
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project_root.settings")
        django.setup()


# --- Test Markers ---
def pytest_collection_modifyitems(config, items):
    for item in items:
        if "test_models" in item.nodeid:
            item.add_marker(pytest.mark.models)
        elif "test_views" in item.nodeid:
            item.add_marker(pytest.mark.views)
        elif "test_forms" in item.nodeid:
            item.add_marker(pytest.mark.forms)
        elif "test_integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        elif "test_commands" in item.nodeid:
            item.add_marker(pytest.mark.commands)
        elif "test_logging" in item.nodeid:
            item.add_marker(pytest.mark.logging)
        elif "test_utils" in item.nodeid:
            item.add_marker(pytest.mark.utils)


# --- Django Client Fixture ---
@pytest.fixture
def client():
    """Django test client fixture."""
    return Client()


# --- User Fixtures ---
@pytest.fixture
def admin_user(db):
    """Create an admin user for testing."""
    return User.objects.create_user(
        email="<EMAIL>",
        password="testpass123",
        role="admin",
        is_staff=True,
        is_superuser=True,
        first_name="Admin",
        last_name="User",
    )


@pytest.fixture
def customer_user(db):
    """Create a customer user for testing."""
    return User.objects.create_user(
        email="<EMAIL>",
        password="testpass123",
        role="customer",
        first_name="Customer",
        last_name="User",
    )


@pytest.fixture
def service_provider_user(db):
    """Create a service provider user for testing."""
    return User.objects.create_user(
        email="<EMAIL>",
        password="testpass123",
        role="service_provider",
        first_name="Provider",
        last_name="User",
    )


# --- Authenticated Clients ---
@pytest.fixture
def authenticated_client(client, customer_user):
    """Client with authenticated customer user."""
    client.force_login(customer_user)
    return client


@pytest.fixture
def admin_client(client, admin_user):
    """Client with authenticated admin user."""
    client.force_login(admin_user)
    return client


@pytest.fixture
def provider_client(client, service_provider_user):
    """Client with authenticated service provider user."""
    client.force_login(service_provider_user)
    return client


# --- Mock Fixtures for Testing ---
@pytest.fixture
def mock_async_notifications():
    """Mock asynchronous notifications to run synchronously during tests."""
    with patch(
        "notifications_app.utils.run_async",
        lambda func, *args, **kwargs: func(*args, **kwargs),
    ):
        yield


@pytest.fixture
def mock_email_sending():
    """Mock email sending during tests."""
    with patch("django.core.mail.send_mail") as mock_send:
        yield mock_send


@pytest.fixture
def mock_logging():
    """Mock logging functions during tests."""
    with patch("logging.getLogger") as mock_logger:
        yield mock_logger


# --- Database Fixtures ---
@pytest.fixture
def transactional_db(db):
    """Fixture that provides access to the database with transaction support."""
    return db


# --- Performance Testing Fixture ---
@pytest.fixture
def performance_timer():
    """Simple performance timer for testing."""
    import time

    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None

        def start(self):
            self.start_time = time.time()

        def stop(self):
            self.end_time = time.time()

        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None

    return Timer()


# --- Pytest Plugins ---
pytest_plugins = [
    "pytest_django",
]


# --- Enable DB Access for All Tests ---
@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db):
    """Enable DB access for all tests automatically (no marker needed)."""
    pass
