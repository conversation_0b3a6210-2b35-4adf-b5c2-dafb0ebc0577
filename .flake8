# CozyWish Flake8 Configuration
# Python linting configuration

[flake8]
max-line-length = 88
max-complexity = 10
select = E,W,F,C
ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501

exclude = 
    .git,
    __pycache__,
    .venv,
    ENV_*,
    venv,
    env,
    migrations,
    .tox,
    build,
    dist,
    *.egg-info,
    .pytest_cache,
    .coverage

per-file-ignores =
    # Django settings files can have long lines and imports
    */settings/*.py:E402,F401
    # Test files can have long lines and unused imports
    */tests/*.py:E402,F401,F811
    # __init__.py files can have unused imports
    __init__.py:F401
    # Management commands can have long lines
    */management/commands/*.py:E501

# Specific Django-related ignores
extend-ignore = 
    # Allow Django model verbose names to be longer
    DJ01,
    # Allow Django model __str__ methods to be simple
    DJ08
