"""
Unit tests for payments_app URL patterns.

This module contains comprehensive unit tests for all URL patterns in the payments_app,
ensuring proper URL resolution and view mapping following the same patterns as accounts_app.
"""

# Standard library imports
import uuid

from django.contrib.auth import get_user_model

# Django imports
from django.test import TestCase
from django.urls import resolve, reverse

# Local imports
from payments_app import views

User = get_user_model()


class PaymentsAppUrlsTest(TestCase):
    """Test URL patterns for the payments_app."""

    def setUp(self):
        """Set up test data."""
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        # Generate test UUIDs for URL testing
        self.test_booking_id = uuid.uuid4()
        self.test_payment_id = uuid.uuid4()
        self.test_refund_id = uuid.uuid4()

    def test_checkout_url(self):
        """Test the checkout URL pattern."""
        url = reverse(
            "payments_app:checkout", kwargs={"booking_id": self.test_booking_id}
        )
        self.assertEqual(url, f"/payments/checkout/{self.test_booking_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.StripeCheckoutView)

    def test_payment_process_url(self):
        """Test the payment process URL pattern."""
        url = reverse(
            "payments_app:payment_process", kwargs={"payment_id": self.test_payment_id}
        )
        self.assertEqual(url, f"/payments/process/{self.test_payment_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.PaymentProcessView)

    def test_payment_success_url(self):
        """Test the payment success URL pattern."""
        url = reverse(
            "payments_app:payment_success", kwargs={"payment_id": self.test_payment_id}
        )
        self.assertEqual(url, f"/payments/success/{self.test_payment_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.PaymentSuccessView)

    def test_payment_cancel_url(self):
        """Test the payment cancel URL pattern."""
        url = reverse(
            "payments_app:payment_cancel", kwargs={"booking_id": self.test_booking_id}
        )
        self.assertEqual(url, f"/payments/cancel/{self.test_booking_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.PaymentCancelView)

    def test_payment_history_url(self):
        """Test the payment history URL pattern."""
        url = reverse("payments_app:payment_history")
        self.assertEqual(url, "/payments/history/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.PaymentHistoryView)

    def test_payment_detail_url(self):
        """Test the payment detail URL pattern."""
        url = reverse(
            "payments_app:payment_detail", kwargs={"payment_id": self.test_payment_id}
        )
        self.assertEqual(url, f"/payments/detail/{self.test_payment_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.PaymentDetailView)

    def test_payment_receipt_url(self):
        """Test the payment receipt URL pattern."""
        url = reverse(
            "payments_app:payment_receipt", kwargs={"payment_id": self.test_payment_id}
        )
        self.assertEqual(url, f"/payments/receipt/{self.test_payment_id}/")

        resolved = resolve(url)
        # payment_receipt_view is a function-based view, not a class-based view
        self.assertEqual(resolved.func, views.payment_receipt_view)

    def test_refund_request_url(self):
        """Test the refund request URL pattern."""
        url = reverse(
            "payments_app:refund_request", kwargs={"payment_id": self.test_payment_id}
        )
        self.assertEqual(url, f"/payments/refund/request/{self.test_payment_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.RefundRequestView)

    def test_refund_history_url(self):
        """Test the refund history URL pattern."""
        url = reverse("payments_app:refund_history")
        self.assertEqual(url, "/payments/refund/history/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.RefundHistoryView)

    def test_refund_detail_url(self):
        """Test the refund detail URL pattern."""
        url = reverse(
            "payments_app:refund_detail", kwargs={"refund_id": self.test_refund_id}
        )
        self.assertEqual(url, f"/payments/refund/detail/{self.test_refund_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.RefundDetailView)


class ProviderPaymentsUrlsTest(TestCase):
    """Test provider-specific URL patterns for the payments_app."""

    def setUp(self):
        """Set up test data."""
        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Generate test UUIDs for URL testing
        self.test_payment_id = uuid.uuid4()

    def test_provider_earnings_url(self):
        """Test the provider earnings URL pattern."""
        url = reverse("payments_app:provider_earnings")
        self.assertEqual(url, "/payments/provider/earnings/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.ProviderEarningsView)

    def test_provider_payment_history_url(self):
        """Test the provider payment history URL pattern."""
        url = reverse("payments_app:provider_payment_history")
        self.assertEqual(url, "/payments/provider/history/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.ProviderPaymentHistoryView)

    def test_provider_payment_detail_url(self):
        """Test the provider payment detail URL pattern."""
        url = reverse(
            "payments_app:provider_payment_detail",
            kwargs={"payment_id": self.test_payment_id},
        )
        self.assertEqual(url, f"/payments/provider/detail/{self.test_payment_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.ProviderPaymentDetailView)

    def test_provider_payout_history_url(self):
        """Test the provider payout history URL pattern."""
        url = reverse("payments_app:provider_payout_history")
        self.assertEqual(url, "/payments/provider/payouts/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.ProviderPayoutHistoryView)


class AdminPaymentsUrlsTest(TestCase):
    """Test admin-specific URL patterns for the payments_app."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        # Generate test UUIDs for URL testing
        self.test_payment_id = uuid.uuid4()
        self.test_refund_id = uuid.uuid4()

    def test_admin_payment_list_url(self):
        """Test the admin payment list URL pattern."""
        url = reverse("payments_app:admin_payment_list")
        self.assertEqual(url, "/payments/admin/payments/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.AdminPaymentListView)

    def test_admin_payment_detail_url(self):
        """Test the admin payment detail URL pattern."""
        url = reverse(
            "payments_app:admin_payment_detail",
            kwargs={"payment_id": self.test_payment_id},
        )
        self.assertEqual(url, f"/payments/admin/payments/{self.test_payment_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.AdminPaymentDetailView)

    def test_admin_refund_list_url(self):
        """Test the admin refund list URL pattern."""
        url = reverse("payments_app:admin_refund_list")
        self.assertEqual(url, "/payments/admin/refunds/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.AdminRefundListView)

    def test_admin_refund_detail_url(self):
        """Test the admin refund detail URL pattern."""
        url = reverse(
            "payments_app:admin_refund_detail",
            kwargs={"refund_id": self.test_refund_id},
        )
        self.assertEqual(url, f"/payments/admin/refunds/{self.test_refund_id}/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.AdminRefundDetailView)

    def test_admin_refund_approve_url(self):
        """Test the admin refund approve URL pattern."""
        url = reverse(
            "payments_app:admin_refund_approve",
            kwargs={"refund_id": self.test_refund_id},
        )
        self.assertEqual(url, f"/payments/admin/refunds/{self.test_refund_id}/approve/")

        resolved = resolve(url)
        # admin_approve_refund is a function-based view, not a class-based view
        self.assertEqual(resolved.func, views.admin_approve_refund)

    def test_admin_refund_decline_url(self):
        """Test the admin refund decline URL pattern."""
        url = reverse(
            "payments_app:admin_refund_decline",
            kwargs={"refund_id": self.test_refund_id},
        )
        self.assertEqual(url, f"/payments/admin/refunds/{self.test_refund_id}/decline/")

        resolved = resolve(url)
        # admin_decline_refund is a function-based view, not a class-based view
        self.assertEqual(resolved.func, views.admin_decline_refund)

    def test_admin_payment_analytics_url(self):
        """Test the admin payment analytics URL pattern."""
        url = reverse("payments_app:admin_payment_analytics")
        self.assertEqual(url, "/payments/admin/analytics/")

        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, views.AdminPaymentAnalyticsView)


class PaymentsUrlsIntegrationTest(TestCase):
    """Test URL integration and edge cases."""

    def test_url_namespace(self):
        """Test that all URLs are properly namespaced."""
        # Test that we can reverse URLs with the payments_app namespace
        test_booking_id = uuid.uuid4()
        test_payment_id = uuid.uuid4()

        urls_to_test = [
            ("payments_app:checkout", {"booking_id": test_booking_id}),
            ("payments_app:payment_history", {}),
            ("payments_app:provider_earnings", {}),
            ("payments_app:admin_payment_list", {}),
        ]

        for url_name, kwargs in urls_to_test:
            try:
                url = reverse(url_name, kwargs=kwargs)
                self.assertTrue(url.startswith("/payments/"))
            except Exception as e:
                self.fail(f"Failed to reverse URL {url_name}: {e}")

    def test_uuid_parameter_validation(self):
        """Test that UUID parameters are properly validated."""
        # Test with valid UUID
        valid_uuid = uuid.uuid4()
        url = reverse("payments_app:payment_detail", kwargs={"payment_id": valid_uuid})
        self.assertIn(str(valid_uuid), url)

        # Test URL resolution with UUID
        resolved = resolve(url)
        # Django's UUID path converter returns UUID objects, not strings
        self.assertEqual(resolved.kwargs["payment_id"], valid_uuid)

    def test_all_payment_urls_resolvable(self):
        """Test that all payment URLs are resolvable."""
        test_ids = {
            "booking_id": uuid.uuid4(),
            "payment_id": uuid.uuid4(),
            "refund_id": uuid.uuid4(),
        }

        # List of all URL patterns that should be resolvable
        url_patterns = [
            ("payments_app:checkout", ["booking_id"]),
            ("payments_app:payment_process", ["payment_id"]),
            ("payments_app:payment_success", ["payment_id"]),
            ("payments_app:payment_cancel", ["booking_id"]),
            ("payments_app:payment_history", []),
            ("payments_app:payment_detail", ["payment_id"]),
            ("payments_app:payment_receipt", ["payment_id"]),
            ("payments_app:refund_request", ["payment_id"]),
            ("payments_app:refund_history", []),
            ("payments_app:refund_detail", ["refund_id"]),
            ("payments_app:provider_earnings", []),
            ("payments_app:provider_payment_history", []),
            ("payments_app:provider_payment_detail", ["payment_id"]),
            ("payments_app:provider_payout_history", []),
            ("payments_app:admin_payment_list", []),
            ("payments_app:admin_payment_detail", ["payment_id"]),
            ("payments_app:admin_refund_list", []),
            ("payments_app:admin_refund_detail", ["refund_id"]),
            ("payments_app:admin_refund_approve", ["refund_id"]),
            ("payments_app:admin_refund_decline", ["refund_id"]),
            ("payments_app:admin_payment_analytics", []),
        ]

        for url_name, required_params in url_patterns:
            kwargs = {param: test_ids[param] for param in required_params}

            try:
                url = reverse(url_name, kwargs=kwargs)
                resolved = resolve(url)
                self.assertIsNotNone(resolved.func)
            except Exception as e:
                self.fail(f"Failed to resolve URL {url_name}: {e}")
