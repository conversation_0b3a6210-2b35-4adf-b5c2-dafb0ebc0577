"""
Unit tests for payments_app views.

This module contains comprehensive unit tests for all view classes in the payments_app,
including customer payment views, refund request views, and utility views following the same patterns as accounts_app.
"""

# Standard library imports
from decimal import Decimal
from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages

# Django imports
from django.test import Client, TestCase
from django.urls import reverse

from accounts_app.models import ServiceProviderProfile
from booking_cart_app.models import Booking, Venue

# Local imports
from payments_app.models import Payment, RefundRequest
from venues_app.models import Category

User = get_user_model()


class CustomerPaymentViewsTest(TestCase):
    """Test customer payment views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="Test venue description",
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="pending",
        )

        # Create payment
        self.payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

    def test_checkout_view_requires_login(self):
        """Test that checkout view requires login."""
        url = reverse(
            "payments_app:checkout", kwargs={"booking_id": self.booking.booking_id}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_checkout_view_customer_only(self):
        """Test that checkout view is only accessible to customers."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:checkout", kwargs={"booking_id": self.booking.booking_id}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect with error message

    def test_checkout_view_get(self):
        """Test GET request to checkout view."""
        # Create a new booking without any payments
        new_booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("150.00"),
            status="pending",
        )

        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:checkout", kwargs={"booking_id": new_booking.booking_id}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Secure Checkout")
        self.assertContains(response, new_booking.venue.venue_name)

    def test_payment_history_view(self):
        """Test payment history view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse("payments_app:payment_history")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payment History")
        self.assertContains(response, str(self.payment.payment_id)[:12])

    def test_payment_detail_view(self):
        """Test payment detail view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:payment_detail",
            kwargs={"payment_id": self.payment.payment_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payment Details")
        self.assertContains(response, str(self.payment.amount_paid))

    def test_payment_detail_view_wrong_customer(self):
        """Test payment detail view with wrong customer."""
        other_customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:payment_detail",
            kwargs={"payment_id": self.payment.payment_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_refund_request_view_get(self):
        """Test GET request to refund request view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:refund_request",
            kwargs={"payment_id": self.payment.payment_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Request Refund")
        self.assertContains(response, str(self.payment.amount_paid))

    def test_refund_request_view_post(self):
        """Test POST request to refund request view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:refund_request",
            kwargs={"payment_id": self.payment.payment_id},
        )

        form_data = {
            "reason_category": RefundRequest.SERVICE_NOT_PROVIDED,
            "reason_description": "Service was not provided as expected. I waited for 2 hours but no one showed up.",
            "requested_amount": Decimal("50.00"),
        }

        response = self.client.post(url, data=form_data)
        self.assertEqual(
            response.status_code, 302
        )  # Redirect after successful submission

        # Check that refund request was created
        refund_request = RefundRequest.objects.filter(payment=self.payment).first()
        self.assertIsNotNone(refund_request)
        self.assertEqual(refund_request.customer, self.customer)
        self.assertEqual(refund_request.requested_amount, Decimal("50.00"))

    def test_refund_request_non_refundable_payment(self):
        """Test refund request for non-refundable payment."""
        # Create a failed payment
        failed_payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.FAILED,
        )

        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:refund_request",
            kwargs={"payment_id": failed_payment.payment_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect with error message

    def test_refund_history_view(self):
        """Test refund history view."""
        # Create a refund request
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Test refund request",
            requested_amount=Decimal("50.00"),
        )

        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse("payments_app:refund_history")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Refund History")
        self.assertContains(response, str(refund_request.refund_request_id)[:12])

    def test_payment_receipt_view(self):
        """Test payment receipt view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:payment_receipt",
            kwargs={"payment_id": self.payment.payment_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payment Receipt")
        self.assertContains(response, "CozyWish")
        self.assertContains(response, str(self.payment.amount_paid))

    def test_payment_receipt_failed_payment(self):
        """Test payment receipt view for failed payment."""
        failed_payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.FAILED,
        )

        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:payment_receipt",
            kwargs={"payment_id": failed_payment.payment_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect with error message


class PaymentProcessViewTest(TestCase):
    """Test payment processing view."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="Test venue description",
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="pending",
        )

    def test_payment_process_view_pending_payment(self):
        """Test payment process view with pending payment."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.PENDING,
        )

        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:payment_process", kwargs={"payment_id": payment.payment_id}
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)

        # Check that payment was updated to succeeded (simulated)
        payment.refresh_from_db()
        self.assertEqual(payment.payment_status, Payment.SUCCEEDED)
        self.assertIsNotNone(payment.completed_date)

        # Check that booking was updated
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, "confirmed")

    def test_payment_process_view_already_succeeded(self):
        """Test payment process view with already succeeded payment."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:payment_process", kwargs={"payment_id": payment.payment_id}
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payment Successful!")
        self.assertContains(response, str(payment.amount_paid))


class ProviderPaymentViewsTest(TestCase):
    """Test provider payment views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="Test venue description",
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Create booking and payment
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="confirmed",
        )

        self.payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

    def test_provider_earnings_view_requires_login(self):
        """Test that provider earnings view requires login."""
        url = reverse("payments_app:provider_earnings")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_provider_earnings_view_provider_only(self):
        """Test that provider earnings view is only accessible to providers."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse("payments_app:provider_earnings")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect with error message

    def test_provider_earnings_view_get(self):
        """Test GET request to provider earnings view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse("payments_app:provider_earnings")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Earnings Overview")
        self.assertContains(response, str(self.payment.amount_paid))

    def test_provider_payment_history_view(self):
        """Test provider payment history view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse("payments_app:provider_payment_history")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payment History")
        self.assertContains(response, str(self.payment.payment_id)[:12])

    def test_provider_payment_detail_view(self):
        """Test provider payment detail view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:provider_payment_detail",
            kwargs={"payment_id": self.payment.payment_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payment Details")
        self.assertContains(response, str(self.payment.amount_paid))

    def test_provider_payment_detail_wrong_provider(self):
        """Test provider payment detail view with wrong provider."""
        other_provider = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="service_provider"
        )
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:provider_payment_detail",
            kwargs={"payment_id": self.payment.payment_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_provider_payout_history_view(self):
        """Test provider payout history view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse("payments_app:provider_payout_history")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payout History")
        # Note: This is a placeholder view for future Stripe integration


class AdminPaymentViewsTest(TestCase):
    """Test admin payment views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="Test venue description",
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Create booking and payment
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="confirmed",
        )

        self.payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

        # Create refund request
        self.refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Service was not provided as expected",
            requested_amount=Decimal("50.00"),
        )

    def test_admin_payment_list_requires_admin(self):
        """Test that admin payment list requires admin access."""
        # Test unauthenticated access
        url = reverse("payments_app:admin_payment_list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login

        # Test customer access
        self.client.login(email="<EMAIL>", password="testpass123")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect with error

        # Test provider access
        self.client.login(email="<EMAIL>", password="testpass123")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect with error

    def test_admin_payment_list_view(self):
        """Test admin payment list view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse("payments_app:admin_payment_list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payment Management")
        self.assertContains(response, str(self.payment.payment_id)[:12])

    def test_admin_payment_detail_view(self):
        """Test admin payment detail view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:admin_payment_detail",
            kwargs={"payment_id": self.payment.payment_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payment Details")
        self.assertContains(response, str(self.payment.amount_paid))
        self.assertContains(response, self.customer.email)
        self.assertContains(response, self.provider.email)

    def test_admin_refund_list_view(self):
        """Test admin refund list view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse("payments_app:admin_refund_list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Refund Management")
        self.assertContains(response, str(self.refund_request.refund_request_id)[:12])

    def test_admin_refund_detail_view(self):
        """Test admin refund detail view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:admin_refund_detail",
            kwargs={"refund_request_id": self.refund_request.refund_request_id},
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Refund Request Details")
        self.assertContains(response, str(self.refund_request.requested_amount))
        self.assertContains(response, self.refund_request.reason_description)

    def test_admin_refund_approve_view(self):
        """Test admin refund approve view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:admin_approve_refund",
            kwargs={"refund_request_id": self.refund_request.refund_request_id},
        )

        # Test POST request to approve refund
        form_data = {
            "admin_notes": "Approved for testing purposes",
            "processed_amount": Decimal("50.00"),
        }
        response = self.client.post(url, data=form_data)
        self.assertEqual(response.status_code, 302)  # Redirect after approval

        # Check that refund request was approved
        self.refund_request.refresh_from_db()
        self.assertEqual(self.refund_request.request_status, RefundRequest.APPROVED)
        self.assertEqual(self.refund_request.reviewed_by, self.admin_user)
        self.assertEqual(
            self.refund_request.admin_notes, "Approved for testing purposes"
        )

    def test_admin_refund_decline_view(self):
        """Test admin refund decline view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse(
            "payments_app:admin_decline_refund",
            kwargs={"refund_request_id": self.refund_request.refund_request_id},
        )

        # Test POST request to decline refund
        form_data = {"admin_notes": "Declined - insufficient evidence"}
        response = self.client.post(url, data=form_data)
        self.assertEqual(response.status_code, 302)  # Redirect after decline

        # Check that refund request was declined
        self.refund_request.refresh_from_db()
        self.assertEqual(self.refund_request.request_status, RefundRequest.DECLINED)
        self.assertEqual(self.refund_request.reviewed_by, self.admin_user)
        self.assertEqual(
            self.refund_request.admin_notes, "Declined - insufficient evidence"
        )

    def test_admin_payment_analytics_view(self):
        """Test admin payment analytics view."""
        self.client.login(email="<EMAIL>", password="testpass123")
        url = reverse("payments_app:admin_payment_analytics")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Payment Analytics")
        self.assertContains(response, "Total Revenue")
        # Note: This is a placeholder view for future analytics implementation

    def test_admin_refund_already_processed(self):
        """Test admin cannot approve/decline already processed refund."""
        # First approve the refund
        self.refund_request.approve(self.admin_user, "Initial approval")

        self.client.login(email="<EMAIL>", password="testpass123")

        # Try to decline already approved refund
        url = reverse(
            "payments_app:admin_decline_refund",
            kwargs={"refund_request_id": self.refund_request.refund_request_id},
        )
        response = self.client.post(url, data={"admin_notes": "Trying to decline"})

        # Should redirect with error message
        self.assertEqual(response.status_code, 302)

        # Refund should still be approved
        self.refund_request.refresh_from_db()
        self.assertEqual(self.refund_request.request_status, RefundRequest.APPROVED)


class PaymentViewsIntegrationTest(TestCase):
    """Test payment views integration and edge cases."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="Test venue description",
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="pending",
        )

    def test_payment_workflow_integration(self):
        """Test complete payment workflow integration."""
        self.client.login(email="<EMAIL>", password="testpass123")

        # Step 1: Access checkout page
        checkout_url = reverse(
            "payments_app:checkout", kwargs={"booking_id": self.booking.booking_id}
        )
        response = self.client.get(checkout_url)
        self.assertEqual(response.status_code, 200)

        # Step 2: Submit payment form (simulated)
        form_data = {
            "payment_method": "stripe",
            "billing_name": "John Doe",
            "billing_email": "<EMAIL>",
            "billing_phone": "+1234567890",
            "billing_address": "123 Main St",
            "billing_city": "Test City",
            "billing_state": "CA",
            "billing_zip_code": "12345",
            "accept_terms": True,
            "save_payment_method": False,
        }
        response = self.client.post(checkout_url, data=form_data)

        # Should redirect to payment processing or success page
        self.assertEqual(response.status_code, 302)

        # Step 3: Check that payment was created
        payment = Payment.objects.filter(booking=self.booking).first()
        self.assertIsNotNone(payment)
        self.assertEqual(payment.customer, self.customer)
        self.assertEqual(payment.provider, self.provider)

    def test_refund_workflow_integration(self):
        """Test complete refund workflow integration."""
        # Create a successful payment
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

        self.client.login(email="<EMAIL>", password="testpass123")

        # Step 1: Request refund
        refund_url = reverse(
            "payments_app:refund_request", kwargs={"payment_id": payment.payment_id}
        )
        form_data = {
            "reason_category": RefundRequest.SERVICE_NOT_PROVIDED,
            "reason_description": "Service was not provided as expected. I waited for 2 hours but no one showed up.",
            "requested_amount": Decimal("50.00"),
        }
        response = self.client.post(refund_url, data=form_data)
        self.assertEqual(response.status_code, 302)  # Redirect after submission

        # Step 2: Check refund request was created
        refund_request = RefundRequest.objects.filter(payment=payment).first()
        self.assertIsNotNone(refund_request)
        self.assertEqual(refund_request.customer, self.customer)
        self.assertEqual(refund_request.requested_amount, Decimal("50.00"))

        # Step 3: Admin approves refund (simulate admin login)
        admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="admin",
            is_staff=True,
        )
        self.client.login(email="<EMAIL>", password="testpass123")

        approve_url = reverse(
            "payments_app:admin_approve_refund",
            kwargs={"refund_request_id": refund_request.refund_request_id},
        )
        response = self.client.post(
            approve_url,
            data={"admin_notes": "Approved", "processed_amount": Decimal("50.00")},
        )
        self.assertEqual(response.status_code, 302)

        # Step 4: Check refund was approved
        refund_request.refresh_from_db()
        self.assertEqual(refund_request.request_status, RefundRequest.APPROVED)

    def test_payment_search_and_filtering(self):
        """Test payment search and filtering functionality."""
        # Create multiple payments
        payment1 = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

        payment2 = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("50.00"),
            payment_status=Payment.FAILED,
        )

        self.client.login(email="<EMAIL>", password="testpass123")

        # Test payment history with search
        url = reverse("payments_app:payment_history")
        response = self.client.get(url, {"search_query": str(payment1.payment_id)[:12]})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, str(payment1.payment_id)[:12])

        # Test filtering by status
        response = self.client.get(url, {"status": Payment.SUCCEEDED})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, str(payment1.payment_id)[:12])
        # Should not contain failed payment
        self.assertNotContains(response, str(payment2.payment_id)[:12])
