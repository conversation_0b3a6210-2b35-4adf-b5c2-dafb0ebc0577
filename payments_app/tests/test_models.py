"""
Unit tests for payments_app models.

This module contains comprehensive unit tests for all model classes in the payments_app,
including Payment and RefundRequest models following the same patterns as accounts_app.
"""

# Standard library imports
from decimal import Decimal
from threading import Thread
from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError

# Django imports
from django.test import TestCase, TransactionTestCase
from django.utils import timezone

from booking_cart_app.models import Booking, Venue

# Local imports
from payments_app.models import Payment, RefundRequest
from venues_app.models import Category

User = get_user_model()


class PaymentModelTest(TestCase):
    """Test the Payment model."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Create service provider profile
        from accounts_app.models import ServiceProviderProfile

        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="Test venue description",
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="pending",
        )

    def test_create_payment(self):
        """Test creating a payment."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_method=Payment.STRIPE,
            payment_status=Payment.PENDING,
        )

        self.assertEqual(payment.booking, self.booking)
        self.assertEqual(payment.customer, self.customer)
        self.assertEqual(payment.provider, self.provider)
        self.assertEqual(payment.amount_paid, Decimal("100.00"))
        self.assertEqual(payment.payment_method, Payment.STRIPE)
        self.assertEqual(payment.payment_status, Payment.PENDING)
        self.assertIsNotNone(payment.payment_id)
        self.assertIsNone(payment.completed_date)

    def test_payment_str_representation(self):
        """Test payment string representation."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
        )

        expected = f"Payment {payment.payment_id} - {self.customer.email} - $100.00"
        self.assertEqual(str(payment), expected)

    def test_payment_completed_date_auto_set(self):
        """Test that completed_date is automatically set when payment succeeds."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.PENDING,
        )

        # Initially no completed date
        self.assertIsNone(payment.completed_date)

        # Set to succeeded
        payment.payment_status = Payment.SUCCEEDED
        payment.save()

        # Should now have completed date
        self.assertIsNotNone(payment.completed_date)

    def test_payment_is_refundable_property(self):
        """Test the is_refundable property."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

        # Should be refundable when succeeded and no refunds
        self.assertTrue(payment.is_refundable)

        # Should not be refundable when failed
        payment.payment_status = Payment.FAILED
        payment.save()
        self.assertFalse(payment.is_refundable)

        # Should not be refundable when fully refunded
        payment.payment_status = Payment.SUCCEEDED
        payment.refunded_amount = Decimal("100.00")
        payment.save()
        self.assertFalse(payment.is_refundable)

    def test_remaining_refundable_amount_property(self):
        """Test the remaining_refundable_amount property."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            refunded_amount=Decimal("30.00"),
        )

        self.assertEqual(payment.remaining_refundable_amount, Decimal("70.00"))

    def test_is_fully_refunded_property(self):
        """Test the is_fully_refunded property."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
        )

        # Not fully refunded initially
        self.assertFalse(payment.is_fully_refunded)

        # Partially refunded
        payment.refunded_amount = Decimal("50.00")
        payment.save()
        self.assertFalse(payment.is_fully_refunded)

        # Fully refunded
        payment.refunded_amount = Decimal("100.00")
        payment.save()
        self.assertTrue(payment.is_fully_refunded)

    def test_process_refund_method(self):
        """Test the process_refund method."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

        # Process partial refund
        payment.process_refund(Decimal("30.00"), "Test refund")

        self.assertEqual(payment.refunded_amount, Decimal("30.00"))
        self.assertEqual(payment.payment_status, Payment.PARTIALLY_REFUNDED)

        # Process full refund
        payment.process_refund(Decimal("70.00"), "Full refund")

        self.assertEqual(payment.refunded_amount, Decimal("100.00"))
        self.assertEqual(payment.payment_status, Payment.REFUNDED)

    def test_process_refund_validation(self):
        """Test process_refund method validation."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.FAILED,  # Not refundable
        )

        with self.assertRaises(ValidationError):
            payment.process_refund(Decimal("50.00"))

        # Test exceeding refundable amount
        payment.payment_status = Payment.SUCCEEDED
        payment.save()

        with self.assertRaises(ValidationError):
            payment.process_refund(Decimal("150.00"))  # More than paid

    def test_payment_clean_validation(self):
        """Test payment model validation."""
        # Test refunded amount exceeding paid amount
        payment = Payment(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            refunded_amount=Decimal("150.00"),  # More than paid
        )

        with self.assertRaises(ValidationError):
            payment.clean()

    def test_payment_indexes(self):
        """Test that payment indexes are created properly."""
        # This test ensures the model is properly configured
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
        )

        # Test querying by indexed fields
        payments = Payment.objects.filter(customer=self.customer)
        self.assertEqual(payments.count(), 1)

        payments = Payment.objects.filter(provider=self.provider)
        self.assertEqual(payments.count(), 1)

        payments = Payment.objects.filter(payment_status=Payment.PENDING)
        self.assertEqual(payments.count(), 1)

    def test_unique_successful_payment_per_booking(self):
        """Ensure only one successful payment per booking is allowed."""
        Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

        with self.assertRaises(IntegrityError):
            Payment.objects.create(
                booking=self.booking,
                customer=self.customer,
                provider=self.provider,
                amount_paid=Decimal("50.00"),
                payment_status=Payment.SUCCEEDED,
            )


class RefundRequestModelTest(TestCase):
    """Test the RefundRequest model."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="admin",
            is_staff=True,
        )

        # Create service provider profile
        from accounts_app.models import ServiceProviderProfile

        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="Test venue description",
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Create booking and payment
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="confirmed",
        )

        self.payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

    def test_create_refund_request(self):
        """Test creating a refund request."""
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Service was not provided as expected",
            requested_amount=Decimal("50.00"),
        )

        self.assertEqual(refund_request.payment, self.payment)
        self.assertEqual(refund_request.customer, self.customer)
        self.assertEqual(
            refund_request.reason_category, RefundRequest.SERVICE_NOT_PROVIDED
        )
        self.assertEqual(refund_request.requested_amount, Decimal("50.00"))
        self.assertEqual(refund_request.request_status, RefundRequest.PENDING)
        self.assertIsNotNone(refund_request.refund_request_id)

    def test_refund_request_str_representation(self):
        """Test refund request string representation."""
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Test reason",
            requested_amount=Decimal("50.00"),
        )

        expected = f"Refund Request {refund_request.refund_request_id} - {self.customer.email} - $50.00"
        self.assertEqual(str(refund_request), expected)

    def test_approve_refund_request(self):
        """Test approving a refund request."""
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Test reason",
            requested_amount=Decimal("50.00"),
        )

        refund_request.approve(self.admin_user, "Approved for testing")

        self.assertEqual(refund_request.request_status, RefundRequest.APPROVED)
        self.assertEqual(refund_request.reviewed_by, self.admin_user)
        self.assertEqual(refund_request.admin_notes, "Approved for testing")
        self.assertIsNotNone(refund_request.reviewed_at)

    def test_decline_refund_request(self):
        """Test declining a refund request."""
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Test reason",
            requested_amount=Decimal("50.00"),
        )

        refund_request.decline(self.admin_user, "Declined for testing")

        self.assertEqual(refund_request.request_status, RefundRequest.DECLINED)
        self.assertEqual(refund_request.reviewed_by, self.admin_user)
        self.assertEqual(refund_request.admin_notes, "Declined for testing")
        self.assertIsNotNone(refund_request.reviewed_at)

    def test_process_refund_request(self):
        """Test processing an approved refund request."""
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Test reason",
            requested_amount=Decimal("50.00"),
        )

        # First approve the request
        refund_request.approve(self.admin_user, "Approved")

        # Then process it
        refund_request.process_refund(Decimal("50.00"))

        self.assertEqual(refund_request.request_status, RefundRequest.PROCESSED)
        self.assertEqual(refund_request.processed_amount, Decimal("50.00"))

        # Check that payment was updated
        self.payment.refresh_from_db()
        self.assertEqual(self.payment.refunded_amount, Decimal("50.00"))
        self.assertEqual(self.payment.payment_status, Payment.PARTIALLY_REFUNDED)

    def test_refund_request_validation(self):
        """Test refund request validation."""
        # Test customer mismatch
        other_customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        refund_request = RefundRequest(
            payment=self.payment,
            customer=other_customer,  # Different customer
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Test reason",
            requested_amount=Decimal("50.00"),
        )

        with self.assertRaises(ValidationError):
            refund_request.clean()

        # Test exceeding refundable amount
        refund_request = RefundRequest(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Test reason",
            requested_amount=Decimal("150.00"),  # More than payment amount
        )

        with self.assertRaises(ValidationError):
            refund_request.clean()

    def test_refund_request_status_validation(self):
        """Test refund request status change validation."""
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description="Test reason",
            requested_amount=Decimal("50.00"),
        )

        # Approve the request
        refund_request.approve(self.admin_user, "Approved")

        # Try to approve again (should fail)
        with self.assertRaises(ValidationError):
            refund_request.approve(self.admin_user, "Approved again")

        # Try to decline approved request (should fail)
        with self.assertRaises(ValidationError):
            refund_request.decline(self.admin_user, "Declined")

        # Try to process non-approved request
        pending_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.POOR_SERVICE_QUALITY,
            reason_description="Poor quality",
            requested_amount=Decimal("30.00"),
        )

        with self.assertRaises(ValidationError):
            pending_request.process_refund()


class RefundConcurrencyTest(TransactionTestCase):
    """Test concurrent refund processing safety."""

    def setUp(self):
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )
        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )
        from accounts_app.models import ServiceProviderProfile

        provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )
        category = Category.objects.create(name="Test Category", description="desc")
        venue = Venue.objects.create(
            service_provider=provider_profile,
            venue_name="Test Venue",
            short_description="desc",
            state="Test",
            county="Test",
            city="Test",
            street_number="1",
            street_name="Test",
            operating_hours="9-5",
            tags="t",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=venue,
            total_price=Decimal("100.00"),
            status="confirmed",
        )
        self.payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

    def test_concurrent_refund_processing(self):
        """Simulate two concurrent refunds and ensure amount is capped."""
        results = {}

        def do_refund(key):
            try:
                Payment.objects.get(pk=self.payment.pk).process_refund(Decimal("60.00"))
                results[key] = "success"
            except ValidationError:
                results[key] = "error"

        t1 = Thread(target=do_refund, args=("t1",))
        t2 = Thread(target=do_refund, args=("t2",))
        t1.start()
        t2.start()
        t1.join()
        t2.join()

        self.payment.refresh_from_db()
        self.assertLessEqual(self.payment.refunded_amount, self.payment.amount_paid)
        self.assertIn(results["t1"], ["success", "error"])
        self.assertIn(results["t2"], ["success", "error"])
