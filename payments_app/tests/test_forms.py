"""
Unit tests for payments_app forms.

This module contains comprehensive unit tests for all form classes in the payments_app,
including StripeCheckoutForm, RefundRequestForm, and search forms following the same patterns as accounts_app.
"""

# Standard library imports
from decimal import Decimal

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

# Django imports
from django.test import TestCase

from accounts_app.models import ServiceProviderProfile
from booking_cart_app.models import Booking, Venue

# Local imports
from payments_app.forms import (
    PaymentSearchForm,
    RefundRequestForm,
    RefundSearchForm,
    StripeCheckoutForm,
)
from payments_app.models import Payment, RefundRequest
from venues_app.models import Category

User = get_user_model()


class StripeCheckoutFormTest(TestCase):
    """Test the StripeCheckoutForm."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="Test venue description",
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="pending",
        )

    def test_valid_form(self):
        """Test form with valid data."""
        form_data = {
            "payment_method": "stripe",
            "billing_name": "John Doe",
            "billing_email": "<EMAIL>",
            "billing_phone": "+**********",
            "billing_address": "123 Main St",
            "billing_city": "Test City",
            "billing_state": "CA",
            "billing_zip_code": "12345",
            "accept_terms": True,
            "save_payment_method": False,
        }

        form = StripeCheckoutForm(
            data=form_data, user=self.customer, booking=self.booking
        )
        self.assertTrue(form.is_valid())

    def test_billing_email_validation(self):
        """Test billing email must match user email."""
        form_data = {
            "payment_method": "stripe",
            "billing_name": "John Doe",
            "billing_email": "<EMAIL>",  # Different from user email
            "billing_phone": "+**********",
            "billing_address": "123 Main St",
            "billing_city": "Test City",
            "billing_state": "CA",
            "billing_zip_code": "12345",
            "accept_terms": True,
            "save_payment_method": False,
        }

        form = StripeCheckoutForm(
            data=form_data, user=self.customer, booking=self.booking
        )
        self.assertFalse(form.is_valid())
        self.assertIn("billing_email", form.errors)

    def test_required_fields(self):
        """Test that required fields are validated."""
        form_data = {}

        form = StripeCheckoutForm(
            data=form_data, user=self.customer, booking=self.booking
        )
        self.assertFalse(form.is_valid())

        required_fields = [
            "billing_name",
            "billing_email",
            "billing_address",
            "billing_city",
            "billing_state",
            "billing_zip_code",
            "accept_terms",
        ]

        for field in required_fields:
            self.assertIn(field, form.errors)


class RefundRequestFormTest(TestCase):
    """Test the RefundRequestForm."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="customer"
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="service_provider",
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345",
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Test Category", description="Test category description"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="Test venue description",
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )

        # Create booking and payment
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal("100.00"),
            status="confirmed",
        )

        self.payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal("100.00"),
            payment_status=Payment.SUCCEEDED,
        )

    def test_valid_form(self):
        """Test form with valid data."""
        form_data = {
            "reason_category": RefundRequest.SERVICE_NOT_PROVIDED,
            "reason_description": "Service was not provided as expected. I waited for 2 hours but no one showed up.",
            "requested_amount": Decimal("50.00"),
        }

        form = RefundRequestForm(
            data=form_data, payment=self.payment, user=self.customer
        )
        self.assertTrue(form.is_valid())

    def test_requested_amount_validation(self):
        """Test requested amount validation."""
        # Test amount exceeding payment amount
        form_data = {
            "reason_category": RefundRequest.SERVICE_NOT_PROVIDED,
            "reason_description": "Service was not provided as expected.",
            "requested_amount": Decimal("150.00"),  # More than payment amount
        }

        form = RefundRequestForm(
            data=form_data, payment=self.payment, user=self.customer
        )
        self.assertFalse(form.is_valid())
        self.assertIn("requested_amount", form.errors)

        # Test zero amount
        form_data["requested_amount"] = Decimal("0.00")
        form = RefundRequestForm(
            data=form_data, payment=self.payment, user=self.customer
        )
        self.assertFalse(form.is_valid())
        self.assertIn("requested_amount", form.errors)

    def test_reason_description_validation(self):
        """Test reason description validation."""
        # Test short description
        form_data = {
            "reason_category": RefundRequest.SERVICE_NOT_PROVIDED,
            "reason_description": "Short",  # Too short
            "requested_amount": Decimal("50.00"),
        }

        form = RefundRequestForm(
            data=form_data, payment=self.payment, user=self.customer
        )
        self.assertFalse(form.is_valid())
        self.assertIn("reason_description", form.errors)

    def test_form_save(self):
        """Test form save method."""
        form_data = {
            "reason_category": RefundRequest.SERVICE_NOT_PROVIDED,
            "reason_description": "Service was not provided as expected.",
            "requested_amount": Decimal("50.00"),
        }

        form = RefundRequestForm(
            data=form_data, payment=self.payment, user=self.customer
        )
        self.assertTrue(form.is_valid())

        refund_request = form.save()

        self.assertEqual(refund_request.payment, self.payment)
        self.assertEqual(refund_request.customer, self.customer)
        self.assertEqual(
            refund_request.reason_category, RefundRequest.SERVICE_NOT_PROVIDED
        )
        self.assertEqual(refund_request.requested_amount, Decimal("50.00"))


class PaymentSearchFormTest(TestCase):
    """Test the PaymentSearchForm."""

    def test_valid_form(self):
        """Test form with valid data."""
        form_data = {
            "search_query": "test payment",
            "status": Payment.SUCCEEDED,
            "payment_method": Payment.STRIPE,
            "date_from": "2023-01-01",
            "date_to": "2023-12-31",
        }

        form = PaymentSearchForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_date_validation(self):
        """Test date range validation."""
        form_data = {
            "date_from": "2023-12-31",
            "date_to": "2023-01-01",  # Earlier than from date
        }

        form = PaymentSearchForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("__all__", form.errors)

    def test_empty_form(self):
        """Test empty form is valid."""
        form = PaymentSearchForm(data={})
        self.assertTrue(form.is_valid())


class RefundSearchFormTest(TestCase):
    """Test the RefundSearchForm."""

    def test_valid_form(self):
        """Test form with valid data."""
        form_data = {
            "search_query": "test refund",
            "status": RefundRequest.PENDING,
            "reason_category": RefundRequest.SERVICE_NOT_PROVIDED,
            "date_from": "2023-01-01",
            "date_to": "2023-12-31",
        }

        form = RefundSearchForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_date_validation(self):
        """Test date range validation."""
        form_data = {
            "date_from": "2023-12-31",
            "date_to": "2023-01-01",  # Earlier than from date
        }

        form = RefundSearchForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("__all__", form.errors)

    def test_empty_form(self):
        """Test empty form is valid."""
        form = RefundSearchForm(data={})
        self.assertTrue(form.is_valid())
