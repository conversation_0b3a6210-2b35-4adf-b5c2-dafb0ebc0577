from datetime import timedelta

from django.core.management.base import BaseCommand
from django.utils import timezone

from payments_app.models import Payment


class Command(BaseCommand):
    help = "Cleanup stale failed or cancelled payments"

    def handle(self, *args, **options):
        stale_time = timezone.now() - timedelta(days=1)
        stale_payments = Payment.objects.filter(
            payment_status__in=[
                Payment.PENDING,
                Payment.PROCESSING,
                Payment.REQUIRES_ACTION,
            ],
            payment_date__lt=stale_time,
        )
        for payment in stale_payments:
            payment.payment_status = Payment.FAILED
            payment.failure_reason = payment.failure_reason or "Auto cleanup"
            payment.save()

        delete_time = timezone.now() - timedelta(days=30)
        Payment.objects.filter(
            payment_status__in=[Payment.FAILED, Payment.CANCELLED],
            payment_date__lt=delete_time,
        ).delete()

        self.stdout.write(self.style.SUCCESS("Cleanup complete"))
