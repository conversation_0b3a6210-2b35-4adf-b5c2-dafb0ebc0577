"""View package for payments_app organized into feature modules."""

# --- Local App Imports ---
from ..logging_utils import log_payment_initiated

# --- Admin Views ---
from .admin import (
    AdminDisputedPaymentsView,
    AdminPaymentAnalyticsView,
    AdminPaymentDetailView,
    AdminPaymentListView,
    AdminPaymentMixin,
    AdminRefundDetailView,
    AdminRefundListView,
    AdminRefundManagementView,
    admin_approve_refund,
    admin_decline_refund,
)
from .common import (
    EMAIL_NOTIFICATIONS_ENABLED,
    INVALID_BOOKING_ERROR,
    INVALID_PAYMENT_ERROR,
    PAYMENT_COMPLETED_SUCCESS,
    PAYMENT_FAILED_ERROR,
    PAYMENT_INITIATED_SUCCESS,
    REFUND_REQUEST_ERROR,
    REFUND_REQUESTED_SUCCESS,
    payment_receipt_etag,
    payment_receipt_view,
    send_payment_receipt_email_async,
    send_payout_email_async,
    stripe_webhook_view,
)

# --- Customer Views ---
from .customer import (
    CustomerPaymentMixin,
    PaymentCancelView,
    PaymentDetailView,
    PaymentHistoryView,
    PaymentProcessView,
    PaymentSuccessView,
    RefundConfirmationView,
    RefundDetailView,
    RefundHistoryView,
    RefundRequestView,
    StripeCheckoutView,
)

# --- Provider Views ---
from .provider import (
    ProviderEarningsOverviewView,
    ProviderEarningsView,
    ProviderPaymentDetailView,
    ProviderPaymentHistoryView,
    ProviderPaymentMixin,
    ProviderPayoutHistoryView,
)

__all__ = [
    # Common utilities
    "log_payment_initiated",
    "PAYMENT_INITIATED_SUCCESS",
    "PAYMENT_COMPLETED_SUCCESS",
    "PAYMENT_FAILED_ERROR",
    "REFUND_REQUESTED_SUCCESS",
    "REFUND_REQUEST_ERROR",
    "INVALID_PAYMENT_ERROR",
    "INVALID_BOOKING_ERROR",
    "send_payment_receipt_email_async",
    "send_payout_email_async",
    "EMAIL_NOTIFICATIONS_ENABLED",
    "payment_receipt_etag",
    "payment_receipt_view",
    "stripe_webhook_view",
    # Customer views
    "CustomerPaymentMixin",
    "StripeCheckoutView",
    "PaymentProcessView",
    "PaymentSuccessView",
    "PaymentCancelView",
    "PaymentHistoryView",
    "PaymentDetailView",
    "RefundRequestView",
    "RefundConfirmationView",
    "RefundHistoryView",
    "RefundDetailView",
    # Provider views
    "ProviderPaymentMixin",
    "ProviderEarningsOverviewView",
    "ProviderEarningsView",
    "ProviderPaymentHistoryView",
    "ProviderPaymentDetailView",
    "ProviderPayoutHistoryView",
    # Admin views
    "AdminPaymentMixin",
    "AdminPaymentListView",
    "AdminPaymentDetailView",
    "AdminRefundManagementView",
    "AdminRefundListView",
    "AdminRefundDetailView",
    "admin_approve_refund",
    "admin_decline_refund",
    "AdminDisputedPaymentsView",
    "AdminPaymentAnalyticsView",
]
