"""Customer-facing payment and refund views."""

# --- Standard Library Imports ---
import hashlib
import json
from decimal import Decimal

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import Http404, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import etag
from django.views.generic import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ail<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>iew,
    <PERSON><PERSON><PERSON><PERSON>,
    Temp<PERSON><PERSON><PERSON><PERSON>,
)

# --- Local App Imports ---
from booking_cart_app.models import Booking

from ..forms import (
    PaymentSearchForm,
    RefundRequestForm,
    RefundSearchForm,
    StripeCheckoutForm,
)
from ..logging_utils import (
    log_admin_disputed_payments_viewed,
    log_admin_payment_analytics_viewed,
    log_admin_refund_detail_viewed,
    log_admin_refund_management_viewed,
    log_customer_payment_history_viewed,
    log_customer_refund_history_viewed,
    log_payment_completed,
    log_payment_error,
    log_payment_failed,
    log_payment_initiated,
    log_provider_earnings_viewed,
    log_provider_payment_detail_viewed,
    log_provider_payment_history_viewed,
    log_provider_payout_history_viewed,
    log_refund_approved,
    log_refund_declined,
    log_refund_processed,
    log_refund_requested,
    log_stripe_event,
    performance_monitor,
)
from ..models import Payment, RefundRequest
from ..utils import quantize_money
from .common import (
    EMAIL_NOTIFICATIONS_ENABLED,
    INVALID_BOOKING_ERROR,
    INVALID_PAYMENT_ERROR,
    PAYMENT_COMPLETED_SUCCESS,
    PAYMENT_FAILED_ERROR,
    PAYMENT_INITIATED_SUCCESS,
    REFUND_REQUEST_ERROR,
    REFUND_REQUESTED_SUCCESS,
    payment_receipt_etag,
    payment_receipt_view,
    send_payment_receipt_email_async,
    send_payout_email_async,
    stripe_webhook_view,
)

# --- Customer Payment Views ---


class CustomerPaymentMixin(LoginRequiredMixin):
    """Mixin to ensure only customers can access payment views."""

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()

        if not hasattr(request.user, "role") or request.user.role != "customer":
            messages.error(
                request, _("You must be a customer to access payment features.")
            )
            return redirect("venues_app:home")

        return super().dispatch(request, *args, **kwargs)


class StripeCheckoutView(CustomerPaymentMixin, FormView):
    """
    View for Stripe checkout process (placeholder implementation).
    In production, this would integrate with Stripe's Payment Intents API.
    """

    template_name = "payments_app/customer/checkout.html"
    form_class = StripeCheckoutForm

    def dispatch(self, request, *args, **kwargs):
        # Check authentication first
        if not request.user.is_authenticated:
            messages.error(request, _("Please log in to access this page."))
            return redirect("accounts_app:customer_login")

        # Check if user is a customer
        if request.user.role != "customer":
            messages.error(request, _("Only customers can access the checkout page."))
            return redirect(
                "accounts_app:service_provider_profile"
                if request.user.role == "service_provider"
                else "accounts_app:admin_dashboard"
            )

        # Get booking from URL parameter
        self.booking = get_object_or_404(
            Booking, booking_id=kwargs.get("booking_id"), customer=request.user
        )

        # Check if booking is eligible for payment
        if self.booking.status not in ["pending", "confirmed"]:
            messages.error(request, _("This booking is not eligible for payment."))
            return redirect(
                "booking_cart_app:booking_detail", booking_slug=self.booking.slug
            )

        # Check if payment already exists
        if self.booking.payments.filter(
            payment_status__in=["succeeded", "processing"]
        ).exists():
            messages.info(
                request, _("Payment for this booking has already been processed.")
            )
            return redirect(
                "booking_cart_app:booking_detail", booking_slug=self.booking.slug
            )

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        kwargs["booking"] = self.booking
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["booking"] = self.booking
        context["total_amount"] = self.booking.total_price

        # Calculate breakdown (placeholder for future discount integration)
        context["price_breakdown"] = {
            "subtotal": self.booking.total_price,
            "discount": Decimal("0.00"),
            "tax": Decimal("0.00"),
            "service_fee": Decimal("0.00"),
            "total": self.booking.total_price,
        }

        return context

    @performance_monitor("stripe_checkout")
    def form_valid(self, form):
        """Process the checkout form and create payment record."""
        try:
            # Create payment record
            payment = Payment.objects.create(
                booking=self.booking,
                customer=self.request.user,
                provider=self.booking.venue.service_provider.user,  # Get user from service provider profile
                amount_paid=self.booking.total_price,
                payment_method=Payment.STRIPE,
                payment_status=Payment.PENDING,
            )

            # Log payment initiation
            log_payment_initiated(
                user=self.request.user, payment=payment, request=self.request
            )

            # In production, this would redirect to Stripe checkout
            # For now, we'll simulate a successful payment
            messages.success(self.request, PAYMENT_INITIATED_SUCCESS)

            # Redirect to payment processing page (placeholder)
            return redirect(
                "payments_app:payment_process", payment_id=payment.payment_id
            )

        except Exception as e:
            log_payment_error(
                error_type="checkout_error",
                error_message=str(e),
                user=self.request.user,
                request=self.request,
                additional_data={"booking_id": str(self.booking.booking_id)},
            )
            messages.error(self.request, PAYMENT_FAILED_ERROR)
            return self.form_invalid(form)


class PaymentProcessView(CustomerPaymentMixin, DetailView):
    """
    View to simulate payment processing (placeholder for Stripe integration).
    In production, this would handle Stripe webhook responses.
    """

    model = Payment
    template_name = "payments_app/customer/payment_process.html"
    context_object_name = "payment"
    pk_url_kwarg = "payment_id"

    def get_object(self, queryset=None):
        payment = get_object_or_404(
            Payment,
            payment_id=self.kwargs.get("payment_id"),
            customer=self.request.user,
        )
        return payment

    def get(self, request, *args, **kwargs):
        """Simulate payment processing and update status."""
        payment = self.get_object()

        # Simulate successful payment (placeholder)
        if payment.payment_status == Payment.PENDING:
            payment.payment_status = Payment.SUCCEEDED
            payment.completed_date = timezone.now()
            payment.save()

            # Update booking status
            payment.booking.status = "confirmed"
            payment.booking.save()

            # Log successful payment
            log_payment_completed(user=request.user, payment=payment, request=request)

            if EMAIL_NOTIFICATIONS_ENABLED:
                send_payment_receipt_email_async(payment)

            messages.success(request, PAYMENT_COMPLETED_SUCCESS)

        return super().get(request, *args, **kwargs)


class PaymentSuccessView(CustomerPaymentMixin, DetailView):
    """
    View to display payment success page.
    """

    model = Payment
    template_name = "payments_app/customer/payment_success.html"
    context_object_name = "payment"
    pk_url_kwarg = "payment_id"

    def get_object(self, queryset=None):
        payment = get_object_or_404(
            Payment,
            payment_id=self.kwargs.get("payment_id"),
            customer=self.request.user,
        )
        return payment


class PaymentCancelView(CustomerPaymentMixin, DetailView):
    """
    View to handle payment cancellation.
    """

    model = Booking
    template_name = "payments_app/customer/payment_cancel.html"
    context_object_name = "booking"
    pk_url_kwarg = "booking_id"

    def get_object(self, queryset=None):
        booking = get_object_or_404(
            Booking,
            booking_id=self.kwargs.get("booking_id"),
            customer=self.request.user,
        )
        return booking


class RefundDetailView(CustomerPaymentMixin, DetailView):
    """
    View to display refund request details.
    """

    model = RefundRequest
    template_name = "payments_app/customer/refund_detail.html"
    context_object_name = "refund_request"
    pk_url_kwarg = "refund_id"

    def get_object(self, queryset=None):
        refund_request = get_object_or_404(
            RefundRequest,
            refund_request_id=self.kwargs.get("refund_id"),
            customer=self.request.user,
        )
        return refund_request


class PaymentHistoryView(CustomerPaymentMixin, ListView):
    """View for displaying customer's payment history with search and filtering."""

    model = Payment
    template_name = "payments_app/customer/payment_history.html"
    context_object_name = "payments"
    paginate_by = 10

    def get(self, request, *args, **kwargs):
        self.object_list = self.get_queryset()
        fmt = request.GET.get("format")
        if fmt in ["csv", "pdf"]:
            return self.download_history(self.object_list, fmt)
        context = self.get_context_data()
        return self.render_to_response(context)

    def get_queryset(self):
        cache_key = (
            f"payment_history_{self.request.user.id}_"
            + hashlib.md5(
                json.dumps(self.request.GET, sort_keys=True).encode()
            ).hexdigest()
        )
        cached_ids = cache.get(cache_key)
        if cached_ids is not None:
            return (
                Payment.objects.filter(id__in=cached_ids)
                .select_related("booking", "booking__venue", "provider")
                .prefetch_related("refund_requests")
                .order_by("-payment_date")
            )

        queryset = (
            Payment.objects.filter(customer=self.request.user)
            .select_related("booking", "booking__venue", "provider")
            .prefetch_related("refund_requests")
        )

        # Apply search and filters
        form = PaymentSearchForm(self.request.GET)
        if form.is_valid():
            search_query = form.cleaned_data.get("search_query")
            if search_query:
                queryset = queryset.filter(
                    Q(payment_id__icontains=search_query)
                    | Q(booking__booking_id__icontains=search_query)
                    | Q(amount_paid__icontains=search_query)
                )

            status = form.cleaned_data.get("status")
            if status:
                queryset = queryset.filter(payment_status=status)

            payment_method = form.cleaned_data.get("payment_method")
            if payment_method:
                queryset = queryset.filter(payment_method=payment_method)

            date_from = form.cleaned_data.get("date_from")
            if date_from:
                queryset = queryset.filter(payment_date__date__gte=date_from)

            date_to = form.cleaned_data.get("date_to")
            if date_to:
                queryset = queryset.filter(payment_date__date__lte=date_to)

        sort_field = self.request.GET.get("sort", "payment_date")
        direction = self.request.GET.get("dir", "desc")
        allowed = {
            "payment_id": "payment_id",
            "payment_date": "payment_date",
            "amount_paid": "amount_paid",
            "payment_status": "payment_status",
        }
        sort = allowed.get(sort_field, "payment_date")
        if direction != "asc":
            sort = "-" + sort

        ordered = queryset.order_by(sort)
        cache.set(cache_key, list(ordered.values_list("id", flat=True)), 300)
        return ordered

    def download_history(self, queryset, fmt):
        if fmt == "csv":
            import csv

            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = "attachment; filename=payment_history.csv"
            writer = csv.writer(response)
            writer.writerow(["Payment ID", "Date", "Amount", "Status"])
            for p in queryset:
                writer.writerow(
                    [
                        p.payment_id,
                        p.payment_date.strftime("%Y-%m-%d"),
                        p.amount_paid,
                        p.get_payment_status_display(),
                    ]
                )
            return response
        elif fmt == "pdf":
            from io import BytesIO

            from reportlab.lib.pagesizes import letter
            from reportlab.pdfgen import canvas

            buffer = BytesIO()
            pdf = canvas.Canvas(buffer, pagesize=letter)
            y = 750
            pdf.drawString(50, y, "Payment History")
            y -= 20
            for p in queryset:
                line = f"{p.payment_id} | {p.payment_date.strftime('%Y-%m-%d')} | ${p.amount_paid} | {p.get_payment_status_display()}"
                pdf.drawString(50, y, line)
                y -= 15
                if y < 50:
                    pdf.showPage()
                    y = 750
            pdf.save()
            buffer.seek(0)
            response = HttpResponse(buffer.getvalue(), content_type="application/pdf")
            response["Content-Disposition"] = "attachment; filename=payment_history.pdf"
            return response
        raise Http404

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = PaymentSearchForm(self.request.GET)
        context["search_form"] = form

        active = []
        if form.is_valid():
            cd = form.cleaned_data
            if cd.get("search_query"):
                active.append(_("Search: ") + cd["search_query"])
            if cd.get("status"):
                active.append(
                    _("Status: ")
                    + dict(Payment.STATUS_CHOICES).get(cd["status"], cd["status"])
                )
            if cd.get("payment_method"):
                active.append(
                    _("Method: ")
                    + dict(Payment.PAYMENT_METHOD_CHOICES).get(
                        cd["payment_method"], cd["payment_method"]
                    )
                )
            if cd.get("date_from"):
                active.append(_("From: ") + cd["date_from"].strftime("%Y-%m-%d"))
            if cd.get("date_to"):
                active.append(_("To: ") + cd["date_to"].strftime("%Y-%m-%d"))
        context["active_filters"] = active

        # Calculate summary statistics
        all_payments = Payment.objects.filter(customer=self.request.user)
        context["payment_stats"] = {
            "total_payments": all_payments.count(),
            "total_amount": sum(p.amount_paid for p in all_payments),
            "successful_payments": all_payments.filter(
                payment_status=Payment.SUCCEEDED
            ).count(),
            "pending_payments": all_payments.filter(
                payment_status=Payment.PENDING
            ).count(),
        }

        # Log customer payment history view
        log_customer_payment_history_viewed(
            customer_user=self.request.user,
            request=self.request,
            filters=dict(self.request.GET.items()),
        )

        return context


class PaymentDetailView(CustomerPaymentMixin, DetailView):
    """View for displaying detailed payment information."""

    model = Payment
    template_name = "payments_app/customer/payment_detail.html"
    context_object_name = "payment"
    pk_url_kwarg = "payment_id"

    def get_object(self, queryset=None):
        payment = get_object_or_404(
            Payment,
            payment_id=self.kwargs.get("payment_id"),
            customer=self.request.user,
        )
        return payment

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        payment = self.get_object()

        # Get related refund requests
        context["refund_requests"] = payment.refund_requests.all().order_by(
            "-created_at"
        )

        # Check if payment is refundable
        context["can_request_refund"] = (
            payment.is_refundable
            and not payment.refund_requests.filter(
                request_status__in=["pending", "approved"]
            ).exists()
        )

        return context


class RefundRequestView(CustomerPaymentMixin, CreateView):
    """View for customers to request refunds for their payments."""

    model = RefundRequest
    form_class = RefundRequestForm
    template_name = "payments_app/customer/refund_request.html"

    def dispatch(self, request, *args, **kwargs):
        # Check authentication first
        if not request.user.is_authenticated:
            messages.error(request, _("Please log in to access this page."))
            return redirect("accounts_app:customer_login")

        # Get payment from URL parameter
        self.payment = get_object_or_404(
            Payment, payment_id=kwargs.get("payment_id"), customer=request.user
        )

        # Check if payment is refundable
        if not self.payment.is_refundable:
            messages.error(request, _("This payment is not eligible for refund."))
            return redirect(
                "payments_app:payment_detail", payment_id=self.payment.payment_id
            )

        # Check if there's already a pending refund request
        if self.payment.refund_requests.filter(
            request_status__in=["pending", "approved"]
        ).exists():
            messages.error(
                request,
                _("There is already a pending refund request for this payment."),
            )
            return redirect(
                "payments_app:payment_detail", payment_id=self.payment.payment_id
            )

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["payment"] = self.payment
        kwargs["user"] = self.request.user
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["payment"] = self.payment
        return context

    @performance_monitor("refund_request_submission")
    def form_valid(self, form):
        """Process the refund request form."""
        try:
            refund_request = form.save()

            # Log refund request
            log_refund_requested(
                user=self.request.user,
                refund_request=refund_request,
                request=self.request,
            )

            messages.success(self.request, REFUND_REQUESTED_SUCCESS)
            return redirect(
                "payments_app:refund_confirmation",
                refund_request_id=refund_request.refund_request_id,
            )

        except Exception as e:
            log_payment_error(
                error_type="refund_request_error",
                error_message=str(e),
                user=self.request.user,
                request=self.request,
                payment=self.payment,
            )
            messages.error(self.request, REFUND_REQUEST_ERROR)
            return self.form_invalid(form)


class RefundConfirmationView(CustomerPaymentMixin, DetailView):
    """View to display refund request confirmation."""

    model = RefundRequest
    template_name = "payments_app/customer/refund_confirmation.html"
    context_object_name = "refund_request"
    pk_url_kwarg = "refund_request_id"

    def get_object(self, queryset=None):
        refund_request = get_object_or_404(
            RefundRequest,
            refund_request_id=self.kwargs.get("refund_request_id"),
            customer=self.request.user,
        )
        return refund_request

    def get(self, request, *args, **kwargs):
        messages.success(request, REFUND_REQUESTED_SUCCESS)
        return super().get(request, *args, **kwargs)


class RefundHistoryView(CustomerPaymentMixin, ListView):
    """View for displaying customer's refund request history."""

    model = RefundRequest
    template_name = "payments_app/customer/refund_history.html"
    context_object_name = "refund_requests"
    paginate_by = 10

    def get_queryset(self):
        queryset = RefundRequest.objects.filter(
            customer=self.request.user
        ).select_related("payment", "payment__booking", "payment__booking__venue")

        # Apply search and filters
        form = RefundSearchForm(self.request.GET)
        if form.is_valid():
            search_query = form.cleaned_data.get("search_query")
            if search_query:
                queryset = queryset.filter(
                    Q(refund_request_id__icontains=search_query)
                    | Q(payment__payment_id__icontains=search_query)
                    | Q(reason_description__icontains=search_query)
                )

            status = form.cleaned_data.get("status")
            if status:
                queryset = queryset.filter(request_status=status)

            reason_category = form.cleaned_data.get("reason_category")
            if reason_category:
                queryset = queryset.filter(reason_category=reason_category)

            date_from = form.cleaned_data.get("date_from")
            if date_from:
                queryset = queryset.filter(created_at__date__gte=date_from)

            date_to = form.cleaned_data.get("date_to")
            if date_to:
                queryset = queryset.filter(created_at__date__lte=date_to)

        return queryset.order_by("-created_at")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = RefundSearchForm(self.request.GET)
        context["search_form"] = form

        active = []
        if form.is_valid():
            cd = form.cleaned_data
            if cd.get("search_query"):
                active.append(_("Search: ") + cd["search_query"])
            if cd.get("status"):
                active.append(
                    _("Status: ")
                    + dict(RefundRequest.STATUS_CHOICES).get(cd["status"], cd["status"])
                )
            if cd.get("reason_category"):
                active.append(
                    _("Category: ")
                    + dict(RefundRequest.REASON_CHOICES).get(
                        cd["reason_category"], cd["reason_category"]
                    )
                )
            if cd.get("date_from"):
                active.append(_("From: ") + cd["date_from"].strftime("%Y-%m-%d"))
            if cd.get("date_to"):
                active.append(_("To: ") + cd["date_to"].strftime("%Y-%m-%d"))
        context["active_filters"] = active

        # Calculate summary statistics
        all_refunds = RefundRequest.objects.filter(customer=self.request.user)
        context["refund_stats"] = {
            "total_requests": all_refunds.count(),
            "pending_requests": all_refunds.filter(
                request_status=RefundRequest.PENDING
            ).count(),
            "approved_requests": all_refunds.filter(
                request_status=RefundRequest.APPROVED
            ).count(),
            "processed_requests": all_refunds.filter(
                request_status=RefundRequest.PROCESSED
            ).count(),
            "total_refunded": sum(
                r.processed_amount
                for r in all_refunds.filter(request_status=RefundRequest.PROCESSED)
            ),
        }

        # Log customer refund history view
        log_customer_refund_history_viewed(
            customer_user=self.request.user,
            request=self.request,
            filters=dict(self.request.GET.items()),
        )

        return context


# ============================================================================
