"""Admin views for payment and refund management."""

# --- Standard Library Imports ---
import hashlib
import json
from decimal import Decimal

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import Http404, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import etag
from django.views.generic import DetailView, FormView, ListView, TemplateView

# --- Local App Imports ---
from booking_cart_app.models import Booking

from ..forms import (
    PaymentSearchForm,
    RefundRequestForm,
    RefundSearchForm,
    StripeCheckoutForm,
)
from ..logging_utils import (
    log_admin_disputed_payments_viewed,
    log_admin_payment_analytics_viewed,
    log_admin_refund_detail_viewed,
    log_admin_refund_management_viewed,
    log_customer_payment_history_viewed,
    log_customer_refund_history_viewed,
    log_payment_completed,
    log_payment_error,
    log_payment_failed,
    log_payment_initiated,
    log_provider_earnings_viewed,
    log_provider_payment_detail_viewed,
    log_provider_payment_history_viewed,
    log_provider_payout_history_viewed,
    log_refund_approved,
    log_refund_declined,
    log_refund_processed,
    log_refund_requested,
    log_stripe_event,
    performance_monitor,
)
from ..models import Payment, RefundRequest
from ..utils import quantize_money
from .common import (
    EMAIL_NOTIFICATIONS_ENABLED,
    INVALID_BOOKING_ERROR,
    INVALID_PAYMENT_ERROR,
    PAYMENT_COMPLETED_SUCCESS,
    PAYMENT_FAILED_ERROR,
    PAYMENT_INITIATED_SUCCESS,
    REFUND_REQUEST_ERROR,
    REFUND_REQUESTED_SUCCESS,
    payment_receipt_etag,
    payment_receipt_view,
    send_payment_receipt_email_async,
    send_payout_email_async,
    stripe_webhook_view,
)

# --- Admin Payment Views ---


class AdminPaymentMixin:
    """Mixin to ensure only admin users can access admin payment views."""

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, _("Please log in to access this page."))
            return redirect("accounts_app:customer_login")

        if not request.user.is_staff:
            messages.error(request, _("Only administrators can access this page."))
            return redirect("venues_app:home")

        return super().dispatch(request, *args, **kwargs)


class AdminPaymentListView(AdminPaymentMixin, ListView):
    """
    Admin view for listing all payments with filtering and search capabilities.
    """

    model = Payment
    template_name = "payments_app/admin/payment_list.html"
    context_object_name = "payments"
    paginate_by = 20

    def get_queryset(self):
        queryset = Payment.objects.select_related(
            "customer", "provider", "booking", "booking__venue"
        ).order_by("-payment_date")

        # Apply search and filters
        form = PaymentSearchForm(self.request.GET)
        if form.is_valid():
            search_query = form.cleaned_data.get("search_query")
            if search_query:
                queryset = queryset.filter(
                    Q(payment_id__icontains=search_query)
                    | Q(customer__email__icontains=search_query)
                    | Q(customer__first_name__icontains=search_query)
                    | Q(customer__last_name__icontains=search_query)
                    | Q(provider__email__icontains=search_query)
                )

            status = form.cleaned_data.get("status")
            if status:
                queryset = queryset.filter(payment_status=status)

            payment_method = form.cleaned_data.get("payment_method")
            if payment_method:
                queryset = queryset.filter(payment_method=payment_method)

            date_from = form.cleaned_data.get("date_from")
            if date_from:
                queryset = queryset.filter(payment_date__date__gte=date_from)

            date_to = form.cleaned_data.get("date_to")
            if date_to:
                queryset = queryset.filter(payment_date__date__lte=date_to)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["search_form"] = PaymentSearchForm(self.request.GET)

        # Calculate summary statistics
        from django.db.models import Count, Sum

        all_payments = Payment.objects.all()
        context["payment_stats"] = {
            "total_payments": all_payments.count(),
            "successful_payments": all_payments.filter(
                payment_status=Payment.SUCCEEDED
            ).count(),
            "failed_payments": all_payments.filter(
                payment_status=Payment.FAILED
            ).count(),
            "pending_payments": all_payments.filter(
                payment_status=Payment.PENDING
            ).count(),
            "total_revenue": all_payments.filter(
                payment_status=Payment.SUCCEEDED
            ).aggregate(total=Sum("amount_paid"))["total"]
            or 0,
        }

        return context


class AdminPaymentDetailView(AdminPaymentMixin, DetailView):
    """
    Admin view for detailed payment information and management.
    """

    model = Payment
    template_name = "payments_app/admin/payment_detail.html"
    context_object_name = "payment"
    pk_url_kwarg = "payment_id"

    def get_object(self, queryset=None):
        return get_object_or_404(Payment, payment_id=self.kwargs.get("payment_id"))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        payment = self.object

        # Get related refund requests
        context["refund_requests"] = payment.refund_requests.all().order_by(
            "-created_at"
        )

        # Calculate platform fee and net earnings
        platform_fee_rate = Decimal(str(settings.PLATFORM_FEE_RATE))
        if payment.payment_status == Payment.SUCCEEDED:
            context["platform_fee"] = quantize_money(
                payment.amount_paid * platform_fee_rate
            )
            context["net_earnings"] = quantize_money(
                payment.amount_paid * (Decimal("1") - platform_fee_rate)
            )
        else:
            context["platform_fee"] = 0
            context["net_earnings"] = 0

        context["platform_fee_rate"] = platform_fee_rate

        return context


class AdminRefundManagementView(AdminPaymentMixin, ListView):
    """
    Admin view for managing refund requests with filtering and bulk actions.
    """

    model = RefundRequest
    template_name = "payments_app/admin/refund_list.html"
    context_object_name = "refund_requests"
    paginate_by = 20

    def get_queryset(self):
        queryset = RefundRequest.objects.select_related(
            "customer",
            "payment",
            "payment__booking",
            "payment__booking__venue",
            "reviewed_by",
        ).order_by("-created_at")

        # Apply search and filters
        from ..forms import AdminRefundSearchForm

        form = AdminRefundSearchForm(self.request.GET)
        if form.is_valid():
            search_query = form.cleaned_data.get("search_query")
            if search_query:
                queryset = queryset.filter(
                    Q(refund_request_id__icontains=search_query)
                    | Q(customer__email__icontains=search_query)
                    | Q(customer__first_name__icontains=search_query)
                    | Q(customer__last_name__icontains=search_query)
                    | Q(payment__payment_id__icontains=search_query)
                    | Q(reason_description__icontains=search_query)
                )

            status = form.cleaned_data.get("status")
            if status:
                queryset = queryset.filter(request_status=status)

            reason_category = form.cleaned_data.get("reason_category")
            if reason_category:
                queryset = queryset.filter(reason_category=reason_category)

            date_from = form.cleaned_data.get("date_from")
            if date_from:
                queryset = queryset.filter(created_at__date__gte=date_from)

            date_to = form.cleaned_data.get("date_to")
            if date_to:
                queryset = queryset.filter(created_at__date__lte=date_to)

            amount_min = form.cleaned_data.get("amount_min")
            if amount_min:
                queryset = queryset.filter(requested_amount__gte=amount_min)

            amount_max = form.cleaned_data.get("amount_max")
            if amount_max:
                queryset = queryset.filter(requested_amount__lte=amount_max)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from ..forms import AdminRefundSearchForm

        context["search_form"] = AdminRefundSearchForm(self.request.GET)

        # Calculate summary statistics
        all_refunds = RefundRequest.objects.all()
        from django.db.models import Count, Sum

        context["refund_stats"] = {
            "total_requests": all_refunds.count(),
            "pending_requests": all_refunds.filter(
                request_status=RefundRequest.PENDING
            ).count(),
            "approved_requests": all_refunds.filter(
                request_status=RefundRequest.APPROVED
            ).count(),
            "declined_requests": all_refunds.filter(
                request_status=RefundRequest.DECLINED
            ).count(),
            "processed_requests": all_refunds.filter(
                request_status=RefundRequest.PROCESSED
            ).count(),
            "total_requested_amount": all_refunds.aggregate(
                total=Sum("requested_amount")
            )["total"]
            or 0,
            "total_processed_amount": all_refunds.filter(
                request_status=RefundRequest.PROCESSED
            ).aggregate(total=Sum("processed_amount"))["total"]
            or 0,
        }

        # Log admin refund management view
        log_admin_refund_management_viewed(
            admin_user=self.request.user,
            request=self.request,
            filters=dict(self.request.GET.items()),
        )

        return context


class AdminRefundDetailView(AdminPaymentMixin, DetailView):
    """
    Admin view for detailed refund request management with approval/decline actions.
    """

    model = RefundRequest
    template_name = "payments_app/admin/refund_detail.html"
    context_object_name = "refund_request"
    pk_url_kwarg = "refund_id"

    def get_object(self, queryset=None):
        return get_object_or_404(
            RefundRequest, refund_request_id=self.kwargs.get("refund_id")
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        refund_request = self.object

        # Add related payment and booking information
        context["payment"] = refund_request.payment
        context["booking"] = refund_request.payment.booking
        context["customer"] = refund_request.customer

        # Check if refund can be processed
        context["can_approve"] = refund_request.request_status == RefundRequest.PENDING
        context["can_decline"] = refund_request.request_status == RefundRequest.PENDING

        # Log admin refund detail view
        log_admin_refund_detail_viewed(
            admin_user=self.request.user,
            refund_request=refund_request,
            request=self.request,
        )

        return context


@login_required
def admin_approve_refund(request, refund_id):
    """Admin view to approve a refund request."""
    if not request.user.is_staff:
        messages.error(request, _("Only administrators can access this page."))
        return redirect("venues_app:home")

    refund_request = get_object_or_404(RefundRequest, refund_request_id=refund_id)

    if refund_request.request_status != RefundRequest.PENDING:
        messages.error(request, _("This refund request has already been processed."))
        return redirect("payments_app:admin_refund_detail", refund_id=refund_id)

    if request.method == "POST":
        from ..forms import AdminRefundApprovalForm

        form = AdminRefundApprovalForm(request.POST, refund_request=refund_request)
        if form.is_valid():
            try:
                # Process the approval
                admin_notes = form.cleaned_data["admin_notes"]
                processed_amount = form.cleaned_data["processed_amount"]

                # Update refund request and payment atomically
                refund_request.request_status = RefundRequest.APPROVED
                refund_request.admin_notes = admin_notes
                refund_request.reviewed_by = request.user
                refund_request.reviewed_at = timezone.now()
                refund_request.processed_amount = processed_amount
                refund_request.save()

                # Use model method to handle payment update with concurrency protection
                refund_request.payment.process_refund(
                    processed_amount, "Admin approved"
                )

                # Log the approval
                log_refund_approved(
                    user=request.user, refund_request=refund_request, request=request
                )
                log_refund_processed(
                    admin_user=request.user,
                    refund_request=refund_request,
                    processed_amount=processed_amount,
                    request=request,
                )

                messages.success(request, _("Refund request approved successfully."))
                return redirect("payments_app:admin_refund_detail", refund_id=refund_id)

            except Exception as e:
                log_payment_error(
                    error_type="refund_approval_error",
                    error_message=str(e),
                    user=request.user,
                    request=request,
                    additional_data={"refund_request_id": str(refund_id)},
                )
                messages.error(
                    request, _("Error processing refund approval. Please try again.")
                )
                return redirect("payments_app:admin_refund_detail", refund_id=refund_id)
        else:
            messages.error(request, _("Please correct the errors below."))
            return redirect("payments_app:admin_refund_detail", refund_id=refund_id)
    else:
        from ..forms import AdminRefundApprovalForm

        form = AdminRefundApprovalForm(refund_request=refund_request)

    context = {
        "form": form,
        "refund_request": refund_request,
        "payment": refund_request.payment,
        "customer": refund_request.customer,
    }

    return render(request, "payments_app/admin/refund_approve.html", context)


@login_required
def admin_decline_refund(request, refund_id):
    """Admin view to decline a refund request."""
    if not request.user.is_staff:
        messages.error(request, _("Only administrators can access this page."))
        return redirect("venues_app:home")

    refund_request = get_object_or_404(RefundRequest, refund_request_id=refund_id)

    if refund_request.request_status != RefundRequest.PENDING:
        messages.error(request, _("This refund request has already been processed."))
        return redirect("payments_app:admin_refund_detail", refund_id=refund_id)

    if request.method == "POST":
        from ..forms import AdminRefundDeclineForm

        form = AdminRefundDeclineForm(request.POST)
        if form.is_valid():
            try:
                # Process the decline
                admin_notes = form.cleaned_data["admin_notes"]

                # Update refund request
                refund_request.request_status = RefundRequest.DECLINED
                refund_request.admin_notes = admin_notes
                refund_request.reviewed_by = request.user
                refund_request.reviewed_at = timezone.now()
                refund_request.save()

                # Log the decline
                log_refund_declined(
                    user=request.user, refund_request=refund_request, request=request
                )

                messages.success(request, _("Refund request declined successfully."))
                return redirect("payments_app:admin_refund_detail", refund_id=refund_id)

            except Exception as e:
                log_payment_error(
                    error_type="refund_decline_error",
                    error_message=str(e),
                    user=request.user,
                    request=request,
                    additional_data={"refund_request_id": str(refund_id)},
                )
                messages.error(
                    request, _("Error processing refund decline. Please try again.")
                )
    else:
        from ..forms import AdminRefundDeclineForm

        form = AdminRefundDeclineForm()

    context = {
        "form": form,
        "refund_request": refund_request,
        "payment": refund_request.payment,
        "customer": refund_request.customer,
    }

    return render(request, "payments_app/admin/refund_decline.html", context)


class AdminDisputedPaymentsView(AdminPaymentMixin, ListView):
    """
    Admin view for monitoring failed and disputed payments.
    """

    model = Payment
    template_name = "payments_app/admin/disputed_payments.html"
    context_object_name = "disputed_payments"
    paginate_by = 20

    def get_queryset(self):
        # Get failed, disputed, or problematic payments
        queryset = (
            Payment.objects.filter(
                Q(payment_status__in=[Payment.FAILED, Payment.REQUIRES_ACTION])
                | Q(failure_reason__isnull=False)
            )
            .select_related("customer", "provider", "booking", "booking__venue")
            .order_by("-payment_date")
        )

        # Apply search and filters
        from ..forms import AdminDisputedPaymentSearchForm

        form = AdminDisputedPaymentSearchForm(self.request.GET)
        if form.is_valid():
            search_query = form.cleaned_data.get("search_query")
            if search_query:
                queryset = queryset.filter(
                    Q(payment_id__icontains=search_query)
                    | Q(customer__email__icontains=search_query)
                    | Q(customer__first_name__icontains=search_query)
                    | Q(customer__last_name__icontains=search_query)
                    | Q(failure_reason__icontains=search_query)
                )

            status = form.cleaned_data.get("status")
            if status:
                queryset = queryset.filter(payment_status=status)

            date_from = form.cleaned_data.get("date_from")
            if date_from:
                queryset = queryset.filter(payment_date__date__gte=date_from)

            date_to = form.cleaned_data.get("date_to")
            if date_to:
                queryset = queryset.filter(payment_date__date__lte=date_to)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from ..forms import AdminDisputedPaymentSearchForm

        context["search_form"] = AdminDisputedPaymentSearchForm(self.request.GET)

        # Calculate summary statistics
        from django.db.models import Count, Sum

        all_disputed = Payment.objects.filter(
            Q(payment_status__in=[Payment.FAILED, Payment.REQUIRES_ACTION])
            | Q(failure_reason__isnull=False)
        )

        context["disputed_stats"] = {
            "total_disputed": all_disputed.count(),
            "failed_payments": all_disputed.filter(
                payment_status=Payment.FAILED
            ).count(),
            "requires_action": all_disputed.filter(
                payment_status=Payment.REQUIRES_ACTION
            ).count(),
            "total_disputed_amount": all_disputed.aggregate(total=Sum("amount_paid"))[
                "total"
            ]
            or 0,
        }

        # Log admin disputed payments view
        log_admin_disputed_payments_viewed(
            admin_user=self.request.user,
            request=self.request,
            filters=dict(self.request.GET.items()),
        )

        return context


class AdminPaymentAnalyticsView(AdminPaymentMixin, TemplateView):
    """
    Admin analytics dashboard for payment metrics and reporting.
    """

    template_name = "payments_app/admin/payment_analytics.html"

    @performance_monitor("admin_payment_analytics")
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        from datetime import datetime, timedelta

        from django.db.models import Avg, Count, Sum
        from django.utils import timezone

        now = timezone.now()
        today = now.date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        year_ago = today - timedelta(days=365)

        # Payment statistics
        all_payments = Payment.objects.all()
        successful_payments = all_payments.filter(payment_status=Payment.SUCCEEDED)

        # Revenue metrics
        total_revenue = (
            successful_payments.aggregate(total=Sum("amount_paid"))["total"] or 0
        )

        weekly_revenue = (
            successful_payments.filter(payment_date__date__gte=week_ago).aggregate(
                total=Sum("amount_paid")
            )["total"]
            or 0
        )

        monthly_revenue = (
            successful_payments.filter(payment_date__date__gte=month_ago).aggregate(
                total=Sum("amount_paid")
            )["total"]
            or 0
        )

        yearly_revenue = (
            successful_payments.filter(payment_date__date__gte=year_ago).aggregate(
                total=Sum("amount_paid")
            )["total"]
            or 0
        )

        # Transaction metrics
        total_transactions = all_payments.count()
        successful_count = successful_payments.count()

        transaction_stats = {
            "total_transactions": total_transactions,
            "successful_transactions": successful_count,
            "failed_transactions": all_payments.filter(
                payment_status=Payment.FAILED
            ).count(),
            "pending_transactions": all_payments.filter(
                payment_status=Payment.PENDING
            ).count(),
            "average_transaction_amount": successful_payments.aggregate(
                avg=Avg("amount_paid")
            )["avg"]
            or 0,
            "success_rate": (
                (successful_count / total_transactions * 100)
                if total_transactions > 0
                else 0
            ),
        }

        # Refund metrics
        all_refunds = RefundRequest.objects.all()
        total_refunds = all_refunds.count()

        refund_stats = {
            "total_refunds": total_refunds,
            "pending_refunds": all_refunds.filter(
                request_status=RefundRequest.PENDING
            ).count(),
            "approved_refunds": all_refunds.filter(
                request_status=RefundRequest.APPROVED
            ).count(),
            "declined_refunds": all_refunds.filter(
                request_status=RefundRequest.DECLINED
            ).count(),
            "total_refunded_amount": all_refunds.filter(
                request_status=RefundRequest.PROCESSED
            ).aggregate(total=Sum("processed_amount"))["total"]
            or 0,
            "refund_rate": (
                (total_refunds / total_transactions * 100)
                if total_transactions > 0
                else 0
            ),
        }

        # Calculate platform revenue
        platform_fee_rate = Decimal(str(settings.PLATFORM_FEE_RATE))

        # Provider earnings overview
        provider_earnings_raw = (
            successful_payments.values("provider")
            .annotate(total_earnings=Sum("amount_paid"), transaction_count=Count("id"))
            .order_by("-total_earnings")[:10]
        )

        # Calculate platform fees for each provider
        provider_earnings = []
        for provider_data in provider_earnings_raw:
            provider_data["platform_fee"] = quantize_money(
                provider_data["total_earnings"] * platform_fee_rate
            )
            provider_earnings.append(provider_data)

        # Recent activity
        recent_payments = all_payments.order_by("-payment_date")[:10]
        recent_refunds = all_refunds.order_by("-created_at")[:10]

        # Calculate platform revenue totals
        total_platform_fees = quantize_money(total_revenue * platform_fee_rate)
        total_provider_payouts = quantize_money(
            total_revenue * (Decimal("1") - platform_fee_rate)
        )

        context.update(
            {
                "total_revenue": total_revenue,
                "weekly_revenue": weekly_revenue,
                "monthly_revenue": monthly_revenue,
                "yearly_revenue": yearly_revenue,
                "transaction_stats": transaction_stats,
                "refund_stats": refund_stats,
                "provider_earnings": provider_earnings,
                "recent_payments": recent_payments,
                "recent_refunds": recent_refunds,
                "platform_fee_rate": platform_fee_rate,
                "total_platform_fees": total_platform_fees,
                "total_provider_payouts": total_provider_payouts,
            }
        )

        # Log admin analytics view
        log_admin_payment_analytics_viewed(
            admin_user=self.request.user,
            request=self.request,
            analytics_data={
                "total_revenue": str(total_revenue),
                "monthly_revenue": str(monthly_revenue),
                "total_transactions": transaction_stats["total_transactions"],
            },
        )

        return context


# Aliases for backward compatibility with tests
AdminRefundListView = AdminRefundManagementView
