"""Forms for service provider payment views."""

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from accounts_app.forms import AccessibleFormMixin

from ..models import Payment

# --- Provider Payment Forms ---


class ProviderPaymentSearchForm(AccessibleFormMixin, forms.Form):
    """Form for searching and filtering provider payment history."""

    STATUS_CHOICES = [("", _("All Statuses"))] + Payment.STATUS_CHOICES
    PAYMENT_METHOD_CHOICES = [
        ("", _("All Payment Methods"))
    ] + Payment.PAYMENT_METHOD_CHOICES

    search_query = forms.CharField(
        label=_("Search"),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": "Search by payment ID, booking ID, or customer name...",
            }
        ),
    )
    status = forms.ChoiceField(
        label=_("Status"),
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={"class": "form-select"}),
    )
    payment_method = forms.ChoiceField(
        label=_("Payment Method"),
        choices=PAYMENT_METHOD_CHOICES,
        required=False,
        widget=forms.Select(attrs={"class": "form-select"}),
    )
    date_from = forms.DateField(
        label=_("From Date"),
        required=False,
        widget=forms.DateInput(attrs={"class": "form-control", "type": "date"}),
    )
    date_to = forms.DateField(
        label=_("To Date"),
        required=False,
        widget=forms.DateInput(attrs={"class": "form-control", "type": "date"}),
    )
    amount_min = forms.DecimalField(
        label=_("Minimum Amount"),
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(
            attrs={
                "class": "form-control",
                "step": "0.01",
                "min": "0",
                "placeholder": "0.00",
            }
        ),
    )
    amount_max = forms.DecimalField(
        label=_("Maximum Amount"),
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(
            attrs={
                "class": "form-control",
                "step": "0.01",
                "min": "0",
                "placeholder": "0.00",
            }
        ),
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get("date_from")
        date_to = cleaned_data.get("date_to")
        amount_min = cleaned_data.get("amount_min")
        amount_max = cleaned_data.get("amount_max")
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_("From date cannot be later than to date."))
        if amount_min and amount_max and amount_min > amount_max:
            raise ValidationError(
                _("Minimum amount cannot be greater than maximum amount.")
            )
        return cleaned_data


class ProviderEarningsFilterForm(AccessibleFormMixin, forms.Form):
    """Form for filtering provider earnings overview by date range."""

    PERIOD_CHOICES = [
        ("7", _("Last 7 days")),
        ("30", _("Last 30 days")),
        ("90", _("Last 90 days")),
        ("365", _("Last year")),
        ("custom", _("Custom range")),
    ]

    period = forms.ChoiceField(
        label=_("Time Period"),
        choices=PERIOD_CHOICES,
        initial="30",
        widget=forms.Select(attrs={"class": "form-select", "id": "period-select"}),
    )
    date_from = forms.DateField(
        label=_("From Date"),
        required=False,
        widget=forms.DateInput(
            attrs={"class": "form-control", "type": "date", "id": "date-from"}
        ),
    )
    date_to = forms.DateField(
        label=_("To Date"),
        required=False,
        widget=forms.DateInput(
            attrs={"class": "form-control", "type": "date", "id": "date-to"}
        ),
    )

    def clean(self):
        cleaned_data = super().clean()
        period = cleaned_data.get("period")
        date_from = cleaned_data.get("date_from")
        date_to = cleaned_data.get("date_to")
        if period == "custom":
            if not date_from or not date_to:
                raise ValidationError(
                    _("Both from and to dates are required for custom period.")
                )
            if date_from > date_to:
                raise ValidationError(_("From date cannot be later than to date."))
        return cleaned_data
