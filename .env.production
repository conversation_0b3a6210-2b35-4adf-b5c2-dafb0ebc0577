# CozyWish Production Environment
# ===============================
# This file contains production-specific environment variables
# IMPORTANT: Keep this file secure and never commit to version control

# ===== ENVIRONMENT SELECTION =====
DJANGO_ENVIRONMENT=production

# ===== CORE DJANGO SETTINGS =====
# REQUIRED: Generate a strong secret key for production
# Use: python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
SECRET_KEY=

# Disable debug mode in production
DEBUG=False

# ===== DATABASE CONFIGURATION =====
# REQUIRED: PostgreSQL database URL for production
# Format: postgres://user:password@host:port/database
DATABASE_URL=

# ===== EMAIL CONFIGURATION =====
# REQUIRED: SendGrid configuration for production emails
EMAIL_HOST=smtp.sendgrid.net
EMAIL_HOST_USER=apikey
EMAIL_HOST_PASSWORD=
EMAIL_PORT=587
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=<EMAIL>
SERVER_EMAIL=<EMAIL>

# Email backend is always SMTP in production
FORCE_EMAIL_BACKEND=True

# ===== AWS S3 CONFIGURATION =====
# REQUIRED: AWS S3 for production file storage
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=

# ===== REDIS CONFIGURATION =====
# Production Redis for caching and Celery
REDIS_URL=
CELERY_BROKER_URL=
CELERY_RESULT_BACKEND=

# ===== APPLICATION SETTINGS =====
LOG_LEVEL=INFO
PLATFORM_FEE_RATE=0.05
DASHBOARD_CACHE_TIMEOUT=300
NOTIFICATION_CACHE_TIMEOUT=60

# ===== DEPLOYMENT SETTINGS =====
# Render deployment hostname
RENDER_EXTERNAL_HOSTNAME=
WEB_CONCURRENCY=4

# ===== SECURITY SETTINGS =====
# Additional allowed hosts (comma-separated)
ADDITIONAL_ALLOWED_HOSTS=

# Additional CSRF trusted origins (comma-separated)
ADDITIONAL_CSRF_ORIGINS=

# ===== MONITORING & ANALYTICS =====
# Sentry for error tracking in production
SENTRY_DSN=

# Google Analytics for production
GA_TRACKING_ID=

# ===== THIRD-PARTY INTEGRATIONS =====
# REQUIRED: Stripe live keys for production payments
STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

# Social authentication for production
GOOGLE_OAUTH2_CLIENT_ID=
GOOGLE_OAUTH2_CLIENT_SECRET=
FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=

# ===== PRODUCTION SECURITY =====
# SSL/TLS settings (automatically configured)
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True

# ===== PRODUCTION PERFORMANCE =====
# Database connection pooling
# DB_CONN_MAX_AGE=600

# Static file compression
# WHITENOISE_USE_FINDERS=True
