# --- Third-Party Imports ---
from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html, mark_safe
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .models import (
    CustomerReviewResponse,
    Review,
    ReviewDraft,
    ReviewFlag,
    ReviewHelpfulness,
    ReviewResponse,
)


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    """Enhanced admin configuration for Review model with moderation features."""

    # Fields to display in the review list
    list_display = (
        "venue_name",
        "customer_email",
        "rating_stars",
        "is_approved",
        "is_flagged",
        "has_response_indicator",
        "created_at",
    )
    list_filter = (
        "rating",
        "is_approved",
        "is_flagged",
        "created_at",
        "venue__service_provider__legal_name",
    )
    search_fields = (
        "customer__email",
        "venue__venue_name",
        "written_review",
        "venue__service_provider__legal_name",
    )
    ordering = ("-created_at",)
    readonly_fields = ("created_at", "updated_at", "rating_display_admin")
    actions = ["approve_reviews", "disapprove_reviews", "clear_flags"]

    # Fields for the review detail/edit form
    fieldsets = (
        (
            _("Review Information"),
            {"fields": ("customer", "venue", "rating", "rating_display_admin")},
        ),
        (_("Review Content"), {"fields": ("written_review",)}),
        (
            _("Moderation"),
            {"fields": ("is_approved", "is_flagged"), "classes": ("collapse",)},
        ),
        (
            _("Timestamps"),
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def venue_name(self, obj):
        """Display venue name with link to venue admin."""
        if obj.venue:
            url = reverse("admin:venues_app_venue_change", args=[obj.venue.pk])
            return format_html('<a href="{}">{}</a>', url, obj.venue.venue_name)
        return "-"

    venue_name.short_description = _("Venue")
    venue_name.admin_order_field = "venue__venue_name"

    def customer_email(self, obj):
        """Display customer email with link to user admin."""
        if obj.customer:
            url = reverse(
                "admin:accounts_app_customuser_change", args=[obj.customer.pk]
            )
            return format_html('<a href="{}">{}</a>', url, obj.customer.email)
        return "-"

    customer_email.short_description = _("Customer")
    customer_email.admin_order_field = "customer__email"

    def rating_stars(self, obj):
        """Display rating as stars."""
        return mark_safe(obj.rating_display)

    rating_stars.short_description = _("Rating")
    rating_stars.admin_order_field = "rating"

    def rating_display_admin(self, obj):
        """Display rating with stars for admin detail view."""
        return mark_safe(f"{obj.rating}/5 stars: {obj.rating_display}")

    rating_display_admin.short_description = _("Rating Display")

    def has_response_indicator(self, obj):
        """Show if review has a provider response."""
        if obj.has_response:
            return format_html('<span style="color: green;">✓ Yes</span>')
        return format_html('<span style="color: red;">✗ No</span>')

    has_response_indicator.short_description = _("Has Response")
    has_response_indicator.admin_order_field = "response"

    # Admin actions
    def approve_reviews(self, request, queryset):
        """Approve selected reviews."""
        updated = queryset.update(is_approved=True)
        self.message_user(request, f"{updated} reviews were approved.")

    approve_reviews.short_description = _("Approve selected reviews")

    def disapprove_reviews(self, request, queryset):
        """Disapprove selected reviews."""
        updated = queryset.update(is_approved=False)
        self.message_user(request, f"{updated} reviews were disapproved.")

    disapprove_reviews.short_description = _("Disapprove selected reviews")

    def clear_flags(self, request, queryset):
        """Clear flags from selected reviews."""
        updated = queryset.update(is_flagged=False)
        # Also resolve all pending flags for these reviews
        for review in queryset:
            review.flags.filter(status=ReviewFlag.PENDING).update(
                status=ReviewFlag.RESOLVED,
                reviewed_by=request.user,
                admin_notes="Bulk cleared by admin",
            )
        self.message_user(request, f"Flags cleared for {updated} reviews.")

    clear_flags.short_description = _("Clear flags from selected reviews")


@admin.register(ReviewResponse)
class ReviewResponseAdmin(admin.ModelAdmin):
    """Enhanced admin configuration for ReviewResponse model."""

    # Fields to display in the response list
    list_display = (
        "review_info",
        "provider_business",
        "response_preview",
        "created_at",
    )
    list_filter = (
        "created_at",
        "provider__service_provider_profile__legal_name",
        "review__venue__venue_name",
    )
    search_fields = (
        "provider__email",
        "response_text",
        "review__venue__venue_name",
        "review__customer__email",
        "provider__service_provider_profile__legal_name",
    )
    ordering = ("-created_at",)
    readonly_fields = ("created_at", "updated_at")

    # Fields for the response detail/edit form
    fieldsets = (
        (_("Response Information"), {"fields": ("review", "provider")}),
        (_("Response Content"), {"fields": ("response_text",)}),
        (
            _("Timestamps"),
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def review_info(self, obj):
        """Display review information with link."""
        if obj.review:
            url = reverse("admin:review_app_review_change", args=[obj.review.pk])
            return format_html(
                '<a href="{}">{} - {} stars</a>',
                url,
                obj.review.venue.venue_name,
                obj.review.rating,
            )
        return "-"

    review_info.short_description = _("Review")
    review_info.admin_order_field = "review__venue__venue_name"

    def provider_business(self, obj):
        """Display provider business name with link."""
        if obj.provider and hasattr(obj.provider, "service_provider_profile"):
            url = reverse(
                "admin:accounts_app_customuser_change", args=[obj.provider.pk]
            )
            return format_html(
                '<a href="{}">{}</a>',
                url,
                obj.provider.service_provider_profile.business_name,
            )
        return "-"

    provider_business.short_description = _("Provider Business")
    provider_business.admin_order_field = (
        "provider__service_provider_profile__legal_name"
    )

    def response_preview(self, obj):
        """Display truncated response text."""
        if obj.response_text:
            return (
                obj.response_text[:100] + "..."
                if len(obj.response_text) > 100
                else obj.response_text
            )
        return "-"

    response_preview.short_description = _("Response Preview")


@admin.register(ReviewFlag)
class ReviewFlagAdmin(admin.ModelAdmin):
    """Enhanced admin configuration for ReviewFlag model with moderation features."""

    # Fields to display in the flag list
    list_display = (
        "review_info",
        "flagged_by_email",
        "reason_display",
        "status_display",
        "reviewed_by_admin",
        "created_at",
    )
    list_filter = (
        "reason",
        "status",
        "created_at",
        "reviewed_at",
        "review__venue__venue_name",
    )
    search_fields = (
        "flagged_by__email",
        "reason_text",
        "admin_notes",
        "review__venue__venue_name",
        "review__customer__email",
    )
    ordering = ("-created_at",)
    readonly_fields = ("created_at", "reviewed_at")
    actions = ["mark_as_reviewed", "mark_as_resolved", "mark_as_pending"]

    # Fields for the flag detail/edit form
    fieldsets = (
        (
            _("Flag Information"),
            {"fields": ("review", "flagged_by", "reason", "reason_text")},
        ),
        (
            _("Status & Resolution"),
            {"fields": ("status", "reviewed_by", "admin_notes")},
        ),
        (
            _("Timestamps"),
            {"fields": ("created_at", "reviewed_at"), "classes": ("collapse",)},
        ),
    )

    def review_info(self, obj):
        """Display review information with link."""
        if obj.review:
            url = reverse("admin:review_app_review_change", args=[obj.review.pk])
            return format_html(
                '<a href="{}">{} - {} stars</a>',
                url,
                obj.review.venue.venue_name,
                obj.review.rating,
            )
        return "-"

    review_info.short_description = _("Review")
    review_info.admin_order_field = "review__venue__venue_name"

    def flagged_by_email(self, obj):
        """Display flagged by email with link."""
        if obj.flagged_by:
            url = reverse(
                "admin:accounts_app_customuser_change", args=[obj.flagged_by.pk]
            )
            return format_html('<a href="{}">{}</a>', url, obj.flagged_by.email)
        return "-"

    flagged_by_email.short_description = _("Flagged By")
    flagged_by_email.admin_order_field = "flagged_by__email"

    def reason_display(self, obj):
        """Display reason with color coding."""
        color_map = {
            "inappropriate": "orange",
            "fake": "red",
            "spam": "purple",
            "offensive": "darkred",
            "other": "gray",
        }
        color = color_map.get(obj.reason, "black")
        return format_html(
            '<span style="color: {};">{}</span>', color, obj.get_reason_display()
        )

    reason_display.short_description = _("Reason")
    reason_display.admin_order_field = "reason"

    def status_display(self, obj):
        """Display status with color coding."""
        color_map = {"pending": "orange", "reviewed": "blue", "resolved": "green"}
        color = color_map.get(obj.status, "black")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display(),
        )

    status_display.short_description = _("Status")
    status_display.admin_order_field = "status"

    def reviewed_by_admin(self, obj):
        """Display admin who reviewed the flag."""
        if obj.reviewed_by:
            url = reverse(
                "admin:accounts_app_customuser_change", args=[obj.reviewed_by.pk]
            )
            return format_html('<a href="{}">{}</a>', url, obj.reviewed_by.email)
        return "-"

    reviewed_by_admin.short_description = _("Reviewed By")
    reviewed_by_admin.admin_order_field = "reviewed_by__email"

    # Admin actions
    def mark_as_reviewed(self, request, queryset):
        """Mark selected flags as reviewed."""
        updated = queryset.filter(status=ReviewFlag.PENDING).update(
            status=ReviewFlag.REVIEWED, reviewed_by=request.user
        )
        self.message_user(request, f"{updated} flags were marked as reviewed.")

    mark_as_reviewed.short_description = _("Mark selected flags as reviewed")

    def mark_as_resolved(self, request, queryset):
        """Mark selected flags as resolved."""
        updated = queryset.exclude(status=ReviewFlag.RESOLVED).update(
            status=ReviewFlag.RESOLVED,
            reviewed_by=request.user,
            admin_notes="Bulk resolved by admin",
        )
        self.message_user(request, f"{updated} flags were marked as resolved.")

    mark_as_resolved.short_description = _("Mark selected flags as resolved")

    def mark_as_pending(self, request, queryset):
        """Mark selected flags as pending."""
        updated = queryset.exclude(status=ReviewFlag.PENDING).update(
            status=ReviewFlag.PENDING, reviewed_by=None, reviewed_at=None
        )
        self.message_user(request, f"{updated} flags were marked as pending.")

    mark_as_pending.short_description = _("Mark selected flags as pending")


@admin.register(ReviewDraft)
class ReviewDraftAdmin(admin.ModelAdmin):
    """Admin configuration for ReviewDraft model."""

    list_display = [
        "customer_email",
        "venue_name",
        "has_rating",
        "has_content",
        "updated_at",
        "can_publish",
    ]
    list_filter = ["created_at", "updated_at", "venue__service_provider__legal_name"]
    search_fields = ["customer__email", "venue__venue_name", "written_review"]
    readonly_fields = ["created_at", "updated_at"]
    raw_id_fields = ["customer", "venue"]

    fieldsets = (
        (_("Draft Information"), {"fields": ("customer", "venue")}),
        (_("Draft Content"), {"fields": ("rating", "written_review")}),
        (
            _("Metadata"),
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def customer_email(self, obj):
        """Display customer email with link to user admin."""
        if obj.customer:
            url = reverse(
                "admin:accounts_app_customuser_change", args=[obj.customer.pk]
            )
            return format_html('<a href="{}">{}</a>', url, obj.customer.email)
        return "-"

    customer_email.short_description = _("Customer")
    customer_email.admin_order_field = "customer__email"

    def venue_name(self, obj):
        """Display venue name with link to venue admin."""
        if obj.venue:
            return obj.venue.venue_name
        return "-"

    venue_name.short_description = _("Venue")
    venue_name.admin_order_field = "venue__venue_name"

    def has_rating(self, obj):
        """Show if draft has a rating."""
        if obj.rating:
            return format_html('<span style="color: green;">✓ {}/5</span>', obj.rating)
        return format_html('<span style="color: red;">✗ No rating</span>')

    has_rating.short_description = _("Has Rating")

    def has_content(self, obj):
        """Show if draft has written content."""
        if obj.written_review:
            char_count = len(obj.written_review)
            return format_html(
                '<span style="color: green;">✓ {} chars</span>', char_count
            )
        return format_html('<span style="color: red;">✗ No content</span>')

    has_content.short_description = _("Has Content")

    def can_publish(self, obj):
        """Show if draft can be published."""
        if obj.can_be_converted_to_review():
            return format_html('<span style="color: green;">✓ Ready</span>')
        return format_html('<span style="color: orange;">⚠ Needs rating</span>')

    can_publish.short_description = _("Can Publish")

    def get_queryset(self, request):
        """Optimize queryset for admin list view."""
        return super().get_queryset(request).select_related("customer", "venue")


@admin.register(CustomerReviewResponse)
class CustomerReviewResponseAdmin(admin.ModelAdmin):
    """Admin configuration for CustomerReviewResponse model."""

    list_display = [
        "customer_email",
        "venue_name",
        "provider_name",
        "response_preview",
        "can_edit",
        "created_at",
    ]
    list_filter = ["created_at", "updated_at"]
    search_fields = [
        "customer__email",
        "provider_response__review__venue__venue_name",
        "response_text",
    ]
    readonly_fields = ["created_at", "updated_at"]
    raw_id_fields = ["customer", "provider_response"]

    fieldsets = (
        (_("Response Information"), {"fields": ("customer", "provider_response")}),
        (_("Response Content"), {"fields": ("response_text",)}),
        (
            _("Metadata"),
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def customer_email(self, obj):
        """Display customer email with link to user admin."""
        if obj.customer:
            url = reverse(
                "admin:accounts_app_customuser_change", args=[obj.customer.pk]
            )
            return format_html('<a href="{}">{}</a>', url, obj.customer.email)
        return "-"

    customer_email.short_description = _("Customer")
    customer_email.admin_order_field = "customer__email"

    def venue_name(self, obj):
        """Display venue name."""
        if obj.provider_response and obj.provider_response.review:
            return obj.provider_response.review.venue.venue_name
        return "-"

    venue_name.short_description = _("Venue")

    def provider_name(self, obj):
        """Display provider business name."""
        if obj.provider_response and obj.provider_response.provider:
            provider_profile = getattr(
                obj.provider_response.provider, "service_provider_profile", None
            )
            if provider_profile:
                return provider_profile.business_name
            return obj.provider_response.provider.email
        return "-"

    provider_name.short_description = _("Provider")

    def response_preview(self, obj):
        """Display a preview of the response text."""
        if obj.response_text:
            preview = obj.response_text[:50]
            if len(obj.response_text) > 50:
                preview += "..."
            return preview
        return "-"

    response_preview.short_description = _("Response Preview")

    def can_edit(self, obj):
        """Show if response can still be edited."""
        if obj.can_be_edited():
            time_remaining = obj.get_edit_time_remaining()
            if time_remaining:
                return format_html(
                    '<span style="color: green;">✓ {}h {}m left</span>',
                    time_remaining["hours"],
                    time_remaining["minutes"],
                )
        return format_html('<span style="color: red;">✗ Edit time expired</span>')

    can_edit.short_description = _("Can Edit")

    def get_queryset(self, request):
        """Optimize queryset for admin list view."""
        return (
            super()
            .get_queryset(request)
            .select_related(
                "customer",
                "provider_response__review__venue",
                "provider_response__provider__service_provider_profile",
            )
        )
