"""Template tags for review_app."""

from django import template
from django.utils.safestring import mark_safe

from ..utils import get_venue_badge_info, is_new_on_cozywish

register = template.Library()


@register.simple_tag
def venue_badge(venue):
    """
    Display a badge for the venue (New on CozyWish, Top Rated, etc.).

    Usage: {% venue_badge venue %}
    """
    badge_info = get_venue_badge_info(venue)

    if not badge_info["has_badge"]:
        return ""

    badge_html = f"""
    <span class="badge {badge_info['badge_class']} text-white small">
        <i class="fas fa-star me-1"></i>{badge_info['badge_text']}
    </span>
    """

    return mark_safe(badge_html)


@register.simple_tag
def is_venue_new(venue):
    """
    Check if venue is new on CozyWish.

    Usage: {% is_venue_new venue as is_new %}
    """
    return is_new_on_cozywish(venue)


@register.inclusion_tag("review_app/tags/venue_badge.html")
def venue_badge_card(venue):
    """
    Render venue badge as a card component.

    Usage: {% venue_badge_card venue %}
    """
    badge_info = get_venue_badge_info(venue)
    return {
        "venue": venue,
        "badge_info": badge_info,
    }


@register.filter
def star_rating(rating):
    """
    Convert numeric rating to star display.

    Usage: {{ rating|star_rating }}
    """
    if not rating:
        return mark_safe('<span class="text-muted">No rating</span>')

    full_stars = int(rating)
    half_star = rating - full_stars >= 0.5
    empty_stars = 5 - full_stars - (1 if half_star else 0)

    stars_html = ""

    # Full stars
    for _ in range(full_stars):
        stars_html += '<i class="fas fa-star text-warning"></i>'

    # Half star
    if half_star:
        stars_html += '<i class="fas fa-star-half-alt text-warning"></i>'

    # Empty stars
    for _ in range(empty_stars):
        stars_html += '<i class="far fa-star text-warning"></i>'

    return mark_safe(stars_html)


@register.filter
def review_count_text(count):
    """
    Format review count for display.

    Usage: {{ review_count|review_count_text }}
    """
    if count == 0:
        return "No reviews"
    elif count == 1:
        return "1 review"
    else:
        return f"{count} reviews"
