# --- Standard Library Imports ---
import uuid

# --- Third-Party Imports ---
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from venues_app.models import Venue


class ReviewDraft(models.Model):
    """
    Model for saving review drafts before final submission.
    Allows customers to save incomplete reviews and return to them later.
    """

    # Core relationships
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="review_drafts",
        help_text=_("Customer who created this draft"),
    )
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name="review_drafts",
        help_text=_("Venue being reviewed"),
    )

    # Draft content
    rating = models.PositiveSmallIntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxV<PERSON>ueValidator(5)],
        help_text=_("Star rating from 1 to 5 (optional in draft)"),
    )
    written_review = models.TextField(
        max_length=1000,
        blank=True,
        help_text=_("Written review content (max 1000 characters, optional)"),
    )

    # Metadata
    created_at = models.DateTimeField(
        auto_now_add=True, help_text=_("When the draft was first created")
    )
    updated_at = models.DateTimeField(
        auto_now=True, help_text=_("When the draft was last updated")
    )

    class Meta:
        verbose_name = _("Review Draft")
        verbose_name_plural = _("Review Drafts")
        ordering = ["-updated_at"]
        unique_together = ["customer", "venue"]  # One draft per customer per venue
        indexes = [
            models.Index(fields=["customer"]),
            models.Index(fields=["venue"]),
            models.Index(fields=["updated_at"]),
        ]

    def __str__(self):
        return f"{self.customer.email} - {self.venue.venue_name} draft"

    def can_be_converted_to_review(self):
        """Check if draft has minimum requirements to become a review."""
        return self.rating is not None

    def convert_to_review(self):
        """Convert this draft to a completed review."""
        if not self.can_be_converted_to_review():
            raise ValidationError(
                _("Draft must have a rating to be converted to review")
            )

        # Check if review already exists
        if Review.objects.filter(customer=self.customer, venue=self.venue).exists():
            raise ValidationError(_("Review already exists for this venue"))

        # Create the review
        review = Review.objects.create(
            customer=self.customer,
            venue=self.venue,
            rating=self.rating,
            written_review=self.written_review or "",
        )

        # Delete the draft
        self.delete()

        return review


class Review(models.Model):
    """
    Model for customer reviews of venues.
    Customers can leave one review per venue with a star rating and written feedback.
    """

    # Rating choices (1-5 stars)
    RATING_CHOICES = [
        (1, _("1 Star - Poor")),
        (2, _("2 Stars - Fair")),
        (3, _("3 Stars - Good")),
        (4, _("4 Stars - Very Good")),
        (5, _("5 Stars - Excellent")),
    ]

    # Core relationships
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="reviews",
        help_text=_("Customer who wrote this review"),
    )
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name="reviews",
        help_text=_("Venue being reviewed"),
    )

    # Review content
    rating = models.PositiveSmallIntegerField(
        choices=RATING_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("Star rating from 1 to 5"),
    )
    written_review = models.TextField(
        max_length=1000,
        blank=True,
        help_text=_("Written review content (max 1000 characters, optional)"),
    )
    slug = models.SlugField(
        max_length=64,
        unique=True,
        blank=True,
        help_text=_("Unique slug for sharing this review"),
    )

    # Status and moderation
    is_approved = models.BooleanField(
        default=True, help_text=_("Whether this review is approved and visible")
    )
    is_flagged = models.BooleanField(
        default=False, help_text=_("Whether this review has been flagged for review")
    )

    # Metadata
    created_at = models.DateTimeField(
        auto_now_add=True, help_text=_("When the review was posted")
    )
    updated_at = models.DateTimeField(
        auto_now=True, help_text=_("When the review was last updated")
    )

    class Meta:
        verbose_name = _("Review")
        verbose_name_plural = _("Reviews")
        ordering = ["-created_at"]  # Most recent first
        unique_together = ["customer", "venue"]  # One review per customer per venue
        indexes = [
            models.Index(fields=["rating"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["venue"]),
        ]

    def __str__(self):
        return f"{self.customer.email} - {self.venue.venue_name} - {self.rating} stars"

    def clean(self):
        """Custom validation for reviews."""
        super().clean()

        # Only validate relationships if they are set (for form testing)
        if hasattr(self, "customer") and self.customer:
            # Validate that customer is not a service provider
            if (
                hasattr(self.customer, "is_service_provider")
                and self.customer.is_service_provider
            ):
                raise ValidationError(_("Service providers cannot leave reviews"))

            # Validate that customer is not reviewing their own venue
            if (
                self.venue
                and hasattr(self.customer, "service_provider_profile")
                and hasattr(self.customer.service_provider_profile, "venue")
                and self.customer.service_provider_profile.venue == self.venue
            ):
                raise ValidationError(_("You cannot review your own venue"))

    @property
    def rating_display(self):
        """Return star rating as a string for display."""
        return "★" * self.rating + "☆" * (5 - self.rating)

    @property
    def has_response(self):
        """Check if this review has a provider response."""
        return hasattr(self, "response") and self.response is not None

    def get_response(self):
        """Get the provider response if it exists."""
        try:
            return self.response
        except ReviewResponse.DoesNotExist:
            return None

    @property
    def is_recent(self):
        """Check if review was created within the last 7 days."""
        from datetime import timedelta

        from django.utils import timezone

        seven_days_ago = timezone.now() - timedelta(days=7)
        return self.created_at >= seven_days_ago

    @property
    def edit_time_remaining_hours(self):
        """Get remaining edit time in hours."""
        edit_window = self.get_edit_time_remaining()
        if edit_window and edit_window.total_seconds() > 0:
            return int(edit_window.total_seconds() / 3600)
        return 0

    def _generate_unique_slug(self):
        """Generate a unique slug using a random UUID."""
        slug = uuid.uuid4().hex[:10]
        while Review.objects.filter(slug=slug).exclude(pk=self.pk).exists():
            slug = uuid.uuid4().hex[:10]
        return slug

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = self._generate_unique_slug()
        super().save(*args, **kwargs)

    def can_be_edited(self):
        """Check if review can still be edited (within 24 hours of creation)."""
        from datetime import timedelta

        from django.utils import timezone

        edit_deadline = self.created_at + timedelta(hours=24)
        return timezone.now() <= edit_deadline

    def get_edit_time_remaining(self):
        """Get remaining time to edit this review."""
        from datetime import timedelta

        from django.utils import timezone

        edit_deadline = self.created_at + timedelta(hours=24)
        remaining = edit_deadline - timezone.now()

        if remaining.total_seconds() <= 0:
            return None

        # Calculate hours and minutes remaining
        total_seconds = int(remaining.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60

        return {
            "hours": hours,
            "minutes": minutes,
            "total_seconds": total_seconds,
            "deadline": edit_deadline,
        }

    def get_edit_deadline(self):
        """Get the deadline for editing this review."""
        from datetime import timedelta

        return self.created_at + timedelta(hours=24)

    def get_helpfulness_stats(self):
        """Get helpfulness statistics for this review."""
        helpful_votes = self.helpfulness_votes.filter(is_helpful=True).count()
        not_helpful_votes = self.helpfulness_votes.filter(is_helpful=False).count()
        total_votes = helpful_votes + not_helpful_votes

        return {
            "helpful_votes": helpful_votes,
            "not_helpful_votes": not_helpful_votes,
            "total_votes": total_votes,
            "helpfulness_ratio": (
                (helpful_votes / total_votes * 100) if total_votes > 0 else 0
            ),
        }


class ReviewResponse(models.Model):
    """
    Model for service provider responses to customer reviews.
    Providers can respond publicly to reviews to address feedback.
    """

    # Core relationships
    review = models.OneToOneField(
        Review,
        on_delete=models.CASCADE,
        related_name="response",
        help_text=_("Review being responded to"),
    )
    provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="review_responses",
        help_text=_("Service provider who wrote this response"),
    )

    # Response content
    response_text = models.TextField(
        max_length=500,
        help_text=_("Provider response to the review (max 500 characters)"),
    )

    # Metadata
    created_at = models.DateTimeField(
        auto_now_add=True, help_text=_("When the response was posted")
    )
    updated_at = models.DateTimeField(
        auto_now=True, help_text=_("When the response was last updated")
    )

    class Meta:
        verbose_name = _("Review Response")
        verbose_name_plural = _("Review Responses")
        ordering = ["-created_at"]

    def __str__(self):
        return f"Response to {self.review.customer.email}'s review of {self.review.venue.venue_name}"

    def clean(self):
        """Custom validation for review responses."""
        super().clean()

        # Only validate relationships if they are set (for form testing)
        if hasattr(self, "provider") and self.provider:
            # Validate that provider is a service provider
            if (
                hasattr(self.provider, "is_service_provider")
                and not self.provider.is_service_provider
            ):
                raise ValidationError(
                    _("Only service providers can respond to reviews")
                )

            # Validate that provider owns the venue being reviewed
            if (
                self.review
                and hasattr(self.provider, "service_provider_profile")
                and hasattr(self.provider.service_provider_profile, "venue")
                and self.provider.service_provider_profile.venue != self.review.venue
            ):
                raise ValidationError(
                    _("You can only respond to reviews of your own venue")
                )


class CustomerReviewResponse(models.Model):
    """
    Model for customer responses to provider responses.
    Allows customers to respond to service provider responses to their reviews.
    """

    # Core relationships
    provider_response = models.OneToOneField(
        ReviewResponse,
        on_delete=models.CASCADE,
        related_name="customer_response",
        help_text=_("Provider response being responded to"),
    )
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="customer_review_responses",
        help_text=_("Customer who wrote this response"),
    )

    # Response content
    response_text = models.TextField(
        max_length=300,
        help_text=_("Customer response to provider response (max 300 characters)"),
    )

    # Metadata
    created_at = models.DateTimeField(
        auto_now_add=True, help_text=_("When the response was posted")
    )
    updated_at = models.DateTimeField(
        auto_now=True, help_text=_("When the response was last updated")
    )

    class Meta:
        verbose_name = _("Customer Review Response")
        verbose_name_plural = _("Customer Review Responses")
        ordering = ["-created_at"]

    def __str__(self):
        return (
            f"Customer response to {self.provider_response.provider.email}'s response"
        )

    def clean(self):
        """Custom validation for customer review responses."""
        super().clean()

        # Only validate relationships if they are set
        if (
            hasattr(self, "customer")
            and self.customer
            and hasattr(self, "provider_response")
        ):
            # Validate that customer owns the original review
            if (
                self.provider_response
                and self.provider_response.review.customer != self.customer
            ):
                raise ValidationError(
                    _("You can only respond to responses on your own reviews")
                )

            # Validate that customer is not a service provider
            if (
                hasattr(self.customer, "is_service_provider")
                and self.customer.is_service_provider
            ):
                raise ValidationError(
                    _("Service providers cannot post customer responses")
                )

    def can_be_edited(self):
        """Check if response can still be edited (within 24 hours of creation)."""
        from datetime import timedelta

        from django.utils import timezone

        edit_deadline = self.created_at + timedelta(hours=24)
        return timezone.now() <= edit_deadline

    def get_edit_time_remaining(self):
        """Get remaining time to edit this response."""
        from datetime import timedelta

        from django.utils import timezone

        edit_deadline = self.created_at + timedelta(hours=24)
        remaining = edit_deadline - timezone.now()

        if remaining.total_seconds() <= 0:
            return None

        # Calculate hours and minutes remaining
        total_seconds = int(remaining.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60

        return {
            "hours": hours,
            "minutes": minutes,
            "total_seconds": total_seconds,
            "deadline": edit_deadline,
        }


class ReviewFlag(models.Model):
    """
    Model for flagging inappropriate or fake reviews.
    Customers can flag reviews for admin review without automatically hiding them.
    """

    # Flag status choices
    PENDING = "pending"
    REVIEWED = "reviewed"
    RESOLVED = "resolved"

    STATUS_CHOICES = [
        (PENDING, _("Pending Review")),
        (REVIEWED, _("Reviewed")),
        (RESOLVED, _("Resolved")),
    ]

    # Flag reason choices
    INAPPROPRIATE_CONTENT = "inappropriate"
    FAKE_REVIEW = "fake"
    SPAM = "spam"
    OFFENSIVE_LANGUAGE = "offensive"
    OTHER = "other"

    REASON_CHOICES = [
        (INAPPROPRIATE_CONTENT, _("Inappropriate Content")),
        (FAKE_REVIEW, _("Fake Review")),
        (SPAM, _("Spam")),
        (OFFENSIVE_LANGUAGE, _("Offensive Language")),
        (OTHER, _("Other")),
    ]

    # Core relationships
    review = models.ForeignKey(
        Review,
        on_delete=models.CASCADE,
        related_name="flags",
        help_text=_("Review being flagged"),
    )
    flagged_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="review_flags",
        help_text=_("Customer who flagged this review"),
    )

    # Flag details
    reason = models.CharField(
        max_length=20,
        choices=REASON_CHOICES,
        help_text=_("Reason for flagging this review"),
    )
    reason_text = models.TextField(
        max_length=500,
        blank=True,
        help_text=_("Additional details about why this review was flagged"),
    )

    # Status and resolution
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=PENDING,
        help_text=_("Current status of this flag"),
    )
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="reviewed_review_flags",
        help_text=_("Admin who reviewed this flag"),
    )
    admin_notes = models.TextField(
        blank=True, help_text=_("Admin notes about the flag resolution")
    )

    # Metadata
    created_at = models.DateTimeField(
        auto_now_add=True, help_text=_("When the flag was created")
    )
    reviewed_at = models.DateTimeField(
        null=True, blank=True, help_text=_("When the flag was reviewed by admin")
    )

    class Meta:
        verbose_name = _("Review Flag")
        verbose_name_plural = _("Review Flags")
        ordering = ["-created_at"]
        unique_together = ["review", "flagged_by"]  # One flag per user per review

    def __str__(self):
        return f"Flag: {self.review.venue.venue_name} review by {self.flagged_by.email}"

    def clean(self):
        """Custom validation for review flags."""
        super().clean()

        # Only validate relationships if they are set (for form testing)
        if hasattr(self, "flagged_by") and self.flagged_by:
            # Validate that flagged_by is not a service provider
            if (
                hasattr(self.flagged_by, "is_service_provider")
                and self.flagged_by.is_service_provider
            ):
                raise ValidationError(_("Service providers cannot flag reviews"))

            # Validate that user is not flagging their own review
            if self.review and self.flagged_by == self.review.customer:
                raise ValidationError(_("You cannot flag your own review"))

    def save(self, *args, **kwargs):
        """Override save to handle review timestamp."""
        # Set reviewed_at when status changes to reviewed or resolved
        if self.status in [self.REVIEWED, self.RESOLVED] and not self.reviewed_at:
            self.reviewed_at = timezone.now()
        elif self.status == self.PENDING:
            self.reviewed_at = None

        super().save(*args, **kwargs)

        # Update the review's is_flagged status
        if self.review:
            has_pending_flags = self.review.flags.filter(status=self.PENDING).exists()
            if self.review.is_flagged != has_pending_flags:
                self.review.is_flagged = has_pending_flags
                self.review.save()

    def resolve(self, admin_user, notes=""):
        """Mark the flag as resolved."""
        self.status = self.RESOLVED
        self.reviewed_by = admin_user
        self.admin_notes = notes
        self.save()

    def mark_reviewed(self, admin_user, notes=""):
        """Mark the flag as reviewed but not resolved."""
        self.status = self.REVIEWED
        self.reviewed_by = admin_user
        self.admin_notes = notes
        self.save()


class ReviewHelpfulness(models.Model):
    """
    Model for tracking review helpfulness votes.
    Customers can vote whether a review was helpful or not.
    """

    # Core relationships
    review = models.ForeignKey(
        Review,
        on_delete=models.CASCADE,
        related_name="helpfulness_votes",
        help_text=_("Review being voted on"),
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="review_helpfulness_votes",
        help_text=_("User who voted on helpfulness"),
    )

    # Vote details
    is_helpful = models.BooleanField(
        help_text=_("True if user found review helpful, False if not helpful")
    )

    # Metadata
    created_at = models.DateTimeField(
        auto_now_add=True, help_text=_("When the vote was cast")
    )
    updated_at = models.DateTimeField(
        auto_now=True, help_text=_("When the vote was last updated")
    )

    class Meta:
        verbose_name = _("Review Helpfulness Vote")
        verbose_name_plural = _("Review Helpfulness Votes")
        unique_together = ["review", "user"]  # One vote per user per review
        indexes = [
            models.Index(fields=["review", "is_helpful"]),
            models.Index(fields=["user"]),
        ]

    def __str__(self):
        helpful_text = "helpful" if self.is_helpful else "not helpful"
        return f"{self.user.email} found review {helpful_text}"

    def clean(self):
        """Custom validation for helpfulness votes."""
        super().clean()

        # Only validate relationships if they are set
        if (
            hasattr(self, "user")
            and self.user
            and hasattr(self, "review")
            and self.review
        ):
            # Validate that user is not voting on their own review
            if self.user == self.review.customer:
                raise ValidationError(_("You cannot vote on your own review"))

            # Validate that user is a customer
            if (
                hasattr(self.user, "is_service_provider")
                and self.user.is_service_provider
            ):
                raise ValidationError(
                    _("Service providers cannot vote on review helpfulness")
                )
