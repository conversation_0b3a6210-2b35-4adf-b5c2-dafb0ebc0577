{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>{{ page_title }}</h1>
            <p class="lead">Manage and resolve review flags</p>

            {% if flag_stats %}
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ flag_stats.total_flags }}</h3>
                                <p>Total Flags</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ flag_stats.pending_flags }}</h3>
                                <p>Pending Flags</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ flag_stats.reviewed_flags }}</h3>
                                <p>Reviewed Flags</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ flag_stats.resolved_flags }}</h3>
                                <p>Resolved Flags</p>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="card">
                <div class="card-header">
                    <h5>Review Flags</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <a href="?status=all" class="btn btn-sm {% if status_filter == 'all' %}btn-primary{% else %}btn-outline-primary{% endif %}">All</a>
                                <a href="?status=pending" class="btn btn-sm {% if status_filter == 'pending' %}btn-warning{% else %}btn-outline-warning{% endif %}">Pending</a>
                                <a href="?status=reviewed" class="btn btn-sm {% if status_filter == 'reviewed' %}btn-info{% else %}btn-outline-info{% endif %}">Reviewed</a>
                                <a href="?status=resolved" class="btn btn-sm {% if status_filter == 'resolved' %}btn-success{% else %}btn-outline-success{% endif %}">Resolved</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if flags %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Review</th>
                                        <th>Venue</th>
                                        <th>Flagged By</th>
                                        <th>Reason</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for flag in flags %}
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>{{ flag.review.customer.first_name|default:"Anonymous" }}</strong>
                                                    <span class="rating">{{ flag.review.rating }}/5</span>
                                                </div>
                                                <small class="text-muted">{{ flag.review.written_review|truncatewords:8 }}</small>
                                            </td>
                                            <td>{{ flag.review.venue.venue_name }}</td>
                                            <td>{{ flag.flagged_by.email }}</td>
                                            <td>{{ flag.reason|truncatewords:5 }}</td>
                                            <td>
                                                {% if flag.status == 'pending' %}
                                                    <span class="badge badge-warning">Pending</span>
                                                {% elif flag.status == 'reviewed' %}
                                                    <span class="badge badge-info">Reviewed</span>
                                                {% elif flag.status == 'resolved' %}
                                                    <span class="badge badge-success">Resolved</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ flag.created_at|date:"M d, Y" }}</td>
                                            <td>
                                                {% if flag.status == 'pending' %}
                                                    <a href="{% url 'review_app:admin_resolve_flag' flag.id %}" class="btn btn-sm btn-primary">Resolve</a>
                                                {% else %}
                                                    <a href="{% url 'review_app:admin_resolve_flag' flag.id %}" class="btn btn-sm btn-outline-secondary">View</a>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        {% if flags.has_other_pages %}
                            <nav aria-label="Flags pagination">
                                <ul class="pagination">
                                    {% if flags.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ flags.previous_page_number }}&status={{ status_filter }}">Previous</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">{{ flags.number }} of {{ flags.paginator.num_pages }}</span>
                                    </li>

                                    {% if flags.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ flags.next_page_number }}&status={{ status_filter }}">Next</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <h5>No flags found</h5>
                            <p>No review flags match the current filter.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
