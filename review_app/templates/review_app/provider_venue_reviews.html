{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}Venue Reviews Management{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Venue Reviews</h1>
            <p class="lead">Manage reviews for {{ venue.venue_name }}</p>

            {% if review_stats %}
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ review_stats.average_rating|floatformat:1 }}</h3>
                                <p>Average Rating</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ review_stats.total_reviews }}</h3>
                                <p>Total Reviews</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ review_stats.response_rate|floatformat:0 }}%</h3>
                                <p>Response Rate</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ review_stats.pending_responses }}</h3>
                                <p>Pending Responses</p>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            {% if reviews %}
                <div class="reviews-list">
                    {% for review in reviews %}
                        <div class="review-item card mb-3">
                            <div class="card-body">
                                <div class="review-header d-flex justify-content-between">
                                    <div>
                                        <strong>{{ review.customer.first_name|default:"Anonymous" }}</strong>
                                        <span class="rating">{{ review.rating }}/5 stars</span>
                                        {% if review.is_flagged %}
                                            <span class="badge badge-warning">Flagged</span>
                                        {% endif %}
                                        {% if not review.is_approved %}
                                            <span class="badge badge-info">Pending</span>
                                        {% endif %}
                                    </div>
                                    <div class="text-right">
                                        <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                                        <div class="review-actions mt-1">
                                            {% if not review.response %}
                                                <a href="{% url 'review_app:provider_respond_to_review' review.slug %}" class="btn btn-sm btn-primary">Respond</a>
                                            {% else %}
                                                <a href="{% url 'review_app:provider_edit_response' review.response.id %}" class="btn btn-sm btn-outline-primary">Edit Response</a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <p class="review-text mt-2">{{ review.written_review }}</p>

                                {% if review.response %}
                                    <div class="provider-response mt-3 p-3 bg-light">
                                        <strong>Your Response:</strong>
                                        <p>{{ review.response.response_text }}</p>
                                        <small class="text-muted">{{ review.response.created_at|date:"M d, Y" }}</small>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>

                {% if reviews.has_other_pages %}
                    <nav aria-label="Reviews pagination">
                        <ul class="pagination">
                            {% if reviews.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ reviews.previous_page_number }}">Previous</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">{{ reviews.number }} of {{ reviews.paginator.num_pages }}</span>
                            </li>

                            {% if reviews.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ reviews.next_page_number }}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="no-reviews text-center py-5">
                    <h3>No reviews yet</h3>
                    <p>Your venue hasn't received any reviews yet.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
