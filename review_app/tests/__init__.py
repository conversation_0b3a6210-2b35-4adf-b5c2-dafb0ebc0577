"""
Tests package for review_app.

This package contains all unit and integration tests for the review_app.
All test modules are imported here to ensure proper test discovery.
"""

# Patch asynchronous notification runner to execute synchronously during tests
from unittest.mock import patch

patch(
    "notifications_app.utils.run_async",
    lambda func, *args, **kwargs: func(*args, **kwargs),
).start()

from .simple_test import SimpleTestCase
from .test_forms import (
    ReviewEditFormTest,
    ReviewFlagFormTest,
    ReviewFormTest,
    ReviewResponseFormTest,
)
from .test_integration import (
    ReviewEdgeCasesIntegrationTest,
    ReviewIntegrationTest,
    ReviewPerformanceIntegrationTest,
)

# Import all test modules to ensure they are discovered by Djan<PERSON>'s test runner
from .test_models import ReviewFlagModelTest, ReviewModelTest, ReviewResponseModelTest
from .test_urls import ReviewAppURLsTest, URLAccessTest
from .test_utils import ReviewUtilityTest
from .test_views import AdminViewsTest, CustomerViewsTest, ProviderViewsTest
