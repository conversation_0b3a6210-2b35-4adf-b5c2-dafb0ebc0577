"""
Unit tests for review_app models.

This module contains comprehensive unit tests for all model classes in the review_app,
including Review, ReviewResponse, and ReviewFlag models.
"""

import os

# Standard library imports
import tempfile
from datetime import timedelta
from unittest.mock import Mock, patch

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError

# Django imports
from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone

from accounts_app.models import ServiceProviderProfile

# Local imports
from review_app.models import Review, ReviewFlag, ReviewResponse
from venues_app.models import Category, Venue

User = get_user_model()


class ReviewModelTest(TestCase):
    """Test the Review model."""

    def setUp(self):
        """Set up test data."""
        # Create test users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        # Create provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa",
            phone="+**********",
            contact_name="John Doe",
            address="123 Test Street",
            city="Test City",
            state="NY",
            zip_code="12345",
        )

        # Create test category and venue
        self.category = Category.objects.create(
            category_name="Spa Services", category_description="Relaxing spa treatments"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa Venue",
            short_description="A relaxing spa venue",
            state="NY",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test Street",
        )

        # Create test review
        self.review = Review.objects.create(
            customer=self.customer,
            venue=self.venue,
            rating=4,
            written_review="Great experience! The staff was very friendly and professional.",
        )

    def test_review_creation(self):
        """Test creating a review is successful."""
        self.assertEqual(self.review.customer, self.customer)
        self.assertEqual(self.review.venue, self.venue)
        self.assertEqual(self.review.rating, 4)
        self.assertEqual(
            self.review.written_review,
            "Great experience! The staff was very friendly and professional.",
        )
        self.assertTrue(self.review.is_approved)
        self.assertFalse(self.review.is_flagged)

        # Test string representation
        expected_str = f"{self.customer.email} - {self.venue.venue_name} - 4 stars"
        self.assertEqual(str(self.review), expected_str)

    def test_review_rating_validation(self):
        """Test review rating validation."""
        # Test rating too low
        with self.assertRaises(ValidationError):
            invalid_review = Review(
                customer=self.customer,
                venue=self.venue,
                rating=0,
                written_review="Invalid rating",
            )
            invalid_review.full_clean()

        # Test rating too high
        with self.assertRaises(ValidationError):
            invalid_review = Review(
                customer=self.customer,
                venue=self.venue,
                rating=6,
                written_review="Invalid rating",
            )
            invalid_review.full_clean()

    def test_review_unique_constraint(self):
        """Test that a customer can only review a venue once."""
        # Try to create a duplicate review
        with self.assertRaises(IntegrityError):
            duplicate_review = Review.objects.create(
                customer=self.customer,
                venue=self.venue,
                rating=5,
                written_review="This is a duplicate review",
            )

    def test_review_written_review_max_length(self):
        """Test that written review respects max length."""
        long_review = "x" * 1001  # Exceeds 1000 character limit

        with self.assertRaises(ValidationError):
            invalid_review = Review(
                customer=self.customer,
                venue=self.venue,
                rating=5,
                written_review=long_review,
            )
            invalid_review.full_clean()

    def test_review_ordering(self):
        """Test that reviews are ordered by creation date (newest first)."""
        # Create another review
        newer_review = Review.objects.create(
            customer=User.objects.create_user(
                email="<EMAIL>",
                password="testpass123",
                role=User.CUSTOMER,
            ),
            venue=self.venue,
            rating=5,
            written_review="Another great review!",
        )

        reviews = Review.objects.all()
        self.assertEqual(reviews[0], newer_review)
        self.assertEqual(reviews[1], self.review)

    def test_review_get_response_method(self):
        """Test the get_response method."""
        # Initially no response
        self.assertIsNone(self.review.get_response())

        # Create a response
        response = ReviewResponse.objects.create(
            review=self.review,
            provider=self.provider,
            response_text="Thank you for your review!",
        )

        # Now should return the response
        self.assertEqual(self.review.get_response(), response)

    def test_review_flagging_relationship(self):
        """Test the relationship between reviews and flags."""
        another_customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        # Create a flag for the review
        flag = ReviewFlag.objects.create(
            review=self.review,
            flagged_by=another_customer,
            reason=ReviewFlag.INAPPROPRIATE_CONTENT,
            reason_text="Inappropriate content",
        )

        # Check that review is now flagged
        self.review.refresh_from_db()
        self.assertTrue(self.review.is_flagged)

        # Check that flag was created
        self.assertTrue(
            ReviewFlag.objects.filter(
                review=self.review, flagged_by=another_customer
            ).exists()
        )

    def test_review_flag_relationship_access(self):
        """Test accessing flags through review relationship."""
        another_customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        # Create a flag for the review
        flag = ReviewFlag.objects.create(
            review=self.review,
            flagged_by=another_customer,
            reason=ReviewFlag.SPAM,
            reason_text="This looks like spam",
        )

        # Check that we can access flags through the review
        flags = self.review.flags.all()
        self.assertEqual(flags.count(), 1)
        self.assertEqual(flags.first(), flag)

    def test_review_meta_options(self):
        """Test model meta options."""
        self.assertEqual(Review._meta.ordering, ["-created_at"])
        self.assertEqual(Review._meta.verbose_name, "Review")
        self.assertEqual(Review._meta.verbose_name_plural, "Reviews")

        # Test unique_together constraint
        unique_together = Review._meta.unique_together
        self.assertIn(("customer", "venue"), unique_together)

    def test_review_timestamps(self):
        """Test that timestamps are set correctly."""
        self.assertIsNotNone(self.review.created_at)
        self.assertIsNotNone(self.review.updated_at)

        # Test that updated_at changes when model is saved
        original_updated_at = self.review.updated_at
        self.review.written_review = "Updated review text"
        self.review.save()

        self.assertGreater(self.review.updated_at, original_updated_at)

    def test_review_default_values(self):
        """Test default values for review fields."""
        review = Review(
            customer=self.customer,
            venue=self.venue,
            rating=3,
            written_review="Test review",
        )

        # Test defaults before saving
        self.assertTrue(review.is_approved)
        self.assertFalse(review.is_flagged)


class ReviewResponseModelTest(TestCase):
    """Test the ReviewResponse model."""

    def setUp(self):
        """Set up test data."""
        # Create test users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        # Create provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa",
            phone="+**********",
            contact_name="John Doe",
            address="123 Test Street",
            city="Test City",
            state="NY",
            zip_code="12345",
        )

        # Create test category and venue
        self.category = Category.objects.create(
            category_name="Spa Services", category_description="Relaxing spa treatments"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa Venue",
            short_description="A relaxing spa venue",
            state="NY",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test Street",
        )

        # Create test review
        self.review = Review.objects.create(
            customer=self.customer,
            venue=self.venue,
            rating=4,
            written_review="Great experience! The staff was very friendly and professional.",
        )

        # Create test response
        self.response = ReviewResponse.objects.create(
            review=self.review,
            provider=self.provider,
            response_text="Thank you for your kind review! We're glad you enjoyed your experience.",
        )

    def test_response_creation(self):
        """Test creating a response is successful."""
        self.assertEqual(self.response.review, self.review)
        self.assertEqual(
            self.response.response_text,
            "Thank you for your kind review! We're glad you enjoyed your experience.",
        )

        # Test string representation
        expected_str = (
            f"Response to {self.customer.email}'s review of {self.venue.venue_name}"
        )
        self.assertEqual(str(self.response), expected_str)

    def test_response_one_to_one_relationship(self):
        """Test that a review can only have one response."""
        # Try to create another response for the same review
        with self.assertRaises(IntegrityError):
            duplicate_response = ReviewResponse.objects.create(
                review=self.review,
                provider=self.provider,
                response_text="This should fail",
            )

    def test_response_ordering(self):
        """Test that responses are ordered by creation date (newest first)."""
        # Create another review and response
        another_review = Review.objects.create(
            customer=User.objects.create_user(
                email="<EMAIL>",
                password="testpass123",
                role=User.CUSTOMER,
            ),
            venue=self.venue,
            rating=5,
            written_review="Another great review!",
        )

        newer_response = ReviewResponse.objects.create(
            review=another_review,
            provider=self.provider,
            response_text="Thank you for this review too!",
        )

        responses = ReviewResponse.objects.all()
        self.assertEqual(responses[0], newer_response)
        self.assertEqual(responses[1], self.response)

    def test_response_timestamps(self):
        """Test that timestamps are set correctly."""
        self.assertIsNotNone(self.response.created_at)
        self.assertIsNotNone(self.response.updated_at)

        # Test that updated_at changes when model is saved
        original_updated_at = self.response.updated_at
        self.response.response_text = "Updated response text"
        self.response.save()

        self.assertGreater(self.response.updated_at, original_updated_at)

    def test_response_meta_options(self):
        """Test model meta options."""
        self.assertEqual(ReviewResponse._meta.ordering, ["-created_at"])
        self.assertEqual(ReviewResponse._meta.verbose_name, "Review Response")
        self.assertEqual(ReviewResponse._meta.verbose_name_plural, "Review Responses")

    def test_response_cascade_delete(self):
        """Test that response is deleted when review is deleted."""
        response_id = self.response.id
        self.review.delete()

        # Response should be deleted
        self.assertFalse(ReviewResponse.objects.filter(id=response_id).exists())


class ReviewFlagModelTest(TestCase):
    """Test the ReviewFlag model."""

    def setUp(self):
        """Set up test data."""
        # Create test users
        self.customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        self.flagger = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        self.admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.ADMIN,
            is_staff=True,
        )

        self.provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        # Create provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name="Test Spa",
            phone="+**********",
            contact_name="John Doe",
            address="123 Test Street",
            city="Test City",
            state="NY",
            zip_code="12345",
        )

        # Create test category and venue
        self.category = Category.objects.create(
            category_name="Spa Services", category_description="Relaxing spa treatments"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa Venue",
            short_description="A relaxing spa venue",
            state="NY",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test Street",
        )

        # Create test review
        self.review = Review.objects.create(
            customer=self.customer,
            venue=self.venue,
            rating=4,
            written_review="Great experience! The staff was very friendly and professional.",
        )

        # Create test flag
        self.flag = ReviewFlag.objects.create(
            review=self.review,
            flagged_by=self.flagger,
            reason=ReviewFlag.INAPPROPRIATE_CONTENT,
            reason_text="Inappropriate content",
        )

    def test_flag_creation(self):
        """Test creating a flag is successful."""
        self.assertEqual(self.flag.review, self.review)
        self.assertEqual(self.flag.flagged_by, self.flagger)
        self.assertEqual(self.flag.reason, ReviewFlag.INAPPROPRIATE_CONTENT)
        self.assertEqual(self.flag.reason_text, "Inappropriate content")
        self.assertEqual(self.flag.status, ReviewFlag.PENDING)
        self.assertIsNone(self.flag.reviewed_by)
        self.assertIsNone(self.flag.reviewed_at)

        # Test string representation
        expected_str = f"Flag: {self.venue.venue_name} review by {self.flagger.email}"
        self.assertEqual(str(self.flag), expected_str)

    def test_flag_status_choices(self):
        """Test flag status choices."""
        self.assertEqual(ReviewFlag.PENDING, "pending")
        self.assertEqual(ReviewFlag.REVIEWED, "reviewed")
        self.assertEqual(ReviewFlag.RESOLVED, "resolved")

    def test_flag_ordering(self):
        """Test that flags are ordered by creation date (newest first)."""
        # Create another flag
        newer_flag = ReviewFlag.objects.create(
            review=self.review,
            flagged_by=User.objects.create_user(
                email="<EMAIL>", password="testpass123", role=User.CUSTOMER
            ),
            reason=ReviewFlag.SPAM,
            reason_text="Spam content",
        )

        flags = ReviewFlag.objects.all()
        self.assertEqual(flags[0], newer_flag)
        self.assertEqual(flags[1], self.flag)

    def test_flag_save_method_reviewed_timestamp(self):
        """Test that reviewed_at is set when status changes to reviewed."""
        # Initially no reviewed_at
        self.assertIsNone(self.flag.reviewed_at)

        # Change status to reviewed
        self.flag.status = ReviewFlag.REVIEWED
        self.flag.reviewed_by = self.admin
        self.flag.save()

        # Should now have reviewed_at timestamp
        self.assertIsNotNone(self.flag.reviewed_at)

    def test_flag_save_method_resolved_timestamp(self):
        """Test that reviewed_at is set when status changes to resolved."""
        # Change status to resolved
        self.flag.status = ReviewFlag.RESOLVED
        self.flag.reviewed_by = self.admin
        self.flag.save()

        # Should have reviewed_at timestamp
        self.assertIsNotNone(self.flag.reviewed_at)

    def test_flag_save_method_pending_clears_timestamp(self):
        """Test that reviewed_at is cleared when status changes back to pending."""
        # First set to reviewed
        self.flag.status = ReviewFlag.REVIEWED
        self.flag.reviewed_by = self.admin
        self.flag.save()
        self.assertIsNotNone(self.flag.reviewed_at)

        # Change back to pending
        self.flag.status = ReviewFlag.PENDING
        self.flag.save()

        # Should clear reviewed_at
        self.assertIsNone(self.flag.reviewed_at)

    def test_flag_save_updates_review_flagged_status(self):
        """Test that saving a flag updates the review's is_flagged status."""
        # Review should be flagged when flag exists
        self.assertTrue(self.review.is_flagged)

        # Resolve the flag
        self.flag.status = ReviewFlag.RESOLVED
        self.flag.reviewed_by = self.admin
        self.flag.save()

        # Refresh review from database
        self.review.refresh_from_db()

        # Review should no longer be flagged (no pending flags)
        self.assertFalse(self.review.is_flagged)

    def test_flag_resolve_method(self):
        """Test the resolve method."""
        notes = "Flag reviewed and resolved - content is appropriate"

        self.flag.resolve(self.admin, notes)

        self.assertEqual(self.flag.status, ReviewFlag.RESOLVED)
        self.assertEqual(self.flag.reviewed_by, self.admin)
        self.assertEqual(self.flag.admin_notes, notes)
        self.assertIsNotNone(self.flag.reviewed_at)

    def test_flag_meta_options(self):
        """Test model meta options."""
        self.assertEqual(ReviewFlag._meta.ordering, ["-created_at"])
        self.assertEqual(ReviewFlag._meta.verbose_name, "Review Flag")
        self.assertEqual(ReviewFlag._meta.verbose_name_plural, "Review Flags")

    def test_flag_cascade_delete(self):
        """Test that flag is deleted when review is deleted."""
        flag_id = self.flag.id
        self.review.delete()

        # Flag should be deleted
        self.assertFalse(ReviewFlag.objects.filter(id=flag_id).exists())

    def test_flag_timestamps(self):
        """Test that timestamps are set correctly."""
        self.assertIsNotNone(self.flag.created_at)

        # Test that reviewed_at is initially None
        self.assertIsNone(self.flag.reviewed_at)

        # Test that reviewed_at is set when status changes
        self.flag.status = ReviewFlag.REVIEWED
        self.flag.reviewed_by = self.admin
        self.flag.save()

        self.assertIsNotNone(self.flag.reviewed_at)

    def test_flag_default_values(self):
        """Test default values for flag fields."""
        flag = ReviewFlag(
            review=self.review,
            flagged_by=self.flagger,
            reason=ReviewFlag.OTHER,
            reason_text="Test reason",
        )

        # Test defaults before saving
        self.assertEqual(flag.status, ReviewFlag.PENDING)
        self.assertIsNone(flag.reviewed_by)
        self.assertIsNone(flag.reviewed_at)
        self.assertEqual(flag.admin_notes, "")
