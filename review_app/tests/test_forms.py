"""
Unit tests for review_app forms.

This module contains comprehensive unit tests for all form classes in the review_app,
including ReviewForm, ReviewResponseForm, ReviewFlagForm, and AdminReviewForm.
"""

from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

# Django imports
from django.test import TestCase

from accounts_app.models import ServiceProviderProfile
from review_app.forms import (
    ReviewEditForm,
    ReviewFlagForm,
    ReviewForm,
    ReviewResponseForm,
)

# Local imports
from review_app.models import Review, ReviewFlag, ReviewResponse
from venues_app.models import Category, Venue

User = get_user_model()

# Disable asynchronous notifications during tests to prevent database locks
patch("notifications_app.utils.run_async", lambda func, *args, **kwargs: None).start()
patch("venues_app.signals.run_async", lambda func, *args, **kwargs: None).start()
patch("review_app.utils.run_async", lambda func, *args, **kwargs: None).start()


class ReviewFormTest(TestCase):
    """Test the ReviewForm."""

    def setUp(self):
        """Set up test data."""
        self.form_data = {
            "rating": 4,
            "written_review": "Great experience! The staff was very friendly and professional.",
        }

    def test_review_form_valid(self):
        """Test that the form is valid with valid data."""
        form = ReviewForm(data=self.form_data)
        self.assertTrue(form.is_valid())

    def test_review_form_missing_rating(self):
        """Test that the form is invalid without rating."""
        form_data = self.form_data.copy()
        del form_data["rating"]
        form = ReviewForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("rating", form.errors)

    def test_review_form_missing_written_review(self):
        """Test that the form is valid without written review (now optional)."""
        form_data = self.form_data.copy()
        del form_data["written_review"]
        form = ReviewForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_review_form_invalid_rating_low(self):
        """Test that the form is invalid with rating too low."""
        form_data = self.form_data.copy()
        form_data["rating"] = 0
        form = ReviewForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("rating", form.errors)

    def test_review_form_invalid_rating_high(self):
        """Test that the form is invalid with rating too high."""
        form_data = self.form_data.copy()
        form_data["rating"] = 6
        form = ReviewForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("rating", form.errors)

    def test_review_form_written_review_max_length(self):
        """Test that the form validates written review max length."""
        form_data = self.form_data.copy()
        form_data["written_review"] = "x" * 1001  # Exceeds 1000 character limit
        form = ReviewForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("written_review", form.errors)

    def test_review_form_written_review_empty(self):
        """Test that the form is valid with empty written review (now optional)."""
        form_data = self.form_data.copy()
        form_data["written_review"] = ""
        form = ReviewForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_review_form_written_review_whitespace_only(self):
        """Test that the form is valid with whitespace-only written review (now optional)."""
        form_data = self.form_data.copy()
        form_data["written_review"] = "   \n\t   "
        form = ReviewForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_review_form_rating_choices(self):
        """Test that all valid rating choices work."""
        for rating in [1, 2, 3, 4, 5]:
            form_data = self.form_data.copy()
            form_data["rating"] = rating
            form = ReviewForm(data=form_data)
            self.assertTrue(form.is_valid(), f"Rating {rating} should be valid")

    def test_review_form_save_method(self):
        """Test that the form save method works correctly."""
        # Create test data
        customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        provider_profile = ServiceProviderProfile.objects.create(
            user=provider,
            legal_name="Test Spa",
            phone="+**********",
            contact_name="John Doe",
            address="123 Test Street",
            city="Test City",
            state="NY",
            zip_code="12345",
        )

        category = Category.objects.create(
            category_name="Spa Services", category_description="Relaxing spa treatments"
        )

        venue = Venue.objects.create(
            service_provider=provider_profile,
            venue_name="Test Spa Venue",
            short_description="A relaxing spa venue",
            state="NY",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test Street",
        )

        form = ReviewForm(data=self.form_data)
        self.assertTrue(form.is_valid())

        review = form.save(commit=False)
        review.customer = customer
        review.venue = venue
        review.save()

        self.assertEqual(review.rating, 4)
        self.assertEqual(
            review.written_review,
            "Great experience! The staff was very friendly and professional.",
        )
        self.assertEqual(review.customer, customer)
        self.assertEqual(review.venue, venue)

    def test_review_form_fields(self):
        """Test that the form has the correct fields."""
        form = ReviewForm()
        self.assertIn("rating", form.fields)
        self.assertIn("written_review", form.fields)
        self.assertEqual(len(form.fields), 2)

    def test_review_form_widgets(self):
        """Test that the form uses correct widgets."""
        form = ReviewForm()

        # Check rating widget
        rating_widget = form.fields["rating"].widget
        self.assertEqual(rating_widget.__class__.__name__, "Select")

        # Check written_review widget
        written_review_widget = form.fields["written_review"].widget
        self.assertEqual(written_review_widget.__class__.__name__, "Textarea")


class ReviewResponseFormTest(TestCase):
    """Test the ReviewResponseForm."""

    def setUp(self):
        """Set up test data."""
        self.form_data = {
            "response_text": "Thank you for your review! We appreciate your feedback."
        }

    def test_response_form_valid(self):
        """Test that the form is valid with valid data."""
        form = ReviewResponseForm(data=self.form_data)
        self.assertTrue(form.is_valid())

    def test_response_form_missing_response_text(self):
        """Test that the form is invalid without response text."""
        form = ReviewResponseForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn("response_text", form.errors)

    def test_response_form_empty_response_text(self):
        """Test that the form is invalid with empty response text."""
        form_data = {"response_text": ""}
        form = ReviewResponseForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("response_text", form.errors)

    def test_response_form_whitespace_only_response_text(self):
        """Test that the form is invalid with whitespace-only response text."""
        form_data = {"response_text": "   \n\t   "}
        form = ReviewResponseForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("response_text", form.errors)

    def test_response_form_save_method(self):
        """Test that the form save method works correctly."""
        # Create test data
        customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        provider_profile = ServiceProviderProfile.objects.create(
            user=provider,
            legal_name="Test Spa",
            phone="+**********",
            contact_name="John Doe",
            address="123 Test Street",
            city="Test City",
            state="NY",
            zip_code="12345",
        )

        category = Category.objects.create(
            category_name="Spa Services", category_description="Relaxing spa treatments"
        )

        venue = Venue.objects.create(
            service_provider=provider_profile,
            venue_name="Test Spa Venue",
            short_description="A relaxing spa venue",
            state="NY",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test Street",
        )

        review = Review.objects.create(
            customer=customer,
            venue=venue,
            rating=5,
            written_review="Excellent service!",
        )

        form = ReviewResponseForm(data=self.form_data)
        self.assertTrue(form.is_valid())

        response = form.save(commit=False)
        response.provider = provider
        response.review = review
        response.save()

        self.assertEqual(
            response.response_text,
            "Thank you for your review! We appreciate your feedback.",
        )
        self.assertEqual(response.provider, provider)
        self.assertEqual(response.review, review)

    def test_response_form_fields(self):
        """Test that the form has the correct fields."""
        form = ReviewResponseForm()
        self.assertIn("response_text", form.fields)
        self.assertEqual(len(form.fields), 1)

    def test_response_form_widget(self):
        """Test that the form uses correct widget."""
        form = ReviewResponseForm()

        # Check response_text widget
        response_text_widget = form.fields["response_text"].widget
        self.assertEqual(response_text_widget.__class__.__name__, "Textarea")


class ReviewFlagFormTest(TestCase):
    """Test the ReviewFlagForm."""

    def setUp(self):
        """Set up test data."""
        self.form_data = {
            "reason": "inappropriate",
            "reason_text": "This review contains inappropriate language.",
        }

    def test_flag_form_valid(self):
        """Test that the form is valid with valid data."""
        form = ReviewFlagForm(data=self.form_data)
        self.assertTrue(form.is_valid())

    def test_flag_form_missing_reason(self):
        """Test that the form is invalid without reason."""
        form_data = self.form_data.copy()
        del form_data["reason"]
        form = ReviewFlagForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("reason", form.errors)

    def test_flag_form_empty_reason(self):
        """Test that the form is invalid with empty reason."""
        form_data = self.form_data.copy()
        form_data["reason"] = ""
        form = ReviewFlagForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("reason", form.errors)

    def test_flag_form_invalid_reason_text(self):
        """Test that the form is invalid with reason text that's too short."""
        form_data = self.form_data.copy()
        form_data["reason_text"] = "Bad"  # Less than 5 characters
        form = ReviewFlagForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("reason_text", form.errors)

    def test_flag_form_save_method(self):
        """Test that the form save method works correctly."""
        # Create test data
        customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        flagger = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        provider_profile = ServiceProviderProfile.objects.create(
            user=provider,
            legal_name="Test Spa",
            phone="+**********",
            contact_name="John Doe",
            address="123 Test Street",
            city="Test City",
            state="NY",
            zip_code="12345",
        )

        category = Category.objects.create(
            category_name="Spa Services", category_description="Relaxing spa treatments"
        )

        venue = Venue.objects.create(
            service_provider=provider_profile,
            venue_name="Test Spa Venue",
            short_description="A relaxing spa venue",
            state="NY",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test Street",
        )

        review = Review.objects.create(
            customer=customer,
            venue=venue,
            rating=5,
            written_review="Excellent service!",
        )

        form = ReviewFlagForm(data=self.form_data)
        self.assertTrue(form.is_valid())

        flag = form.save(commit=False)
        flag.flagged_by = flagger
        flag.review = review
        flag.save()

        self.assertEqual(flag.reason, "inappropriate")
        self.assertEqual(
            flag.reason_text, "This review contains inappropriate language."
        )
        self.assertEqual(flag.flagged_by, flagger)
        self.assertEqual(flag.review, review)

    def test_flag_form_fields(self):
        """Test that the form has the correct fields."""
        form = ReviewFlagForm()
        self.assertIn("reason", form.fields)
        self.assertIn("reason_text", form.fields)
        self.assertEqual(len(form.fields), 2)

    def test_flag_form_widgets(self):
        """Test that the form uses correct widgets."""
        form = ReviewFlagForm()

        # Check reason widget
        reason_widget = form.fields["reason"].widget
        self.assertEqual(reason_widget.__class__.__name__, "Select")

        # Check reason_text widget
        reason_text_widget = form.fields["reason_text"].widget
        self.assertEqual(reason_text_widget.__class__.__name__, "Textarea")


class ReviewEditFormTest(TestCase):
    """Test the ReviewEditForm."""

    def setUp(self):
        """Set up test data."""
        self.form_data = {
            "rating": 4,
            "written_review": "Updated review content with more details.",
        }

    def test_edit_form_valid(self):
        """Test that the form is valid with valid data."""
        form = ReviewEditForm(data=self.form_data)
        self.assertTrue(form.is_valid())

    def test_edit_form_missing_required_fields(self):
        """Test that the form is invalid without required rating field."""
        form_data = {}
        form = ReviewEditForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("rating", form.errors)
        # written_review is now optional, so it should not be in errors

    def test_edit_form_invalid_rating(self):
        """Test that the form is invalid with invalid rating."""
        form_data = self.form_data.copy()
        form_data["rating"] = 6  # Invalid rating
        form = ReviewEditForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("rating", form.errors)

    def test_edit_form_invalid_written_review(self):
        """Test that the form is invalid with written review that's too short."""
        form_data = self.form_data.copy()
        form_data["written_review"] = "Short"  # Less than 10 characters
        form = ReviewEditForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("written_review", form.errors)

    def test_edit_form_fields(self):
        """Test that the form has the correct fields."""
        form = ReviewEditForm()
        self.assertIn("rating", form.fields)
        self.assertIn("written_review", form.fields)
        self.assertEqual(len(form.fields), 2)

    def test_edit_form_widgets(self):
        """Test that the form uses correct widgets."""
        form = ReviewEditForm()

        # Check rating widget
        rating_widget = form.fields["rating"].widget
        self.assertEqual(rating_widget.__class__.__name__, "Select")

        # Check written_review widget
        written_review_widget = form.fields["written_review"].widget
        self.assertEqual(written_review_widget.__class__.__name__, "Textarea")

    def test_edit_form_save_method(self):
        """Test that the form save method works correctly."""
        # Create test data
        customer = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role=User.CUSTOMER
        )

        provider = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role=User.SERVICE_PROVIDER,
        )

        provider_profile = ServiceProviderProfile.objects.create(
            user=provider,
            legal_name="Test Spa",
            phone="+**********",
            contact_name="John Doe",
            address="123 Test Street",
            city="Test City",
            state="NY",
            zip_code="12345",
        )

        category = Category.objects.create(
            category_name="Spa Services", category_description="Relaxing spa treatments"
        )

        venue = Venue.objects.create(
            service_provider=provider_profile,
            venue_name="Test Spa Venue",
            short_description="A relaxing spa venue",
            state="NY",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test Street",
        )

        # Create existing review
        review = Review.objects.create(
            customer=customer,
            venue=venue,
            rating=3,
            written_review="Original review content",
        )

        form = ReviewEditForm(data=self.form_data, instance=review)
        self.assertTrue(form.is_valid())

        updated_review = form.save()

        self.assertEqual(updated_review.rating, 4)
        self.assertEqual(
            updated_review.written_review, "Updated review content with more details."
        )
        self.assertEqual(updated_review.customer, customer)
        self.assertEqual(updated_review.venue, venue)
