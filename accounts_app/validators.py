# --- Standard Library Imports ---
import re

# --- Third-Party Imports ---
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


def normalize_phone(phone: str):
    """Validate and normalize a phone number."""
    if not phone:
        return phone
    digits_only = re.sub(r"\D", "", phone)
    if len(digits_only) < 10:
        raise ValidationError(_("Phone number must contain at least 10 digits."))
    return f"+{digits_only}"
