# --- Standard Library Imports ---
import csv
import logging
import time
import traceback
from datetime import timedelta
from functools import wraps
from typing import Any, Dict, Optional

from django.contrib.auth import get_user_model
from django.http import HttpRequest, HttpResponse

# --- Django Imports ---
from django.utils import timezone

User = get_user_model()

# --- Logger Instances ---
security_logger = logging.getLogger("accounts_app.security")
activity_logger = logging.getLogger("accounts_app.activity")
error_logger = logging.getLogger("accounts_app.errors")
performance_logger = logging.getLogger("accounts_app.performance")
audit_logger = logging.getLogger("accounts_app.audit")


# --- Client Info Utility ---
def get_client_info(request: Optional[HttpRequest]) -> Dict[str, str]:
    """
    Extract IP, user agent, session, and request metadata.
    """
    if not request:
        return {
            "ip_address": "unknown",
            "user_agent": "unknown",
            "session_key": "unknown",
            "referer": "unknown",
            "method": "unknown",
            "path": "unknown",
            "query_string": "",
        }

    xf = request.META.get("HTTP_X_FORWARDED_FOR")
    ip = xf.split(",")[0].strip() if xf else request.META.get("REMOTE_ADDR", "unknown")
    session_key = getattr(request.session, "session_key", "no_session") or "no_session"

    return {
        "ip_address": ip,
        "user_agent": request.META.get("HTTP_USER_AGENT", "unknown"),
        "session_key": session_key,
        "referer": request.META.get("HTTP_REFERER", "direct"),
        "method": getattr(request, "method", "unknown"),
        "path": getattr(request, "path", "unknown"),
        "query_string": request.META.get("QUERY_STRING", ""),
    }


# --- Security Logging ---


def log_security_event(
    event_type: str,
    user_email: Optional[str] = None,
    user_id: Optional[int] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    severity: str = "INFO",
) -> None:
    """
    Log security-related events with context.
    """
    info = get_client_info(request)
    data = {
        "event_type": event_type,
        "timestamp": timezone.now().isoformat(),
        "user_email": user_email,
        "user_id": user_id,
        "severity": severity,
        **info,
    }
    if details:
        data.update(details)

    msg = f"SECURITY_EVENT: {event_type}"
    if user_email:
        msg += f" | User: {user_email}"
    if info["ip_address"] != "unknown":
        msg += f" | IP: {info['ip_address']}"

    level = getattr(logging, severity.upper(), logging.INFO)
    security_logger.log(level, msg, extra=data)


def log_authentication_event(
    event_type: str,
    user_email: str,
    success: bool,
    request: Optional[HttpRequest] = None,
    failure_reason: Optional[str] = None,
    additional_data: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Log authentication successes and failures.
    """
    severity = "INFO" if success else "WARNING"
    details = {
        "success": success,
        "failure_reason": failure_reason,
        "event_category": "authentication",
    }
    if additional_data:
        details.update(additional_data)

    log_security_event(
        event_type=event_type,
        user_email=user_email,
        request=request,
        details=details,
        severity=severity,
    )


# --- User Activity Logging ---


def log_user_activity(
    activity_type: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    target_object: Optional[str] = None,
) -> None:
    """
    Record general user actions (e.g., profile updates).
    """
    info = get_client_info(request)
    data = {
        "activity_type": activity_type,
        "timestamp": timezone.now().isoformat(),
        "user_email": getattr(user, "email", None),
        "user_id": getattr(user, "id", None),
        "target": target_object,
        **info,
    }
    if details:
        data.update(details)

    msg = f"USER_ACTIVITY: {activity_type}"
    if user:
        msg += f" | User: {user.email}"
    if target_object:
        msg += f" | Target: {target_object}"

    activity_logger.info(msg, extra=data)


def log_profile_change(
    user: User,
    profile_type: str,
    changed_fields: Dict[str, Any],
    request: Optional[HttpRequest] = None,
    old_values: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Log changes to user profiles.
    """
    details = {
        "profile_type": profile_type,
        "changed_fields": list(changed_fields.keys()),
        "new_values": changed_fields,
        "old_values": old_values or {},
    }
    log_user_activity(
        activity_type="profile_update",
        user=user,
        request=request,
        details=details,
        target_object=f"{profile_type}_profile",
    )


def log_team_management_event(
    action: str,
    service_provider: User,
    team_member_name: str,
    request: Optional[HttpRequest] = None,
    team_member_id: Optional[int] = None,
    additional_details: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Log additions, edits, and removals of team members.
    """
    details = {
        "action": action,
        "team_member_name": team_member_name,
        "team_member_id": team_member_id,
    }
    if additional_details:
        details.update(additional_details)

    log_user_activity(
        activity_type=f"team_member_{action}",
        user=service_provider,
        request=request,
        details=details,
        target_object=f"team_member_{team_member_id}",
    )


def log_account_lifecycle_event(
    event_type: str,
    user: User,
    request: Optional[HttpRequest] = None,
    performed_by: Optional[User] = None,
    reason: Optional[str] = None,
) -> None:
    """
    Log account creations, deactivations, or deletions.
    """
    details = {"reason": reason, "performed_by": getattr(performed_by, "email", None)}
    if performed_by and performed_by != user:
        log_audit_event(
            action=f"account_{event_type}",
            admin_user=performed_by,
            target_user=user,
            request=request,
            details=details,
        )
    else:
        log_user_activity(
            activity_type=f"account_{event_type}",
            user=user,
            request=request,
            details=details,
        )


def log_data_access_event(
    access_type: str,
    user: User,
    data_type: str,
    request: Optional[HttpRequest] = None,
    record_count: Optional[int] = None,
    filters_applied: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Log data views, exports, or searches.
    """
    details = {
        "data_type": data_type,
        "record_count": record_count,
        "filters": filters_applied or {},
    }
    log_user_activity(
        activity_type=f"data_{access_type}",
        user=user,
        request=request,
        details=details,
        target_object=data_type,
    )


# --- Error Logging ---


def log_error(
    error_type: str,
    error_message: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    exception: Optional[Exception] = None,
    details: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Log exceptions and errors with traceback.
    """
    info = get_client_info(request)
    data = {
        "error_type": error_type,
        "error_message": error_message,
        "timestamp": timezone.now().isoformat(),
        "user_email": getattr(user, "email", None),
        **info,
    }
    if exception:
        data.update(
            {
                "exception_type": type(exception).__name__,
                "exception_message": str(exception),
                "traceback": traceback.format_exc(),
            }
        )
    if details:
        data.update(details)

    msg = f"ERROR: {error_type} - {error_message}"
    if user:
        msg += f" | User: {user.email}"

    error_logger.error(msg, extra=data)


def log_info(
    info_type: str,
    message: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Log general informational messages.
    """
    info = get_client_info(request)
    data = {
        "info_type": info_type,
        "message": message,
        "timestamp": timezone.now().isoformat(),
        "user_email": getattr(user, "email", None),
        **info,
    }
    if details:
        data.update(details)

    msg = f"INFO: {info_type} - {message}"
    if user:
        msg += f" | User: {user.email}"

    activity_logger.info(msg, extra=data)


# --- Performance Logging ---


def log_performance(
    operation: str,
    duration: float,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Log execution time of operations.
    """
    info = get_client_info(request)
    data = {
        "operation": operation,
        "duration": duration,
        "timestamp": timezone.now().isoformat(),
        "user_email": getattr(user, "email", None),
        **info,
    }
    if details:
        data.update(details)

    if duration > 5.0:
        level = logging.WARNING
        tag = "SLOW"
    elif duration > 2.0:
        level = logging.INFO
        tag = "MODERATE"
    else:
        level = logging.DEBUG
        tag = "NORMAL"

    msg = f"PERF: {operation} took {duration:.2f}s ({tag})"
    if user:
        msg += f" | User: {user.email}"

    performance_logger.log(level, msg, extra=data)


def performance_monitor(operation_name: str):
    """
    Decorator to measure and log function execution time.
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start = time.time()
            request = next((a for a in args if hasattr(a, "META")), None)
            user = next(
                (a for a in args if hasattr(a, "email") and hasattr(a, "id")), None
            )
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start
                log_performance(
                    f"{func.__module__}.{func.__name__}",
                    duration,
                    user,
                    request,
                    {"operation_name": operation_name},
                )
                return result
            except Exception as e:
                duration = time.time() - start
                log_error(
                    "function_execution",
                    f"Error in {operation_name}",
                    user,
                    request,
                    e,
                    {"duration": duration},
                )
                raise

        return wrapper

    return decorator


# --- Audit Logging ---


def log_audit_event(
    action: str,
    admin_user: User,
    target_user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None,
    changes: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Log administrative audit events.
    """
    info = get_client_info(request)
    data = {
        "action": action,
        "timestamp": timezone.now().isoformat(),
        "admin_email": admin_user.email,
        "admin_id": admin_user.id,
        "target_email": getattr(target_user, "email", None),
        "changes": changes,
        **info,
    }
    if details:
        data.update(details)

    msg = f"AUDIT: {action} by {admin_user.email}"
    if target_user:
        msg += f" on {target_user.email}"

    audit_logger.info(msg, extra=data)
