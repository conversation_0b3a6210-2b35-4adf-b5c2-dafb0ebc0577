"""Template tags for custom avatar integration."""

from django import template
from django.contrib.auth import get_user_model

register = template.Library()
User = get_user_model()


@register.inclusion_tag('accounts_app/includes/avatar_display.html')
def display_avatar(user, size=80, css_class=""):
    """
    Display user avatar with fallback to profile picture.
    
    Args:
        user: User instance
        size: Avatar size in pixels (default: 80)
        css_class: Additional CSS classes
    
    Returns:
        Context dict with avatar information
    """
    context = {
        'user': user,
        'size': size,
        'css_class': css_class,
        'has_avatar': False,
        'avatar_url': None,
        'fallback_url': None,
    }
    
    if not user or not user.is_authenticated:
        return context
    
    # Check for django-avatar
    try:
        from avatar.templatetags.avatar_tags import avatar_url
        avatar_url_result = avatar_url(user, size)
        if avatar_url_result and not avatar_url_result.endswith('default.png'):
            context['has_avatar'] = True
            context['avatar_url'] = avatar_url_result
    except (ImportError, AttributeError):
        pass
    
    # Fallback to profile picture
    if not context['has_avatar']:
        if hasattr(user, 'customer_profile') and user.customer_profile.profile_picture:
            context['fallback_url'] = user.customer_profile.profile_picture.url
        elif hasattr(user, 'service_provider_profile') and user.service_provider_profile.logo:
            context['fallback_url'] = user.service_provider_profile.logo.url
    
    return context


@register.simple_tag
def avatar_upload_form(user):
    """
    Generate avatar upload form for the given user.
    
    Args:
        user: User instance
    
    Returns:
        HTML form for avatar upload
    """
    if not user or not user.is_authenticated:
        return ""
    
    try:
        from avatar.forms import PrimaryAvatarForm, UploadAvatarForm
        upload_form = UploadAvatarForm(user=user)
        primary_form = PrimaryAvatarForm(user=user)
        
        return {
            'upload_form': upload_form,
            'primary_form': primary_form,
            'user': user,
        }
    except ImportError:
        return ""
