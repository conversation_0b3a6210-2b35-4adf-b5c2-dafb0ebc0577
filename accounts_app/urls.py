from django.urls import path, include

from . import views
from .views import avatar_views

app_name = "accounts_app"

urlpatterns = [
    # Business Information Page
    path("for-business/", views.business_landing_view, name="for_business"),

    # New Allauth-based Authentication URLs
    path(
        "customer/signup/",
        views.AllauthCustomerSignupView.as_view(),
        name="customer_signup"
    ),
    path(
        "customer/login/",
        views.AllauthCustomerLoginView.as_view(),
        name="customer_login"
    ),
    path(
        "provider/signup/",
        views.AllauthServiceProviderSignupView.as_view(),
        name="service_provider_signup",
    ),
    path(
        "provider/login/",
        views.AllauthServiceProviderLoginView.as_view(),
        name="service_provider_login",
    ),
    path("logout/", views.AllauthUnifiedLogoutView.as_view(), name="logout"),

    # Legacy Authentication URLs (for backward compatibility)
    path(
        "legacy/customer/signup/", views.CustomerSignupView.as_view(), name="legacy_customer_signup"
    ),
    path("legacy/customer/login/", views.customer_login_view, name="legacy_customer_login"),
    path("legacy/logout/", views.unified_logout_view, name="legacy_logout"),
    # Customer Profile URLs
    path(
        "customer/profile/",
        views.CustomerProfileView.as_view(),
        name="customer_profile",
    ),
    path(
        "customer/profile/edit/",
        views.CustomerProfileEditView.as_view(),
        name="customer_profile_edit",
    ),
    path(
        "customer/change-password/",
        views.customer_change_password_view,
        name="customer_change_password",
    ),
    path(
        "customer/deactivate/",
        views.customer_deactivate_account_view,
        name="customer_deactivate",
    ),

    # Avatar Management URLs
    path(
        "avatar/upload/",
        avatar_views.upload_avatar,
        name="avatar_upload",
    ),
    path(
        "avatar/delete/",
        avatar_views.delete_avatar,
        name="avatar_delete",
    ),
    path(
        "avatar/manage/",
        avatar_views.avatar_management,
        name="avatar_management",
    ),

    # Customer Password Reset URLs
    path(
        "customer/password-reset/",
        views.CustomerPasswordResetView.as_view(),
        name="customer_password_reset",
    ),
    path(
        "customer/password-reset/done/",
        views.CustomerPasswordResetDoneView.as_view(),
        name="customer_password_reset_done",
    ),
    path(
        "customer/password-reset/confirm/<uidb64>/<token>/",
        views.CustomerPasswordResetConfirmView.as_view(),
        name="customer_password_reset_confirm",
    ),
    path(
        "customer/password-reset/complete/",
        views.CustomerPasswordResetCompleteView.as_view(),
        name="customer_password_reset_complete",
    ),
    # Service Provider Authentication URLs
    path(
        "provider/signup/",
        views.ServiceProviderSignupView.as_view(),
        name="service_provider_signup",
    ),
    path(
        "provider/signup/done/",
        views.provider_signup_done_view,
        name="provider_signup_done",
    ),
    path(
        "provider/verify/<uidb64>/<token>/",
        views.provider_email_verify_view,
        name="provider_email_verify",
    ),
    path(
        "provider/login/",
        views.service_provider_login_view,
        name="service_provider_login",
    ),
    # Service Provider Profile URLs
    path(
        "provider/profile/",
        views.ServiceProviderProfileView.as_view(),
        name="service_provider_profile",
    ),
    path(
        "provider/profile/edit/",
        views.ServiceProviderProfileEditView.as_view(),
        name="service_provider_profile_edit",
    ),
    path(
        "provider/change-password/",
        views.service_provider_change_password_view,
        name="service_provider_change_password",
    ),
    path(
        "provider/deactivate/",
        views.service_provider_deactivate_account_view,
        name="service_provider_deactivate",
    ),
    # Team Management URLs
    path("provider/team/", views.team_member_list_view, name="team_member_list"),
    path("provider/team/add/", views.team_member_add_view, name="team_member_add"),
    path(
        "provider/team/<int:member_id>/edit/",
        views.team_member_edit_view,
        name="team_member_edit",
    ),
    path(
        "provider/team/<int:member_id>/delete/",
        views.team_member_delete_view,
        name="team_member_delete",
    ),
    path(
        "provider/team/<int:member_id>/toggle/",
        views.team_member_toggle_status_view,
        name="team_member_toggle_status",
    ),
    # Service Provider Password Reset URLs
    path(
        "provider/password-reset/",
        views.ServiceProviderPasswordResetView.as_view(),
        name="service_provider_password_reset",
    ),
    path(
        "provider/password-reset/done/",
        views.ServiceProviderPasswordResetDoneView.as_view(),
        name="service_provider_password_reset_done",
    ),
    path(
        "provider/password-reset/confirm/<uidb64>/<token>/",
        views.ServiceProviderPasswordResetConfirmView.as_view(),
        name="service_provider_password_reset_confirm",
    ),
    path(
        "provider/password-reset/complete/",
        views.ServiceProviderPasswordResetCompleteView.as_view(),
        name="service_provider_password_reset_complete",
    ),
    # Premium Features
    path("premium/upgrade/", views.premium_upgrade, name="premium_upgrade"),
]
