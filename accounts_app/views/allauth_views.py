# --- Standard Library Imports ---
import logging

# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect, render
from django.urls import reverse, reverse_lazy
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.http import require_http_methods

# --- Third-Party Imports ---
from allauth.account import app_settings
from allauth.account.views import (
    LoginView,
    LogoutView,
    PasswordResetView,
    SignupView,
)
from allauth.utils import get_form_class

# --- Local App Imports ---
from ..forms.allauth_forms import (
    CustomerLoginForm,
    CustomerSignupForm,
    ServiceProviderLoginForm,
    ServiceProviderSignupForm,
    CustomerResetPasswordForm,
    ServiceProviderResetPasswordForm,
)
from ..logging_utils import log_user_activity, performance_monitor
from .common import get_client_ip

logger = logging.getLogger(__name__)


class CustomerSignupView(SignupView):
    """
    Customer signup view using allauth with custom form and templates.
    """
    form_class = CustomerSignupForm
    template_name = "accounts_app/customer/signup.html"
    success_url = reverse_lazy("accounts_app:customer_profile")

    def dispatch(self, request, *args, **kwargs):
        """Redirect authenticated users to appropriate destinations"""
        if request.user.is_authenticated:
            return redirect(
                "accounts_app:customer_profile" if request.user.is_customer else "home"
            )
        return super().dispatch(request, *args, **kwargs)

    @method_decorator(performance_monitor("customer_signup"))
    def form_valid(self, form):
        """Process valid signup form submissions"""
        response = super().form_valid(form)
        
        # Log successful signup
        log_user_activity(
            activity_type="signup",
            user=self.user,
            request=self.request,
            details={"user_type": "customer", "signup_method": "web_form"},
        )
        
        messages.success(
            self.request,
            _("Welcome to CozyWish! Your account has been created successfully.")
        )
        
        return response


class ServiceProviderSignupView(SignupView):
    """
    Service provider signup view using allauth with custom form and templates.
    """
    form_class = ServiceProviderSignupForm
    template_name = "accounts_app/provider/signup.html"
    success_url = reverse_lazy("accounts_app:provider_signup_done")

    def dispatch(self, request, *args, **kwargs):
        """Redirect authenticated users to appropriate destinations"""
        if request.user.is_authenticated:
            if request.user.is_service_provider:
                return redirect("accounts_app:service_provider_profile")
            return redirect("home")
        return super().dispatch(request, *args, **kwargs)

    @method_decorator(performance_monitor("provider_signup"))
    def form_valid(self, form):
        """Process valid signup form submissions"""
        response = super().form_valid(form)
        
        # Log successful signup
        log_user_activity(
            activity_type="signup",
            user=self.user,
            request=self.request,
            details={"user_type": "service_provider", "signup_method": "web_form"},
        )
        
        messages.success(
            self.request,
            _("Welcome to CozyWish! Please check your email to verify your account.")
        )
        
        return response


class CustomerLoginView(LoginView):
    """
    Customer login view using allauth with role validation.
    """
    form_class = CustomerLoginForm
    template_name = "accounts_app/customer/login.html"
    success_url = reverse_lazy("accounts_app:customer_profile")

    def dispatch(self, request, *args, **kwargs):
        """Redirect authenticated users"""
        if request.user.is_authenticated:
            if request.user.is_customer:
                return redirect("accounts_app:customer_profile")
            return redirect("home")
        return super().dispatch(request, *args, **kwargs)

    @method_decorator(performance_monitor("customer_login"))
    def form_valid(self, form):
        """Process valid login form submissions"""
        response = super().form_valid(form)
        
        # Log successful login
        log_user_activity(
            activity_type="login",
            user=self.user,
            request=self.request,
            details={"user_type": "customer", "login_method": "web_form"},
        )
        
        return response


class ServiceProviderLoginView(LoginView):
    """
    Service provider login view using allauth with role validation.
    """
    form_class = ServiceProviderLoginForm
    template_name = "accounts_app/provider/login.html"
    success_url = reverse_lazy("accounts_app:service_provider_profile")

    def dispatch(self, request, *args, **kwargs):
        """Redirect authenticated users"""
        if request.user.is_authenticated:
            if request.user.is_service_provider:
                return redirect("accounts_app:service_provider_profile")
            return redirect("home")
        return super().dispatch(request, *args, **kwargs)

    @method_decorator(performance_monitor("provider_login"))
    def form_valid(self, form):
        """Process valid login form submissions"""
        response = super().form_valid(form)
        
        # Log successful login
        log_user_activity(
            activity_type="login",
            user=self.user,
            request=self.request,
            details={
                "user_type": "service_provider",
                "login_method": "web_form",
                "ip_address": get_client_ip(self.request),
            },
        )
        
        return response


class CustomerPasswordResetView(PasswordResetView):
    """
    Customer password reset view using allauth.
    """
    form_class = CustomerResetPasswordForm
    template_name = "accounts_app/customer/password_reset.html"
    success_url = reverse_lazy("accounts_app:customer_password_reset_done")
    email_template_name = "accounts_app/customer/password_reset_email.html"
    subject_template_name = "accounts_app/customer/password_reset_subject.txt"


class ServiceProviderPasswordResetView(PasswordResetView):
    """
    Service provider password reset view using allauth.
    """
    form_class = ServiceProviderResetPasswordForm
    template_name = "accounts_app/provider/password_reset.html"
    success_url = reverse_lazy("accounts_app:service_provider_password_reset_done")
    email_template_name = "accounts_app/provider/password_reset_email.html"
    subject_template_name = "accounts_app/provider/password_reset_subject.txt"


class UnifiedLogoutView(LogoutView):
    """
    Unified logout view for all user types using allauth.
    """
    template_name = "accounts_app/logout.html"

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        """Handle logout with proper logging"""
        if request.user.is_authenticated:
            user_type = None
            user_email = request.user.email

            # Determine user type for logging
            if request.user.is_customer:
                user_type = "customer"
            elif request.user.is_service_provider:
                user_type = "service_provider"

            # Log the logout activity
            if user_type:
                log_user_activity(
                    activity_type="logout",
                    user=request.user,
                    request=request,
                    details={"user_type": user_type},
                )

        response = super().post(request, *args, **kwargs)
        
        messages.success(request, _("You have been logged out successfully."))
        return response


# --- Function-based views for backward compatibility ---

@require_http_methods(["GET", "POST"])
@csrf_protect
@never_cache
@performance_monitor("customer_login")
def customer_login_view(request):
    """
    Function-based customer login view for backward compatibility.
    Redirects to the class-based allauth view.
    """
    view = CustomerLoginView.as_view()
    return view(request)


@require_http_methods(["GET", "POST"])
@csrf_protect
@never_cache
@performance_monitor("provider_login")
def service_provider_login_view(request):
    """
    Function-based service provider login view for backward compatibility.
    Redirects to the class-based allauth view.
    """
    view = ServiceProviderLoginView.as_view()
    return view(request)


@login_required
def unified_logout_view(request):
    """
    Function-based unified logout view for backward compatibility.
    """
    view = UnifiedLogoutView.as_view()
    return view(request)
