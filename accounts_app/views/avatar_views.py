"""Avatar management views."""

from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import redirect, render
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST

from ..logging_utils import log_error, log_info


@login_required
@require_POST
def upload_avatar(request):
    """
    Handle avatar upload via AJAX.
    
    Returns:
        JsonResponse with success/error status
    """
    try:
        from avatar.forms import UploadAvatarForm
        from avatar.models import Avatar
        
        form = UploadAvatarForm(request.POST, request.FILES, user=request.user)
        
        if form.is_valid():
            # Delete existing avatars
            Avatar.objects.filter(user=request.user).delete()
            
            # Save new avatar
            avatar = form.save()
            
            log_info(
                info_type="avatar_upload",
                info_message=f"Avatar uploaded successfully for user {request.user.email}",
                user=request.user,
                details={"avatar_id": avatar.id},
            )
            
            return JsonResponse({
                'success': True,
                'message': str(_('Avatar uploaded successfully!')),
                'avatar_url': avatar.avatar.url,
            })
        else:
            return JsonResponse({
                'success': False,
                'message': str(_('Invalid image file. Please try again.')),
                'errors': form.errors,
            })
            
    except ImportError:
        return JsonResponse({
            'success': False,
            'message': str(_('Avatar functionality not available.')),
        })
    except Exception as e:
        log_error(
            error_type="avatar_upload",
            error_message="Failed to upload avatar",
            user=request.user,
            exception=e,
        )
        return JsonResponse({
            'success': False,
            'message': str(_('An error occurred while uploading your avatar.')),
        })


@login_required
@require_POST
def delete_avatar(request):
    """
    Delete user's avatar.
    
    Returns:
        JsonResponse with success/error status
    """
    try:
        from avatar.models import Avatar
        
        deleted_count = Avatar.objects.filter(user=request.user).delete()[0]
        
        if deleted_count > 0:
            log_info(
                info_type="avatar_delete",
                info_message=f"Avatar deleted for user {request.user.email}",
                user=request.user,
            )
            return JsonResponse({
                'success': True,
                'message': str(_('Avatar deleted successfully!')),
            })
        else:
            return JsonResponse({
                'success': False,
                'message': str(_('No avatar found to delete.')),
            })
            
    except ImportError:
        return JsonResponse({
            'success': False,
            'message': str(_('Avatar functionality not available.')),
        })
    except Exception as e:
        log_error(
            error_type="avatar_delete",
            error_message="Failed to delete avatar",
            user=request.user,
            exception=e,
        )
        return JsonResponse({
            'success': False,
            'message': str(_('An error occurred while deleting your avatar.')),
        })


@login_required
def avatar_management(request):
    """
    Avatar management page.
    
    Returns:
        Rendered avatar management template
    """
    try:
        from avatar.forms import UploadAvatarForm
        from avatar.models import Avatar
        
        context = {
            'upload_form': UploadAvatarForm(user=request.user),
            'avatars': Avatar.objects.filter(user=request.user),
            'has_avatar': Avatar.objects.filter(user=request.user).exists(),
        }
        
        return render(request, 'accounts_app/avatar_management.html', context)
        
    except ImportError:
        messages.error(request, _('Avatar functionality is not available.'))
        return redirect('accounts_app:customer_profile')
