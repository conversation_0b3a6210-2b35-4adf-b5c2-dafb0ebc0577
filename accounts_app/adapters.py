# --- Standard Library Imports ---
import logging

# --- Django Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import login
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

# --- Third-Party Imports ---
from allauth.account.adapter import DefaultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter

# --- Local App Imports ---
from .models import CustomerProfile, ServiceProviderProfile, CustomUser
from .views.common import record_login_attempt
from utility_app.email_utils import send_welcome_email

logger = logging.getLogger(__name__)


class CustomAccountAdapter(DefaultAccountAdapter):
    """
    Custom adapter for django-allauth to handle role-based redirects
    and integrate with existing CustomUser model and profile creation.
    """

    def get_login_redirect_url(self, request):
        """
        Redirect users based on their role after login.
        """
        user = request.user
        if user.is_authenticated:
            if user.is_customer:
                return reverse('accounts_app:customer_profile')
            elif user.is_service_provider:
                return reverse('accounts_app:service_provider_profile')
            elif user.is_admin:
                return reverse('admin:index')
        
        # Default redirect
        return super().get_login_redirect_url(request)

    def get_signup_redirect_url(self, request):
        """
        Redirect users based on their role after signup.
        """
        user = request.user
        if user.is_authenticated:
            if user.is_customer:
                return reverse('accounts_app:customer_profile')
            elif user.is_service_provider:
                return reverse('accounts_app:service_provider_profile')
        
        # Default redirect
        return super().get_signup_redirect_url(request)

    def save_user(self, request, user, form, commit=True):
        """
        Save user with role assignment based on signup context.
        """
        user = super().save_user(request, user, form, commit=False)
        
        # Determine role based on signup URL or form data
        signup_type = getattr(form, 'signup_type', None) or request.session.get('signup_type', 'customer')
        
        if signup_type == 'service_provider':
            user.role = CustomUser.SERVICE_PROVIDER
        else:
            user.role = CustomUser.CUSTOMER
        
        if commit:
            user.save()
            self._create_user_profile(user)
            self._send_welcome_email(user)
            
        return user

    def _create_user_profile(self, user):
        """Create appropriate profile based on user role."""
        try:
            if user.is_customer:
                CustomerProfile.objects.get_or_create(user=user)
                logger.info(f"Customer profile created for user: {user.email}")
            elif user.is_service_provider:
                ServiceProviderProfile.objects.get_or_create(user=user)
                logger.info(f"Service provider profile created for user: {user.email}")
        except Exception as error:
            logger.error(f"Failed to create profile for user {user.email}: {error}")

    def _send_welcome_email(self, user):
        """Send welcome email to new users."""
        try:
            send_welcome_email(user)
            logger.info(f"Welcome email sent to: {user.email}")
        except Exception as error:
            logger.error(f"Failed to send welcome email to {user.email}: {error}")

    def login(self, request, user):
        """
        Custom login handling with activity tracking.
        """
        # Record successful login attempt
        record_login_attempt(user, request, success=True)
        
        # Perform the actual login
        ret = super().login(request, user)
        
        # Add success message
        messages.success(request, _("Successfully logged in!"))
        
        logger.info(f"User logged in: {user.email} (Role: {user.role})")
        return ret

    def authentication_failed(self, request, **credentials):
        """
        Handle failed authentication attempts.
        """
        email = credentials.get('email')
        if email:
            try:
                user = CustomUser.objects.get(email=email)
                record_login_attempt(user, request, success=False)
            except CustomUser.DoesNotExist:
                logger.warning(f"Login attempt for non-existent user: {email}")
        
        return super().authentication_failed(request, **credentials)

    def is_open_for_signup(self, request):
        """
        Allow signup for all users.
        """
        return True

    def get_email_confirmation_url(self, request, emailconfirmation):
        """
        Customize email confirmation URL if needed.
        """
        return super().get_email_confirmation_url(request, emailconfirmation)


class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):
    """
    Custom adapter for social account authentication.
    """

    def pre_social_login(self, request, sociallogin):
        """
        Handle social login pre-processing.
        """
        # Check if user already exists with this email
        if sociallogin.user.email:
            try:
                existing_user = CustomUser.objects.get(email=sociallogin.user.email)
                if not sociallogin.is_existing:
                    # Connect social account to existing user
                    sociallogin.connect(request, existing_user)
                    logger.info(f"Connected social account to existing user: {existing_user.email}")
            except CustomUser.DoesNotExist:
                pass

    def save_user(self, request, sociallogin, form=None):
        """
        Save user from social login with appropriate role.
        """
        user = super().save_user(request, sociallogin, form)
        
        # Set default role for social signups
        if not user.role:
            user.role = CustomUser.CUSTOMER
            user.save()
        
        # Create profile if it doesn't exist
        if user.is_customer and not hasattr(user, 'customer_profile'):
            CustomerProfile.objects.get_or_create(user=user)
        elif user.is_service_provider and not hasattr(user, 'service_provider_profile'):
            ServiceProviderProfile.objects.get_or_create(user=user)
        
        return user

    def get_connect_redirect_url(self, request, socialaccount):
        """
        Redirect after connecting social account.
        """
        user = request.user
        if user.is_customer:
            return reverse('accounts_app:customer_profile')
        elif user.is_service_provider:
            return reverse('accounts_app:service_provider_profile')
        
        return super().get_connect_redirect_url(request, socialaccount)

    def is_open_for_signup(self, request, sociallogin):
        """
        Allow social signup for all users.
        """
        return True

    def populate_user(self, request, sociallogin, data):
        """
        Populate user data from social account.
        """
        user = super().populate_user(request, sociallogin, data)
        
        # Set additional user data from social account
        if not user.first_name and data.get('first_name'):
            user.first_name = data['first_name']
        if not user.last_name and data.get('last_name'):
            user.last_name = data['last_name']
        
        return user
