# Generated by Django 5.2.3 on 2025-07-05 20:01

import phonenumber_field.modelfields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("accounts_app", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="customerprofile",
            name="phone_number",
            field=phonenumber_field.modelfields.PhoneNumberField(
                blank=True,
                help_text="Enter phone number with country code (e.g., ******-123-4567)",
                max_length=128,
                region=None,
                verbose_name="phone number",
            ),
        ),
        migrations.AlterField(
            model_name="serviceproviderprofile",
            name="phone",
            field=phonenumber_field.modelfields.PhoneNumberField(
                help_text="Business contact number with country code (e.g., ******-123-4567)",
                max_length=128,
                region=None,
                verbose_name="business phone",
            ),
        ),
    ]
