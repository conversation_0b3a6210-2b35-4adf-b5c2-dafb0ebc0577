import pytest
from model_bakery import baker

from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from django.template.loader import render_to_string
from django.test import Client, RequestFactory
from django.urls import reverse
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode

from accounts_app.forms import (
    AccountDeactivationForm,
    CustomerLoginForm,
    CustomerProfileForm,
    CustomerSignupForm,
    ServiceProviderLoginForm,
    ServiceProviderProfileForm,
    ServiceProviderSignupForm,
)
from accounts_app.models import (
    CustomerProfile,
    CustomUser,
    ServiceProviderProfile,
    TeamMember,
)

# All tests use the database
pytestmark = pytest.mark.django_db


# --- Base Template Tests ---


def test_base_account_template_structure():
    """Test base_account.html template structure and CSS includes."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/base_account.html", context)

    # Test CSS includes
    assert "Inter" in rendered
    assert "Poppins" in rendered
    assert "fonts.googleapis.com" in rendered

    # Test account wrapper structure
    assert "account-wrapper" in rendered
    assert "account-card" in rendered
    assert "account-card-body" in rendered

    # Test responsive design elements
    assert "container" in rendered


def test_base_account_template_accessibility_features():
    """Test accessibility features in base_account.html template."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/base_account.html", context)

    # Test accessibility enhancements
    assert "aria-label" in rendered
    assert 'role="alert"' in rendered
    assert 'aria-live="polite"' in rendered


def test_base_account_template_password_toggle_functionality():
    """Test password toggle functionality in base_account.html."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/base_account.html", context)

    # Test password toggle JavaScript functionality
    assert "toggle-password" in rendered
    assert "fa-eye" in rendered  # CSS classes for icons
    assert "fa-eye-slash" in rendered

    # Test JavaScript functionality
    assert "addEventListener" in rendered


# --- Customer Authentication Templates ---


def test_customer_signup_template_structure():
    """Test customer signup template structure and form elements."""
    form = CustomerSignupForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test page structure
    assert "Join CozyWish" in rendered
    assert "Create your account" in rendered
    assert "account-header" in rendered

    # Test form elements
    assert 'form method="post"' in rendered
    assert "form-control" in rendered

    # Test password fields
    assert "password1" in rendered
    assert "password2" in rendered
    assert "toggle-password" in rendered


def test_customer_signup_template_form_validation():
    """Test customer signup template form validation display."""
    form = CustomerSignupForm(
        data={
            "email": "invalid-email",
            "password1": "weak",
            "password2": "different",
        }
    )
    form.is_valid()  # Trigger validation

    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test form structure (errors may not be visible without proper form rendering)
    assert 'form method="post"' in rendered
    assert "email" in rendered
    assert "password1" in rendered


def test_customer_login_template_structure():
    """Test customer login template structure and elements."""
    form = CustomerLoginForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test page structure
    assert "Welcome Back" in rendered
    assert "Sign in to your CozyWish account" in rendered
    assert "fas fa-user" in rendered

    # Test form elements
    assert 'form method="post"' in rendered
    assert "email" in rendered
    assert "password" in rendered
    assert "Remember me" in rendered
    assert "Forgot password?" in rendered

    # Test navigation links
    assert "Sign In" in rendered or "customer_login" in rendered
    assert "For Business" in rendered


def test_customer_login_template_error_handling():
    """Test customer login template error handling."""
    form = CustomerLoginForm(data={"email": "<EMAIL>", "password": "wrong"})
    form.is_valid()  # Trigger validation

    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test basic form structure
    assert 'form method="post"' in rendered
    assert "email" in rendered
    assert "password" in rendered


def test_customer_login_template_forgot_password_integration():
    """Test forgot password link integration in login template."""
    form = CustomerLoginForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test forgot password functionality
    assert "Forgot password?" in rendered
    assert "/accounts/customer/password-reset/" in rendered


# --- Service Provider Authentication Templates ---


def test_provider_signup_template_structure():
    """Test service provider signup template structure."""
    form = ServiceProviderSignupForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/provider/signup.html", context)

    # Test page structure
    assert "Join CozyWish" in rendered
    assert "business" in rendered.lower()
    assert "fas fa-store" in rendered

    # Test form structure
    assert 'form method="post"' in rendered
    assert "email" in rendered
    assert "password1" in rendered


def test_provider_signup_template_form_sections():
    """Test service provider signup template form sections."""
    form = ServiceProviderSignupForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/provider/signup.html", context)

    # Test form structure
    assert 'form method="post"' in rendered

    # Test required field indicators
    assert "required" in rendered


def test_provider_signup_done_template_structure():
    """Test provider signup done template structure."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/provider/signup_done.html", context)

    # Test success message structure
    assert "Account Created Successfully!" in rendered
    assert "verification email" in rendered
    assert "success-steps" in rendered

    # Test instructions
    assert "verification" in rendered.lower()


def test_provider_login_template_structure():
    """Test service provider login template structure."""
    form = ServiceProviderLoginForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/provider/login.html", context)

    # Test business-specific elements
    assert "Business Login" in rendered
    assert "business account" in rendered

    # Test form elements
    assert 'form method="post"' in rendered
    assert "email" in rendered
    assert "password" in rendered


# --- Password Reset Templates ---


def test_customer_password_reset_template_structure():
    """Test customer password reset template structure."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/password_reset.html", context)

    # Test page structure
    assert "Reset Password" in rendered
    assert "email address" in rendered
    assert "instructions" in rendered

    # Test form elements
    assert 'form method="post"' in rendered
    assert "email" in rendered


def test_customer_password_reset_done_template_structure():
    """Test customer password reset done template structure."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string(
        "accounts_app/customer/password_reset_done.html", context
    )

    # Test success message
    assert "Reset Link Sent!" in rendered
    assert "emailed you instructions" in rendered
    assert "fas fa-envelope-open-text" in rendered

    # Test next steps
    assert "email" in rendered.lower()


def test_customer_password_reset_confirm_template_structure():
    """Test customer password reset confirm template structure."""
    user = baker.make(CustomUser)
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)

    context = {
        "validlink": True,
        "form": None,  # Would be PasswordResetForm in real usage
        "uid": uid,
        "token": token,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string(
        "accounts_app/customer/password_reset_confirm.html", context
    )

    # Test form structure
    assert "new password" in rendered.lower()
    assert 'form method="post"' in rendered


def test_customer_password_reset_complete_template_structure():
    """Test customer password reset complete template structure."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string(
        "accounts_app/customer/password_reset_complete.html", context
    )

    # Test completion message
    assert "Password Reset Complete" in rendered
    assert "successfully" in rendered
    assert "fas fa-check-circle" in rendered

    # Test login link
    assert "sign in" in rendered.lower()


# --- Profile Templates ---


def test_customer_profile_template_structure():
    """Test customer profile template structure."""
    user = baker.make(CustomUser, role=CustomUser.CUSTOMER)
    profile = baker.make(CustomerProfile, user=user)

    # Create a proper request with the correct URL name
    request = RequestFactory().get("/accounts/customer/profile/")
    request.resolver_match = type("obj", (object,), {"url_name": "customer_profile"})()

    context = {
        "user": user,
        "profile": profile,
        "request": request,
    }
    rendered = render_to_string("accounts_app/customer/profile.html", context)

    # Test profile sections
    assert "Profile Information" in rendered
    assert "Personal Details" in rendered
    assert "Contact Information" in rendered

    # Test action buttons
    assert "Edit Profile" in rendered
    assert "Change Password" in rendered


def test_customer_profile_edit_template_structure():
    """Test customer profile edit template structure."""
    user = baker.make(CustomUser, role=CustomUser.CUSTOMER)
    profile = baker.make(CustomerProfile, user=user)
    form = CustomerProfileForm(instance=profile)

    context = {
        "form": form,
        "user": user,
        "profile": profile,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/profile_edit.html", context)

    # Test form sections
    assert "Edit Profile" in rendered
    assert "first_name" in rendered
    assert "last_name" in rendered
    assert "phone_number" in rendered

    # Test form actions
    assert "Save Changes" in rendered
    assert "Cancel" in rendered


def test_service_provider_profile_template_structure():
    """Test service provider profile template structure."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    profile = baker.make(ServiceProviderProfile, user=user)

    # Create a proper request with the correct URL name
    request = RequestFactory().get("/accounts/provider/profile/")
    request.resolver_match = type(
        "obj", (object,), {"url_name": "service_provider_profile"}
    )()

    context = {
        "user": user,
        "profile": profile,
        "request": request,
    }
    rendered = render_to_string("accounts_app/provider/profile.html", context)

    # Test business profile sections
    assert "Business Information" in rendered
    assert "Team Members" in rendered
    assert "Account Settings" in rendered

    # Test business-specific elements
    assert "Business Name" in rendered
    assert "DBA Name" in rendered
    assert "Contact Person" in rendered

    # Test social media URL display
    assert "Instagram" in rendered
    assert "Facebook" in rendered


def test_service_provider_profile_edit_template_structure():
    """Test service provider profile edit template structure."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    profile = baker.make(ServiceProviderProfile, user=user)
    form = ServiceProviderProfileForm(instance=profile)

    context = {
        "form": form,
        "user": user,
        "profile": profile,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/provider/profile_edit.html", context)

    # Test business form sections
    assert "Edit Business Profile" in rendered
    assert "legal_name" in rendered
    assert "display_name" in rendered
    assert "description" in rendered

    # Test address fields
    assert "address" in rendered
    assert "city" in rendered
    assert "state" in rendered
    assert "zip_code" in rendered


# --- Account Management Templates ---


def test_customer_change_password_template_structure():
    """Test customer change password template structure."""
    from django.contrib.auth.forms import PasswordChangeForm

    user = baker.make(CustomUser)
    form = PasswordChangeForm(user)

    # Create a proper request with the correct URL name
    request = RequestFactory().get("/accounts/customer/change-password/")
    request.resolver_match = type(
        "obj", (object,), {"url_name": "customer_change_password"}
    )()

    context = {
        "form": form,
        "request": request,
    }
    rendered = render_to_string("accounts_app/customer/change_password.html", context)

    # Test page structure
    assert "Change Password" in rendered
    assert 'form method="post"' in rendered


def test_customer_deactivate_account_template_structure():
    """Test customer deactivate account template structure."""
    user = baker.make(CustomUser, email="<EMAIL>")
    form = AccountDeactivationForm(user)

    context = {
        "form": form,
        "user": user,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string(
        "accounts_app/customer/deactivate_account.html", context
    )

    # Test warning structure
    assert "Deactivate Account" in rendered
    assert 'form method="post"' in rendered


def test_provider_change_password_template_structure():
    """Test provider change password template structure."""
    from django.contrib.auth.forms import PasswordChangeForm

    user = baker.make(CustomUser)
    form = PasswordChangeForm(user)

    # Create a proper request with the correct URL name
    request = RequestFactory().get("/accounts/provider/change-password/")
    request.resolver_match = type(
        "obj", (object,), {"url_name": "service_provider_change_password"}
    )()

    context = {
        "form": form,
        "request": request,
    }
    rendered = render_to_string("accounts_app/provider/change_password.html", context)

    # Test basic structure
    assert "Change Password" in rendered
    assert 'form method="post"' in rendered


# --- Team Management Templates ---


def test_team_member_list_template_structure():
    """Test team member list template structure."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    profile = baker.make(ServiceProviderProfile, user=user)
    team_member = baker.make(TeamMember, service_provider=profile)

    context = {
        "team_members": [team_member],
        "user": user,
        "profile": profile,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/provider/team_member_list.html", context)

    # Test team management structure
    assert "Team Management" in rendered or "Team Members" in rendered
    assert "team-member-card" in rendered  # Check for actual card structure

    # Test team member actions
    assert "Edit" in rendered
    assert "Remove" in rendered or "Delete" in rendered


def test_team_member_add_template_structure():
    """Test team member add template structure."""
    context = {
        "form": None,  # Would be TeamMemberForm in real usage
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/provider/team_member_form.html", context)

    # Test form structure
    assert "Add Team Member" in rendered or "Team Member" in rendered
    assert 'form method="post"' in rendered

    # Test form actions
    assert "Save" in rendered or "submit" in rendered.lower()
    assert "Cancel" in rendered or "Back" in rendered


def test_team_member_edit_template_structure():
    """Test team member edit template structure."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    profile = baker.make(ServiceProviderProfile, user=user)
    team_member = baker.make(TeamMember, service_provider=profile)

    context = {
        "form": None,  # Would be TeamMemberForm in real usage
        "team_member": team_member,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/team/edit.html", context)

    # Test edit form structure
    assert "Edit Team Member" in rendered
    assert 'form method="post"' in rendered

    # Test form actions
    assert "Update" in rendered or "Save" in rendered
    assert "Cancel" in rendered


# --- Email Verification Templates ---


def test_provider_email_verification_template_structure():
    """Test provider email verification template structure."""
    context = {
        "validlink": True,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/provider/email_verify.html", context)

    # Test verification success
    assert "Email Verified" in rendered
    assert "successfully verified" in rendered.lower()


def test_provider_email_verification_invalid_template():
    """Test provider email verification invalid link template."""
    context = {
        "validlink": False,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/provider/email_verify.html", context)

    # Test invalid link message
    assert "invalid" in rendered.lower() or "expired" in rendered.lower()
    assert "verification" in rendered.lower()


# --- Business Landing Template ---


def test_business_landing_template_structure():
    """Test business landing page template structure."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/business_landing.html", context)

    # Test landing page structure
    assert "For Business" in rendered
    assert "Join CozyWish" in rendered

    # Test call-to-action elements
    assert "Get Started" in rendered or "Sign Up" in rendered


# --- Template Context and Variables Tests ---


def test_template_context_user_variables():
    """Test template context includes proper user variables."""
    user = baker.make(CustomUser, first_name="John", last_name="Doe")
    profile = baker.make(CustomerProfile, user=user, first_name="John", last_name="Doe")

    # Create a proper request with the correct URL name
    request = RequestFactory().get("/accounts/customer/profile/")
    request.resolver_match = type("obj", (object,), {"url_name": "customer_profile"})()

    context = {
        "user": user,
        "profile": profile,
        "request": request,
    }
    rendered = render_to_string("accounts_app/customer/profile.html", context)

    # Test user context variables (profile template displays profile data, not user data directly)
    assert user.email in rendered
    assert "Personal Details" in rendered


def test_template_context_form_variables():
    """Test template context includes proper form variables."""
    form = CustomerSignupForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test form field names are present in rendered HTML
    assert 'name="email"' in rendered
    assert 'name="password1"' in rendered
    assert 'name="password2"' in rendered


def test_template_context_profile_variables():
    """Test template context includes proper profile variables."""
    user = baker.make(CustomUser, role=CustomUser.CUSTOMER)
    profile = baker.make(CustomerProfile, user=user, first_name="Jane")

    # Create a proper request with the correct URL name
    request = RequestFactory().get("/accounts/customer/profile/")
    request.resolver_match = type("obj", (object,), {"url_name": "customer_profile"})()

    context = {
        "user": user,
        "profile": profile,
        "request": request,
    }
    rendered = render_to_string("accounts_app/customer/profile.html", context)

    # Test profile context variables are displayed
    assert "Personal Details" in rendered
    assert "Contact Information" in rendered


# --- Template Inheritance Tests ---


def test_template_inheritance_structure():
    """Test template inheritance from base templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test inheritance from base_account.html
    assert "account-wrapper" in rendered
    assert "account-card" in rendered

    # Test CSS inheritance
    assert "fonts.googleapis.com" in rendered


def test_template_block_structure():
    """Test template block structure and overrides."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test block overrides
    assert "Join CozyWish" in rendered  # title block
    assert "Create your account" in rendered  # content block


# --- Template Security Tests ---


def test_template_csrf_protection():
    """Test CSRF protection in form templates."""
    form = CustomerSignupForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test form structure (CSRF tokens are added by middleware in real requests)
    assert 'form method="post"' in rendered


def test_template_xss_protection():
    """Test XSS protection in templates with user data."""
    user = baker.make(CustomUser, first_name='<script>alert("xss")</script>')
    profile = baker.make(
        CustomerProfile, user=user, first_name='<script>alert("xss")</script>'
    )

    # Create a proper request with the correct URL name
    request = RequestFactory().get("/accounts/customer/profile/")
    request.resolver_match = type("obj", (object,), {"url_name": "customer_profile"})()

    context = {
        "user": user,
        "profile": profile,
        "request": request,
    }
    rendered = render_to_string("accounts_app/customer/profile.html", context)

    # Test that script tags are escaped (Django auto-escapes by default)
    assert "<script>" not in rendered or "&lt;script&gt;" in rendered


# --- Template Responsive Design Tests ---


def test_template_responsive_classes():
    """Test responsive design classes in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test Bootstrap responsive classes
    assert "d-flex" in rendered
    assert "flex-column" in rendered or "flex-sm-row" in rendered
    assert "justify-content-center" in rendered

    # Test responsive utilities
    assert "mb-3" in rendered or "mb-4" in rendered


def test_template_mobile_optimization():
    """Test mobile optimization in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/base_account.html", context)

    # Test mobile-specific CSS
    assert "@media (max-width: 576px)" in rendered
    assert "viewport" in rendered  # Mobile viewport meta tag


# --- Template Accessibility Tests ---


def test_template_accessibility_attributes():
    """Test accessibility attributes in templates."""
    form = CustomerLoginForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test basic accessibility attributes that should exist
    assert "aria-label" in rendered

    # Test form accessibility - check for form elements and proper structure
    assert "input" in rendered  # Form has input fields
    assert "id=" in rendered  # Form field IDs exist
    assert "label" in rendered  # Form has labels
    assert "for=" in rendered  # Label associations exist


def test_template_semantic_html():
    """Test semantic HTML structure in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test semantic elements
    assert "<form" in rendered
    assert "<label" in rendered
    assert "<button" in rendered

    # Test heading hierarchy
    assert "<h1" in rendered or "<h2" in rendered or "<h3" in rendered


# --- Template JavaScript Tests ---


def test_template_javascript_functionality():
    """Test JavaScript functionality in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test password toggle JavaScript
    assert "toggle-password" in rendered
    assert "addEventListener" in rendered
    assert "click" in rendered

    # Test form enhancement JavaScript
    assert "DOMContentLoaded" in rendered


def test_template_javascript_accessibility():
    """Test JavaScript accessibility features in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/base_account.html", context)

    # Test keyboard navigation JavaScript
    assert "keydown" in rendered
    assert "Enter" in rendered
    assert "Space" in rendered or " " in rendered

    # Test ARIA updates
    assert "setAttribute" in rendered


# --- Template Form Validation Tests ---


def test_template_form_validation_display():
    """Test form validation error display in templates."""
    form = CustomerSignupForm(
        data={"email": "invalid", "password1": "weak", "password2": "different"}
    )
    form.is_valid()

    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test validation error structure
    assert "invalid-feedback" in rendered
    assert "form.errors" in rendered or "error" in rendered

    # Test error icons
    assert "fas fa-exclamation-circle" in rendered


def test_template_form_help_text():
    """Test form help text display in templates."""
    form = CustomerSignupForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test help text structure
    assert "form-text" in rendered
    assert "help_text" in rendered or "help text" in rendered


# --- Template Navigation Tests ---


def test_template_navigation_links():
    """Test navigation links in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test authentication navigation (checking resolved URLs)
    assert "/accounts/customer/signup/" in rendered
    assert "/accounts/for-business/" in rendered
    assert "/accounts/customer/password-reset/" in rendered

    # Test link accessibility
    assert "href=" in rendered
    assert "class=" in rendered


def test_template_breadcrumb_navigation():
    """Test breadcrumb navigation in profile templates."""
    user = baker.make(CustomUser, role=CustomUser.CUSTOMER)
    profile = baker.make(CustomerProfile, user=user)

    context = {
        "user": user,
        "profile": profile,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/profile_edit.html", context)

    # Test breadcrumb structure
    assert "breadcrumb" in rendered or "nav" in rendered
    assert "Profile" in rendered
    assert "Edit" in rendered


# --- Template Icon and Image Tests ---


def test_template_icon_usage():
    """Test icon usage in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test FontAwesome icons
    assert "fas fa-user" in rendered
    assert "fas fa-envelope" in rendered
    assert "fas fa-lock" in rendered
    assert "fas fa-eye" in rendered

    # Test icon accessibility
    assert 'aria-hidden="true"' in rendered


def test_template_image_accessibility():
    """Test image accessibility in templates."""
    user = baker.make(CustomUser, role=CustomUser.CUSTOMER)
    profile = baker.make(CustomerProfile, user=user)

    # Create a proper request with the correct URL name
    request = RequestFactory().get("/accounts/customer/profile/")
    request.resolver_match = type("obj", (object,), {"url_name": "customer_profile"})()

    context = {
        "user": user,
        "profile": profile,
        "request": request,
    }
    rendered = render_to_string("accounts_app/customer/profile.html", context)

    # Test image alt attributes
    assert "alt=" in rendered


# --- Template Performance Tests ---


def test_template_css_optimization():
    """Test CSS optimization in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/base_account.html", context)

    # Test CSS loading optimization
    assert "preconnect" in rendered
    assert "fonts.googleapis.com" in rendered
    assert "fonts.gstatic.com" in rendered

    # Test CSS variables usage
    assert "--font-primary" in rendered
    assert "--font-heading" in rendered


def test_template_script_optimization():
    """Test JavaScript optimization in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test script placement and loading
    assert "DOMContentLoaded" in rendered
    assert "addEventListener" in rendered


# --- Template Content Tests ---


def test_template_content_structure():
    """Test content structure and hierarchy in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test content hierarchy
    assert "account-header" in rendered
    assert "h1" in rendered or "h2" in rendered or "h3" in rendered

    # Test content sections
    assert "Create your account" in rendered
    assert "Join CozyWish" in rendered


def test_template_content_localization():
    """Test content localization support in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test localization tags (if used)
    assert "Welcome Back" in rendered
    assert "Sign in" in rendered


# --- Template Error Handling Tests ---


def test_template_error_page_structure():
    """Test error page template structure."""
    context = {
        "error_message": "Test error message",
        "request": RequestFactory().get("/"),
    }

    # Test 404 error template if exists
    try:
        rendered = render_to_string("accounts_app/errors/404.html", context)
        assert "Not Found" in rendered or "404" in rendered
        assert "error" in rendered.lower()
    except:
        pass  # Template may not exist


def test_template_validation_error_structure():
    """Test validation error structure in templates."""
    form = CustomerLoginForm(data={"email": "", "password": ""})
    form.is_valid()

    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test error display structure
    assert "alert" in rendered or "invalid-feedback" in rendered
    assert "error" in rendered


# --- Template Integration Tests ---


def test_template_form_integration():
    """Test form integration in templates."""
    form = CustomerSignupForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test form field integration
    assert "Email" in rendered
    assert "Password" in rendered


def test_template_model_integration():
    """Test model data integration in templates."""
    user = baker.make(CustomUser, first_name="John", last_name="Doe")
    profile = baker.make(CustomerProfile, user=user, first_name="John", last_name="Doe")

    # Create a proper request with the correct URL name
    request = RequestFactory().get("/accounts/customer/profile/")
    request.resolver_match = type("obj", (object,), {"url_name": "customer_profile"})()

    context = {
        "user": user,
        "profile": profile,
        "request": request,
    }
    rendered = render_to_string("accounts_app/customer/profile.html", context)

    # Test model data display (profile template displays profile data, not user data directly)
    assert user.email in rendered
    assert "Personal Details" in rendered


# --- Template URL Integration Tests ---


def test_template_url_integration():
    """Test URL integration in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test URL name usage (checking resolved URLs)
    assert "/accounts/customer/signup/" in rendered
    assert "/accounts/customer/password-reset/" in rendered
    assert "/accounts/for-business/" in rendered

    # Test URL structure
    assert "href=" in rendered
    assert "window.location.href" in rendered


def test_template_static_file_integration():
    """Test static file integration in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/base_account.html", context)

    # Test static file loading (checking resolved static URLs)
    assert "/static/" in rendered
    assert "css" in rendered

    # Test CSS and font loading
    assert "fonts.googleapis.com" in rendered


# --- Template Widget Tests ---


def test_template_widget_integration():
    """Test form widget integration in templates."""
    form = CustomerSignupForm()
    context = {
        "form": form,
        "request": RequestFactory().get("/"),
    }
    rendered = render_to_string("accounts_app/customer/signup.html", context)

    # Test widget functionality (checking for form controls and styling)
    assert "form-control" in rendered
    assert "class=" in rendered
    assert "placeholder=" in rendered


def test_template_custom_widget_styling():
    """Test custom widget styling in templates."""
    context = {"request": RequestFactory().get("/")}
    rendered = render_to_string("accounts_app/customer/login.html", context)

    # Test custom styling classes
    assert "form-control" in rendered
    assert "form-floating" in rendered
    assert "btn btn-primary" in rendered
