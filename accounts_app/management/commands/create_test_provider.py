from django.core.management.base import BaseCommand

from accounts_app.models import CustomUser, ServiceProviderProfile


class Command(BaseCommand):
    help = "Create a test service provider account for development"

    def add_arguments(self, parser):
        parser.add_argument(
            "--email",
            type=str,
            default="<EMAIL>",
            help="Email for the test provider account",
        )
        parser.add_argument(
            "--password",
            type=str,
            default="testpass123",
            help="Password for the test provider account",
        )

    def handle(self, *args, **options):
        email = options["email"]
        password = options["password"]

        # Check if user already exists
        if CustomUser.objects.filter(email=email).exists():
            self.stdout.write(
                self.style.WARNING(f"User with email {email} already exists")
            )
            return

        # Create service provider user
        user = CustomUser.objects.create_user(
            email=email,
            password=password,
            role=CustomUser.SERVICE_PROVIDER,
            is_active=True,  # Skip email verification for testing
        )

        # Create service provider profile
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name="Test Business LLC",
            display_name="Test Spa",
            phone="+***********",
            contact_name="Test Manager",
            address="123 Test Street",
            city="Los Angeles",
            state="CA",
            zip_code="90210",
        )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created test service provider:\n"
                f"Email: {email}\n"
                f"Password: {password}\n"
                f"Business: {profile.legal_name}"
            )
        )
