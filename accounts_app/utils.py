# --- Standard Library Imports ---
import os
import uuid
from typing import Optional

from django.utils.translation import gettext_lazy as _

# --- File Path Generators for Uploaded Images ---


def _generate_filename(prefix: str, identifier: Optional[str], filename: str) -> str:
    ext = filename.split(".")[-1]
    unique_id = uuid.uuid4().hex
    identifier_part = f"{identifier}_" if identifier else ""
    return f"{prefix}_{identifier_part}{unique_id}.{ext}"


def get_customer_profile_image_path(instance, filename: str) -> str:
    return os.path.join(
        "customers",
        "profile_images",
        _generate_filename("customer", str(instance.user.id), filename),
    )


def get_provider_profile_image_path(instance, filename: str) -> str:
    return os.path.join(
        "providers",
        "logos",
        _generate_filename("provider", str(instance.user.id), filename),
    )


def get_staff_profile_image_path(instance, filename: str) -> str:
    return os.path.join(
        "providers", "staff_images", _generate_filename("staff", None, filename)
    )
