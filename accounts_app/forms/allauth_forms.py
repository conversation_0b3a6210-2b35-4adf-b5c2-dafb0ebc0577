# --- Django Imports ---
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.utils.translation import gettext_lazy as _

# --- Third-Party Imports ---
from allauth.account.forms import SignupForm, LoginForm, ResetPasswordForm
from allauth.account.utils import filter_users_by_email

# --- Local App Imports ---
from ..models import CustomUser
from .common import AccessibleFormMixin


class CustomSignupForm(AccessibleFormMixin, SignupForm):
    """
    Custom signup form that integrates with allauth while maintaining
    existing design and functionality.
    """
    
    first_name = forms.CharField(
        label=_("First Name"),
        max_length=30,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your first name"),
                "autocomplete": "given-name",
            }
        ),
    )
    
    last_name = forms.CharField(
        label=_("Last Name"),
        max_length=30,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your last name"),
                "autocomplete": "family-name",
            }
        ),
    )
    
    agree_to_terms = forms.BooleanField(
        label=_("I agree to the Terms of Service and Privacy Policy"),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                "class": "form-check-input",
            }
        ),
        error_messages={
            "required": _(
                "You must agree to the Terms of Service and Privacy Policy to create an account."
            ),
        },
    )

    def __init__(self, *args, **kwargs):
        self.signup_type = kwargs.pop('signup_type', 'customer')
        super().__init__(*args, **kwargs)
        
        # Customize email field
        self.fields['email'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': _('Enter your email address'),
            'autocomplete': 'email',
        })
        self.fields['email'].help_text = _("We'll never share your email with anyone else.")
        
        # Customize password fields
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': _('Enter your password'),
            'autocomplete': 'new-password',
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': _('Confirm your password'),
            'autocomplete': 'new-password',
        })

    def save(self, request):
        """
        Save the user with the appropriate role.
        """
        # Store signup type in session for adapter to use
        request.session['signup_type'] = self.signup_type
        
        user = super().save(request)
        
        # Set additional fields
        if self.cleaned_data.get('first_name'):
            user.first_name = self.cleaned_data['first_name']
        if self.cleaned_data.get('last_name'):
            user.last_name = self.cleaned_data['last_name']
        
        # Set role based on signup type
        if self.signup_type == 'service_provider':
            user.role = CustomUser.SERVICE_PROVIDER
        else:
            user.role = CustomUser.CUSTOMER
            
        user.save()
        return user


class CustomerSignupForm(CustomSignupForm):
    """Customer-specific signup form."""
    
    def __init__(self, *args, **kwargs):
        kwargs['signup_type'] = 'customer'
        super().__init__(*args, **kwargs)


class ServiceProviderSignupForm(CustomSignupForm):
    """Service provider-specific signup form."""
    
    business_name = forms.CharField(
        label=_("Business Name"),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your business name"),
            }
        ),
        help_text=_("You can update this later in your profile."),
    )
    
    def __init__(self, *args, **kwargs):
        kwargs['signup_type'] = 'service_provider'
        super().__init__(*args, **kwargs)


class CustomLoginForm(AccessibleFormMixin, LoginForm):
    """
    Custom login form that integrates with allauth while maintaining
    existing design and role validation.
    """
    
    def __init__(self, *args, **kwargs):
        self.user_type = kwargs.pop('user_type', None)
        super().__init__(*args, **kwargs)
        
        # Customize field styling
        self.fields['login'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': _('Enter your email address'),
            'autocomplete': 'email',
        })
        self.fields['login'].label = _('Email Address')
        
        self.fields['password'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': _('Enter your password'),
            'autocomplete': 'current-password',
        })

    def clean(self):
        """
        Add role-based validation to the login form.
        """
        cleaned_data = super().clean()
        
        if self.user_type and hasattr(self, 'user') and self.user:
            # Validate user role matches expected type
            if self.user_type == 'customer' and not self.user.is_customer:
                raise forms.ValidationError(
                    _("Invalid email or password."), 
                    code="invalid_login"
                )
            elif self.user_type == 'service_provider' and not self.user.is_service_provider:
                raise forms.ValidationError(
                    _("Invalid email or password."), 
                    code="invalid_login"
                )
        
        return cleaned_data


class CustomerLoginForm(CustomLoginForm):
    """Customer-specific login form."""
    
    def __init__(self, *args, **kwargs):
        kwargs['user_type'] = 'customer'
        super().__init__(*args, **kwargs)


class ServiceProviderLoginForm(CustomLoginForm):
    """Service provider-specific login form."""
    
    def __init__(self, *args, **kwargs):
        kwargs['user_type'] = 'service_provider'
        super().__init__(*args, **kwargs)


class CustomResetPasswordForm(AccessibleFormMixin, ResetPasswordForm):
    """
    Custom password reset form with role-based filtering.
    """
    
    def __init__(self, *args, **kwargs):
        self.user_type = kwargs.pop('user_type', None)
        super().__init__(*args, **kwargs)
        
        # Customize email field
        self.fields['email'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': _('Enter your email address'),
            'autocomplete': 'email',
        })

    def clean_email(self):
        """
        Validate email and optionally filter by user type.
        """
        email = self.cleaned_data["email"]
        email = filter_users_by_email(email, is_active=True)
        
        if self.user_type:
            # Filter by user role
            if self.user_type == 'customer':
                email = email.filter(role=CustomUser.CUSTOMER)
            elif self.user_type == 'service_provider':
                email = email.filter(role=CustomUser.SERVICE_PROVIDER)
        
        if not email:
            raise forms.ValidationError(
                _("The e-mail address is not assigned to any user account")
            )
        
        return self.cleaned_data["email"]


class CustomerResetPasswordForm(CustomResetPasswordForm):
    """Customer-specific password reset form."""
    
    def __init__(self, *args, **kwargs):
        kwargs['user_type'] = 'customer'
        super().__init__(*args, **kwargs)


class ServiceProviderResetPasswordForm(CustomResetPasswordForm):
    """Service provider-specific password reset form."""
    
    def __init__(self, *args, **kwargs):
        kwargs['user_type'] = 'service_provider'
        super().__init__(*args, **kwargs)
