# --- Django Imports ---
from django import forms
from django.contrib.auth.forms import PasswordChangeForm, UserCreationForm
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Third-Party Imports (Optional Utils) ---
try:
    from utils.forms import ProfileImageForm
    from utils.image_service import ImageService
except ImportError:
    ProfileImageForm = None
    ImageUploadForm = None
    ImageService = None

# --- Third-Party Imports ---
from phonenumber_field.formfields import PhoneNumberField

# --- Local App Imports ---
from ..logging_utils import log_error, log_info
from ..models import CustomerProfile, CustomUser
from .common import AccessibleFormMixin

# --- Customer signup form ---


class CustomerSignupForm(AccessibleFormMixin, UserCreationForm):
    """
    Sign up a new customer with email and password.

    Features:
    - Email uniqueness validation
    - Password confirmation
    - Accessible form styling
    - Password strength validation with proper error display
    """

    email = forms.EmailField(
        label=_("Email Address"),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your email address"),
                "autocomplete": "email",
            }
        ),
        help_text=_("We'll never share your email with anyone else."),
    )
    password1 = forms.CharField(
        label=_("Password"),
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Create a strong password"),
                "autocomplete": "new-password",
            }
        ),
        help_text=_(
            "Your password must contain at least 8 characters, "
            "cannot be entirely numeric, and should not be too common."
        ),
    )
    password2 = forms.CharField(
        label=_("Confirm Password"),
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Confirm your password"),
                "autocomplete": "new-password",
            }
        ),
        help_text=_("Enter the same password as before, for verification."),
    )
    agree_to_terms = forms.BooleanField(
        label=_("I agree to the Terms of Service and Privacy Policy"),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                "class": "form-check-input",
            }
        ),
        error_messages={
            "required": _(
                "You must agree to the Terms of Service and Privacy Policy to create an account."
            ),
        },
    )

    class Meta:
        model = CustomUser
        fields = ("email", "password1", "password2", "agree_to_terms")

    def clean_email(self):
        """
        Ensure the email address is unique.

        Raises:
            ValidationError: If the email already exists.

        Returns:
            str: The cleaned email address.
        """
        email = self.cleaned_data.get("email")
        if email and CustomUser.objects.filter(email=email).exists():
            raise ValidationError(
                _("A user with this email already exists."), code="email_exists"
            )
        return email

    def clean_password1(self):
        """
        Validate password1 using Django's password validators.

        This ensures password strength errors are displayed on the password1 field
        instead of password2 field.

        Returns:
            str: The cleaned password.

        Raises:
            ValidationError: If password doesn't meet requirements.
        """
        password1 = self.cleaned_data.get("password1")
        if password1:
            # Import here to avoid circular imports
            from django.contrib.auth.password_validation import validate_password

            try:
                # Create a temporary user instance for validation if none exists
                user = self.instance
                if user is None:
                    # Create a temporary user with the email for validation
                    email = self.cleaned_data.get("email")
                    user = CustomUser(email=email)
                validate_password(password1, user)
            except ValidationError as error:
                raise ValidationError(error.messages)
        return password1

    def clean_password2(self):
        """
        Override the default clean_password2 to avoid duplicate validation errors.

        Only check if passwords match, not password strength (already done in clean_password1).

        Returns:
            str: The cleaned password2.

        Raises:
            ValidationError: If passwords don't match.
        """
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")

        if password1 and password2 and password1 != password2:
            raise ValidationError(
                _("The two password fields didn't match."),
                code="password_mismatch",
            )
        return password2

    def _post_clean(self):
        """
        Override _post_clean to prevent duplicate password validation.

        UserCreationForm's _post_clean validates passwords and adds errors to password2.
        Since we already validate in clean_password1, we skip the password validation
        but still call the parent's _post_clean for other processing.
        """
        # Call ModelForm's _post_clean, but skip UserCreationForm's password validation
        super(UserCreationForm, self)._post_clean()
        # Note: We don't call UserCreationForm's _post_clean to avoid duplicate validation

    def validate_password_for_user(self, user):
        """
        Override to prevent password validation errors from being added to password2.

        Since we already validate passwords in clean_password1, we don't need
        to validate them again here. This prevents duplicate validation and
        ensures errors appear on the password1 field.
        """
        # Do nothing - password validation is handled in clean_password1
        # This prevents Django from running validation again and adding errors to password2
        pass

    def clean_agree_to_terms(self):
        """
        Ensure the user has agreed to the terms of service.

        Raises:
            ValidationError: If the terms checkbox is not checked.

        Returns:
            bool: The cleaned agree_to_terms value.
        """
        agree_to_terms = self.cleaned_data.get("agree_to_terms")
        if not agree_to_terms:
            raise ValidationError(
                _(
                    "You must agree to the Terms of Service and Privacy Policy to create an account."
                ),
                code="terms_required",
            )
        return agree_to_terms

    def save(self, commit=True):
        """
        Create and return a new customer user.

        Args:
            commit (bool): Whether to save the user to the database.

        Returns:
            CustomUser: The created user instance.
        """
        user = super().save(commit=False)
        user.email = self.cleaned_data["email"]
        user.role = CustomUser.CUSTOMER
        if commit:
            user.save()
        return user


# --- Customer login form ---


class CustomerLoginForm(AccessibleFormMixin, forms.Form):
    """
    Authenticate a customer using email and password.

    Features:
    - Role validation
    - Active account check
    """

    email = forms.EmailField(
        label=_("Email Address"),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your email address"),
                "autocomplete": "email",
            }
        ),
    )
    password = forms.CharField(
        label=_("Password"),
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your password"),
                "autocomplete": "current-password",
            }
        ),
    )

    def __init__(self, request=None, *args, **kwargs):
        """
        Store HTTP request for authentication context.
        """
        self.request = request
        super().__init__(*args, **kwargs)

    def clean(self):
        """
        Validate the user's email and password combination.

        Raises:
            ValidationError: On authentication failure.

        Returns:
            dict: The cleaned data.
        """
        cleaned = super().clean()
        email = cleaned.get("email")
        password = cleaned.get("password")

        if email and password:
            from django.contrib.auth import authenticate

            try:
                user = CustomUser.objects.get(email=email)
                if not user.is_customer:
                    raise ValidationError(
                        _("Invalid email or password."), code="invalid_login"
                    )
                if not user.is_active and user.check_password(password):
                    raise ValidationError(
                        _("Your account has been deactivated."), code="inactive"
                    )
                authenticated = authenticate(
                    self.request, username=email, password=password
                )
                if not authenticated:
                    raise ValidationError(
                        _("Invalid email or password."), code="invalid_login"
                    )
                self.user_cache = authenticated
            except CustomUser.DoesNotExist:
                raise ValidationError(
                    _("Invalid email or password."), code="invalid_login"
                )
        return cleaned

    def get_user(self):
        """
        Retrieve the authenticated user after clean().

        Returns:
            CustomUser or None: The authenticated user.
        """
        return getattr(self, "user_cache", None)


# --- Customer password change form ---


class CustomerPasswordChangeForm(AccessibleFormMixin, PasswordChangeForm):
    """
    Allow customers to change their password.

    Inherits built-in PasswordChangeForm with custom styling.
    """

    old_password = forms.CharField(
        label=_("Current Password"),
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your current password"),
                "autocomplete": "current-password",
            }
        ),
    )
    new_password1 = forms.CharField(
        label=_("New Password"),
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your new password"),
                "autocomplete": "new-password",
            }
        ),
        help_text=_(
            "Your password must contain at least 8 characters and "
            "cannot be entirely numeric."
        ),
    )
    new_password2 = forms.CharField(
        label=_("Confirm New Password"),
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Confirm your new password"),
                "autocomplete": "new-password",
            }
        ),
    )


# --- Customer profile form ---


class CustomerProfileForm(AccessibleFormMixin, forms.ModelForm):
    """
    Edit customer profile personal information.

    Features:
    - Date of birth selection
    - Phone number normalization
    - Profile picture processing
    """

    MONTH_CHOICES = [
        ("", _("Select Month")),
        (1, _("January")),
        (2, _("February")),
        (3, _("March")),
        (4, _("April")),
        (5, _("May")),
        (6, _("June")),
        (7, _("July")),
        (8, _("August")),
        (9, _("September")),
        (10, _("October")),
        (11, _("November")),
        (12, _("December")),
    ]
    YEAR_CHOICES = [("", _("Select Year"))] + [
        (year, str(year)) for year in range(1920, 2010)
    ]

    first_name = forms.CharField(
        label=_("First Name"),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your first name"),
            }
        ),
    )
    last_name = forms.CharField(
        label=_("Last Name"),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your last name"),
            }
        ),
    )
    phone_number = PhoneNumberField(
        label=_("Phone Number"),
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": "+****************",
                "type": "tel",
            }
        ),
        help_text=_("Enter phone number with country code (e.g., ******-123-4567)"),
    )
    gender = forms.ChoiceField(
        label=_("Gender"),
        choices=[("", _("Select Gender"))] + list(CustomerProfile.GENDER_CHOICES),
        required=False,
        widget=forms.Select(attrs={"class": "form-select"}),
    )
    birth_month = forms.ChoiceField(
        label=_("Birth Month"),
        choices=MONTH_CHOICES,
        required=False,
        widget=forms.Select(attrs={"class": "form-select"}),
    )
    birth_year = forms.ChoiceField(
        label=_("Birth Year"),
        choices=YEAR_CHOICES,
        required=False,
        widget=forms.Select(attrs={"class": "form-select"}),
    )
    address = forms.CharField(
        label=_("Address"),
        max_length=255,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your address"),
            }
        ),
    )
    city = forms.CharField(
        label=_("City"),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your city"),
            }
        ),
    )
    zip_code = forms.CharField(
        label=_("ZIP Code"),
        max_length=10,
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter your ZIP code"),
            }
        ),
    )
    profile_picture = forms.ImageField(
        label=_("Profile Picture"),
        required=False,
        widget=forms.FileInput(
            attrs={
                "class": "form-control",
                "accept": "image/jpeg,image/png,image/webp,image/gif",
            }
        ),
        help_text=_(
            "Upload a profile picture (JPG, PNG, WebP, or GIF, max 5MB). "
            "Image will be automatically resized and optimized."
        ),
    )

    # Avatar upload field (django-avatar integration)
    avatar_upload = forms.ImageField(
        label=_("Avatar Upload"),
        required=False,
        widget=forms.FileInput(
            attrs={
                "class": "form-control",
                "accept": "image/jpeg,image/png,image/webp,image/gif",
                "style": "display: none;",  # Hidden by default, shown via JavaScript
            }
        ),
        help_text=_("Upload an avatar image (automatically resized to multiple sizes)"),
    )

    class Meta:
        model = CustomerProfile
        fields = [
            "first_name",
            "last_name",
            "phone_number",
            "gender",
            "birth_month",
            "birth_year",
            "address",
            "city",
            "zip_code",
            "profile_picture",
        ]

    def clean_birth_month(self):
        """
        Convert empty birth_month selection to None.
        """
        month = self.cleaned_data.get("birth_month")
        return None if month == "" else month

    def clean_birth_year(self):
        """
        Convert empty birth_year selection to None.
        """
        year = self.cleaned_data.get("birth_year")
        return None if year == "" else year

    def clean_profile_picture(self):
        """
        Validate and process the uploaded profile picture.

        Returns:
            UploadedFile or None: The validated image or None.

        Raises:
            ValidationError: On invalid or oversized image.
        """
        image = self.cleaned_data.get("profile_picture")
        if not image:
            return None
        # Skip validation for existing images
        if hasattr(image, "url") and not hasattr(image, "content_type"):
            return image
        # Advanced processing if utilities available
        if ProfileImageForm:
            try:
                form = ProfileImageForm(
                    files={"image": image},
                    entity_type="customers",
                    entity_id=self.instance.user.id if self.instance else None,
                )
                if not form.is_valid():
                    raise ValidationError(form.errors["image"])
                return image
            except ValidationError as e:
                log_error(
                    error_type="image_validation",
                    error_message="Invalid customer profile image",
                    user=self.instance.user if self.instance else None,
                    exception=e,
                )
                raise
            except Exception as e:
                log_error(
                    error_type="image_processing",
                    error_message="Failed to process customer profile image",
                    user=self.instance.user if self.instance else None,
                    exception=e,
                )
                raise ValidationError(
                    _("Invalid image file. Please upload JPG or PNG.")
                )
        # Basic fallback validation
        if image.size > 5 * 1024 * 1024:
            raise ValidationError(
                _("Image file too large. Maximum size is 5MB."), code="file_too_large"
            )
        if hasattr(image, "content_type"):
            allowed = ["image/jpeg", "image/png"]
            if image.content_type not in allowed:
                raise ValidationError(
                    _("Invalid image format. Please use JPG or PNG only."),
                    code="invalid_format",
                )
        return image

    def save(self, commit=True):
        """
        Persist profile updates, processing picture when needed.

        Returns:
            CustomerProfile: The saved profile instance.
        """
        profile = super().save(commit=False)
        image = self.cleaned_data.get("profile_picture")
        avatar_image = self.cleaned_data.get("avatar_upload")

        # Process avatar upload with django-avatar
        if avatar_image:
            try:
                from avatar.models import Avatar
                # Delete existing avatars for this user
                Avatar.objects.filter(user=profile.user).delete()
                # Create new avatar
                avatar = Avatar(user=profile.user, primary=True)
                avatar.avatar.save(avatar_image.name, avatar_image, save=True)
                log_info(
                    info_type="avatar_processing",
                    info_message=f"Avatar uploaded successfully for user {profile.user.email}",
                    user=profile.user,
                    details={"avatar_name": avatar_image.name},
                )
            except Exception as e:
                log_error(
                    error_type="avatar_processing",
                    error_message="Failed to process avatar upload",
                    user=profile.user,
                    exception=e,
                )

        # Process new image if utilities are available
        if image and ImageService and ProfileImageForm:
            try:
                log_info(
                    info_type="image_processing",
                    info_message=f"Starting profile image processing for user {profile.user.email}",
                    user=profile.user,
                    details={"image_name": image.name, "image_size": image.size},
                )

                form = ProfileImageForm(
                    files={"image": image},
                    entity_type="customers",
                    entity_id=profile.user.id,
                )
                if form.is_valid():
                    log_info(
                        info_type="image_processing",
                        info_message="Profile image form is valid, processing image",
                        user=profile.user,
                    )
                    processed = form.process()
                    path, _ = ImageService.save_image(processed, user=profile.user)
                    profile.profile_picture = path
                    log_info(
                        info_type="image_processing",
                        info_message=f"Profile image saved successfully: {path}",
                        user=profile.user,
                        details={"saved_path": path},
                    )
                else:
                    log_error(
                        error_type="image_validation",
                        error_message="Profile image form validation failed",
                        user=profile.user,
                        details={"form_errors": form.errors},
                    )
            except Exception as e:
                log_error(
                    error_type="image_processing",
                    error_message="Failed to process profile image",
                    user=profile.user,
                    exception=e,
                )
        if commit:
            profile.save()
        return profile
