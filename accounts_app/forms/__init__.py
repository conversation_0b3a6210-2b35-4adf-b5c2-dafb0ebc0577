"""
Forms package for accounts_app.

This package contains all form classes organized by feature area for better maintainability.
All forms are imported here to maintain backward compatibility.
"""

# --- Local App Imports ---
from .allauth_forms import (
    CustomerLoginForm as AllauthCustomerLoginForm,
    CustomerResetPasswordForm,
    CustomerSignupForm as AllauthCustomerSignupForm,
    CustomLoginForm,
    CustomResetPasswordForm,
    CustomSignupForm,
    ServiceProviderLoginForm as AllauthServiceProviderLoginForm,
    ServiceProviderResetPasswordForm,
    ServiceProviderSignupForm as AllauthServiceProviderSignupForm,
)
from .common import AccessibleFormMixin, AccountDeactivationForm
from .customer import (
    CustomerLoginForm,
    CustomerPasswordChangeForm,
    CustomerProfileForm,
    CustomerSignupForm,
)
from .provider import (
    ServiceProviderLoginForm,
    ServiceProviderPasswordChangeForm,
    ServiceProviderProfileForm,
    ServiceProviderSignupForm,
)
from .team import TeamMemberForm

# Make all forms available at package level for backward compatibility
__all__ = [
    # Common forms and mixins
    "AccessibleFormMixin",
    "AccountDeactivationForm",
    # Allauth forms
    "CustomSignupForm",
    "CustomLoginForm",
    "CustomResetPasswordForm",
    "AllauthCustomerSignupForm",
    "AllauthCustomerLoginForm",
    "CustomerResetPasswordForm",
    "AllauthServiceProviderSignupForm",
    "AllauthServiceProviderLoginForm",
    "ServiceProviderResetPasswordForm",
    # Customer forms (legacy)
    "CustomerSignupForm",
    "CustomerLoginForm",
    "CustomerProfileForm",
    "CustomerPasswordChangeForm",
    # Service provider forms (legacy)
    "ServiceProviderSignupForm",
    "ServiceProviderLoginForm",
    "ServiceProviderProfileForm",
    "ServiceProviderPasswordChangeForm",
    # Team management forms
    "TeamMemberForm",
]
