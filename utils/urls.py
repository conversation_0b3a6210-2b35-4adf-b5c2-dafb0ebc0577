from django.urls import path

from . import test_views, views

app_name = "utils"

urlpatterns = [
    # Image upload views
    path("images/upload/", views.image_upload_view, name="image_upload"),
    path(
        "images/upload/<str:image_type>/",
        views.image_upload_view,
        name="image_upload_type",
    ),
    path(
        "images/upload/<str:image_type>/<str:entity_type>/<int:entity_id>/",
        views.image_upload_view,
        name="image_upload_entity",
    ),
    # AJAX image upload
    path("api/images/upload/", views.ajax_image_upload, name="ajax_image_upload"),
    # Error testing views (DEBUG mode only)
    path("test-errors/", test_views.test_error_menu, name="test_error_menu"),
    path("test-errors/404/", test_views.test_404_error, name="test_404"),
    path("test-errors/500/", test_views.test_500_error, name="test_500"),
    path("test-errors/403/", test_views.test_403_error, name="test_403"),
    path("test-errors/400/", test_views.test_400_error, name="test_400"),
]
