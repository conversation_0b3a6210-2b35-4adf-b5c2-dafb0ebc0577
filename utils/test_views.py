"""
Test views for manually testing error handling.
These views should only be available in DEBUG mode.
"""

from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.http import (
    Http404,
    HttpResponseBadRequest,
    HttpResponseForbidden,
    HttpResponseServerError,
)
from django.shortcuts import render
from django.views.decorators.http import require_http_methods


def test_404_error(request):
    """Trigger a 404 error for testing."""
    if not settings.DEBUG:
        raise Http404("Test views only available in DEBUG mode")

    raise Http404("This is a test 404 error")


def test_500_error(request):
    """Trigger a 500 error for testing."""
    if not settings.DEBUG:
        raise Http404("Test views only available in DEBUG mode")

    # Intentionally raise an exception to trigger 500 error
    raise Exception("This is a test 500 error")


def test_403_error(request):
    """Trigger a 403 error for testing."""
    if not settings.DEBUG:
        raise Http404("Test views only available in DEBUG mode")

    return HttpResponseForbidden("This is a test 403 error")


def test_400_error(request):
    """Trigger a 400 error for testing."""
    if not settings.DEBUG:
        raise Http404("Test views only available in DEBUG mode")

    return HttpResponseBadRequest("This is a test 400 error")


def test_error_menu(request):
    """Display a menu of error tests."""
    if not settings.DEBUG:
        raise Http404("Test views only available in DEBUG mode")

    return render(request, "utils/test_error_menu.html")
